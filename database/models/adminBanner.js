const mongoose = require("mongoose");
const { DB_COLLECTIONS } = require("../../utils/db-collection-names");
Schema = mongoose.Schema;

const adminBannerSchema = new mongoose.Schema({
    bannerImage: { type: String, default: "" },
    webBannerImage: { type: String, default: "" },
    bannerUrl: { type: String, default: "" },
    publicationStartDate: { type: String, default: "" },
    publicationStartTime: { type: String, default: "" },
    publicationEndDate: { type: String, default: "" },
    publicationEndTime: { type: String, default: "" },
    saveAs: { type: String, enum: ["draft", "publish"] },
    order: { type: Number, default: 0 },
    isDelete: { type: Boolean, default: false },
    restrictionAccess: { type: String, enum: ["public", "restricted"], default: "public" },
    restrictedAccessGroupId: [{ type: mongoose.Schema.Types.ObjectId, ref: "group", default: null }],
    restrictedAccessMembershipPlanId: [{ type: mongoose.Schema.Types.ObjectId, ref: "membership_plan", default: null }],
    restrictedAccessUserId:[{ type: mongoose.Schema.Types.ObjectId, ref: "airtable-syncs", default: null }],
    restrictedAccessEventId: [{ type: mongoose.Schema.Types.ObjectId, ref: 'event', default: null }],
    restrictedAccessTagId: [{ type: mongoose.Schema.Types.ObjectId, ref: 'contentArchive_tag', default: null }],
    restrictedAccessTierId: [{ type: mongoose.Schema.Types.ObjectId, default: null }],
    relation_id: { type: mongoose.Schema.Types.ObjectId, ref:  DB_COLLECTIONS.COMMUNITIES },
}, {
    timestamps: true
});

const autoPopulateChildren = function (next) {
    this.populate("relation_id" );
    this.populate("restrictedAccessGroupId", "groupTitle", { isDelete: false });
    this.populate("restrictedAccessUserId", { "Preferred Email": 1, "attendeeDetail": 1,"email": 1,"first_name": 1,"last_name": 1,"firebaseId": 1,"profileImg": 1,});
    this.populate("restrictedAccessMembershipPlanId" );
    this.populate("restrictedAccessEventId", "title", { isDelete: false });
    this.populate("restrictedAccessTagId", "name", { isDelete: false });
    next();
} 
adminBannerSchema.pre("findOne", autoPopulateChildren)
adminBannerSchema.pre("findById", autoPopulateChildren) 
adminBannerSchema.pre("find", autoPopulateChildren)

// create index
adminBannerSchema.index({ isDelete: 1 });
	
module.exports = mongoose.model("adminBanner", adminBannerSchema);
