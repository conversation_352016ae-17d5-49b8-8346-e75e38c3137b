const mongoose = require("mongoose");
const validator = require("validator");

const config = require("config");
const { DB_COLLECTIONS } = require("../../utils/db-collection-names");
const user_role = config.get("user");

const fieldSchema = mongoose.Schema(
  {
    title: { type: String },
    photo: { type: String },
    name: { type: String, trim: true, default: "" },
    firstName: { type: String, trim: true, default: "" },
    lastName: { type: String, trim: true, default: "" },
    email: { type: String, lowercase: true, default: "" },
    company: { type: String, default: "" },
    profession: { type: String, default: "" },
    phone: { type: String, default: "" },
    facebook: { type: String, default: "" },
    linkedin: { type: String, default: "" },
    auth0Id: { type: String, default: "" },
    firebaseId: { type: String, default: "" },
    description: { type: String, default: "" },
    offer: { type: String, default: "" },
    contactPartnerName: { type: String, default: "" },
    evntData: [
      {
        event: {
          type: mongoose.Schema.Types.ObjectId,
          ref: "event",
          default: null,
        },
        privateProfile: { type: Boolean, default: false },
        member: { type: Boolean, default: false },
        speaker: { type: Boolean, default: false },
        partner: { type: Boolean, default: false },
        guest: { type: Boolean, default: false },
        partnerOrder: { type: Number, default: 0 },
      },
    ],
  },
  { _id: false }
);

const airTableSyncSchema = new mongoose.Schema(
  {
    "# of Days Since MDS Only Census": {
      $numberInt: {
        type: Date,
      },
    },
    "AT Database Status": {
      type: Array,
      default: [],
    },
    "About Me": {
      type: String,
    },
    Access: {
      type: [String],
    },
    "Annual Reminder Date": {
      type: Date,
    },
    "Chapter Affiliation": {
      type: [String],
    },
    City: {
      type: String,
    },
    Country: {
      type: String,
    },
    "Events Attended": {
      type: Array,
    },
    "Facebook Profile Link": {
      type: String,
    },
    first_name: {
      type: String,
      default: "",
    },
    last_name: {
      type: String,
      default: "",
    },
    "Intercom User ID": {
      type: "ObjectId",
    },
    "Order Date": {
      type: Date,
    },
    "Preferred Email": {
      type: String,
    },
    apple_private_email: {
      type: String,
      default: null,
    },
    State: {
      type: String,
    },
    "Verification URL": {
      type: String,
    },
    Zip: {
      type: String,
    },
    email: { type: String },
    passcode: { type: String, default: "" },
    secondary_email: {
      type: String,
      lowercase: true,
      // validate: async (value) => {
      //   if (!validator.isEmail(value)) {
      //     throw new Error("Invalid Email address");
      //   }
      // },
    },
    facebookLinkedinId: { type: String, default: "" },
    auth0Id: { type: String, default: "" },
    firebaseId: { type: String, default: "" },
    socialauth0id: { type: String, default: "" },
    profileImg: { type: String, default: "" },
    active: { type: Boolean, default: false },
    blocked: { type: Boolean, default: false },
    verified: { type: Boolean, default: false },
    saveVideos: [
      {
        type: mongoose.Schema.Types.ObjectId,
        ref: "contentArchive_video",
        default: [],
      },
    ],
    provider: {
      type: String,
      default: "firebase",
      enum: [
        "",
        "auth0",
        "facebook",
        "linkedin",
        "apple",
        "firebase",
        "google",
        "password",
      ],
    },
    isSocial: { type: Boolean, default: false },
    payment_id: { type: mongoose.Schema.Types.ObjectId, ref: "payment" },
    purchased_plan: {
      type: mongoose.Schema.Types.ObjectId,
      ref: "membership_plan",
    },
    accessible_groups: [
      { type: mongoose.Schema.Types.ObjectId, ref: "group", default: [] },
    ],
    last_login: { type: Date, default: "" },
    last_activity_log: { type: Date, default: Date.now() },
    isDelete: { type: Boolean, default: false },
    register_status: { type: Boolean, default: false },
    personalDetail_status: { type: Boolean, default: false },
    payment_status: { type: Boolean, default: false },
    QA_status: { type: Boolean, default: false },
    user_role: {
      type: mongoose.Schema.Types.ObjectId,
      ref: "userrole",
      default: user_role.role_id,
    },
    forgot_ticket: { type: String, default: "" },
    blocked_chat: [{ type: mongoose.Schema.Types.ObjectId, default: [] }],
    blocked_by_who_chat: [
      { type: mongoose.Schema.Types.ObjectId, default: [] },
    ],
    clear_chat_data: [
      {
        id: { type: mongoose.Schema.Types.ObjectId, required: true },
        deleteConversation: { type: Boolean, default: false },
        type: { type: String, default: "" },
        date: { type: Date, default: Date.now },
      },
    ],
    deleted_group_of_user: [
      { type: mongoose.Schema.Types.ObjectId, default: [] },
    ],
    star_chat: [{ type: mongoose.Schema.Types.ObjectId, default: [] }],
    migrate_user_status: { type: Boolean, default: false },
    migrate_user: { type: Object, default: {} },
    userEvents: { type: Object, default: {} },
    video_history_data: [
      {
        video_id: { type: mongoose.Schema.Types.ObjectId, required: true },
        history_date: { type: Date, default: "" },
      },
    ],
    muteNotification: {
      type: Array,
    },
    deactivate_account_request: { type: Boolean, default: false },
    deviceToken: { type: [{ type: String }], default: [] },
    webDeviceToken: { type: [{ type: String }], default: [] },
    attendeeDetail: {
      type: fieldSchema,
    },
    notificationFor: [
      {
        id: { type: mongoose.Schema.Types.ObjectId },
        type: { type: String, default: "" },
        setBy: { type: String, default: "" },
      },
    ],
    partnerIcon: { type: String, default: "" },
    "Upcoming Events": { type: Array, default: [] },
    "Upcoming Events Registered": { type: Array, default: [] },
    isCollaborator: { type: Boolean, default: false },
    status: {
      type: String,
      default: "INACTIVE",
      enum: ["ACTIVE", "INACTIVE", "BLOCKED"],
      required: true,
    },
    user_edges: [
      {
        type: mongoose.Schema.Types.ObjectId,
        ref: DB_COLLECTIONS.USER_EDGES,
      },
    ],
    activation_code: { type: String, default: null },
    refresh_token: { type: Boolean },
    no_of_team_mate: { type: Number, default: 0 },
    joinDate: { type: Date, },
    groupos_join_date: { type: Date, },
    migration_date: { type: Date },
    display_name: { type: String, default: "" },
    providers: {
      type: [
        {
          name: { type: String },
          email: { type: String },
          socialId: { type: String },
          date: { type: Date, default: Date.now() },
          isDelete: { type: Boolean, default: false },
        },
      ],
      default: [],
    },
    activationLink: { type: String, default: null },
    providers_history: {
      type: [
        {
          name: { type: String },
          email: { type: String },
          socialId: { type: String },
          date: { type: Date, default: Date.now() },
          isDelete: { type: Boolean, default: false },
          firebaseId: { type: String, default: "" },
          connectTo: {
            type: mongoose.Schema.Types.ObjectId,
            ref: "airtable-syncs",
            default: null,
          },
          deletedAt: { type: Date, default: Date.now() },
        },
      ],
      default: [],
    },
    "Denver Check in Form": {
      type: String,
      default: null
    },
  },
  { timestamps: true }
);

airTableSyncSchema.index({ "Preferred Email": 1, isDelete: 1 });

module.exports = mongoose.model("airtable-syncs", airTableSyncSchema);
