const mongoose = require("mongoose");
const Schema = mongoose.Schema;
const { DB_COLLECTIONS } = require("../../utils/db-collection-names");

const userCustomProfileSchema = new Schema(
  {  
  type: {
    type: String,
    default: "text",
    enum: ["text", "number", "date", "checkbox", "radio", "select", "file", "textarea"],
    required: true,
  },
  response: {type: [Schema.Types.Mixed]},
  user_id:{ type: Schema.Types.ObjectId, ref: "airtable-syncs", required:true },
  custom_field_id:{ type: Schema.Types.ObjectId, ref: "user_custom_form", required:true },
  status: {
    type: String,
    default: "PENDING",
    enum: ["PENDING", "APPROVED", "REJECTED", "DONE"],
  },
  remark: { type: String, dafault:""},
  is_admin_edited:{type: Boolean,default: false,},
  relation_id: { type: mongoose.Schema.Types.ObjectId, ref:  DB_COLLECTIONS.COMMUNITIES },
  isDelete: {type: Boolean,default: false,},
},
{ timestamps: true }
);

// create Index
userCustomProfileSchema.index({ isDelete: 1 });

module.exports = mongoose.model("user_custom_form_submission",userCustomProfileSchema,);
