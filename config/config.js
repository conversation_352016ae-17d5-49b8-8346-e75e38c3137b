const config = {
  production: {
    SECRET: process.env.SECRET,
    DATABASE: process.env.MONGODB_URI,
  },
  default: {
    SECRET: process.env.SECRET,
    DATABASE: process.env.DB_URL,
  },
};

module.exports = {
  ENV: process.env.NODE_ENV,
  GATEWAY_DOMAIN: process.env.GATEWAY_DOMAIN,
  FRONTEND_DOMAIN: process.env.FRONTEND_DOMAIN,
  BASE_URL: process.env.BASE_URL,
  SOCIAL_URL: process.env.SOCIAL_URL,
  MESSAGE_QUEUE_URL: process.env.MESSAGE_QUEUE_URL,
  MESSAGE_QUEUE_EXCHANGE_NAME: process.env.MESSAGE_QUEUE_EXCHANGE_NAME,
  MESSAGE_QUEUE_MONOLITH_SERVICE_NAME: "MONOLITH",
  MESSAGE_QUEUE_BILLING_SERVICE_NAME: "BILLING",
  supportedImageFileTypes: [
    "image/jpeg",
    "image/svg+xml",
    "image/png", // added
    "image/bmp",
    "image/gif", // added
    "image/webp",
    "image/heic", // added
  ],
  supportedOtherFileTypes: [
    "application/msword", // .doc
    "application/vnd.openxmlformats-officedocument.wordprocessingml.document", // .docx
    "application/vnd.ms-works", // .wps
    "application/vnd.openxmlformats-officedocument.wordprocessingml.document", // .wpd
    "application/x-tex", // .tex
    "text/plain", // .txt
    "application/rtf", // .rtf
    "application/vnd.oasis.opendocument.text", // .odt
    "audio/mpeg", // .mp3
    "audio/wav", // .wav
    "audio/aac", // .aac
    "audio/ogg", // .ogg
    "audio/x-ms-wma", // .wma
    "audio/flac", // .flac
    "audio/x-m4a", // .m4a
    "audio/x-aiff", // .aiff
    "audio/x-aif", // .aif
    "audio/midi", // .mid
    "audio/x-midi", // .midi
    "audio/amr", // .amr
  ],
  supportedVideoFileTypes: [
    "video/mp4", // added
    "video/quicktime", // .mov
    "video/webm", // added
    "video/avi", // added
    "video/x-matroska", // .mkv
    "video/x-ms-wmv", // .wmv
    "video/x-flv", // .flv
    "video/mpeg", // added
    "video/mpg", // added
    "video/3gp", // added
    "video/x-m4v", // .m4v
  ],
  JWT_SECRET: process.env.JWT_SECRET,
  allowedMaxFileUploadSize: 10,
  AWS_ID: process.env.AWS_ID,
  AWS_SECRET: process.env.AWS_SECRET,
  AWS_BUCKET: process.env.AWS_BUCKET,
  AWS_REGION: process.env.AWS_REGION,
  get: function get(env) {
    return config[env] || config.default;
  },

  //groupos admin (super admin)
  GROUPOS_ADMIN_FIRST_NAME: process.env.GROUPOS_ADMIN_FIRST_NAME,
  GROUPOS_ADMIN_LAST_NAME: process.env.GROUPOS_ADMIN_LAST_NAME,
  GROUPOS_ADMIN_PREFERRED_EMAIL: process.env.GROUPOS_ADMIN_PREFERRED_EMAIL,

  SOCIAL_PROVIDERS: JSON.parse(process.env.SOCIAL_PROVIDERS.replace(/'/g, '"')),
  JWT_PEMCERT: process.env.JWT_PEMCERT,
  APPLE_PEMCERT: process.env.APPLE_PEMCERT,
  FIREBASE_API_KEY: process.env.FIREBASE_API_KEY, //Firebase api key
  // FIREBASE_TOKEN_EXPIRE_TIME: 60 * 60 * 24 * 7 * 1000, // 7 days firebase token expire time
  FIREBASE_TOKEN_EXPIRE_TIME: 60 * 30 * 1000, // 30 minute firebase token expire time
  SERVER_KEY: process.env.SERVER_KEY,

  //* Configuration of the mongodb fuzzy search
  MAX_EDITS: 2,
  PRE_FIX_LENGTH: 1,

  EMAIL_REGEX: /^([a-z][a-z0-9_]*|(([^<>()[\]\\.,;:\s@"]+(\.[^<>()[\]\\.,;:\s@"]+)*)|(".+"))@((\[[0-9]{1,3}\.[0-9]{1,3}\.[0-9]{1,3}\.[0-9]{1,3}\])|(([a-zA-Z\-0-9]+\.)+[a-zA-Z]{2,})))$/,

  INTERCOM_SECRET_KEY: process.env.INTERCOM_SECRET_KEY, //* Intercom secret key
};
