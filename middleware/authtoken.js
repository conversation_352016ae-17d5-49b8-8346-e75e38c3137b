const jwt = require("jsonwebtoken");
const User = require("../database/models/airTableSync");

const { AdminUser } = require("../database/models/adminuser");
const { JWT_SECRET, JWT_PEMCERT } = require("../config/config");
const admin = require("../utils/firebase-admin");


const verifyToken = async (req, res, next) => {
  // pull out token from the request
  let token = req.headers.authorization;
  // console.log(token, "token");
  if (!token || token.length === 0) {
    return res.status(200).send({
      status: false,
      message: "No token provided!",
      invalidToken: true,
    });
  }

  var tokenCrop = token.replace("Bearer ", "");
  jwt.verify(
    tokenCrop,
    JWT_PEMCERT,
    { algorithm: "RS256" },
    async (err, decoded) => {
      if (err) {
        return res.status(200).send({
          status: false,
          message: "Invalid token!",
          err: err,
          invalidToken: true,
        });
      }

      // get auth0Id from the jwt [sub]
      var authDetail = decoded.sub.split("|");
      var authId = authDetail[1];
      var auth_provider = authDetail[0];

      // find user by auth0Id
      var userData = await User.findOne({
        $or: [{ firebaseId: firebaseId }, { facebookLinkedinId: firebaseId }],
        // provider: auth_provider,
        isDelete: false,
      }).select("email");

      if (!userData)
        return res.status(200).json({
          status: false,
          message: "User not found.",
          invalidToken: true,
        });

      // set userId in the req object
      req.authUserId = userData._id;
      next();
    }
  );
};

const isAdmin = async (req, res, next) => {
  // pull out token from the request
  let token = req.headers.authorization;
  if (!token || token.length === 0) {
    return res.status(200).send({
      status: false,
      message: "No token provided!",
      invalidToken: true,
    });
  }

  const tokenCrop = token.replace("Bearer ", "");
  
  jwt.verify(
    tokenCrop,
    JWT_PEMCERT,
    { algorithm: "RS256" },
    async (err, decoded) => {
      if (err) {
        return res.status(200).send({
          status: false,
          message: "Invalid token!",
          invalidToken: true,
        });
      }

      // get auth0Id from the jwt [sub]
      const adminId = decoded.sub;

      // find adminuser by auth0Id
      var adminData = await AdminUser.findOne({
        oauthId: adminId,
        isDelete: false,
      });
      if (!adminData)
        return res.status(200).json({
          status: false,
          message: "You are not admin.",
          invalidToken: true,
        });

      // set userId in the req object
      req.admin_Id = adminData._id;
      next();
    }
  );
};

const verifyGuest = async (req, res, next) => {
  let token;
  // pull out token from the request
  const { authorization } = req.headers;
  if (authorization && authorization.startsWith("Bearer")) {
    try {
      token = req.headers.authorization.split(" ")[1];

      // get userId from the jwt
      const { userID } = jwt.verify(token, process.env.JWT_SECRET);

      // find user by userId
      const userData = await User.findById(userID).select("-password");

      // set userId in the req object
      req.authUserId = userData._id;
      next();
    } catch (error) {
      console.log(error);
      res.status(401).send({ status: "failed", message: "Unauthorised User!" });
    }
  }
  if (!token) {
    res
      .status(401)
      .send({ status: "failed", message: "Unauthorised User, No Token!" });
  }
};

const verifyGuestOrUser = async (req, res, next) => {
  // pull out token from the request
  let token = req.headers.authorization;
  if (!token || token.length === 0) {
    return res.status(200).send({
      status: false,
      message: "No token provided!",
      invalidToken: true,
    });
  }
  var tokenCrop = token.replace("Bearer ", "");

  if (token.length > 200) {
    jwt.verify(
      tokenCrop,
      JWT_PEMCERT,
      { algorithm: "RS256" },
      async (err, decoded) => {
        if (err) {
          return res.status(200).send({
            status: false,
            message: "Invalid token!",
            err: err,
            invalidToken: true,
          });
        }

        // get auth0Id from the jwt [sub]
        var authDetail = decoded.sub.split("|");
        var authId = authDetail[1];

        // find user by auth0Id
        var authUserData = await User.findOne({
          $or: [{ firebaseId: authId }, { facebookLinkedinId: authId }],
          isDelete: false,
        }).select("email");

        if (!authUserData) {
          return res.status(200).json({
            status: false,
            message: "User not found.",
            invalidToken: true,
          });
        }

        // set userId in the req object
        req.authUserId = authUserData._id;
        next();
      }
    );
  } else {
    if (token && token.startsWith("Bearer")) {
      try {
        // get userId from the jwt
        const { userID } = jwt.verify(tokenCrop, process.env.JWT_SECRET);

        // find user by userId
        const userData = await User.findById(userID).select("-password");

        // set userId in the req object
        req.authUserId = userData._id;
        next();
      } catch (error) {
        console.log(error);
        res
          .status(401)
          .send({ status: "failed", message: "Unauthorised User!" });
      }
    }
  }

  if (!token) {
    res
      .status(401)
      .send({ status: "failed", message: "Unauthorised User, No Token!" });
  }
};

const isAuth = async (req, res, next) => {
  // pull out token from the request
  if (!req.headers.authorization) {
    return res.status(401).send("Unauthorized request!");
  }
  const token = req.headers["authorization"].split(" ")[1];
  if (!token) {
    return res.status(401).send("Access denied!");
  }
  try {
    const tokenDecoded = jwt.verify(token, JWT_SECRET);
    const subdomain = req.subdomains[0];
    let hostname = req.hostname ? req.hostname : "";

    switch (subdomain) {
      case "app":
        {
          if (
            tokenDecoded.currentEdge.type !== "DEFAULT" &&
            hostname.includes("groupos")
          ) {
            return res.status(401).send("Access denied!");
          }
        }
        break;

      case "admin":
        {
          if (tokenDecoded.currentEdge?.relation_id?.nickname !== "groupos") {
            return res.status(401).send("Access denied!");
          }
        }
        break;

      default:
        {
          if (
            tokenDecoded.currentEdge.type === "DEFAULT" ||
            tokenDecoded.currentEdge?.relation_id?.nickname === "groupos" ||
            subdomain !== tokenDecoded.currentEdge?.relation_id?.nickname
          ) {
            if (
              tokenDecoded?.currentEdge?.relation_id?.customDomain?.domain !==
              req?.headers?.host &&
              tokenDecoded?.currentEdge?.relation_id?.customDomain?.status !==
              "active"
            ) {
              return res.status(401).send("Access denied!");
            }
          }
        }
        break;
    }

    if (tokenDecoded.currentEdge?.relation_id?._id) {
      req.relation_id = tokenDecoded.currentEdge.relation_id._id;
    }
    req.userId = tokenDecoded.userId;
    req.authUserId = tokenDecoded.userId;
    req.userName = tokenDecoded.userName;
    req.userEmail = tokenDecoded.userEmail;
    req.userAvatar = tokenDecoded.userAvatar;
    req.userStatus = tokenDecoded.userStatus;
    req.currentEdge = tokenDecoded.currentEdge;

    if (
      tokenDecoded?.currentEdge &&
      tokenDecoded.currentEdge.owner &&
      tokenDecoded.currentEdge.owner == true
    ) {
      if (tokenDecoded.currentEdge.type == "CO") {
        req.owner = true;
        req.admin_Id = tokenDecoded.userId;
      } else if (tokenDecoded.currentEdge.type == "PO") {
        req.super_admin_Id = tokenDecoded.userId;
      }
    }
    next();
  } catch (error) {
    console.log("🚀 ~~ isAuth ~~ error:", error);
    return res.status(401).send("Invalid request!");
  }
};

const isAuthCustomDomain = async (req, res, next) => {
  try {
    if (!req.headers.authorization) {
      return res.status(401).send("Unauthorized request!");
    }

    const token = req.headers["authorization"].split(" ")[1];

    if (!token) {
      return res.status(401).send("Access denied!");
    }

    const tokenDecoded = jwt.verify(token, JWT_SECRET);

    if (!tokenDecoded || !tokenDecoded?.community_id) {
      return res.status(401).send("Invalid request!");
    }
    req.community_id = tokenDecoded?.community_id;
    // req.domain = tokenDecoded?.domain;

    next();
  } catch (error) {
    console.log("🚀 ~~ isAuthCustomDomain ~~ error:", error);
    return res.status(401).send("Invalid request!");
  }
};

const isAuthForFirebase = async (req, res, next) => {
  try {
    if (!req.headers.authorization) {
      return res.status(401).send("Unauthorized request!");
    }

    const token = req.headers["authorization"].split(" ")[1];

    if (!token) {
      return res.status(401).send("Access denied!");
    }

    const decodedToken = await admin.auth().verifyIdToken(token);

    if (!decodedToken && !decodedToken?.user_id && !decodedToken?.email) {
      return res.status(401).send("Invalid token!");
    }

    // if (
    //   decodedToken?.firebase?.sign_in_provider !== "apple.com" &&
    //   decodedToken?.email !== req.body["preferred_email"]
    // ) {
    //   return res.status(401).send("Invalid request (Email is not natch)!");
    // }

    req.body.firebaseEmail = decodedToken?.email;
    req.body.sub = decodedToken?.sub || decodedToken?.user_id;
    req.body.provider =
      decodedToken?.firebase?.sign_in_provider === "password"
        ? "password"
        : decodedToken?.firebase?.sign_in_provider === "apple.com"
          ? "apple"
          : decodedToken?.firebase?.sign_in_provider === "facebook.com"
            ? "facebook"
            : decodedToken?.firebase?.sign_in_provider === "google.com"
              ? "google"
              : null;

    if (!req.body.provider)
      return res.status(401).send("Invalid request (Provider is not match)!");
    else {
      if (req.body.provider === "facebook") {
        req.body.socialauth0id =
          decodedToken?.firebase?.identities["facebook.com"][0];
      }

      if (req.body.provider === "apple") {
        req.body.socialauth0id =
          decodedToken?.firebase?.identities["apple.com"][0];

        req.body.apple_private_email = decodedToken?.email;
      }

      if (req.body.provider === "google") {
        req.body.socialauth0id =
          decodedToken?.firebase?.identities["google.com"][0];
      }
    }

    if (decodedToken?.firebase?.sign_in_provider !== "password")
      req.socialId =
        decodedToken?.firebase?.identities[
        decodedToken?.firebase.sign_in_provider
        ][0] || null;

    next();
  } catch (error) {
    console.log("🚀 ~~ isAuthForFirebase ~~ error:", error);

    if (error.code === "auth/id-token-expired") {
      return res.status(401).send("Token is expired!");
    }

    if (error.code === "auth/argument-error")
      return res.status(401).send(error.message);

    return res.status(401).send("Invalid request!");
  }
};

const isAuthForFirebaseV2 = async (req, res, next) => {
  try {
    if (!req.headers.authorization) {
      return res.status(401).send("Unauthorized request!");
    }

    if (!req?.headers["x-firebase-refresh-token"])
      return res.status(401).json("Firebase refresh token is required!");

    const token = req.headers["authorization"].split(" ")[1];

    if (!token) {
      return res.status(401).send("Access denied!");
    }

    const decodedToken = await admin.auth().verifyIdToken(token);

    if (!decodedToken && !decodedToken?.user_id && !decodedToken?.email) {
      return res.status(401).send("Invalid token!");
    }

    // if (
    //   decodedToken?.firebase?.sign_in_provider !== "apple.com" &&
    //   decodedToken?.email !== req.body["preferred_email"]
    // ) {
    //   return res.status(401).send("Invalid request (Email is not natch)!");
    // }

    req.body.firebaseEmail = decodedToken?.email;
    req.body.sub = decodedToken?.sub || decodedToken?.user_id;
    req.body.provider =
      decodedToken?.firebase?.sign_in_provider === "password"
        ? "password"
        : decodedToken?.firebase?.sign_in_provider === "apple.com"
          ? "apple"
          : decodedToken?.firebase?.sign_in_provider === "facebook.com"
            ? "facebook"
            : decodedToken?.firebase?.sign_in_provider === "google.com"
              ? "google"
              : null;

    if (!req.body.provider)
      return res.status(401).send("Invalid request (Provider is not match)!");
    else {
      if (req.body.provider === "facebook") {
        req.body.socialauth0id =
          decodedToken?.firebase?.identities["facebook.com"][0];
      }

      if (req.body.provider === "apple") {
        req.body.socialauth0id =
          decodedToken?.firebase?.identities["apple.com"][0];

        req.body.apple_private_email = decodedToken?.email;
      }

      if (req.body.provider === "google") {
        req.body.socialauth0id =
          decodedToken?.firebase?.identities["google.com"][0];
      }
    }

    if (decodedToken?.firebase?.sign_in_provider !== "password")
      req.socialId =
        decodedToken?.firebase?.identities[
        decodedToken?.firebase.sign_in_provider
        ][0] || null;

    next();
  } catch (error) {
    console.log("🚀 ~~ isAuthForFirebase ~~ error:", error);

    if (error.code === "auth/id-token-expired") {
      return res.status(401).send("Token is expired!");
    }

    if (error.code === "auth/argument-error")
      return res.status(401).send(error.message);

    return res.status(401).send("Invalid request!");
  }
};

const isAdminForDefaultEdge = async (req, res, next) => {
  // pull out token from the request
  if (!req.headers.authorization) {
    return res.status(401).send("Unauthorized request!");
  }
  const token = req.headers["authorization"].split(" ")[1];
  if (!token) {
    return res.status(401).send("Access denied!");
  }
  try {
    const tokenDecoded = jwt.verify(token, JWT_SECRET);
    let subdomain = req.subdomains[0];
    let hostname = req.hostname ? req.hostname : "";

    if (
      subdomain !== tokenDecoded.currentEdge?.relation_id?.nickname &&
      tokenDecoded?.currentEdge?.relation_id?.customDomain?.status !== "active"
    ) {
      subdomain = "isDefaultEdge";
    }

    switch (subdomain) {
      case "app":
        {
          if (
            tokenDecoded.currentEdge.type !== "DEFAULT" &&
            hostname.includes("groupos")
          ) {
            // return res.status(401).send("Access denied!");
            req.isDefaultEdge = true;
          }
        }
        break;

      case "admin":
        {
          if (tokenDecoded.currentEdge?.relation_id?.nickname !== "groupos") {
            return res.status(401).send("Access denied!");
          }
        }
        break;

      case "isDefaultEdge":
        {
          if (
            tokenDecoded.currentEdge?.relation_id?.nickname !==
            req.subdomains[0]
          ) {
            if (
              !(
                req.subdomains[0] === "admin" &&
                tokenDecoded.currentEdge?.relation_id?.nickname === "groupos"
              )
            ) {
              req.isDefaultEdge = true;
            }
          }
        }
        break;

      default:
        {
          if (
            tokenDecoded.currentEdge.type === "DEFAULT" ||
            tokenDecoded.currentEdge?.relation_id?.nickname === "groupos" ||
            subdomain !== tokenDecoded.currentEdge?.relation_id?.nickname
          ) {
            if (
              tokenDecoded?.currentEdge?.relation_id?.customDomain.domain !==
              req?.headers?.host &&
              tokenDecoded?.currentEdge?.relation_id?.customDomain?.status !==
              "active"
            ) {
              return res.status(401).send("Access denied!");
            }
          }
        }
        break;
    }

    if (tokenDecoded.currentEdge?.relation_id?._id) {
      req.relation_id = tokenDecoded.currentEdge.relation_id._id;
    }
    req.userId = tokenDecoded.userId;
    req.authUserId = tokenDecoded.userId;
    req.userName = tokenDecoded.userName;
    req.userEmail = tokenDecoded.userEmail;
    req.userAvatar = tokenDecoded.userAvatar;
    req.userStatus = tokenDecoded.userStatus;
    req.currentEdge = tokenDecoded.currentEdge;

    if (
      tokenDecoded?.currentEdge &&
      tokenDecoded.currentEdge.owner &&
      tokenDecoded.currentEdge.owner == true
    ) {
      if (tokenDecoded.currentEdge.type == "CO") {
        req.admin_Id = tokenDecoded.userId;
      } else if (tokenDecoded.currentEdge.type == "PO") {
        req.super_admin_Id = tokenDecoded.userId;
      }
    }
    next();
  } catch (error) {
    console.log("🚀 ~~ isAuth ~~ error:", error);
    return res.status(401).send("Invalid request!");
  }
};

const isAdminTokenParam = async (req, res, next) => {
  // Check if token is provide
  const token = req.query.token.split(" ")[1];
  if (!token || token.length === 0) {
    return res.status(200).json({
      status: false,
      message: "No token provided!",
      invalidToken: true,
    });
  }
  
  
  try {
    // Decode token
    const tokenDecoded = jwt.verify(token, JWT_SECRET);
    const subdomain = req.subdomains[0];
    let hostname = req.hostname || "";

    // Switch based on the subdomain
    switch (subdomain) {
      case "app":
        // Check conditions for 'app' subdomain
        if (tokenDecoded.currentEdge.type !== "DEFAULT" && hostname.includes("groupos")) {
          return res.status(401).send("Access denied!");
        }
        break;

      case "admin":
        // Check conditions for 'admin' subdomain
        if (tokenDecoded.currentEdge?.relation_id?.nickname !== "groupos") {
          return res.status(401).send("Access denied!");
        }
        break;

      default:
        // Default case for other subdomains
        if (
          tokenDecoded.currentEdge.type === "DEFAULT" ||
          tokenDecoded.currentEdge?.relation_id?.nickname === "groupos" ||
          subdomain !== tokenDecoded.currentEdge?.relation_id?.nickname
        ) {
          const customDomain = tokenDecoded?.currentEdge?.relation_id?.customDomain;
          if (customDomain?.domain !== req?.headers?.host || customDomain?.status !== "active") {
            return res.status(401).send("Access denied!");
          }
        }
        break;
    }

    // Check if the user is an admin (CO type)
    if (tokenDecoded.currentEdge.type === "CO") {
      return next();
    } else {
      return res.status(200).json({
        status: false,
        message: "You are not admin.",
        invalidToken: true,
      });
    }

  } catch (error) {
    // Handle invalid token error
    return res.status(200).json({
      status: false,
      message: "Invalid token!",
      invalidToken: true,
    });
  }
};

module.exports = {
  verifyToken: verifyToken,
  isAdmin: isAdmin,
  verifyGuest: verifyGuest,
  verifyGuestOrUser: verifyGuestOrUser,
  isAuth,
  isAuthCustomDomain,
  isAuthForFirebase,
  isAuthForFirebaseV2,
  isAdminForDefaultEdge,
  isAdminTokenParam,
};
