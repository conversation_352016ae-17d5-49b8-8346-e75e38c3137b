const PageBuilder = require("../database/models/pageBuilder");
const {
    ObjectId
} = require("mongodb");
exports.titleSuggestion = async (req, res) => {
    try {
        const titles = await PageBuilder.find({
          relation_id: req.relation_id,
          isDelete: false,
        })
          .select("title _id")
          .sort({ title: 1 })
          .lean();

        return res.status(200).send({
            status: true,
            message: titles.length ? "Get Titles Successfully!" : "No Titles Found!",
            data: titles
        });
    } catch (error) {
        return res.status(500).send({
            status: false,
            message: error.message
        });
    }
};

exports.addPageBuilderContent = async (req, res) => {
    try {
        const {
            title,
            HTML_Content,
            JSON_Content
        } = req.body;


        if (!title || !HTML_Content) {
            return res.status(400).send({
                status: false,
                message: "Title and HTML_Content are required."
            });
        }

        const regexPattern = new RegExp(`^${title}$`, 'i');
        const existingTitle = await PageBuilder.findOne({
            title: {
                $regex: regexPattern
            },
            relation_id: ObjectId(req.relation_id),
            isDelete: false
        });
        if (existingTitle) {
            return res.send({
                status: false,
                message: "Title is Already Exists!"
            });
        } else {
            const newHTML_content = new PageBuilder({
                title: title,
                HTML_Content: `${HTML_Content}`,
                JSON_Content: `${JSON_Content}`,
                relation_id: ObjectId(req.relation_id)
            });
            await newHTML_content.save();
            return res.status(200).send({
                status: true,
                message: "HTML content Added SuccessFully!",
                data: newHTML_content
            })
        }
    } catch (error) {
        return res.send({
            status: false,
            message: "Something went wrong."
        });
    }
}

exports.getPageBuilderContentBYID = async (req, res) => {
    try {
        const id = req.params.id;
        const getHTML_CONTENT = await PageBuilder.findOne({
            _id: ObjectId(id),
            relation_id: ObjectId(req.relation_id),
            isDelete: false
        });
        if (!getHTML_CONTENT) {
            return res.status(400).send({
                status: false,
                message: "HTML_CONTENT not Found!"
            });
        } else {
            return res.status(200).send({
                status: true,
                message: "Get HTML_CONTENT Successfully!",
                data: getHTML_CONTENT
            });
        }
    } catch (error) {
        return res.send({
            status: false,
            message: error.message
        });
    }
}
exports.updatePageBuilderContent = async (req, res) => {
    try {
        const id = req.params.id;
        const { title, HTML_Content, JSON_Content} = req.body;

        if (!id) {
            return res.status(400).send({
                status: false,
                message: "ID is required !"
            });
        }

        const existingContent = await PageBuilder.findOne({
            _id: ObjectId(id),
            relation_id: ObjectId(req.relation_id),
            isDelete: false
        });

        if (!existingContent) {
            return res.status(400).send({
                status: false,
                message: "Content not found or already deleted."
            });
        }
        if (title) {
            const regexPattern = new RegExp(`^${title}$`, 'i');
            const duplicateTitle = await PageBuilder.findOne({
                _id: { $ne: ObjectId(id) },
                title: { $regex: regexPattern },
                relation_id: ObjectId(req.relation_id),
                isDelete: false
            });

            if (duplicateTitle) {
                return res.status(400).send({
                    status: false,
                    message: "Title is already in use by another content!"
                });
            }
        }
        existingContent.title = title || existingContent.title;
        existingContent.HTML_Content = HTML_Content || existingContent.HTML_Content;
        existingContent.JSON_Content = JSON_Content || existingContent.JSON_Content;
        await existingContent.save();

        return res.status(200).send({
            status: true,
            message: "Content updated successfully!",
            data: existingContent
        });
    } catch (error) {
        return res.status(500).send({
            status: false,
            message: error.message
        });
    }
};

exports.getPageBuilderContentBYTitle = async (req, res) => {
    try {
        const title = req.query.title;
        const id = req.query.id;
        let query = { isDelete: false, relation_id: ObjectId(req.relation_id) };
    
        if (id) {
            query._id = ObjectId(id);
        } else if (title) {
            query.title = title;
        } else {
            return res.status(400).send({
                status: false,
                message: "Either '_id' or 'title' must be provided!"
            });
        }        
        const getHTML_CONTENT = await PageBuilder.findOne(query);
        if (!getHTML_CONTENT) {
            return res.status(400).send({
                status: false,
                message: "HTML_CONTENT not Found!"
            });
        } else {
            return res.status(200).send({
                status: true,
                message: "Get HTML_CONTENT Successfully!",
                data: getHTML_CONTENT
            });
        }
    } catch (error) {
        return res.send({
            status: false,
            message: error.message
        });
    }
}

exports.getAllPageBuilderContentList = async (req, res) => {
    try {
        const page = parseInt(req.query.page) || 1;
        const limit = parseInt(req.query.limit) || 20;
        const skip = (page - 1) * limit;

        const searchQuery = req.query.search || "";

        const filter = {
            relation_id: ObjectId(req.relation_id),
            isDelete: false
        };

        const decodeAndEscapeString = (str) => {
            let decodedStr = decodeURIComponent(str);
            decodedStr = decodedStr.replace(/[.*+?^=!:${}()|\[\]\/\\^-]/g, '\\$&');
            return decodedStr;
        };
        if (searchQuery) {
            let searchStr = decodeAndEscapeString(searchQuery);
            filter.title = {
                $regex: ".*" + searchStr + ".*",
                $options: "i"
            };
        }

        const getHTML_CONTENT = await PageBuilder.find(filter, { title: 1, _id: 1 }).sort({createdAt: -1})
            .skip(skip)
            .limit(limit);

        const totalRecords = await PageBuilder.countDocuments(filter);

        if (!getHTML_CONTENT || getHTML_CONTENT.length === 0) {
            return res.status(200).send({
                status: false,
                message: "HTML_CONTENT not Found!",
                data: [],
                currentPage: page,
                totalPages: 0,
                totalRecords: 0,
            });
        }

        return res.status(200).send({
            status: true,
            message: "Get HTML_CONTENT Successfully!",
            data: getHTML_CONTENT,
            currentPage: page,
            totalPages: Math.ceil(totalRecords / limit),
            totalRecords: totalRecords
        });
    } catch (error) {
        return res.status(500).send({
            status: false,
            message: error.message,
        });
    }
};

exports.deletePageBuilderContentBYID = async (req, res) => {
    try {
        const getHTML_CONTENT = await PageBuilder
      .findOne({ _id: new ObjectId(req.params.id), isDelete: false, relation_id: ObjectId(req.relation_id) })
      .lean();
    if (getHTML_CONTENT) {
      const HTML_CONTENT = await PageBuilder.findByIdAndUpdate(
        req.params.id,
        { isDelete: true },
        { new: true }
      );
        return res.status(200).json({
          status: true,
          message: "HTML_CONTENT deleted successfully!",
          data: HTML_CONTENT,
        });
    } else {
        return res.status(200).json({
          status: false,
          message: "Something went wrong while deleteing HTML_CONTENT!",
        });
    }
    } catch (error) {
        return res.send({
            status: false,
            message: error.message
        });
    }
}