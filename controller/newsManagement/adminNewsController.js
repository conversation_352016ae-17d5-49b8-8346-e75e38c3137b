const adminNews = require("../../database/models/adminNews");
const User = require("../../database/models/airTableSync");
const ContentArchiveVideo = require("../../database/models/contentArchive_video");
const ContentEvent = require("../../database/models/contentArchive_event");
const collabaorators = require("../../database/models/collaborator/inviteCollaborator");
const group = require("../../database/models/group");
const membershipPlan = require("../../database/models/membershipPlanManagement/membership_plan");
const event = require("../../database/models/event");
const EventParticipantAttendees = require("../../database/models/eventParticipantAttendees");
const { userAccessRulesCommonCondition, checkValidIds} = require("../../controller/userAccessRules/restrictionAccess");

const ObjectId = require("mongoose").Types.ObjectId;
const { deleteImage } = require("../../utils/mediaUpload");
const AWS = require("aws-sdk");
const { getAllTiersfromBilling } = require("../userAccessRules/tiers");

var s3 = new AWS.S3({
    accessKeyId: process.env.AWS_ID,
    secretAccessKey: process.env.AWS_SECRET,
    Bucket: process.env.AWS_BUCKET,
    signatureVersion: 'v4',
    region: process.env.AWS_REGION
  });

// create news
exports.createNews = async (req, res) => {
    try {
        if (req.body.makeFeaturedCheckbox.toString() === "true") {
            const changeFeaturedNews = await adminNews.updateMany(
                {},
                {
                    $set: { makeFeaturedCheckbox: false },
                }
            );
        }
        let description = `<div "font-family: 'Muller';">${req.body.description}</div>`;
        const newNewsData = new adminNews({
            title: req.body.title,
            thumbnail: req.newsThumbnail,
            description: description,
            date: req.body.date,
            publishOrHide: req.body.publishOrHide,
            makeFeaturedCheckbox: req.body.makeFeaturedCheckbox,
            newsType: "news",
        });
        const saveNews = await newNewsData.save();
        if (saveNews)
            return res
                .status(200)
                .json({
                    status: true,
                    message: `News created successfully!`,
                    newsData: saveNews,
                });
        else
            return res
                .status(200)
                .json({
                    status: false,
                    message: `Something went wrong while adding news!`,
                });
    } catch (error) {
        return res.status(200).json({ status: false, message: `${error.message}` });
    }
};

// create news
exports.createNewsV2 = async (req, res) => {
    try {
      // check valid or not
      let checkIds =  await checkValidIds({
        restrictedAccessGroupId:req.body.restrictedAccessGroupId,
        restrictedAccessMembershipPlanId:req.body.restrictedAccessMembershipPlanId,
        restrictedAccessUserId:req.body.restrictedAccessUserId,
        restrictedAccessTagId:req.body.restrictedAccessTagId,
        restrictedAccessEventId:req.body.restrictedAccessEventId,
        restrictedAccessTierId: req.body.restrictedAccessTierId,
        relation_id: req.relation_id
        });

        if(checkIds.status === false)
        { return res.status(200).json({ status: false, message: checkIds.msg }); }
            
        if (req.body.makeFeaturedCheckbox.toString() === "true") {
            const changeFeaturedNews = await adminNews.updateMany(
                {},
                {
                    $set: { makeFeaturedCheckbox: false },
                }
            );
        }
        let description = `<div "font-family: 'Muller';">${req.body.description}</div>`;
        const newNewsData = new adminNews({
            title: req.body.title,
            thumbnail: req.newsThumbnail,
            description: description,
            date: req.body.date,
            publishOrHide: req.body.publishOrHide,
            makeFeaturedCheckbox: req.body.makeFeaturedCheckbox,
            newsType: "news",
            // newsAccessType: req.body.newsAccessType,
            restrictionAccess: req.body.restrictionAccess,
            restrictedAccessGroupId: req.body.restrictedAccessGroupId?req.body.restrictedAccessGroupId:[],
            restrictedAccessMembershipPlanId: req.body.restrictedAccessMembershipPlanId?req.body.restrictedAccessMembershipPlanId:[],
            restrictedAccessUserId: req.body.restrictedAccessUserId?req.body.restrictedAccessUserId:[],
            restrictedAccessEventId: req.body.restrictedAccessEventId?req.body.restrictedAccessEventId:[],
            restrictedAccessTagId: req.body.restrictedAccessTagId?req.body.restrictedAccessTagId:[],
            restrictedAccessTierId: req.body.restrictedAccessTierId ? req.body.restrictedAccessTierId : [],
            relation_id: ObjectId(req.relation_id),
        });
        const saveNews = await newNewsData.save();
        if (saveNews)
            return res
                .status(200)
                .json({
                    status: true,
                    message: `News created successfully!`,
                    newsData: saveNews,
                });
        else
            return res
                .status(200)
                .json({
                    status: false,
                    message: `Something went wrong while adding news!`,
                });
    } catch (error) {
        return res.status(200).json({ status: false, message: `${error.message}` });
    }
};

// edit news
exports.editNews = async (req, res) => {
    try {
        const newsExist = await adminNews.findById(req.params.id);
        if (!newsExist)
            return res.status(200).json({ status: false, message: `News not found` });
        if (req.body.makeFeaturedCheckbox.toString() === "true") {
            const changeFeaturedNews = await adminNews.updateMany(
                { _id: { $ne: new ObjectId(req.params.id) } },
                {
                    $set: { makeFeaturedCheckbox: false },
                }
            );
        }
        if (req.newsThumbnail) {
            deleteImage(newsExist.thumbnail);
        }
        let description = `<div "font-family: 'Muller';">${req.body.description}</div>`;
        const updatedNews = await adminNews.findByIdAndUpdate(
            req.params.id,
            {
                title: req.body.title ?? newsExist.title,
                thumbnail: req.newsThumbnail ?? newsExist.thumbnail,
                description: description ?? newsExist.description,
                date: req.body.date ?? newsExist.date,
                publishOrHide: req.body.publishOrHide ?? newsExist.publishOrHide,
                makeFeaturedCheckbox:
                    req.body.makeFeaturedCheckbox !== null &&
                        req.body.makeFeaturedCheckbox !== undefined
                        ? req.body.makeFeaturedCheckbox
                        : newsExist.makeFeaturedCheckbox,
            },
            { new: true }
        );
        if (updatedNews)
            return res
                .status(200)
                .json({
                    status: true,
                    message: `News updated successfully!`,
                    newsData: updatedNews,
                });
        else
            return res
                .status(200)
                .json({
                    status: false,
                    message: `Something went wrong while updating news!`,
                });
    } catch (error) {
        return res.status(200).json({ status: false, message: `${error.message}` });
    }
};
// edit news
exports.editNewsV2 = async (req, res) => {
    try {
        const newsExist = await adminNews.findById(req.params.id);
        if (!newsExist)
            return res.status(200).json({ status: false, message: `News not found` });

       // check valid or not
       let checkIds =  await checkValidIds({
        restrictedAccessGroupId:req.body.restrictedAccessGroupId,
        restrictedAccessMembershipPlanId:req.body.restrictedAccessMembershipPlanId,
        restrictedAccessUserId:req.body.restrictedAccessUserId,
        restrictedAccessTagId:req.body.restrictedAccessTagId,
        restrictedAccessEventId:req.body.restrictedAccessEventId,
        restrictedAccessTierId: req.body.restrictedAccessTierId,
        relation_id: req.relation_id
        });

        if(checkIds.status === false)
        { return res.status(200).json({ status: false, message: checkIds.msg }); }
              
        if (req.body.makeFeaturedCheckbox.toString() === "true") {
            const changeFeaturedNews = await adminNews.updateMany(
                { _id: { $ne: new ObjectId(req.params.id) } },
                {
                    $set: { makeFeaturedCheckbox: false },
                }
            );
        }
        if (req.newsThumbnail) {
            deleteImage(newsExist.thumbnail);
        }
        let description = `<div "font-family: 'Muller';">${req.body.description}</div>`;
        const updatedNews = await adminNews.findByIdAndUpdate(
            req.params.id,
            {
                title: req.body.title ?? newsExist.title,
                thumbnail: req.newsThumbnail ?? newsExist.thumbnail,
                description: description ?? newsExist.description,
                date: req.body.date ?? newsExist.date,
                publishOrHide: req.body.publishOrHide ?? newsExist.publishOrHide,
                // newsAccessType: req.body.newsAccessType,
                restrictionAccess: req.body.restrictionAccess,
                restrictedAccessGroupId: req.body.restrictedAccessGroupId ?req.body.restrictedAccessGroupId:[],
                restrictedAccessMembershipPlanId: req.body.restrictedAccessMembershipPlanId ?req.body.restrictedAccessMembershipPlanId:[],
                restrictedAccessUserId: req.body.restrictedAccessUserId ?req.body.restrictedAccessUserId:[],
                restrictedAccessEventId: req.body.restrictedAccessEventId ?req.body.restrictedAccessEventId:[],
                restrictedAccessTagId: req.body.restrictedAccessTagId?req.body.restrictedAccessTagId:[],
                restrictedAccessTierId: req.body.restrictedAccessTierId ? req.body.restrictedAccessTierId : [],
                makeFeaturedCheckbox:
                    req.body.makeFeaturedCheckbox !== null &&
                        req.body.makeFeaturedCheckbox !== undefined
                        ? req.body.makeFeaturedCheckbox
                        : newsExist.makeFeaturedCheckbox,
            },
            { new: true }
        );
        if (updatedNews)
            return res
                .status(200)
                .json({
                    status: true,
                    message: `News updated successfully!`,
                    newsData: updatedNews,
                });
        else
            return res
                .status(200)
                .json({
                    status: false,
                    message: `Something went wrong while updating news!`,
                });
    } catch (error) {
        return res.status(200).json({ status: false, message: `${error.message}` });
    }
};

// delete news
exports.deleteNews = async (req, res) => {
    try {
        const newsExist = await adminNews.findById(req.params.id);
        if (!newsExist)
            return res.status(200).json({ status: false, message: `News not found` });
        if (newsExist.thumbnail) deleteImage(newsExist.thumbnail);
        const deleteNews = await adminNews.findByIdAndDelete(req.params.id);
        if (deleteNews)
            return res
                .status(200)
                .json({ status: true, message: `News deleted successfully!` });
        else
            return res
                .status(200)
                .json({
                    status: false,
                    message: `Something went wrong while deleting news!`,
                });
    } catch (error) {
        return res.status(200).json({ status: false, message: `${error.message}` });
    }
};

// all news list
exports.getNewsList = async (req, res) => {
    try {
        const sortField = (req.query.sortField === "title" ?"title" : req.query.sortField === "date" ? "date" :req.query.sortField === "publishOrHide" ? "publishOrHide": "createdAt")
        const sortType = req.query.sortType ==="Asc"? 1 : -1;
    
        var match = {
            newsType: "news",
            relation_id: ObjectId(req.relation_id),
        };

        var search = "";
        if (req.query.search) {
            search = req.query.search;
            match = {
                ...match,
                title: { $regex: ".*" + search + ".*", $options: "i" },
            };
        }

        const newsListPromise = await adminNews.find(match)
        // .collation({ locale: "en" })
        .sort({ [`${sortField}`]: sortType });

        // Fetch total count of matching documents
        const countAllDataPromise = adminNews.countDocuments({
          relation_id: ObjectId(req.relation_id),
          isDelete: false
        });
       const [newsList, countAllData] = await Promise.all([newsListPromise, countAllDataPromise]);
        if (newsList)
          return res.status(200).json({ status: true, message: `News list`, newsList: newsList, countAllData });
        else
            return res.status(200).json({ status: false, message: `Something went wrong while getting news list!`, });
    } catch (error) {
        return res.status(200).json({ status: false, message: `${error.message}` });
    }
};

// get News Suggestion List
exports.getNewsSuggestionList = async (req, res) => {
    try {
        var match = {newsType: "news",relation_id: ObjectId(req.relation_id)};
        const newsList = await adminNews.find(match,{title:1,_id:0}).sort({ title: 1 }).lean();
        if (newsList)
            return res.status(200).json({ status: true, message: `News list`, newsList: newsList });
        else
            return res.status(200).json({ status: false, message: `Something went wrong while getting news list!`, });
    } catch (error) {
        console
        return res.status(200).json({ status: false, message: `${error.message}` });
    }
};

// news detail api
exports.getNewsDetailById = async (req, res) => {
    try {
        const newsDetail = await adminNews.findById(req.params.id);
        if (newsDetail)
            return res
                .status(200)
                .json({ status: true, message: `News detail`, newsDetail: newsDetail });
        else
            return res
                .status(200)
                .json({ status: false, message: `No data found for this news id!` });
    } catch (error) {
        return res.status(200).json({ status: false, message: `${error.message}` });
    }
};

// news detail api
exports.getNewsDetailByIdV2 = async (req, res) => {
    try {
        const newsDetail = await adminNews.findById(req.params.id)
        .populate({ path: "restrictedAccessGroupId", select: "groupTitle" })
        .populate({ path: "restrictedAccessMembershipPlanId", select: "plan_name -accessResources" })
        .populate({ path: "restrictedAccessUserId", select: {first_name:1,last_name:1, display_name:1, attendeeDetail:1,'Preferred Email':1}})
        .populate({ path: "restrictedAccessEventId", select: "title -tag -category -subcategory" })
        .populate({ path: "restrictedAccessTagId", select: "name" }).lean();

          let newsData = { ...newsDetail }
          const obj = { relation_id: req.relation_id }
          const tiers = await getAllTiersfromBilling(obj, expand = true);
          const restrictedTierIds = Array.isArray(newsData.restrictedAccessTierId)
          ? newsData.restrictedAccessTierId.map(id => id.toString())
            : [];
          const filteredTiers = tiers.filter(tier =>
            restrictedTierIds.includes(tier._id.toString())
          ).map(tier => ({
            _id: tier._id,
            name: tier.name
          }));
          newsData.restrictedAccessTierId = filteredTiers;

        if (newsData)
            return res
                .status(200)
              .json({ status: true, message: `News detail`, newsDetail: newsData });
        else
            return res
                .status(200)
                .json({ status: false, message: `No data found for this news id!` });
    } catch (error) {
        return res.status(200).json({ status: false, message: `${error.message}` });
    }
};

// content news listing api
exports.getContentNewsList = async (req, res) => {
    try {
        const sortField = (req.query.sortField === "title" ?"videoReferenceId.title" : req.query.sortField === "publishOrHide" ? "publishOrHide" :req.query.sortField === "date" ? "date" : "createdAt")
        const sortType = req.query.sortType ==="Asc"? 1 : -1;
    
        var match = {
            newsType: "video",
            relation_id: ObjectId(req.relation_id),
        };

        var search = "";
        if (req.query.search) {
            search = req.query.search;
        }

          const pipeline = [
            {
              $match: match,
            },
            {
              $lookup: {
                from: "contentarchive_videos",
                let: { video_id: "$videoReferenceId" },
                pipeline: [
                  {
                    $match: {
                      $expr: {
                        $eq: ["$_id", "$$video_id"],
                      },
                      $or: [{ title: { $regex: ".*" + search + ".*", $options: "i" }, },]
                    },
                  },
                  { $project: { video: 1, title: 1, description: 1, thumbnail: 1 } },
                ],
                as: "videoReferenceId",
              },
            },
            {
              $unwind: "$videoReferenceId",
            },
            {
              $project: {
                _id: 1,
                title: 1,
                thumbnail: 1,
                description: 1,
                date: 1,
                publishOrHide: 1,
                makeFeaturedCheckbox: 1,
                newsType: 1,
                videoReferenceId: 1,
                createdAt: 1,
                updatedAt: 1,
              },
            },
            {
              $addFields: {
                // Convert the sorting field to lowercase for case-insensitive sorting
                sortFieldLower: { $toLower: `$${sortField}` },
              },
            },
            { $sort: { sortFieldLower: sortType } },
            {
              $project: {
                // Exclude the sortFieldLower from the final result
                sortFieldLower: 0,
              },
            },
          ]

        const [contentNewsList, countAllData] = await Promise.all([
            adminNews.aggregate(pipeline),
          adminNews.countDocuments({ relation_id: ObjectId(req.relation_id), isDelete: false }),
          ]);

        // const contentNewsList = await adminNews.find(match).sort({ createdAt: -1 });
        if (contentNewsList)
          return res.status(200).json({ status: true, message: `Content news list`, newsList: contentNewsList, countAllData });
        else
            return res.status(200).json({ status: false, message: `Something went wrong while getting content news list!`, });
    } catch (error) {
        return res.status(200).json({ status: false, message: `${error.message}` });
    }
};

// get Content News Suggestion List
exports.getContentNewsSuggestionList = async (req, res) => {
    try {
        var match = {newsType: "video",relation_id: ObjectId(req.relation_id)};

        const contentNewsList = await adminNews.aggregate([
            {
                $match: match,
            },
            {
                $lookup: {
                    from: "contentarchive_videos",
                    let: { video_id: "$videoReferenceId" },
                    pipeline: [
                        {
                            $match: {
                                $expr: {
                                    $eq: ["$_id", "$$video_id"],
                                },
                            },
                        },
                    ],
                    as: "videoReferenceId",
                },
            },
            {
                $unwind: "$videoReferenceId",
            },
            {
              $addFields: {
                titlelower: {
                  $toLower: "$videoReferenceId.title"
                }
              }
            },
            {
              $sort: {
                titlelower: 1
              }
            },
            {
              $unset: ["titlelower"]
            },
            {
                $project: {
                    _id: 0,
                    title: "$videoReferenceId.title",
                },
            },
            {
              $sort: {
                "title": 1, 
              },
            },
        ]);

        if (contentNewsList)
            return res.status(200).json({ status: true, message: `Content news list`, newsList: contentNewsList });
        else
            return res.status(200).json({ status: false, message: `Something went wrong while getting content news list!`, });
    } catch (error) {
        return res.status(200).json({ status: false, message: `${error.message}` });
    }
};

// make featured by id
exports.makeNewsFeaturedById = async (req, res) => {
    try {
        const newsExist = await adminNews.findById(req.params.id);
        if (!newsExist)
            return res.status(200).json({ status: false, message: `News not found` });
        if (Boolean(req.body.makeFeaturedCheckbox)) {
            const changeFeaturedNews = await adminNews.updateMany(
                { _id: { $ne: new ObjectId(req.params.id) } },
                {
                    $set: { makeFeaturedCheckbox: false },
                }
            );
        }
        const updatedNews = await adminNews.findByIdAndUpdate(
            req.params.id,
            {
                makeFeaturedCheckbox:
                    req.body.makeFeaturedCheckbox !== null &&
                        req.body.makeFeaturedCheckbox !== undefined
                        ? req.body.makeFeaturedCheckbox
                        : newsExist.makeFeaturedCheckbox,
            },
            { new: true }
        );
        if (updatedNews)
            return res
                .status(200)
                .json({
                    status: true,
                    message: `News updated successfully!`,
                    newsData: updatedNews,
                });
        else
            return res
                .status(200)
                .json({
                    status: false,
                    message: `Something went wrong while updating news!`,
                });
    } catch (error) {
        return res.status(200).json({ status: false, message: `${error.message}` });
    }
};
// Save images sapratly

exports.saveFiles = async (req, res) => {
    try {
        const { image } = req;

        if (image) {
            return res
                .status(200)
                .json({
                    status: true,
                    media: image,
                    message: "Files saved successfully!",
                });
        } else
            return res
                .status(200)
                .json({ status: false, message: "Something went wrong!" });
    } catch (error) {
        return res
            .status(200)
        return res.status(200).json({ status: false, message: `${error.message}` });
    }
};

// all news list for frontend
exports.getNewsAndContentList = async (req, res) => {
    try {
        const authUser = req.authUserId;
        const userData = await User.findById(authUser).lean();
        const currentEdgeId = req.currentEdge._id;
        let ruleCondition = await userAccessRulesCommonCondition({ userId: authUser, relation_id: req.relation_id });

      //  var userDayData = userData["migrate_user"] && userData["migrate_user"].plan_id === "Staff" ? userData["# of Days Since MDS Only Census"]  : userData["# of Days Since MDS Only Census"] && typeof
      //   userData["# of Days Since MDS Only Census"] !== "object" ? userData["# of Days Since MDS Only Census"] : 400 ; 
        
        const accessibleVideosList = await ContentArchiveVideo.find({
            isDelete: false,
            uploadstatus: { $ne: "inprocess" },
            ...ruleCondition
        })
            .select("_id")
            .lean();

        var videoIds = accessibleVideosList.map((vid) => {
            return vid._id;
        });

        let isCollaboratorVideoAccess = false
        if (userData.isCollaborator !== undefined && userData.isCollaborator !== null && userData.isCollaborator)
        {
            const collabaorator = await collabaorators.findOne({email: userData["Preferred Email"], isDelete: false}).populate('memberShipPlanDetails.planId').lean()
            if (collabaorator)
            {
                isCollaboratorVideoAccess = collabaorator.memberShipPlanDetails.planId.accessResources.filter(item=>item.name.toLowerCase().includes("video")).length > 0 ? true: false
            }
            
            if (!isCollaboratorVideoAccess)
            {
                videoIds = []
            }     
        }
        
        const totalCount = await adminNews.count({
            $or: [{ newsType: "news" }, { videoReferenceId: { $in: videoIds } }],
            publishOrHide: "publish"
        });
        const allNewsLists = await adminNews.aggregate([
            {
                $match: {
                    $or: [{ newsType: "news" }, { videoReferenceId: { $in: videoIds } }],
                    makeFeaturedCheckbox: false,
                    publishOrHide: "publish"
                },
            },
            {
                $lookup: {
                    from: "contentarchive_videos",
                    localField: "videoReferenceId",
                    foreignField: "_id",
                    as: "videoData",
                }
            },
            {
                $unwind: { path: "$videoData", preserveNullAndEmptyArrays: true }
            },
            {
                $addFields: {
                    thumbnailUrl: {
                        $cond: [
                            {
                                $eq: ["$newsType", "video"],
                            },
                            "$videoData.thumbnail", "$thumbnail"
                        ],
                    },
                    descriptionMain: {
                        $cond: [
                            {
                                $eq: ["$newsType", "video"],
                            },
                            "$videoData.description", "$description"
                        ],
                    },
                    titleMain: {
                        $cond: [
                            {
                                $eq: ["$newsType", "video"],
                            },
                            "$videoData.title", "$title"
                        ],
                    },
                },
            },
            {
                $unset: ["title", "description", "thumbnail", "videodata"]
            },
            { $sort: { date: -1 } },
            { $skip: req.query.page ? (parseInt(req.query.page) - 1) * parseInt(req.query.limit) : 0 },
            { $limit: req.query.limit ? parseInt(req.query.limit) : totalCount },
        ])


        var arr = [];
        for (var i = 0; i < allNewsLists.length; i++) {
            if (allNewsLists[i].videoData) {
                var url = s3.getSignedUrl("getObject", {
                    Bucket: "arn:aws:s3:us-east-2:496737174815:accesspoint/accessforapp",
                    Key: allNewsLists[i].videoData.video,
                    Expires: 100000,
                });
                arr.push({ ...allNewsLists[i], videoData: { ...allNewsLists[i].videoData, video: url } });
            } else {
                arr.push(allNewsLists[i]);

            }

        }
        data = arr;

        if (data)
            return res.status(200).json({
                status: true,
                message: `News list`,
                newsList: {
                    list: data,
                    totalPages: Math.ceil(totalCount / parseInt(req.query.limit)),
                    currentPage: parseInt(req.query.page),
                    totalCount: totalCount,
                },
            });
        else
            return res
                .status(200)
                .json({
                    status: false,
                    message: `Something went wrong while getting news list!`,
                });
    } catch (error) {
        return res.status(200).json({ status: false, message: `${error.message}` });
    }
};

// get featured news
exports.getFeaturedNews = async (req, res) => {
    try {
        const authUser = req.authUserId;
        const currentEdgeId = req.currentEdge._id;
        let ruleCondition = await userAccessRulesCommonCondition({ userId: authUser, relation_id: req.relation_id });
        const accessibleVideosList = await ContentArchiveVideo.find({
            isDelete: false,
            uploadstatus: { $ne: "inprocess" },
            ...ruleCondition
        }).select("_id").lean();

        const videoIds = accessibleVideosList.map((vid) => {
            return vid._id;
        });

        const featuredNews = await adminNews.aggregate([
            {
                $match: {
                    $or: [{ newsType: "news" }, { videoReferenceId: { $in: videoIds } }],
                    makeFeaturedCheckbox: true,
                    publishOrHide: "publish"
                },
            },
            {
                $lookup: {
                    from: "contentarchive_videos",
                    localField: "videoReferenceId",
                    foreignField: "_id",
                    as: "videoData",
                }
            },
            {
                $unwind: { path: "$videoData", preserveNullAndEmptyArrays: true }
            },
            {
                $addFields: {
                    thumbnailUrl: {
                        $cond: [
                            {
                                $eq: ["$newsType", "video"],
                            },
                            "$videoData.thumbnail", "$thumbnail"
                        ],
                    },
                    descriptionMain: {
                        $cond: [
                            {
                                $eq: ["$newsType", "video"],
                            },
                            "$videoData.description", "$description"
                        ],
                    },
                    titleMain: {
                        $cond: [
                            {
                                $eq: ["$newsType", "video"],
                            },
                            "$videoData.title", "$title"
                        ],
                    },
                },
            },
            {
                $unset: ["title", "description", "thumbnail", "videodata"]
            },
            { $sort: { createdAt: -1 } },
        ]);

        var data = featuredNews[0];
        if (featuredNews && featuredNews[0] && featuredNews[0].videoData) {
            var url = s3.getSignedUrl("getObject", {
                Bucket: "arn:aws:s3:us-east-2:496737174815:accesspoint/accessforapp",
                Key: featuredNews[0].videoData.video,
                Expires: 100000,
            });

            

            data = { ...featuredNews[0], videoData: { ...featuredNews[0].videoData, video: url } };
        }

        if (data)
            return res.status(200).json({ status: true, message: `Featured news`, featuredNews: data, });
        else
            return res.status(200).json({ status: false, message: `Featured news not found!`, });
    } catch (error) {
        return res.status(200).json({ status: false, message: `${error.message}` });
    }
}
// get featured news
exports.getFeaturedNewsV2 = async (req, res) => {
    try {
      const authUser = req.authUserId;
      const currentEdgeId = req.currentEdge._id;
      let ruleCondition =  await userAccessRulesCommonCondition({ userId: authUser, relation_id: req.relation_id });
      const userData = await User.findById(authUser).lean();
  
      const accessibleVideosList = await ContentArchiveVideo.find({
        isDelete: false,
        relation_id: ObjectId(req.relation_id),
        uploadstatus: { $ne: "inprocess" },
        ...ruleCondition
      })
        .select("_id")
        .lean();
  
      const videoIds = accessibleVideosList.map((vid) => {
        return vid._id;
      });
  
      const featuredNews = await adminNews.aggregate([
        {
          $match: {
            $or: [{ newsType: "news" }, { videoReferenceId: { $in: videoIds } }],
            makeFeaturedCheckbox: true,
            publishOrHide: "publish",
            relation_id: ObjectId(req.relation_id),
          },
        },
        {
          $match: ruleCondition
        },
        {
          $lookup: {
            from: "contentarchive_videos",
            localField: "videoReferenceId",
            foreignField: "_id",
            as: "videoData",
          },
        },
        {
          $unwind: { path: "$videoData", preserveNullAndEmptyArrays: true },
        },
        {
          $addFields: {
            thumbnailUrl: {
              $cond: [
                {
                  $eq: ["$newsType", "video"],
                },
                "$videoData.thumbnail",
                "$thumbnail",
              ],
            },
            descriptionMain: {
              $cond: [
                {
                  $eq: ["$newsType", "video"],
                },
                "$videoData.description",
                "$description",
              ],
            },
            titleMain: {
              $cond: [
                {
                  $eq: ["$newsType", "video"],
                },
                "$videoData.title",
                "$title",
              ],
            },
          },
        },
        {
          $unset: ["title", "description", "thumbnail", "videodata"],
        },
        { $sort: { createdAt: -1 } },
      ]);
  
      var data = featuredNews[0];
      if (featuredNews && featuredNews[0] && featuredNews[0].videoData) {
        var url = s3.getSignedUrl("getObject", {
          Bucket: "arn:aws:s3:us-east-2:496737174815:accesspoint/accessforapp",
          Key: featuredNews[0].videoData.video,
          Expires: 100000,
        });
  
        data = {
          ...featuredNews[0],
          videoData: { ...featuredNews[0].videoData, video: url },
        };
      }
  
      if (data)
        return res
          .status(200)
          .json({ status: true, message: `Featured news`, featuredNews: data });
      else
        return res
          .status(200)
          .json({ status: false, message: `Featured news not found!` });
    } catch (error) {
      return res.status(200).json({ status: false, message: `${error.message}` });
    }
  };
  
  // all news list for frontend
  exports.getNewsAndContentListV2 = async (req, res) => {
    try {
      const authUser = req.authUserId;
      const currentEdgeId = req.currentEdge._id;
      let ruleCondition = await userAccessRulesCommonCondition({ userId: authUser, relation_id: req.relation_id });
      const userData = await User.findById(authUser).lean();
      
      // var userDayData =
      //   userData["migrate_user"] && userData["migrate_user"].plan_id === "Staff"
      //     ? userData["# of Days Since MDS Only Census"]
      //     : userData["# of Days Since MDS Only Census"] &&
      //       typeof userData["# of Days Since MDS Only Census"] !== "object"
      //     ? userData["# of Days Since MDS Only Census"]
      //     : 400;
      let videoIds = [];
      if (
        Array.isArray(req.currentEdge?.scopes) &&
        req.currentEdge.scopes.includes("videos")
      ) {
        const accessibleVideosList = await ContentArchiveVideo.find({
          isDelete: false,
          relation_id: ObjectId(req.relation_id),
          uploadstatus: { $ne: "inprocess" },
          ...(ruleCondition || {}), // Ensure ruleCondition is an object
        })
          .select("_id")
          .lean();

        videoIds = accessibleVideosList.map((vid) => vid._id);
      }
      
  
      let isCollaboratorVideoAccess = false;
      if (
        userData.isCollaborator !== undefined &&
        userData.isCollaborator !== null &&
        userData.isCollaborator
      ) {
        const collabaorator = await collabaorators
          .findOne({ email: userData["Preferred Email"], isDelete: false })
          .populate("memberShipPlanDetails.planId")
          .lean();
        if (collabaorator) {
          isCollaboratorVideoAccess =
            collabaorator.memberShipPlanDetails.planId.accessResources.filter(
              (item) => item.name.toLowerCase().includes("video")
            ).length > 0
              ? true
              : false;
        }
  
        if (!isCollaboratorVideoAccess) {
          videoIds = [];
        }
      }
  
      let pipeline = [
        {
          $match: {
            $or: [{ newsType: "news" }, { videoReferenceId: { $in: videoIds } }],
            makeFeaturedCheckbox: false,
            publishOrHide: "publish",
            relation_id: ObjectId(req.relation_id),
          },
        },
        {
          $match: ruleCondition
        },
        {
          $lookup: {
            from: "contentarchive_videos",
            localField: "videoReferenceId",
            foreignField: "_id",
            as: "videoData",
          },
        },
        {
          $unwind: { path: "$videoData", preserveNullAndEmptyArrays: true },
        },
        {
          $addFields: {
            thumbnailUrl: {
              $cond: [
                {
                  $eq: ["$newsType", "video"],
                },
                "$videoData.thumbnail",
                "$thumbnail",
              ],
            },
            descriptionMain: {
              $cond: [
                {
                  $eq: ["$newsType", "video"],
                },
                "$videoData.description",
                "$description",
              ],
            },
            titleMain: {
              $cond: [
                {
                  $eq: ["$newsType", "video"],
                },
                "$videoData.title",
                "$title",
              ],
            },
          },
        },
        {
          $unset: ["title", "description", "thumbnail", "videodata"],
        },
      ];
  
      const allNews = await adminNews.aggregate([...pipeline]);
      const totalCount = allNews.length;
  
      const allNewsLists = await adminNews.aggregate([
        ...pipeline,
        { $sort: { date: -1 } },
        {
          $skip: req.query.page
            ? (parseInt(req.query.page) - 1) * parseInt(req.query.limit)
            : 0,
        },
        { $limit: req.query.limit ? parseInt(req.query.limit) : totalCount },
      ]);
  
      var arr = [];
      for (var i = 0; i < allNewsLists.length; i++) {
        if (allNewsLists[i].videoData) {
          var url = s3.getSignedUrl("getObject", {
            Bucket: "arn:aws:s3:us-east-2:496737174815:accesspoint/accessforapp",
            Key: allNewsLists[i].videoData.video,
            Expires: 100000,
          });
          arr.push({
            ...allNewsLists[i],
            videoData: { ...allNewsLists[i].videoData, video: url },
          });
        } else {
          arr.push(allNewsLists[i]);
        }
      }
      data = arr;
  
      if (data)
        return res.status(200).json({
          status: true,
          message: `News list`,
          newsList: {
            list: data,
            totalPages: Math.ceil(totalCount / parseInt(req.query.limit)),
            currentPage: parseInt(req.query.page),
            totalCount: totalCount,
          },
        });
      else
        return res.status(200).json({
          status: false,
          message: `Something went wrong while getting news list!`,
        });
    } catch (error) {
      return res.status(200).json({ status: false, message: `${error.message}` });
    }
  };
  
  // news detail api(user Api)
  exports.getNewsDetailByIdForUserV2 = async (req, res) => {
    try {
      const userId = req.authUserId;
      let ruleCondition = await userAccessRulesCommonCondition({ userId: userId, relation_id: req.relation_id });
      const loginUserData = await User.findOne(
        { _id: userId, isDelete: false },
        { _id: 1, purchased_plan: 1, accessible_groups: 1 }
      );
  
      const newsDetail = await adminNews
        .findOne({
          _id: req.params.id,
          ...(req.owner ? {}: {...ruleCondition}),
        })
        .populate({ path: "restrictedAccessGroupId", select: "groupTitle" })
        .populate({
          path: "restrictedAccessMembershipPlanId",
          select: "plan_name -accessResources",
        })
        .populate({
          path: "restrictedAccessUserId",
          select: "first_name last_name display_name attendeeDetail",
        })
        .populate({
          path: "restrictedAccessEventId",
          select: "title -tag -category -subcategory",
        });
  
      if (newsDetail)
        return res
          .status(200)
          .json({ status: true, message: `News detail`, newsDetail: newsDetail });
      else
        return res
          .status(200)
          .json({ status: false, message: `No data found for this news id!` });
    } catch (error) {
      return res.status(200).json({ status: false, message: `${error.message}` });
    }
  };
// get Users we want to add rules
exports.getUserCount = async (req, res) => {
    try {

        let body = req.body;
        let match;
    
        //get event Attendee users
        let matchAttendees, uniqueEventUserIds;
        if (body.restrictedAccessEventId) {
            const eventIds = body.restrictedAccessEventId.map((id) => new ObjectId(id));
    
            matchAttendees = {
              event: { $in: eventIds },
              relation_id: ObjectId(req.relation_id),
              isDelete: false,
            };
    
          const list = await EventParticipantAttendees.aggregate([
            {
              $match: matchAttendees
            },
            {
                $lookup: {
                  from: "event_wise_participant_types",
                  localField: "role",
                  foreignField: "_id",
                  pipeline: [
                  {
                  $match: {
                    isDelete: false,
                  },
                  },
                ],
                  as: "roleData",
                },
              },
              {
                $match: {"roleData.role":"Member"}
              },
            ]); 
    
          const eventUserIds = list.map((item) => item.user);
          const uniqueUserIds = [...new Set(eventUserIds)];
          uniqueEventUserIds = Array.from(new Set(uniqueUserIds));
        }
    
        // match = { isDelete: false };
        match = { isDelete: false ,$or: [{ blocked: false }, { blocked: { $exists: false } }],firebaseId: { $nin: ["", null] },};
        if (body.restrictionAccess === "restricted") {
            const uniqueAllUserIds = [...new Set([...body.restrictedAccessUserId, ...(uniqueEventUserIds || [])]),];
      
            match = {
            ...match,
            $or: [
              { _id: { $in: uniqueAllUserIds } },
              { purchased_plan: { $in: body.restrictedAccessMembershipPlanId } },
              { accessible_groups: { $in: body.restrictedAccessGroupId } },
            ],
          };
        } else {
          match = {
            ...match,
            isCollaborator: false
          };
        }
    
        const userData = await User.find(match, {
          _id: 1,
          first_name: { '$ifNull': ['$first_name', ''] },
          last_name: { '$ifNull': ['$last_name', ''] },
          display_name: { '$ifNull': ['$display_name', ''] },
          'Preferred Email': 1,
        })
          .populate({ path: "accessible_groups", select: "groupTitle groupInfo " })
          .populate({
            path: "purchased_plan",
            select: "plan_name -accessResources",
          })
          .populate({
            path: "attendeeDetail",
            populate: {
              path: "evntData",
              populate: {
                path: "event",
                select: { _id: 1, title: 1 },
              },
            },
          });
    
        if (userData.length !== 0) {
          return res.status(200).json({
            status: true,
            message: "Get notification list successfully!",
            data: userData,
          });
        } else {
          return res.status(200).json({
            status: false,
            message: "Data not found!",
          });
        }
    } catch (error) {
      return res.status(500).json({ status: false, message: error.message });
    }
  };

    // replace only restrictionAccess,restrictedAccessGroups and restrictedAccessMembership
exports.replaceRestrictionFieldForNews = async (req, res) => {
  try {
    const newsList = await adminNews.find({ isDelete: false });
    let countAll = 0;
    let updateData;

    for (let item of newsList) {
      let updateFields;

      if (item.newsAccessType == "all") {
        updateFields = {
          restrictionAccess: "public",
        };
        countAll++; 
      }else{
        updateFields = {
          restrictionAccess: "restricted",
        };
        countAll++; 
      } 
      updateData = await adminNews.findOneAndUpdate(
        { _id: new ObjectId(item._id) },
        { $set: updateFields },
        { new: true }
      );
    }

    return res.status(200).json({
      status: true,
      message: "Replace Restriction Field For news successfully!",
      countAll: countAll, 
    });
  } catch (error) {
    return res.status(500).json({
      status: false,
      message: "Internal server error!",
      error: error.message, 
    });
  }
};