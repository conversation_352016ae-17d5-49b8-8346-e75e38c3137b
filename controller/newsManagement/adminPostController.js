const { ObjectId } = require("mongodb");
const jwt = require("jsonwebtoken");
const postModel = require("../../database/models/adminPost");
const AWS = require("aws-sdk");
const User = require("../../database/models/airTableSync");
const group = require("../../database/models/group");
const membershipPlan = require("../../database/models/membershipPlanManagement/membership_plan");
const event = require("../../database/models/event");
const EventParticipantAttendees = require("../../database/models/eventParticipantAttendees");
const { userAccessRulesCommonCondition, checkValidIds} = require("../../controller/userAccessRules/restrictionAccess");
const { getAllTiersfromBilling } = require("../userAccessRules/tiers");

var s3 = new AWS.S3({
    accessKeyId: process.env.AWS_ID,
    secretAccessKey: process.env.AWS_SECRET,
    Bucket: process.env.AWS_BUCKET,
});

/*Create Post*/
exports.createPost = async (req, res) => {
    try {
        body = req.body;
        const post = new postModel(body)
        const resPost = await post.save();
        return res.status(200).json({ status: true, message: `Post saved successfully!`, postData: resPost });
    } catch (error) {
        return res.status(200).json({ status: false, message: `${error.message}` });
    }
};

/*Create Post*/
exports.createPostV2 = async (req, res) => {
    try {
      // check valid or not
      let checkIds =  await checkValidIds({
        restrictedAccessGroupId:req.body.restrictedAccessGroupId,
        restrictedAccessMembershipPlanId:req.body.restrictedAccessMembershipPlanId,
        restrictedAccessUserId:req.body.restrictedAccessUserId,
        restrictedAccessTagId:req.body.restrictedAccessTagId,
        restrictedAccessEventId:req.body.restrictedAccessEventId,
        restrictedAccessTierId: req.body.restrictedAccessTierId,
        relation_id: req.relation_id
        });

        if(checkIds.status === false)
        { return res.status(200).json({ status: false, message: checkIds.msg }); }

        const post = new postModel({...req.body,relation_id: ObjectId(req.relation_id)})
        const resPost = await post.save();
        return res.status(200).json({ status: true, message: `Post saved successfully!`, postData: resPost });
    } catch (error) {
        return res.status(200).json({ status: false, message: `${error.message}` });
    }
};

/*Edit Post*/
exports.editPost = async (req, res) => {
    try {
        const { title, name, url, date } = req.body;
        const post = await postModel.findOne({ _id: ObjectId(req.params.id), isDelete: false })
        if (!post) {
            return res.status(200).json({ status: false, message: `Post not found!` });
        } else {
            const postData = {
                title: title ?? post.title,
                name: name ?? post.name,
                url: url ?? post.url,
                date: date ?? post.date,
            }
            const resPost = await postModel.findByIdAndUpdate(ObjectId(req.params.id), postData, { new: true })
            return res.status(200).json({ status: true, message: `Post updated successfully`, postData: resPost });
        }

    } catch (error) {
        return res.status(200).json({ status: false, message: `${error.message}` });
    }
};

/*Edit Post*/
exports.editPostV2 = async (req, res) => {
    try {
        const { title, name, url, date } = req.body;
        const post = await postModel.findOne({ _id: ObjectId(req.params.id), isDelete: false })
        if (!post) {
            return res.status(200).json({ status: false, message: `Post not found!` });
        } else {
            // check valid or not
            let checkIds =  await checkValidIds({
                restrictedAccessGroupId:req.body.restrictedAccessGroupId,
                restrictedAccessMembershipPlanId:req.body.restrictedAccessMembershipPlanId,
                restrictedAccessUserId:req.body.restrictedAccessUserId,
                restrictedAccessTagId:req.body.restrictedAccessTagId,
                restrictedAccessEventId:req.body.restrictedAccessEventId,
                restrictedAccessTierId: req.body.restrictedAccessTierId,
                relation_id: req.relation_id
                });
        
            if(checkIds.status === false)
            { return res.status(200).json({ status: false, message: checkIds.msg }); }

            const postData = {
                title: title ?? post.title,
                name: name ?? post.name,
                url: url ?? post.url,
                date: date ?? post.date,
                restrictionAccess: req.body.restrictionAccess ?? post.restrictionAccess,
                restrictedAccessGroupId: req.body.restrictedAccessGroupId ?req.body.restrictedAccessGroupId:[],
                restrictedAccessMembershipPlanId: req.body.restrictedAccessMembershipPlanId ?req.body.restrictedAccessMembershipPlanId:[],
                restrictedAccessUserId: req.body.restrictedAccessUserId ?req.body.restrictedAccessUserId:[],
                restrictedAccessEventId: req.body.restrictedAccessEventId ?req.body.restrictedAccessEventId:[],
                restrictedAccessTagId: req.body.restrictedAccessTagId?req.body.restrictedAccessTagId:[],
                restrictedAccessTierId: req.body.restrictedAccessTierId ? req.body.restrictedAccessTierId : [],
            }
            const resPost = await postModel.findByIdAndUpdate(ObjectId(req.params.id), postData, { new: true })
            return res.status(200).json({ status: true, message: `Post updated successfully`, postData: resPost });
        }

    } catch (error) {
        return res.status(200).json({ status: false, message: `${error.message}` });
    }
};
// delete Post
exports.deletePost = async (req, res) => {
    try {
        const getPost = await postModel.findOne({ _id: new ObjectId(req.params.id), isDelete: false }).lean();
        if (getPost) {
            const postData = await postModel.findByIdAndUpdate(req.params.id, { isDelete: true }, { new: true });
            if (postData)
                return res.status(200).json({ status: true, message: "Post deleted successfully!", data: postData });
            else
                return res.status(200).json({ status: false, message: "something went wrong while deleteing Post!", });
        } else {
            return res.status(200).json({ status: false, message: "Post not found!" });
        }
    } catch (e) {
        return res.status(500).json({ status: false, message: "internal server error!", error: e });
    }
};

// get all Post
exports.getAllPost = async (req, res) => {
    try {
        const sortField = (req.query.sortField === "name" ?"name" : req.query.sortField === "title" ? "title" :req.query.sortField === "url" ? "url" : req.query.sortField === "date" ? "date" : "createdAt")
        const sortType = req.query.sortType ==="Asc"? 1 : -1;
    
        var match = {
            isDelete: false,
            relation_id: ObjectId(req.relation_id),
        };

        var search = "";
        if (req.query.search) {
            search = req.query.search;
            match = {
                ...match,
                $or: [
                    { title: { $regex: ".*" + search + ".*", $options: "i" }, },
                    { name: { $regex: ".*" + search + ".*", $options: "i" }, },
                ]
            };
        }

      const allPostDataPromise = await postModel.find(match)
        // .collation({ locale: "en" })
        .sort({ [`${sortField}`]: sortType });

        // Fetch total count of matching documents
      const countAllDataPromise = postModel.countDocuments({
          relation_id: ObjectId(req.relation_id),
          isDelete: false
        });
      const [allPostData, countAllData] = await Promise.all([allPostDataPromise, countAllDataPromise]);

        if (allPostData)
          return res.status(200).json({ status: true, message: "All Post retrieved!", data: allPostData, countAllData });
        else
            return res.status(200).json({ status: false, message: "something went wrong while getting Post!", });
    } catch (e) {
        return res.status(200).json({ status: false, message: `${error.message}` });
    }
};

// get All Post Suggestion List
exports.getAllPostSuggestionList = async (req, res) => {
    try {

        var match = {
            isDelete: false,
            relation_id: ObjectId(req.relation_id),
        };

        const allPostData = await postModel.find(match,{title:1,name:1,_id:0}).sort({ name: 1 }).lean();
        if (allPostData)
            return res.status(200).json({ status: true, message: "All Post retrieved!", data: allPostData });
        else
            return res.status(200).json({ status: false, message: "something went wrong while getting Post!", });
    } catch (e) {
        return res.status(200).json({ status: false, message: `${error.message}` });
    }
};

// get all Post
exports.getPostDetail = async (req, res) => {
    try {

        const id = req.params.id
        const postData = await postModel.findOne({ _id: ObjectId(id), isDelete: false })
        .populate({ path: "restrictedAccessGroupId", select: "groupTitle" })
        .populate({ path: "restrictedAccessMembershipPlanId", select: "plan_name -accessResources" })
        .populate({ path: "restrictedAccessUserId", select: {'Preferred Email':1 ,first_name:1,last_name:1,display_name:1,attendeeDetail:1}})
        .populate({ path: "restrictedAccessTagId", select: "name" })
        .populate({ path: "restrictedAccessEventId", select: "title -tag -category -subcategory" }).lean();
        
        let newsDetails = { ...postData }
        const obj = { relation_id: req.relation_id }
        const tiers = await getAllTiersfromBilling(obj, expand = true);
        const restrictedTierIds = Array.isArray(newsDetails.restrictedAccessTierId)
          ? newsDetails.restrictedAccessTierId.map(id => id.toString())
          : [];
        const filteredTiers = tiers.filter(tier =>
          restrictedTierIds.includes(tier._id.toString())
        ).map(tier => ({
          _id: tier._id,
          name: tier.name
        }));
        newsDetails.restrictedAccessTierId = filteredTiers;

      if (newsDetails)
          return res.status(200).json({ status: true, message: "Post details retrieved!", data: newsDetails });
        else
            return res.status(200).json({ status: false, message: "something went wrong while getting Post!", });
    } catch (e) {
        return res.status(200).json({ status: false, message: `${error.message}` });
    }
};

// get all Post for users
exports.getAllPostUsers = async (req, res) => {
    try {
        const allPostData = await postModel.find({ isDelete: false,relation_id: ObjectId(req.relation_id), });
        if (allPostData)
            return res.status(200).json({ status: true, message: "All Post retrieved!", data: allPostData });
        else
            return res.status(200).json({ status: false, message: "something went wrong while getting Post!", });
    } catch (e) {
        return res.status(200).json({ status: false, message: `${error.message}` });
    }
};
exports.getAllPostUsersV2 = async (req, res) => {
    try {
        const userId = req.authUserId
        let ruleCondition = await userAccessRulesCommonCondition({ userId: userId, relation_id: req.relation_id });

        var match = { 
            isDelete: false,
            relation_id: ObjectId(req.relation_id), 
            ...ruleCondition
        };
        const allPostData = await postModel.find(match);
        if (allPostData)
            return res.status(200).json({ status: true, message: "All Post retrieved!", data: allPostData });
        else
            return res.status(200).json({ status: false, message: "something went wrong while getting Post!", });
    } catch (e) {
        return res.status(200).json({ status: false, message: `${error.message}` });
    }
};
// get Users we want to add rules
exports.getUserCount = async (req, res) => {
    try {

        let body = req.body;
        let match;
    
        //get event Attendee users
        let matchAttendees, uniqueEventUserIds;
        if (body.restrictedAccessEventId) {
            const eventIds = body.restrictedAccessEventId.map((id) => new ObjectId(id));
    
            matchAttendees = {
              event: { $in: eventIds },
              relation_id: ObjectId(req.relation_id), 
              isDelete: false,
            };
    
          const list = await EventParticipantAttendees.aggregate([
            {
              $match: matchAttendees
            },
            {
                $lookup: {
                  from: "event_wise_participant_types",
                  localField: "role",
                  foreignField: "_id",
                  pipeline: [
                  {
                  $match: {
                    isDelete: false,
                  },
                  },
                ],
                  as: "roleData",
                },
              },
              {
                $match: {"roleData.role":"Member"}
              },
            ]); 
    
          const eventUserIds = list.map((item) => item.user);
          const uniqueUserIds = [...new Set(eventUserIds)];
          uniqueEventUserIds = Array.from(new Set(uniqueUserIds));
        }
    
        // match = { isDelete: false };
        match = { isDelete: false ,$or: [{ blocked: false }, { blocked: { $exists: false } }],firebaseId: { $nin: ["", null] },};
        if (body.restrictionAccess === "restricted") {
            const uniqueAllUserIds = [...new Set([...body.restrictedAccessUserId, ...(uniqueEventUserIds || [])]),];
      
            match = {
            ...match,
            $or: [
              { _id: { $in: uniqueAllUserIds } },
              { purchased_plan: { $in: body.restrictedAccessMembershipPlanId } },
              { accessible_groups: { $in: body.restrictedAccessGroupId } },
            ],
          };
        } else {
          match = {
            ...match,
            isCollaborator: false
          };
        }
    
        const userData = await User.find(match, {
          _id: 1,
          first_name: { '$ifNull': ['$first_name', ''] },
          last_name: { '$ifNull': ['$last_name', ''] },
          display_name: { '$ifNull': ['$display_name', ''] },
          'Preferred Email': 1,
        })
          .populate({ path: "accessible_groups", select: "groupTitle groupInfo " })
          .populate({
            path: "purchased_plan",
            select: "plan_name -accessResources",
          })
          .populate({
            path: "attendeeDetail",
            populate: {
              path: "evntData",
              populate: {
                path: "event",
                select: { _id: 1, title: 1 },
              },
            },
          });
    
        if (userData.length !== 0) {
          return res.status(200).json({
            status: true,
            message: "Get notification list successfully!",
            data: userData,
          });
        } else {
          return res.status(200).json({
            status: false,
            message: "Data not found!",
          });
        }
    } catch (error) {
      return res.status(500).json({ status: false, message: error.message });
    }
  };

    // replace only restrictionAccess,restrictedAccessGroups and restrictedAccessMembership
exports.replaceRestrictionFieldForpost = async (req, res) => {
    try {
      const postList = await postModel.find({ isDelete: false });
      let countAll = 0;
      let updateData;
  
      for (let item of postList) {
        let updateFields;
  
        if (item.postAccessType == "all") {
          updateFields = {
            restrictionAccess: "public",
          };
          countAll++; 
        }else{
          updateFields = {
            restrictionAccess: "restricted",
          };
          countAll++; 
        } 
        updateData = await postModel.findOneAndUpdate(
          { _id: new ObjectId(item._id) },
          { $set: updateFields },
          { new: true }
        );
      }
  
      return res.status(200).json({
        status: true,
        message: "Replace Restriction Field For post successfully!",
        countAll: countAll, 
      });
    } catch (error) {
      return res.status(500).json({
        status: false,
        message: "Internal server error!",
        error: error.message, 
      });
    }
  };
