const { ObjectId } = require("mongodb");
const jwt = require("jsonwebtoken");
const bannerModel = require("../../database/models/adminBanner");
const AWS = require("aws-sdk");
const { checkValidIds,userAccessRulesCommonCondition } = require("../userAccessRules/restrictionAccess");
const { getAllTiersfromBilling } = require("../userAccessRules/tiers");

var s3 = new AWS.S3({
    accessKeyId: process.env.AWS_ID,
    secretAccessKey: process.env.AWS_SECRET,
    Bucket: process.env.AWS_BUCKET,
});

/*Create Banner*/
exports.createBanner = async (req, res) => {
    try {
        const { bannerUrl, publicationStartDate, publicationStartTime, publicationEndDate, publicationEndTime, saveAs, } = req.body;
        const ids = await bannerModel.find({ isDelete: false }, { _id: 1, order: 1 }).sort({ order: -1 });
        let newOrder = (ids && ids.length > 0) ? ids[0].order + 1 : 1
        const BannerData = {
            bannerImage: req.bannerImage,
            webBannerImage: req.webBannerImage,
            bannerUrl: bannerUrl,
            publicationStartDate: publicationStartDate,
            publicationStartTime: publicationStartTime,
            publicationEndDate: publicationEndDate,
            publicationEndTime: publicationEndTime,
            saveAs: saveAs,
            order:newOrder,
            relation_id: ObjectId(req.relation_id),
        }
        const banner = new bannerModel(BannerData);
        const resBanner = await banner.save();
        return res.status(200).json({ status: true, message: `Banner saved successfully!`, bannerData: resBanner });
    } catch (error) {
        return res.status(200).json({ status: false, message: `${error.message}` });
    }
};

exports.createBannerV2 = async (req, res) => {
    try {
        const body = req.body;
        const ids = await bannerModel.find({ isDelete: false }, { _id: 1, order: 1 }).sort({ order: -1 });
      
        let checkIds =  await checkValidIds({
          restrictedAccessGroupId:req.body.restrictedAccessGroupId,
          restrictedAccessEventId:req.body.restrictedAccessEventId,
          restrictedAccessMembershipPlanId:req.body.restrictedAccessMembershipPlanId,
          restrictedAccessUserId:req.body.restrictedAccessUserId,
          restrictedAccessTagId:req.body.restrictedAccessTagId,
          restrictedAccessTierId: req.body.restrictedAccessTierId,
          relation_id: req.relation_id
      });
      if(checkIds.status === false) { 
         res.status(200).json({ status: false, message: checkIds.msg }); 
        }
        let newOrder = (ids && ids.length > 0) ? ids[0].order + 1 : 1
        const BannerData = {
            bannerImage: req.bannerImage,
            webBannerImage: req.webBannerImage,
            bannerUrl: body.bannerUrl,
            publicationStartDate: body.publicationStartDate,
            publicationStartTime: body.publicationStartTime,
            publicationEndDate: body.publicationEndDate,
            publicationEndTime: body.publicationEndTime,
            restrictionAccess:body.restrictionAccess,
            restrictedAccessGroupId:body.restrictedAccessGroupId ? body.restrictedAccessGroupId : [],
            restrictedAccessMembershipPlanId: body.restrictedAccessMembershipPlanId ? body.restrictedAccessMembershipPlanId:[],
            restrictedAccessUserId:body.restrictedAccessUserId ? body.restrictedAccessUserId:[],
            restrictedAccessTagId:body.restrictedAccessTagId ? body.restrictedAccessTagId:[],
            restrictedAccessEventId: body.restrictedAccessEventId ? body.restrictedAccessEventId:[],
            restrictedAccessTierId: body.restrictedAccessTierId ? body.restrictedAccessTierId:[],
            saveAs: body.saveAs,
            order:newOrder,
            relation_id: ObjectId(req.relation_id),
        }
        const banner = new bannerModel(BannerData);
        const resBanner = await banner.save();
        return res.status(200).json({ status: true, message: `Banner saved successfully!`, bannerData: resBanner });
    } catch (error) {
        return res.status(200).json({ status: false, message: `${error.message}` });
    }
};

/*Edit Banner*/
exports.editBanner = async (req, res) => {
    try {
        const { bannerUrl, publicationStartDate, publicationEndDate, publicationStartTime, publicationEndTime, saveAs, } = req.body;
        const banner = await bannerModel.findOne({ _id: ObjectId(req.params.id), isDelete: false })
        if (!banner) {
            return res.status(200).json({ status: false, message: `Banner not found!` });
        } else {
            const bannerData = {
                bannerImage: req.bannerImage ?? banner.bannerImage,
                webBannerImage: req.webBannerImage ?? banner.webBannerImage,
                bannerUrl: bannerUrl ?? banner.bannerUrl,
                publicationStartDate: publicationStartDate ?? banner.publicationStartDate,
                publicationEndDate: publicationEndDate ?? banner.publicationEndDate,
                publicationStartTime: publicationStartTime ?? banner.publicationStartTime,
                publicationEndTime: publicationEndTime ?? banner.publicationEndTime,
                saveAs: saveAs ?? banner.saveAs
            }
            const resBanner = await bannerModel.findByIdAndUpdate(ObjectId(req.params.id), bannerData,
                { new: true })
            return res.status(200).json({ status: true, message: `Banner updated successfully`, bannerData: resBanner });
        }

    } catch (error) {
        return res.status(200).json({ status: false, message: `${error.message}` });
    }
};

exports.editBannerV2 = async (req, res) => {
    try {
      const body = req.body;
  
        let checkIds =  await checkValidIds({
          restrictedAccessGroupId:req.body.restrictedAccessGroupId,
          restrictedAccessEventId:req.body.restrictedAccessEventId,
          restrictedAccessMembershipPlanId:req.body.restrictedAccessMembershipPlanId,
          restrictedAccessUserId:req.body.restrictedAccessUserId,
          restrictedAccessTagId:req.body.restrictedAccessTagId,
          restrictedAccessTierId: req.body.restrictedAccessTierId,
          relation_id: req.relation_id
      });
      if(checkIds.status === false) { 
         res.status(200).json({ status: false, message: checkIds.msg }); 
        }
        const banner = await bannerModel.findOne({ _id: ObjectId(req.params.id), isDelete: false })
        if (!banner) {
            return res.status(200).json({ status: false, message: `Banner not found!` });
        } else {
            const bannerData = {
                bannerImage: req.bannerImage ?? banner.bannerImage,
                webBannerImage: req.webBannerImage ?? banner.webBannerImage,
                bannerUrl: body.bannerUrl ?? banner.bannerUrl,
                publicationStartDate: body.publicationStartDate ?? banner.publicationStartDate,
                publicationEndDate: body.publicationEndDate ?? banner.publicationEndDate,
                publicationStartTime: body.publicationStartTime ?? banner.publicationStartTime,
                publicationEndTime: body.publicationEndTime ?? banner.publicationEndTime,
                restrictionAccess:body.restrictionAccess,
                restrictedAccessGroupId:body.restrictedAccessGroupId ? body.restrictedAccessGroupId : [],
                restrictedAccessMembershipPlanId: body.restrictedAccessMembershipPlanId ? body.restrictedAccessMembershipPlanId:[],
                restrictedAccessUserId:body.restrictedAccessUserId ? body.restrictedAccessUserId:[],
                restrictedAccessEventId:body.restrictedAccessEventId ? body.restrictedAccessEventId:[],
                restrictedAccessTagId:body.restrictedAccessTagId ? body.restrictedAccessTagId:[],
                restrictedAccessTierId: body.restrictedAccessTierId ? body.restrictedAccessTierId : [],
                saveAs: body.saveAs ?? banner.saveAs
            }
            const resBanner = await bannerModel.findByIdAndUpdate(ObjectId(req.params.id), bannerData,
                { new: true })
            return res.status(200).json({ status: true, message: `Banner updated successfully`, bannerData: resBanner });
        }
  
    } catch (error) {
        return res.status(200).json({ status: false, message: `${error.message}` });
    }
};

// Delete Banner
exports.deleteBanner = async (req, res) => {
    try {
        const getBanner = await bannerModel.findOne({ _id: new ObjectId(req.params.id), isDelete: false }).lean();
        if (getBanner) {
            const bannerData = await bannerModel.findByIdAndUpdate(req.params.id, { isDelete: true }, { new: true });
            if (bannerData)
                return res.status(200).json({ status: true, message: "Banner deleted successfully!", data: bannerData });
            else
                return res.status(200).json({ status: false, message: "something went wrong while deleteing Banner!", });
        } else {
            return res.status(200).json({ status: false, message: "Banner not found!" });
        }
    } catch (error) {
        return res.status(500).json({ status: false, message: "internal server error!", error: e });
    }
};

// Get all Banner
exports.getAllBanner = async (req, res) => {
    try {
        const sortType = req.query.sortType ==="Asc"? -1 : 1;
        let search = req.query.search;
        // const allBannerData = await bannerModel.find({ isDelete: false }).collation({ locale: "en" }).sort({ [`${sortField}`]: -1 });
        const pipeline = [
            {
                $match: {
                    isDelete: false,
                    relation_id: ObjectId(req.relation_id),
                    publicationStartDate: { $ne: "Invalid date" },
                    publicationEndDate: { $ne: "Invalid date" },
                    publicationStartTime: { $ne: "Invalid date" },
                    publicationEndTime: { $ne: "Invalid date" },
                },
            },
            ...(search && search != "" ? [
                {
                    $match: {
                        $or: [
                            { bannerUrl: { $regex: ".*" + search + ".*", $options: "i" }, },
                        ]
                    },
                },
            ] : []),
            {
                $addFields: {
                    StartDateAndTime: {
                        $let: {
                            vars: {
                                year_: { $substr: ["$publicationStartDate", 6, 4] },
                                month_: { $substr: ["$publicationStartDate", 0, 2] },
                                dayOfMonth_: { $substr: ["$publicationStartDate", 3, 2] },
                            },
                            in: {
                                $toDate: {
                                    $concat: ["$$year_", "-", "$$month_", "-", "$$dayOfMonth_", " ", "$publicationStartTime"]
                                },
                            },
                        },
                    },
                },
            },
            {
                $addFields: {
                    EndDateAndTime: {
                        $let: {
                            vars: {
                                year_: { $substr: ["$publicationEndDate", 6, 4] },
                                month_: { $substr: ["$publicationEndDate", 0, 2] },
                                dayOfMonth_: { $substr: ["$publicationEndDate", 3, 2] },
                            },
                            in: {
                                $toDate: {
                                    $concat: ["$$year_", "-", "$$month_", "-", "$$dayOfMonth_", " ", "$publicationEndTime"]
                                },
                            },
                        },
                    },
                },
            },
            {
                $project: {
                    _id: 1,
                    webBannerImage: 1,
                    bannerImage: 1,
                    bannerUrl: 1,
                    publicationStartDate: 1,
                    publicationStartTime: 1,
                    publicationEndDate: 1,
                    publicationEndTime: 1,
                    saveAs: 1,
                    order: 1,
                    createdAt: 1,
                    StartDateAndTime: 1,
                    EndDateAndTime: 1
                }
            },
            {
                $addFields: {
                    sortFieldLower: (req.query.sortField === "bannerUrl" ? { $toLower: "$bannerUrl" } : req.query.sortField === "saveAs" ? { $toLower: "$saveAs" } : { $toInt: "$order" })
                }
            },
            {
                $sort: (req.query.sortField === "publicationStartDate" ? { "publicationStartDate": sortType } : req.query.sortField === "publicationStartTime" ? { "StartDateAndTime": sortType } : req.query.sortField === "publicationEndDate" ? { "publicationEndDate": sortType } : req.query.sortField === "publicationEndTime" ? { "EndDateAndTime": sortType } : { sortFieldLower: sortType })
            },
            //   {
            //     $project: {
            //       sortFieldLower: 0,
            //     },
            //   },
        ];
        const [allBannerData, countAllData] = await Promise.all([
            bannerModel.aggregate(pipeline),
            bannerModel.countDocuments({ relation_id: ObjectId(req.relation_id), isDelete: false }),
        ]);

        if (allBannerData)
            return res.status(200).json({ status: true, message: "All Banner retrieved!", data: allBannerData, countAllData });
        else
            return res.status(200).json({ status: false, message: "something went wrong while getting Banner!", });
    } catch (error) {
        return res.status(200).json({ status: false, message: `${error.message}` });
    }
};

// get All Banner Suggestion List
exports.getAllBannerSuggestionList = async (req, res) => {
    try {
        const allBannerData = await bannerModel.find({ isDelete: false,relation_id: ObjectId(req.relation_id) }).select("bannerUrl -_id").sort({ bannerUrl: 1 }).lean();
        if (allBannerData)
            return res.status(200).json({ status: true, message: "All Banner retrieved!", data: allBannerData });
        else
            return res.status(200).json({ status: false, message: "something went wrong while getting Banner!", });
    } catch (error) {
        return res.status(200).json({ status: false, message: `${error.message}` });
    }
};

// Get Banner Detail
exports.getBannerDetail = async (req, res) => {
    try {
        const id = req.params.id
        const bannerData = await bannerModel.findOne({ _id: ObjectId(id), isDelete: false }).lean();
        let bannerDetails = { ...bannerData }
        const obj = { relation_id: req.relation_id }
        const tiers = await getAllTiersfromBilling(obj, expand = true);
        const restrictedTierIds = Array.isArray(bannerDetails.restrictedAccessTierId)
            ? bannerDetails.restrictedAccessTierId.map(id => id.toString())
            : [];
        const filteredTiers = tiers.filter(tier =>
            restrictedTierIds.includes(tier._id.toString())
        ).map(tier => ({
            _id: tier._id,
            name: tier.name
        }));
        bannerDetails.restrictedAccessTierId = filteredTiers;
        if (bannerDetails)
            return res.status(200).json({ status: true, message: "Banner details retrieved!", data: bannerDetails });
        else
            return res.status(200).json({ status: false, message: "something went wrong while getting Banner!", });
    } catch (error) {
        return res.status(200).json({ status: false, message: `${error.message}` });
    }
};

// Reorder Banner
exports.reorderBanner = async (req, res) => {
    try {
        const ids = req.body.ids
        if (ids.length > 0) {
            let resOrder = ids.map(async (item, i) => {
                await bannerModel.findByIdAndUpdate(ObjectId(item), { order: i + 1 }, { new: true })
            });
            await Promise.all([...resOrder]);
        }
        const reorderedBanner = await bannerModel.find({ isDelete: false ,relation_id: ObjectId(req.relation_id)}).sort({ order: 1 });
        if (reorderedBanner.length > 0)
            return res.status(200).json({ status: true, message: "Reordered banners retrieved!", data: reorderedBanner });
        else
            return res.status(200).json({ status: false, message: "Banners not found!" });
    } catch (error) {
        return res.status(200).json({ status: false, message: `${error.message}` });
    }
};

// get all banners for frontend users
exports.getAllBannerUsers = async (req, res) => {
    try {
        const allBannerData = await bannerModel.find({ saveAs: "publish", isDelete: false,relation_id: ObjectId(req.relation_id) }).sort({ order: 1 });
        if (allBannerData)
            return res.status(200).json({ status: true, message: "All Banner retrieved!", data: allBannerData });
        else
            return res.status(200).json({ status: false, message: "something went wrong while getting Banner!", });
    } catch (error) {
        return res.status(200).json({ status: false, message: `${error.message}` });
    }
};

exports.getAllBannerUsersV2 = async (req, res) => {
    try {
        const userId = req.authUserId
        let ruleCondition = await userAccessRulesCommonCondition({ userId: userId,relation_id:req.relation_id });
  
        const allBannerData = await bannerModel.aggregate([
          {
            $match: {
              saveAs: "publish",
              isDelete: false,
              relation_id: ObjectId(req.relation_id),
              ...ruleCondition
            },
          },
          {
            $sort: {
              order: 1
            }
          },
        ])
  
        if (allBannerData)
            return res.status(200).json({ status: true, message: "All Banner retrieved!", data: allBannerData });
        else
            return res.status(200).json({ status: false, message: "something went wrong while getting Banner!", });
    } catch (error) {
        return res.status(200).json({ status: false, message: `${error.message}` });
    }
  };

