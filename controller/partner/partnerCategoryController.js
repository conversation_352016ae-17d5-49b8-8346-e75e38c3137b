const { ObjectId } = require("mongodb");
const { deleteImage } = require("../../utils/mediaUpload");
const PartnerCategory = require("../../database/models/partner/partner_category");
const partnerSubCategory = require("../../database/models/partner/partner_subcategory");
const Partner = require("../../database/models/partner/partner");

exports.createCategoty = async (req, res) => {
  try {
    const subCatagoryArray = (req.body.subcategory !== undefined && req.body.subcategory !== null && req.body.subcategory !== "") ? req.body.subcategory : []
    const isPartnerCategoryExist = await PartnerCategory.find({name: new RegExp('^' + req.body.name + '$', 'i'), isDelete: false});
    if (isPartnerCategoryExist && isPartnerCategoryExist.length>0) {
      return res.status(200).json({ status: false, message: `Category name must be unique.` });
    }
    let subCategory = [];
    var subCategoryData = subCatagoryArray.map(async (item, index) => {
      if (await partnerSubCategory.findOne({ name: item, isDelete: false }))
        return res.status(200).json({status: false,message: `Sub Category name must be unique.`});

      const addSubCategory = new partnerSubCategory({ name: item });
      const subResult = await addSubCategory.save();
      subCategory.push(subResult._id);
    });
    await Promise.all([...subCategoryData]);

    const addPartnerCategory = new PartnerCategory({
      name: req.body.name,
      categoryImage: req.categoryImage,
      subcategory: subCategory,
      relation_id: ObjectId(req.relation_id),
    });
    const result = await addPartnerCategory.save();
    return res.status(200).json({ status: true, message: `Category created.`, data: result });
  } catch (error) {
    if (error.name === "MongoServerError" && error.code === 11000) {
      return res.status(200).json({ status: false, message: `Category name must be unique.` });
    } else {
      return res.status(200).json({ status: false, message: `Something went wrong. ${error}` });
    }
  }
};


exports.createCategoryV2 = async (req, res) => {
  try {
      const subCategoryArray = (req.body.subcategory !== undefined && req.body.subcategory !== null && req.body.subcategory !== "") ? req.body.subcategory.split(",") : [];
      const isPartnerCategoryExist = await PartnerCategory.aggregate([
        {
          $match: {
            isDelete: false,
            relation_id: ObjectId(req.relation_id),
            $expr: {
              $eq: [
                { $toLower: "$name" },
                req.body.name.toLowerCase()
              ]
            }
          }
        }
      ]);
      
      if (isPartnerCategoryExist && isPartnerCategoryExist.length > 0) {
          return res.status(200).json({ status: false, message: `Category name must be unique.` });
      }
      
      let subCategoryIds = [];
      for (const subcategory of subCategoryArray) {
          if (await partnerSubCategory.findOne({ name: subcategory, relation_id: ObjectId(req.relation_id), isDelete: false })) {
              return res.status(200).json({ status: false, message: `Sub Category name must be unique.` });
          }
          const addSubCategory = new partnerSubCategory({ name: subcategory, relation_id: ObjectId(req.relation_id), });
          const subResult = await addSubCategory.save();
          subCategoryIds.push(subResult._id);
      }
      
      const addPartnerCategory = new PartnerCategory({
          name: req.body.name,
          categoryImage: req.categoryImage,
          subcategory: subCategoryIds,
          relation_id: ObjectId(req.relation_id),
      });
      const result = await addPartnerCategory.save();
      return res.status(200).json({ status: true, message: `Category created.`, data: result });
  } catch (error) {
      if (error.name === "MongoServerError" && error.code === 11000) {
          return res.status(200).json({ status: false, message: `Category name must be unique.` });
      } else {
          return res.status(200).json({ status: false, message: `Something went wrong. ${error}` });
      }
  }
};


exports.deleteCategory = async (req, res) => {
  try {
    const { id } = req.params;
    const updatedData = await PartnerCategory.findByIdAndUpdate(id,{ isDelete: true },{ new: true }).select("-__v -createdAt -updatedAt");
    if (updatedData && updatedData.subcategory) {
      await partnerSubCategory.deleteMany({ _id: { $in: updatedData.subcategory } });
        partnerSubCategory.remove({
          _id: { $in: [...updatedData.subcategory] },
        });
    }
    return res.status(200).json({ status: true, message: `Category deleted.`, data: updatedData });
  } catch (error) {
    return res.status(200).json({ status: false, message: `${error.message}` });
  }
};

// delete partner Category and assign another category api
exports.deleteCategoryV2 = async (req, res) => {
try {
  const { deletePartnerCategoryId, reassignPartnerCategoryId } = req.body;
  if (deletePartnerCategoryId) {
    const getPartnerCategory = await PartnerCategory.findById(
      deletePartnerCategoryId
    );
    if (!getPartnerCategory)
      return res.status(200).json({ status: false, message: `Partner Category not found` });

    const alreadyAssignPartner = await Partner.find({ category: deletePartnerCategoryId, relation_id: ObjectId(req.relation_id), isDelete: false },{ _id: 1 }).lean();

    // delete category
    const deletePartnerCategory = await PartnerCategory.findByIdAndUpdate(
      deletePartnerCategoryId,
      { isDelete: true },
      { new: true }
    ).select("-__v -createdAt -updatedAt");

    if (deletePartnerCategory) {
      // delete subcategory
      if (deletePartnerCategory.subcategory) {
        await partnerSubCategory.deleteMany({
          _id: { $in: deletePartnerCategory.subcategory },
        });
        partnerSubCategory.remove({
          _id: { $in: [...deletePartnerCategory.subcategory] },
        });
      }

      // reassign Category
      if (reassignPartnerCategoryId) {
        if (alreadyAssignPartner.length > 0) {
          let reassignPartnerCategory = alreadyAssignPartner.map(
            async (item, i) => {
              await Partner.findByIdAndUpdate(
                ObjectId(item._id),
                { $pull: { category: ObjectId(deletePartnerCategoryId) } },
                { new: true }
              ).select("_id");
              
              await Partner.findOneAndUpdate(
                {
                  _id: item._id,
                  category: { $nin: reassignPartnerCategoryId },
                },
                { $push: { category: ObjectId(reassignPartnerCategoryId) } },
                { new: true }
              ).select("_id");
            }
          );
          await Promise.all([...reassignPartnerCategory]);
        }
      }
      return res
        .status(200)
        .json({
          status: true,
          message: `Category deleted.`,
          data: deletePartnerCategory,
        });
    } else {
      return res
        .status(200)
        .json({
          status: false,
          message: `Something went wrong while deleting partner Category!`,
        });
    }
  } else {
    return res
      .status(200)
      .json({ status: false, message: `Input parameters are missings!` });
  }
} catch (error) {
  return res.status(200).json({ status: false, message: `${error.message}` });
}
};

exports.getCategoriesList_as = async (req, res) => {
  try {
    const sortType = req.query.sortType ==="Asc"? 1 : -1;
  
    const pipeline = [
      {
        $match: {
          isDelete: false,
          relation_id: ObjectId(req.relation_id),
        },
      },
      {
        $lookup: {
          from: "partner_subcategories",
          localField: "subcategory",
          foreignField: "_id",
          pipeline: [
            {
              $match: {
                isDelete: false,
              },
            },
          ],
          as: "subcategory",
        },
      },
      {
        $lookup: {
          from: "partners",
          localField: "_id",
          foreignField: "category",
          pipeline: [
            { $match: { isDelete: false } },
          ],
          as: "categoryWisePartnerData",
        },
      },
      {
        $project: {
          _id: 1,
          name: 1,
          categoryImage: 1,
          isDelete: 1,
          categoryWisePartnerDataCount: {
            $cond: {
              if: { $isArray: "$categoryWisePartnerData" },
              then: { $size: "$categoryWisePartnerData" },
              else: 0,
            },
          },
          "subcategory": { name: 1, _id: 1, isDelete: 1 }
        },
      },
      {
        $addFields: {
          sortFieldLower:
            req.query.sortField === "name"
              ? { $toLower: "$name" }
              : req.query.sortField === "categoryWisePartnerDataCount"
                ? { $toInt: "$categoryWisePartnerDataCount" }
                : "$createdAt",
        },
      },
      { $sort: { sortFieldLower: sortType } },
      // {
      //   $project: {
      //     sortFieldLower: 0,
      //   },
      // },
    ]

    const [partnerCategorydata, countAllData] = await Promise.all([
      PartnerCategory.aggregate(pipeline),
      PartnerCategory.countDocuments({ relation_id: ObjectId(req.relation_id), isDelete: false }),
    ]);

    return res.status(200).json({ status: true, message: `List of categories.`, data: partnerCategorydata, countAllData });
  } catch (error) {
    return res.status(200).json({ status: false, message: `${error.message}` });
  }
};

exports.getReassignCategories = async (req, res) => {
  try {

    const categoryId = req.params.id;
  
    const partnerCategorydata = await PartnerCategory.aggregate([
      {
        $match: {
          _id: { $ne: ObjectId(categoryId) },
          relation_id: ObjectId(req.relation_id),
          isDelete: false,
        },
      },
      {
        $project: {
          _id: 1,
          name: 1,
          isDelete:1,
        },
      },
    ]);
    return res.status(200).json({ status: true, message: `List of categories.`, data: partnerCategorydata });
  } catch (error) {
    return res.status(200).json({ status: false, message: `${error.message}` });
  }
};

// categories Suggestion List
exports.categoriesSuggestionList = async (req, res) => {
  try {
    const partnerCategorydata = await PartnerCategory.find({ isDelete: false, relation_id: ObjectId(req.relation_id), }, { _id: 0, name: 1, subcategory: 0 }).sort({ name: 1 }).lean();
    return res.status(200).json({ status: true, message: `List of categories.`, data: partnerCategorydata });
  } catch (error) {
    return res.status(200).json({ status: false, message: `${error.message}` });
  }
};

exports.editCategory = async (req, res) => {
try {
  const { id } = req.params;
  const isPartnerCategoryExist = await PartnerCategory.aggregate([
          {
            $match: {
              _id: { $ne: ObjectId(id) },
              isDelete: false,
              $expr: {
                $eq: [
                  { $toLower: "$name" },
                  req.body.name.toLowerCase()
                ]
              }
            }
          }
        ]);
  if (isPartnerCategoryExist && isPartnerCategoryExist.length > 0) {
    return res.status(200).json({ status: false, message: `Category name must be unique.` });
  }
  const subCategoryArray = req.body.subcategory !== undefined && req.body.subcategory !== null && req.body.subcategory.length > 0 ? req.body.subcategory : []
  // update subcategory
  var partnerSubCategoryIds=[]
  if(!req.body.subcategory){
    const categoryData = await PartnerCategory.findOne(
      { _id: new ObjectId(id), isDelete: false }
    );
    if(categoryData.subcategory){
      categoryData.subcategory.map(async (item) => {
        const deleteSubCategory = await partnerSubCategory.findByIdAndUpdate(
          { _id: item._id },
          { isDelete:true },
          { new: true }
        );
      })
    }
    partnerSubCategoryIds = [];
  }
  else{
    var subcategoryData = req.body.subcategory !== undefined
      && req.body.subcategory !== null
      && req.body.subcategory.length > 0 && subCategoryArray.map(async (item, index) => {
        const partnerSubCategoryDataExist = await partnerSubCategory.findOne({ name: item,relation_id: ObjectId(req.relation_id), isDelete: false })
        if (partnerSubCategoryDataExist){
          partnerSubCategoryIds.push(partnerSubCategoryDataExist._id)
        }
        else{
          const addPartnerSubCategory = new partnerSubCategory({ name: item });
          const subResult = await addPartnerSubCategory.save();
          partnerSubCategoryIds.push(subResult._id)
        }
      });
      await Promise.all([...subcategoryData]);
    }
    await PartnerCategory.findByIdAndUpdate(id, { subcategory: partnerSubCategoryIds }, { new: true });

    // update img
    const catExists = await PartnerCategory.findById(ObjectId(id))
    if (catExists && catExists.categoryImage !== undefined && catExists.categoryImage !== null)
      deleteImage(catExists.categoryImage)
    const updatedData = await PartnerCategory.findByIdAndUpdate(id, { name: req.body.name, categoryImage: req.categoryImage ? req.categoryImage : catExists.categoryImage }, { new: true });
    return res.status(200).json({ status: true, message: `Category updated successfully.`, data: updatedData });
  } catch (error) {
    return res.status(200).json({ status: false, message: `${error.message}` });
  }
};

exports.getCategorybyId = async (req, res) => {
  try {
    const { id } = req.params;
    if (ObjectId.isValid(id)) {
      const data = await PartnerCategory.findOne({ _id: id, isDelete: false, }).select("-__v -createdAt -updatedAt");
      return res.status(200).json({ status: true, message: `Category data.`, data: data });
    } else {
      return res.status(200).json({ status: false, message: `Id is invalid`, data: [] });
    }
  } catch (error) {
    return res.status(200).json({ status: false, message: `${error.message}` });
  }
};

// check Alreday Exist SubCategory
exports.checkAlredayExistSubCategory = async (req, res) => {
try {
  if (req.body.name) {
    const subcategory = await partnerSubCategory.findOne({
      name: new RegExp('^' + req.body.name + '$', 'i'),
      relation_id: ObjectId(req.relation_id),
      isDelete: false,
    });
    if (subcategory) {
      const isAssignToCategory = await PartnerCategory.find({subcategory:subcategory._id,relation_id: ObjectId(req.relation_id),isDelete:false});
      const names = isAssignToCategory.map(category => category.name);
      return res.status(200).json({ status: false, message: `You can not add this subcategory because it is assigned to '${names}' category:`, data: names });
    } else {
      return res.status(200).json({ status: true, message: `This subcategory does not exist, you can add subcategory` });
    }
  } else {
    return res.status(200).json({ status: false, message: `Input parameter name is missing` });
  }
} catch (error) {
  return res.status(200).json({ status: false, message: `${error.message}` });
}
};