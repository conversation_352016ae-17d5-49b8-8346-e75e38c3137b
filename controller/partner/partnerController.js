const User = require("../../database/models/airTableSync");
const ContentArchiveVideo = require("../../database/models/contentArchive_video");
const Partner = require("../../database/models/partner/partner");
const ContentCategory = require("../../database/models/partner/partner_category");
const ContentSubCategory = require("../../database/models/partner/partner_subcategory");
const ObjectId = require("mongoose").Types.ObjectId;
const mongoose = require("mongoose");
const { deleteImage } = require("../../utils/mediaUpload");
const AWS = require("aws-sdk");
const categoryPartner = require("../../database/models/partner/categoryPartner");
const moment = require("moment");
const PartnerBadge = require("../../database/models/partner/partnerBadges");
const partnerReview = require("../../database/models/partner/partnerReview");
const group = require("../../database/models/group");
const membershipPlan = require("../../database/models/membershipPlanManagement/membership_plan");
const { userAccessRulesCommonConditionForPartner } = require("../../controller/userAccessRules/partner");
const { checkValidIds,userAccessRulesCommonCondition } = require("../../controller/userAccessRules/restrictionAccess");
const { getAllTiersfromBilling } = require("../userAccessRules/tiers");
const { MAX_EDITS, PRE_FIX_LENGTH } = require("../../config/config");

var s3 = new AWS.S3({
    accessKeyId: process.env.AWS_ID,
    secretAccessKey: process.env.AWS_SECRET,
    Bucket: process.env.AWS_BUCKET,
});

/** start Create, edit, delete and get all partner adminside apis**/

// create Partner
exports.createPartner = async (req, res) => {
    try {
        const body = req.body;
        let description = `<div "font-family: 'Muller';">${body.description}</div>`;
        body.description = description;
        let tagVideoIds = []
        if (body.companyName === undefined && body.companyName === "" && body.companyName === null) {
            return res.status(401).json({ status: false, message: "Company name is required!", });
        }

        const partnerBadge = await PartnerBadge.findOne({ name: "nobadge", isDelete: false }, { _id: 1 });

        if (req.body.partnerType === undefined) {
            req.body.partnerType = partnerBadge._id;
        }

        if (req.body.tag) {
            const objTagIds = req.body.tag.map((tagId) => {
                return ObjectId(tagId)
            })

            const tagVideos = await ContentArchiveVideo.find({ isDelete: false, tag: { $in: objTagIds } }).select({ _id: 1, categories: 0, subcategory: 0, speaker: 0, tag: 0, group_ids: 0, eventIds: 0 }).sort({ createdAt: -1 })
            if (tagVideos.length > 0) {
                tagVideoIds = tagVideos.length > 0 ? tagVideos.map((tagVideo, index) => { return { id: tagVideo._id, order: index + 1 } }) : []
            }

        }

        const newPartner = new Partner({
            companyName: req.body.companyName,
            companyLogo: req.partnerIcon,
            darkCompanyLogo: req.darkCompanyLogo,
            description: req.body.description ? `<div "font-family: 'Muller';">${body.description}</div>` : '',
            contactInfo: req.body.contactInfo,
            isMDSPartner: req.body.isMDSPartner,
            status: req.body.status,
            MDSType: req.body.MDSType,
            partnerType: req.body.partnerType,
            category: req.body.category,
            subcategory: req.body.subcategory,
            webBanner: req.webBanner,
            thumbnail: req.thumbnail,
            mobileBanner: req.mobileBanner,
            offerValue: req.body.offerValue,
            OfferDescription: req.body.OfferDescription ? `<div "font-family: 'Muller';">${body.OfferDescription}</div>` : '',
            OfferInstructions: req.body.OfferInstructions ? `<div "font-family: 'Muller';">${body.OfferInstructions}</div>` : '',
            tag: body.tag,
            videoIds: tagVideoIds,
            shortDescription: body.shortDescription ? body.shortDescription : '',
        });

        const partnerData = await newPartner.save();

        if (partnerData) {
            return res.status(200).json({ status: true, message: "Partner added successfully!", data: partnerData });
        } else {
            return res.status(200).json({ status: false, message: "Something went wrong while adding partner!", });
        }
    } catch (error) {
        console
        return res.status(500).json({ status: false, message: `Internal server error. ${error}` });
    }
};

// edit Partner
exports.editPartner = async (req, res) => {

    try {
        const body = req.body;
        const partnerExist = await Partner.findById(req.params.id);
        let videosWithOrder = []
        let relatedVideos = []
        let getExistingOnlyVideoIdsByTag = []
        let tagNewVideoIds = []

        if (!partnerExist)
            return res.status(200).json({ status: false, message: ` not found` });

        let existingVideoIds = partnerExist.videoIds
        videosWithOrder = existingVideoIds

        if (req.partnerIcon) {
            deleteImage(partnerExist.companyLogo);
        }
        if (req.darkCompanyLogo) {
            deleteImage(partnerExist.darkCompanyLogo);
        }
        if (req.webBanner) {
            deleteImage(partnerExist.webBanner);
        }
        if (req.thumbnail) {
            deleteImage(partnerExist.thumbnail);
        }
        if (req.mobileBanner) {
            deleteImage(partnerExist.mobileBanner);
        }

        const partnerBadge = await PartnerBadge.findOne({ name: "nobadge", isDelete: false }, { _id: 1 });

        if (req.body.partnerType === undefined) {
            req.body.partnerType = partnerBadge._id;
        }

        if (req.body.tag) {

            const existingTags = partnerExist.tag && partnerExist.tag.length > 0 ? partnerExist.tag.map(tag => tag._id.toString()) : []
            const newTags = (typeof req.body.tag === "string" ? [req.body.tag] : req.body.tag)
            let getExistingDiff = existingTags.filter(x => !newTags.includes(x));

            //get total video ids to remove 
            if (getExistingDiff.length > 0) {
                const objExistingDiff = getExistingDiff.map(x => ObjectId(x))
                const getExistingVideoIdsByTag = await ContentArchiveVideo.find({ isDelete: false, tag: { $in: objExistingDiff } }).select({ _id: 1, categories: 0, subcategory: 0, speaker: 0, tag: 0, group_ids: 0, eventIds: 0 }).sort({ createdAt: -1 })
                getExistingOnlyVideoIdsByTag = getExistingVideoIdsByTag.map(x => x._id.toString())

            }

            //get total video ids to add 
            let getNewTagDiff = newTags.filter(x => !existingTags.includes(x));
            if (getNewTagDiff.length > 0) {
                const objNewDiff = getNewTagDiff.map(x => ObjectId(x))
                const getNewVideoIdsByTag = await ContentArchiveVideo.find({ isDelete: false, tag: { $in: objNewDiff } }).select({ _id: 1, categories: 0, subcategory: 0, speaker: 0, tag: 0, group_ids: 0, eventIds: 0 }).sort({ createdAt: -1 })

                if (getNewVideoIdsByTag.length > 0) {
                    tagNewVideoIds = getNewVideoIdsByTag.map((tagVideo) => { return tagVideo._id.toString() })
                }

            }

            ///remove video ids  by removed tag
            const RemoveVideoIds = getExistingOnlyVideoIdsByTag.filter(x => !tagNewVideoIds.includes(x.id))
            existingVideoIds = existingVideoIds.filter(x => !RemoveVideoIds.includes(x.id.toString()))

            ///add video ids by  tag
            const AddVideoIds = tagNewVideoIds.filter(x => !getExistingOnlyVideoIdsByTag.includes(x.id))
            const tagVideosArr = [...existingVideoIds.map(video => video.id), ...AddVideoIds.map(id => ObjectId(id))]

            let sortingOption = partnerExist.relatedVideoSortOption ? partnerExist.relatedVideoSortOption : "default";

            switch (sortingOption) {
                case "custom":
                    relatedVideos = tagVideosArr
                    break;
                case "views":
                    relatedVideos = await ContentArchiveVideo.aggregate([
                        {
                            $match: {
                                _id: { $in: tagVideosArr }
                            }
                        },
                        {
                            $project: {
                                _id: 1,
                                views: { $sum: [{ $cond: [{ $ifNull: ['$starting_view_cnt', false] }, "$starting_view_cnt", 0] }, { $cond: [{ $not: ["views.0"] }, 0, { $size: "$views" }] }] }
                            }
                        },
                        {
                            $sort: { views: -1 }
                        }
                    ])
                    break;
                case "likes":
                    relatedVideos = await ContentArchiveVideo.aggregate([
                        {
                            $match: {
                                _id: { $in: tagVideosArr }
                            }
                        },
                        {
                            $project: {
                                _id: 1,
                                likes: { $cond: [{ $not: ["likes.0"] }, 0, { $size: "$likes" }] }
                            }
                        },
                        {
                            $sort: { likes: -1 }
                        }
                    ])
                    break;

                case "comments":
                    relatedVideos = await ContentArchiveVideo.aggregate([
                        {
                            $match: {
                                _id: { $in: tagVideosArr }
                            }
                        },
                        {
                            $project: {
                                _id: 1,
                                comments: { $cond: [{ $not: ["comments.0"] }, 0, { $size: "$comments" }] }
                            }
                        },
                        {
                            $sort: { comments: -1 }
                        }
                    ])
                    break;

                case "default":
                    relatedVideos = await ContentArchiveVideo.aggregate([
                        {
                            $match: {
                                _id: { $in: tagVideosArr }
                            }
                        },
                        {
                            $project: {
                                _id: 1,
                                createdAt: 1
                            }
                        },
                        {
                            $sort: { createdAt: -1 }
                        }
                    ])
                    break;
                case "latest":
                    relatedVideos = await ContentArchiveVideo.aggregate([
                        {
                            $match: {
                                _id: { $in: tagVideosArr }
                            }
                        },
                        {
                            $project: {
                                _id: 1,
                                createdAt: 1
                            }
                        },
                        {
                            $sort: { createdAt: -1 }
                        }
                    ])
                    break;
            }
            videosWithOrder = relatedVideos.map((videoId, index) => {
                if (videoId && typeof videoId === "object") {
                    const singleExistsVideo = existingVideoIds.filter((existsVideo) => {
                        if (existsVideo !== null) {
                            return existsVideo.id.toString() === videoId._id.toString()
                        }
                    })
                    if (singleExistsVideo.length > 0) {
                        const obj = { id: videoId, order: index + 1, status: singleExistsVideo[0].status }
                        return obj
                    } else {
                        const obj = { id: videoId, order: index + 1 }
                        return obj
                    }
                } else {
                    const singleExistsVideo = existingVideoIds.filter((existsVideo) => existsVideo.id.toString() === videoId.toString())
                    if (singleExistsVideo.length > 0) {
                        const obj = { id: videoId, order: index + 1, status: singleExistsVideo[0].status }
                        return obj
                    } else {
                        const obj = { id: videoId, order: index + 1 }
                        return obj
                    }
                }

            })

        } else {

            const existingTags = partnerExist.tag && partnerExist.tag.length > 0 ? partnerExist.tag.map(tag => tag._id) : []
            const videoIdsByTag = await ContentArchiveVideo.find({ isDelete: false, tag: { $in: existingTags } }).select({ _id: 1, categories: 0, subcategory: 0, speaker: 0, tag: 0, group_ids: 0, eventIds: 0 }).sort({ createdAt: -1 })
            const videoIdsByTagArr = videoIdsByTag.map(x => x._id.toString())
            let videoIdsAfterRemove = partnerExist.videoIds.filter(x => !videoIdsByTagArr.includes(x.id.toString()))

            videosWithOrder = videoIdsAfterRemove.map((videoId, index) => {
                return { id: videoId.id, order: index + 1, status: videoId.status }
            })

        }



        const updated = await Partner.findByIdAndUpdate(
            req.params.id,
            {
                companyName: req.body.companyName ?? partnerExist.companyName,
                companyLogo: req.partnerIcon ?? partnerExist.companyLogo,
                darkCompanyLogo: req.darkCompanyLogo ?? partnerExist.darkCompanyLogo,
                description: req.body.description ? `<div "font-family: 'Muller';">${body.description}</div>` : partnerExist.description,
                contactInfo: req.body.contactInfo ?? partnerExist.contactInfo,
                isMDSPartner: req.body.isMDSPartner ?? partnerExist.isMDSPartner,
                status: req.body.status ?? partnerExist.status,
                MDSType: req.body.MDSType ?? partnerExist.MDSType,
                partnerType: req.body.partnerType !== undefined ? req.body.partnerType : null,
                category: req.body.category ?? partnerExist.category,
                subcategory: req.body.subcategory ?? partnerExist.subcategory,
                webBanner: req.webBanner ?? partnerExist.webBanner,
                thumbnail: req.thumbnail ?? partnerExist.thumbnail,
                mobileBanner: req.mobileBanner ?? partnerExist.mobileBanner,
                offerValue: req.body.offerValue ?? partnerExist.offerValue,
                OfferDescription: `<div "font-family: 'Muller';">${req.body.OfferDescription}</div>` ?? partnerExist.OfferDescription,
                OfferInstructions: `<div "font-family: 'Muller';">${req.body.OfferInstructions}</div>` ?? partnerExist.OfferInstructions,
                tag: req.body.tag ? req.body.tag : [],
                shortDescription: req.body.shortDescription ?? partnerExist.shortDescription,
                videoIds: videosWithOrder
            },
            { new: true }
        );

        if (updated)
            return res.status(200).json({ status: true, message: `Partner Successfully Updated.`, Data: updated, });
        else
            return res.status(200).json({ status: false, message: `Something went wrong while updating partner!`, });
    } catch (error) {
        return res.status(200).json({ status: false, message: `${error.message}` });
    }

};


// ----------------BP code start--------------
//create partner added with rules 
exports.createPartnerV2 = async (req, res) => {
    try {
        const body = req.body;
        let description = `<div "font-family: 'Muller';">${body.description}</div>`;
        body.description = description;
        let tagVideoIds = []

        // check valid or not
        let checkIds =  await checkValidIds({
            restrictedAccessGroupId:req.body.restrictedAccessGroupId,
            restrictedAccessUserId:req.body.restrictedAccessUserId,
            restrictedAccessTagId:req.body.restrictedAccessTagId,
            restrictedAccessTierId: req.body.restrictedAccessTierId,
            relation_id: req.relation_id 
        });  

        if(checkIds.status === false)
        { return res.status(200).json({ status: false, message: checkIds.msg }); }
        
        if (body.companyName === undefined && body.companyName === "" && body.companyName === null) {
            return res.status(401).json({ status: false, message: "Company name is required!", });
        }

        const partnerBadge = await PartnerBadge.findOne({ name: "nobadge", isDelete: false }, { _id: 1 });
        if (req.body.partnerType === undefined) {
            req.body.partnerType = partnerBadge._id;
        }

        if (req.body.tag) {
            const objTagIds = req.body.tag.map((tagId) => {
                return ObjectId(tagId)
            })

            const tagVideos = await ContentArchiveVideo.find({ isDelete: false,relation_id: ObjectId(req.relation_id), tag: { $in: objTagIds } }).select({ _id: 1, categories: 0, subcategory: 0, speaker: 0, tag: 0, restrictedAccessGroupId: 0, eventIds: 0 }).sort({ createdAt: -1 })
            if (tagVideos.length > 0) {
                tagVideoIds = tagVideos.length > 0 ? tagVideos.map((tagVideo, index) => { return { id: tagVideo._id, order: index + 1 } }) : []
            }

        }

        const newPartner = new Partner({
            companyName: req.body.companyName,
            companyLogo: req.partnerIcon,
            darkCompanyLogo: req.darkCompanyLogo,
            description: req.body.description ? `<div "font-family: 'Muller';">${body.description}</div>` : '',
            contactInfo: req.body.contactInfo,
            isMDSPartner: req.body.isMDSPartner,
            status: req.body.status,
            MDSType: req.body.MDSType,
            partnerType: req.body.partnerType,
            category: req.body.category,
            subcategory: req.body.subcategory,
            webBanner: req.webBanner,
            thumbnail: req.thumbnail,
            mobileBanner: req.mobileBanner,
            offerValue: req.body.offerValue,
            OfferDescription: req.body.OfferDescription ? `<div "font-family: 'Muller';">${body.OfferDescription}</div>` : '',
            OfferInstructions: req.body.OfferInstructions ? `<div "font-family: 'Muller';">${body.OfferInstructions}</div>` : '',
            tag: body.tag,
            videoIds: tagVideoIds,
            shortDescription: body.shortDescription ? body.shortDescription : '',
            restrictionAccess:body.restrictionAccess,
            restrictedAccessGroupId:body.restrictedAccessGroupId ? body.restrictedAccessGroupId : [],
            restrictedAccessUserId:body.restrictedAccessUserId ? body.restrictedAccessUserId:[],
            restrictedAccessTagId:body.restrictedAccessTagId ? body.restrictedAccessTagId:[],
            restrictedAccessTierId: body.restrictedAccessTierId ? body.restrictedAccessTierId:[],
            relation_id: ObjectId(req.relation_id),
        });

        const partnerData = await newPartner.save();

        if (partnerData) {
            return res.status(200).json({ status: true, message: "Partner added successfully!", data: partnerData });
        } else {
            return res.status(200).json({ status: false, message: "Something went wrong while adding partner!", });
        }
    } catch (error) {
        return res.status(500).json({ status: false, message: `Internal server error. ${error}` });
    }
};

// edit Partner
exports.editPartnerV2 = async (req, res) => {
    try {
        const body = req.body;
        const partnerExist = await Partner.findById(req.params.id);
        let videosWithOrder = []
        let relatedVideos = []
        let getExistingOnlyVideoIdsByTag = []
        let tagNewVideoIds = []

        if (!partnerExist)
            return res.status(200).json({ status: false, message: ` not found` });

        // check valid or not
        let checkIds =  await checkValidIds({
            restrictedAccessGroupId:req.body.restrictedAccessGroupId,
            restrictedAccessUserId:req.body.restrictedAccessUserId,
            restrictedAccessTagId:req.body.restrictedAccessTagId,
            restrictedAccessTierId: req.body.restrictedAccessTierId,
            relation_id: req.relation_id
          });
      
        if(checkIds.status === false)
        { return res.status(200).json({ status: false, message: checkIds.msg }); }

        let existingVideoIds = partnerExist.videoIds
        videosWithOrder = existingVideoIds

        if (req.partnerIcon) {
            deleteImage(partnerExist.companyLogo);
        }
        if (req.darkCompanyLogo) {
            deleteImage(partnerExist.darkCompanyLogo);
        }
        if (req.webBanner) {
            deleteImage(partnerExist.webBanner);
        }
        if (req.thumbnail) {
            deleteImage(partnerExist.thumbnail);
        }
        if (req.mobileBanner) {
            deleteImage(partnerExist.mobileBanner);
        }

        const partnerBadge = await PartnerBadge.findOne({ name: "nobadge", isDelete: false }, { _id: 1 });

        if (req.body.partnerType === undefined) {
            req.body.partnerType = partnerBadge._id;
        }

        if (req.body.tag) {

            const existingTags = partnerExist.tag && partnerExist.tag.length > 0 ? partnerExist.tag.map(tag => tag._id.toString()) : []
            const newTags = (typeof req.body.tag === "string" ? [req.body.tag] : req.body.tag)
            let getExistingDiff = existingTags.filter(x => !newTags.includes(x));

            //get total video ids to remove 
            if (getExistingDiff.length > 0) {
                const objExistingDiff = getExistingDiff.map(x => ObjectId(x))
                const getExistingVideoIdsByTag = await ContentArchiveVideo.find({ isDelete: false, tag: { $in: objExistingDiff } }).select({ _id: 1, categories: 0, subcategory: 0, speaker: 0, tag: 0, restrictedAccessGroupId: 0, eventIds: 0 }).sort({ createdAt: -1 })
                getExistingOnlyVideoIdsByTag = getExistingVideoIdsByTag.map(x => x._id.toString())

            }

            //get total video ids to add 
            let getNewTagDiff = newTags.filter(x => !existingTags.includes(x));
            if (getNewTagDiff.length > 0) {
                const objNewDiff = getNewTagDiff.map(x => ObjectId(x))
                const getNewVideoIdsByTag = await ContentArchiveVideo.find({ isDelete: false, tag: { $in: objNewDiff } }).select({ _id: 1, categories: 0, subcategory: 0, speaker: 0, tag: 0, restrictedAccessGroupId: 0, eventIds: 0 }).sort({ createdAt: -1 })

                if (getNewVideoIdsByTag.length > 0) {
                    tagNewVideoIds = getNewVideoIdsByTag.map((tagVideo) => { return tagVideo._id.toString() })
                }

            }

            ///remove video ids  by removed tag
            const RemoveVideoIds = getExistingOnlyVideoIdsByTag.filter(x => !tagNewVideoIds.includes(x.id))
            existingVideoIds = existingVideoIds.filter(x => !RemoveVideoIds.includes(x.id.toString()))

            ///add video ids by  tag
            const AddVideoIds = tagNewVideoIds.filter(x => !getExistingOnlyVideoIdsByTag.includes(x.id))
            const tagVideosArr = [...existingVideoIds.map(video => video.id), ...AddVideoIds.map(id => ObjectId(id))]

            let sortingOption = partnerExist.relatedVideoSortOption ? partnerExist.relatedVideoSortOption : "default";

            switch (sortingOption) {
                case "custom":
                    relatedVideos = tagVideosArr
                    break;
                case "views":
                    relatedVideos = await ContentArchiveVideo.aggregate([
                        {
                            $match: {
                                _id: { $in: tagVideosArr }
                            }
                        },
                        {
                            $project: {
                                _id: 1,
                                views: { $sum: [{ $cond: [{ $ifNull: ['$starting_view_cnt', false] }, "$starting_view_cnt", 0] }, { $cond: [{ $not: ["views.0"] }, 0, { $size: "$views" }] }] }
                            }
                        },
                        {
                            $sort: { views: -1 }
                        }
                    ])
                    break;
                case "likes":
                    relatedVideos = await ContentArchiveVideo.aggregate([
                        {
                            $match: {
                                _id: { $in: tagVideosArr }
                            }
                        },
                        {
                            $project: {
                                _id: 1,
                                likes: { $cond: [{ $not: ["likes.0"] }, 0, { $size: "$likes" }] }
                            }
                        },
                        {
                            $sort: { likes: -1 }
                        }
                    ])
                    break;

                case "comments":
                    relatedVideos = await ContentArchiveVideo.aggregate([
                        {
                            $match: {
                                _id: { $in: tagVideosArr }
                            }
                        },
                        {
                            $project: {
                                _id: 1,
                                comments: { $cond: [{ $not: ["comments.0"] }, 0, { $size: "$comments" }] }
                            }
                        },
                        {
                            $sort: { comments: -1 }
                        }
                    ])
                    break;

                case "default":
                    relatedVideos = await ContentArchiveVideo.aggregate([
                        {
                            $match: {
                                _id: { $in: tagVideosArr }
                            }
                        },
                        {
                            $project: {
                                _id: 1,
                                createdAt: 1
                            }
                        },
                        {
                            $sort: { createdAt: -1 }
                        }
                    ])
                    break;
                case "latest":
                    relatedVideos = await ContentArchiveVideo.aggregate([
                        {
                            $match: {
                                _id: { $in: tagVideosArr }
                            }
                        },
                        {
                            $project: {
                                _id: 1,
                                createdAt: 1
                            }
                        },
                        {
                            $sort: { createdAt: -1 }
                        }
                    ])
                    break;
            }
            videosWithOrder = relatedVideos.map((videoId, index) => {
                if (videoId && typeof videoId === "object") {
                    const singleExistsVideo = existingVideoIds.filter((existsVideo) => {
                        if (existsVideo !== null) {
                            return existsVideo.id.toString() === videoId._id.toString()
                        }
                    })
                    if (singleExistsVideo.length > 0) {
                        const obj = { id: videoId, order: index + 1, status: singleExistsVideo[0].status }
                        return obj
                    } else {
                        const obj = { id: videoId, order: index + 1 }
                        return obj
                    }
                } else {
                    const singleExistsVideo = existingVideoIds.filter((existsVideo) => existsVideo.id.toString() === videoId.toString())
                    if (singleExistsVideo.length > 0) {
                        const obj = { id: videoId, order: index + 1, status: singleExistsVideo[0].status }
                        return obj
                    } else {
                        const obj = { id: videoId, order: index + 1 }
                        return obj
                    }
                }

            })

        } else {

            const existingTags = partnerExist.tag && partnerExist.tag.length > 0 ? partnerExist.tag.map(tag => tag._id) : []
            const videoIdsByTag = await ContentArchiveVideo.find({ isDelete: false, tag: { $in: existingTags } }).select({ _id: 1, categories: 0, subcategory: 0, speaker: 0, tag: 0,  restrictedAccessGroupId: 0, eventIds: 0 }).sort({ createdAt: -1 })
            const videoIdsByTagArr = videoIdsByTag.map(x=>x._id.toString()) 
            let videoIdsAfterRemove = partnerExist.videoIds.filter(x => !videoIdsByTagArr.includes(x.id.toString()))

            videosWithOrder = videoIdsAfterRemove.map((videoId, index) => {
                return { id: videoId.id, order: index + 1, status: videoId.status }
            })

        }



        const updated = await Partner.findByIdAndUpdate(
            req.params.id,
            {
                companyName: req.body.companyName ?? partnerExist.companyName,
                companyLogo: req.partnerIcon ?? partnerExist.companyLogo,
                darkCompanyLogo: req.darkCompanyLogo ?? partnerExist.darkCompanyLogo,
                description: req.body.description ? `<div "font-family: 'Muller';">${body.description}</div>` : partnerExist.description,
                contactInfo: req.body.contactInfo?req.body.contactInfo:{},
                isMDSPartner: req.body.isMDSPartner ?? partnerExist.isMDSPartner,
                status: req.body.status ?? partnerExist.status,
                MDSType: req.body.MDSType ?? partnerExist.MDSType,
                partnerType: req.body.partnerType !== undefined ? req.body.partnerType : null,
                category: req.body.category ?? partnerExist.category,
                subcategory: req.body.subcategory ?? partnerExist.subcategory,
                webBanner: req.webBanner ?? partnerExist.webBanner,
                thumbnail: req.thumbnail ?? partnerExist.thumbnail,
                mobileBanner: req.mobileBanner ?? partnerExist.mobileBanner,
                offerValue: req.body.offerValue ?? partnerExist.offerValue,
                OfferDescription: `<div "font-family: 'Muller';">${req.body.OfferDescription}</div>` ?? partnerExist.OfferDescription,
                OfferInstructions: `<div "font-family: 'Muller';">${req.body.OfferInstructions}</div>` ?? partnerExist.OfferInstructions,
                tag: req.body.tag ? req.body.tag : [],
                shortDescription: req.body.shortDescription ?? partnerExist.shortDescription,
                videoIds: videosWithOrder,
                restrictionAccess:body.restrictionAccess,
                restrictedAccessGroupId:body.restrictedAccessGroupId ? body.restrictedAccessGroupId : [],
                restrictedAccessMembershipPlanId:body.restrictedAccessMembershipPlanId ? body.restrictedAccessMembershipPlanId : [],
                restrictedAccessTierId:body.restrictedAccessTierId ? body.restrictedAccessTierId : [],
                restrictedAccessUserId:body.restrictedAccessUserId ? body.restrictedAccessUserId : [],
                restrictedAccessTagId:body.restrictedAccessTagId ? body.restrictedAccessTagId : [],
            },
            { new: true }
        );

        if (updated)
            return res.status(200).json({ status: true, message: `Partner Successfully Updated.`, Data: updated, });
        else
            return res.status(200).json({ status: false, message: `Something went wrong while updating partner!`, });
    } catch (error) {
        return res.status(200).json({ status: false, message: `${error.message}` });
    }

};

// get featured partner list for user
exports.getFeaturedOrFreshDealPartnersListForUserV2 = async (req, res) => {
    try {
        const userId = req.authUserId
        let ruleCondition = await userAccessRulesCommonCondition({ userId: userId,relation_id:req.relation_id });

        const field = req.query.type !== undefined && req.query.type !== null && req.query.type === "freshdeal" ? "freshDealPartner" : "featuredPartner";
        const orderField = req.query.type !== undefined && req.query.type !== null && req.query.type === "freshdeal" ? "freshDealPartnerOrder" : "featuredPartnerOrder";
        const filter = req.query.filter !== undefined && req.query.filter !== null ? req.query.filter : "offer";
        var matchDefault = {
            isDelete: false,
            status: "published",
            isMDSPartner: true,
            relation_id: ObjectId(req.relation_id),
        };

        var match = {};
        if (req.query.type) {
            match = {
                ...matchDefault,
                [`${field}`]: true,
            };
        }

        const partnerList = await Partner.aggregate([
            { $sort: { [`${orderField}`]: 1 } },
            {
                $match: ruleCondition
            },
            { $match: match },
            {
                $lookup: {
                    from: 'partnerbadges',
                    localField: 'partnerType',
                    foreignField: '_id',
                    as: 'typeData'
                }
            },
            { $unwind: { path: "$typeData", preserveNullAndEmptyArrays: true } },
            {
                $project: {
                    companyName: 1,
                    companyLogo: 1,
                    darkCompanyLogo: 1,
                    featuredPartnerOrder: 1,
                    freshDealPartnerOrder: 1,
                    thumbnail: 1,
                    MDSType: 1,
                    rating: 1,
                    description: 1,
                    shortDescription: 1,
                    offerValue: 1,
                    partnerType: {
                        $cond: [
                            {
                                "$ifNull":
                                    ["$typeData", false]
                            },
                            {
                                _id: "$typeData._id",
                                name: "$typeData.name",
                                badgeColor: "$typeData.badgeColor",
                            }, null
                        ]
                    },
                },
            }
        ]);

        const partnerCount = await Partner.countDocuments({ ...matchDefault, MDSType: filter });

        if (partnerList.length > 0)
            return res.status(200).json({ status: true, message: `Successfully retrived ${req.query.type} partners list`, data: { partnerList: partnerList, partnerCount: partnerCount } });
        else
            return res.status(200).json({ status: false, message: `${req.query.type} partners list not found!`, data: partnerList });
    } catch (e) {
        return res.status(200).json({ status: false, message: "Something went wrong!", error: e })
    }
}

// select partner list for user
exports.getSelectPartnerListV2 = async (req, res) => {
    try {
        const userId = req.authUserId
        let ruleCondition = await userAccessRulesCommonCondition({ userId: userId,relation_id:req.relation_id });

        var match = {
            isDelete: false,
            isMDSPartner: true,
            MDSType: "offer",
            status: "published",
            relation_id: ObjectId(req.relation_id),
        }

        var search = "";
        if (req.query.search) {
            search = req.query.search;
            match = {
                ...match,
                companyName: { $regex: ".*" + search + ".*", $options: "i" },
            };
        }

        const partnerList = await Partner.aggregate([
            {
                $match: ruleCondition
            },
            { $match: match },
            {
                $lookup: {
                    from: 'partnerreviews',
                    localField: '_id',
                    foreignField: 'partnerId',
                    as: 'partnerReviews'
                }
            },
            {
                $project: {
                    _id: 1,
                    companyName: 1,
                    thumbnail: 1,
                    isMDSPartner: 1,
                    description: 1,
                    shortDescription: 1,
                    status: 1,
                    offerValue: 1,
                    countPartnerReviews: { $cond: { if: { $isArray: "$partnerReviews" }, then: { $size: "$partnerReviews" }, else: "NA" } },
                    "partnerReviews": { _id: 1, status: 1 }
                },
            },
            { $sort: { countPartnerReviews: 1 } },
        ]);

        if (partnerList.length > 0) {
            return res.status(200).json({ status: true, message: `Partner list retrive successfully.`, data: partnerList, });
        } else {
            return res.status(200).json({ status: true, message: `Partner list not found!`, data: [], });
        }
    } catch (error) {
        return res.status(200).json({ status: false, message: `${error.message}` });
    }
};
// ----------------BP code end--------------


// delete partner
exports.deletePartner = async (req, res) => {
    try {
        if (req.params.id !== undefined && req.params.id !== null && req.params.id !== "") {
            const partnerExist = await Partner.findById(req.params.id);
            if (!partnerExist)
                return res.status(200).json({ status: false, message: `Partner not found!` });

            if (partnerExist.companyLogo) deleteImage(partnerExist.companyLogo);

            const deletePartner = await Partner.findByIdAndUpdate(req.params.id, { isDelete: true }).select("_id");
            if (deletePartner)
                return res.status(200).json({ status: true, message: `Partner  deleted successfully!` });
            else
                return res.status(200).json({ status: false, message: `Something went wrong while deleting partner !`, });
        }
    } catch (error) {
        return res.status(200).json({ status: false, message: `${error.message}` });
    }

};


// get all filter partner list - MDS partner
exports.getPartnerList = async (req, res) => {
    try {
        const sortType = req.query.sortType === "Asc" ? 1 : -1;

        const page = req.query.page ? parseInt(req.query.page) : 1;
        const limit = req.query.limit ? parseInt(req.query.limit) : 1;
        const skip = (page - 1) * limit;
        const mdsType = req.query.mdstype ? req.query.mdstype : "all";
        const tag = req.query.tag ? req.query.tag : "";
        var field = req.query.field;
        const filterType = req.query.filtertype;

        var match = {
            isDelete: false,
            isMDSPartner: true,
            relation_id: ObjectId(req.relation_id),
        };

        if (mdsType !== "all") {
            match = {
                ...match,
                MDSType: mdsType,
            };
        }

        if (tag !== "") {
            match = {
                ...match,
                tag: { $in: [ObjectId(tag)] },
            };
        }

    // var search = "";
    // if (
    //     req.query.search &&
    //     req.query.search !== null &&
    //     req.query.search !== ""
    // ) {
    //     search = req.query.search;
    //     match = {
    //         ...match,
    //         companyName: { $regex: ".*" + search + ".*", $options: "i" },
    //     };
    // }

        var toDate = new Date();
        var fromDate = new Date("1970-01-01T00:00:00Z");
        if (filterType) {
            var addFilterCount = 0;
            var toDate = new Date();
            var fromDate = new Date(toDate.toJSON().slice(0, 10));
            if (filterType == "first24hrs") {

          fromDate = moment(toDate).subtract(1, 'days').toDate();

        } else if (filterType == "past7days") {

            fromDate = moment(toDate).subtract(6, 'days').startOf('day').toDate();
            toDate = moment(toDate).endOf('day').toDate();

        } else if (filterType == "past28days") {

            fromDate = moment(toDate).subtract(27, 'days').startOf('day').toDate();
            toDate = moment(toDate).endOf('day').toDate();
            
        } else if (filterType == "past90days") {

            fromDate = moment(toDate).subtract(89, 'days').startOf('day').toDate();
            toDate = moment(toDate).endOf('day').toDate();

        } else if (filterType == "past365days") {
                
            fromDate = moment(toDate).subtract(364, 'days').startOf('day').toDate();
            toDate = moment(toDate).endOf('day').toDate();
                
        } else if (filterType === "custom") {
            fromDate = moment(new Date(req.query.fromdate)).startOf('day').toDate();
            toDate = moment(new Date(req.query.toDate)).endOf('day').toDate();
        }
        }

        var badge = "";
        let matchBadge;
        let badgeSort = {
            sortFieldLower: sortType,
        }
        if (req.query.badge) {
            badge = req.query.badge;
            matchBadge = {
                ...match,
                partnerType: ObjectId(badge),
            };

            badgeSort = {
                badgeOrder: 1,
            };
        }

        let mergedMatch;
        mergedMatch = { 
            ...match, 
            ...matchBadge, 
            ...(filterType && filterType != "lifetime" ? { createdAt: { $gte: fromDate, $lte: toDate } } : {})
          };


        if (filterType !== "lifetime") {
            var aggregatePipeline = [
                ...(req.query.search
                    ? [
                        {
                          $search: {
                            index: "default",
                            text: {
                              query: req.query.search,
                              path: ["companyName"],
                              fuzzy: {
                                maxEdits: MAX_EDITS,
                                prefixLength: PRE_FIX_LENGTH,
                              },
                            },
                          },
                        },
                      ]
                    : []),
                { $match: mergedMatch },
                {
                    $lookup: {
                        from: "partnerreviews",
                        localField: "_id",
                        foreignField: "partnerId",
                        pipeline: [
                            {
                                $match: {
                                    isDelete: false,
                                    createdAt: { $gte: fromDate, $lte: toDate },
                                },
                            },
                        ],
                        as: "partnerreviews",
                    },
                },
                ...(filterType && filterType != "lifetime"
                    ? [
                        {
                            $match: {
                                $or: [
                                  {
                                    $and: [
                                      { "userViews.viewData.viewDate": { $exists: true } },
                                      { "userViews.viewData.viewDate": { $gte: fromDate, $lte: toDate } }
                                    ]
                                  },
                                  {
                                    $and: [
                                      { "userOfferViews.offerViewData.viewOfferDate": { $exists: true } },
                                      { "userOfferViews.offerViewData.viewOfferDate": { $gte: fromDate, $lte: toDate } }
                                    ]
                                  },
                                  {
                                    $and: [
                                      { "partnerreviews.createdAt": { $exists: true } },
                                      { "partnerreviews.createdAt": { $gte: fromDate, $lte: toDate } }
                                    ]
                                  },
                                  // This condition ensures documents without the specified fields are included
                                  {
                                    $nor: [
                                      { "userViews.viewData.viewDate": { $exists: true } },
                                      { "userOfferViews.offerViewData.viewOfferDate": { $exists: true } },
                                      { "partnerreviews.createdAt": { $exists: true } }
                                    ]
                                  }
                                ]
                              }
                            },
                    ]
                    : []),
                {
                    $addFields: { approvedReviews: { $size: "$partnerreviews" } },
                },
                {
                    $lookup: {
                        from: "partnerbadges",
                        localField: "partnerType",
                        foreignField: "_id",
                        as: "typeData",
                    },
                },
                { $unwind: { path: "$typeData", preserveNullAndEmptyArrays: true } },
                {
                    $addFields: {
                        sortFieldLower:
                            req.query.sortField === "partnerType"
                                ? {
                                    $cond: {
                                        if: { $ne: ["$typeData.name", "nobadge"] },
                                        then: { $toLower: "$typeData.name" },
                                        else: "",
                                    },
                                }
                                : req.query.sortField === "companyName"
                                    ? { $toLower: "$companyName" }
                                    : req.query.sortField === "status"
                                        ? { $toLower: "$status" }
                                        : req.query.sortField === "createdAt"
                                            ? { $toLower: "$createdAt" }
                                            : req.query.sortField === "updatedAt"
                                                ? { $toLower: "$updatedAt" }
                                                : req.query.sortField === "pageView"
                                                    ? { $toInt: "$pageView" }
                                                    : req.query.sortField === "claims"
                                                        ? { $toInt: "$claims" }
                                                        : req.query.sortField === "rating"
                                                            ? { $toLower: "$rating" }
                                                            : req.query.sortField === "approvedReviews"
                                                                ? { $toInt: "$approvedReviews" }
                                                                : "$createdAt",
                    },
                },
                {
                    $unwind: { path: "$partnerreviews", preserveNullAndEmptyArrays: true },
                },
                {
                    $group: {
                        _id: "$_id",
                        avgStar: { $avg: { $toDouble: "$partnerreviews.star" } }, // Convert to double before averaging
                        companyName: { $first: "$companyName" },
                        companyLogo: { $first: "$companyLogo" },
                        darkCompanyLogo: { $first: "$darkCompanyLogo" },
                        isMDSPartner: { $first: "$isMDSPartner" },
                        MDSType: { $first: "$MDSType" },
                        status: { $first: "$status" },
                        partnerType: { $first: "$typeData.name" },
                        pageView: { $first: "$pageView" },
                        claims: { $first: "$claims" },
                        rating: { $first: "$rating" },
                        badgeOrder: { $first: "$badgeOrder" },
                        createdAt: { $first: "$createdAt" },
                        updatedAt: { $first: "$updatedAt" },
                        sortFieldLower: { $first: "$sortFieldLower" },
                        approvedReviews: { $first: "$approvedReviews" },
                        userViews: { $first: "$userViews" },
                        userOfferViews: { $first: "$userOfferViews" },
                    },
                },
                {
                    $project: {
                        _id: 1,
                        companyName: 1,
                        companyLogo: 1,
                        darkCompanyLogo: 1,
                        isMDSPartner: 1,
                        MDSType: 1,
                        status: 1,
                        partnerType: 1,
                        pageView: 1,
                        claims: 1,
                        rating: "$avgStar",
                        badgeOrder: 1,
                        createdAt: 1,
                        updatedAt: 1,
                        sortFieldLower: 1,
                        approvedReviews: 1,
                        userViews: 1,
                        userOfferViews: 1,
                        partnerReviewCount: {
                            $cond: {
                                if: { $isArray: "$partnerreviews" },
                                then: { $size: "$partnerreviews" },
                                else: 0,
                            },
                        },
                    },
                },
                {
                    $unwind: {
                        path: "$userViews",
                        preserveNullAndEmptyArrays: true,
                    },
                },
                {
                    $project: {
                        _id: 1,
                        companyName: 1,
                        companyLogo: 1,
                        darkCompanyLogo: 1,
                        isMDSPartner: 1,
                        MDSType: 1,
                        status: 1,
                        partnerType: 1,
                        pageView: 1,
                        claims: 1,
                        rating: 1,
                        badgeOrder: 1,
                        createdAt: 1,
                        updatedAt: 1,
                        sortFieldLower: 1,
                        approvedReviews: 1,
                        userOfferViews: 1,
                        "userViews.viewData": {
                            $filter: {
                                input: "$userViews.viewData",
                                as: "view",
                                cond: {
                                    $and: [
                                        { $gte: ["$$view.viewDate", fromDate || new Date(0)] },
                                        { $lt: ["$$view.viewDate", toDate || new Date()] },
                                    ],
                                },
                            },
                        },
                    },
                },
                {
            $addFields: {
                userViewsCount: {
                    $cond: {
                        if: {
                            $isArray: "$userViews.viewData"
                        },
                        then: {
                            $size: "$userViews.viewData"
                        },
                        else: 0
                    }
                }
            }
                },
                {
                    $group: {
                        _id: {
                            id: "$_id",
                            companyName: "$companyName",
                            companyLogo: "$companyLogo",
                            darkCompanyLogo: "$darkCompanyLogo",
                            isMDSPartner: "$isMDSPartner",
                            MDSType: "$MDSType",
                            status: "$status",
                            partnerType: "$partnerType",
                            // pageView: "$pageView",
                            claims: "$claims",
                            rating: "$rating",
                            badgeOrder: "$badgeOrder",
                            createdAt: "$createdAt",
                            updatedAt: "$updatedAt",
                            sortFieldLower: "$sortFieldLower",
                            pageView: {
                                $cond: {
                                    if: { $isArray: "$userViews.viewData" },
                                    then: { $size: "$userViews.viewData" },
                                    else: 0,
                                },
                            },
                            approvedReviews: "$approvedReviews",
                        },
                        userViewsCount: { $sum: "$userViewsCount" },
                        userOfferViews: { $first: "$userOfferViews" },
                    },
                },
                {
                    $group: {
                        _id: "$_id.id",
                        companyName: { $first: "$_id.companyName" },
                        companyLogo: { $first: "$_id.companyLogo" },
                        //   pageView: { $sum: "$_id.pageView" },
                        pageView: { $sum: "$userViewsCount" },
                        darkCompanyLogo: { $first: "$_id.darkCompanyLogo" },
                        isMDSPartner: { $first: "$_id.isMDSPartner" },
                        MDSType: { $first: "$_id.MDSType" },
                        status: { $first: "$_id.status" },
                        partnerType: { $first: "$_id.partnerType" },
                        claims: { $first: "$_id.claims" },
                        rating: { $first: "$_id.rating" },
                        badgeOrder: { $first: "$_id.badgeOrder" },
                        createdAt: { $first: "$_id.createdAt" },
                        updatedAt: { $first: "$_id.updatedAt" },
                        sortFieldLower: { $first: "$_id.sortFieldLower" },
                        approvedReviews: { $first: "$_id.approvedReviews" },
                        userOfferViews: { $first: "$userOfferViews" },
                    },
                },
                {
                    $unwind: {
                        path: "$userOfferViews",
                        preserveNullAndEmptyArrays: true,
                    },
                },
                {
                    $project: {
                        _id: 1,
                        companyName: 1,
                        companyLogo: 1,
                        darkCompanyLogo: 1,
                        isMDSPartner: 1,
                        MDSType: 1,
                        status: 1,
                        partnerType: 1,
                        pageView: 1,
                        // claims: 1,
                        rating: 1,
                        badgeOrder: 1,
                        createdAt: 1,
                        updatedAt: 1,
                        sortFieldLower: 1,
                        approvedReviews: 1,
                        "userOfferViews.offerViewData": {
                            $filter: {
                                input: "$userOfferViews.offerViewData",
                                as: "offerView",
                                cond: {
                                    $and: [
                                        {
                                            $gte: [
                                                "$$offerView.viewOfferDate",
                                                fromDate || new Date(0),
                                            ],
                                        },
                                        { $lt: ["$$offerView.viewOfferDate", toDate || new Date()] },
                                    ],
                                },
                            },
                        },
                    },
                },
                {
                  $addFields: {
                      userClaimCount: {
                          $cond: {
                              if: {
                                  $isArray: "$userOfferViews.offerViewData"
                              },
                              then: {
                                  $size: "$userOfferViews.offerViewData"
                              },
                              else: 0
                          }
                      }
                  },
                },
                {
                    $group: {
                        _id: {
                            id: "$_id",
                            companyName: "$companyName",
                            companyLogo: "$companyLogo",
                            darkCompanyLogo: "$darkCompanyLogo",
                            isMDSPartner: "$isMDSPartner",
                            MDSType: "$MDSType",
                            status: "$status",
                            partnerType: "$partnerType",
                            pageView: "$pageView",
                            rating: "$rating",
                            badgeOrder: "$badgeOrder",
                            createdAt: "$createdAt",
                            updatedAt: "$updatedAt",
                            sortFieldLower: "$sortFieldLower",
                            // claims: {
                            //   $cond: {
                            //     if: { $isArray: "$userOfferViews.offerViewData" },
                            //     then: { $size: "$userOfferViews.offerViewData" },
                            //     else: 0,
                            //   },
                            // },
                            approvedReviews: "$approvedReviews",
                        },
                        userClaimCount: { $sum: "$userClaimCount" },
                    },
                },
                {
                    $group: {
                        _id: "$_id.id",
                        companyName: { $first: "$_id.companyName" },
                        companyLogo: { $first: "$_id.companyLogo" },
                        pageView: { $first: "$_id.pageView" },
                        claims: { $sum: "$userClaimCount" },
                        rating: { $first: "$_id.rating" },
                        darkCompanyLogo: { $first: "$_id.darkCompanyLogo" },
                        isMDSPartner: { $first: "$_id.isMDSPartner" },
                        MDSType: { $first: "$_id.MDSType" },
                        status: { $first: "$_id.status" },
                        partnerType: { $first: "$_id.partnerType" },
                        badgeOrder: { $first: "$_id.badgeOrder" },
                        createdAt: { $first: "$_id.createdAt" },
                        updatedAt: { $first: "$_id.updatedAt" },
                        sortFieldLower: { $first: "$_id.sortFieldLower" },
                        approvedReviews: { $first: "$_id.approvedReviews" },
                    },
                },
                { $sort: req.query.badge ? badgeSort : { sortFieldLower: sortType } },
            ];
        } else {
            var aggregatePipeline = [
                ...(req.query.search
                    ? [
                        {
                          $search: {
                            index: "default",
                            text: {
                              query: req.query.search,
                              path: ["companyName"],
                              fuzzy: {
                                maxEdits: MAX_EDITS,
                                prefixLength: PRE_FIX_LENGTH,
                              },
                            },
                          },
                        },
                      ]
                    : []), 
                { $match: mergedMatch },
                {
                    $lookup: {
                        from: "partnerreviews",
                        let: { id: "$_id" },
                        pipeline: [
                            {
                                $match: {
                                    $expr: {
                                        $and: [
                                            { $eq: ["$partnerId", "$$id"] },
                                            // { $eq: ["$status", "approved"] },
                                            { $eq: ["$isDelete", false] },
                                        ],
                                    },
                                },
                            },
                            {
                                $project: { _id: 0, createdAt: 0 },
                            },
                        ],
                        as: "partnerreviews",
                    },
                },
                {
                    $addFields: { approvedReviews: { $size: "$partnerreviews" } },
                },
                {
                    $lookup: {
                        from: "partnerbadges",
                        localField: "partnerType",
                        foreignField: "_id",
                        as: "typeData",
                    },
                },
                { $unwind: { path: "$typeData", preserveNullAndEmptyArrays: true } },
                {
                    $addFields: {
                        sortFieldLower:
                            req.query.sortField === "partnerType"
                                ? {
                                    $cond: {
                                        if: { $ne: ["$typeData.name", "nobadge"] },
                                        then: { $toLower: "$typeData.name" },
                                        else: "",
                                    },
                                }
                                : req.query.sortField === "companyName"
                                    ? { $toLower: "$companyName" }
                                    : req.query.sortField === "status"
                                        ? { $toLower: "$status" }
                                        : req.query.sortField === "createdAt"
                                            ? { $toLower: "$createdAt" }
                                            : req.query.sortField === "updatedAt"
                                                ? { $toLower: "$updatedAt" }
                                                : req.query.sortField === "pageView"
                                                    ? { $toInt: "$pageView" }
                                                    : req.query.sortField === "claims"
                                                        ? { $toInt: "$claims" }
                                                        : req.query.sortField === "rating"
                                                            ? { $toLower: "$rating" }
                                                            : req.query.sortField === "approvedReviews"
                                                                ? { $toInt: "$approvedReviews" }
                                                                : "$createdAt",
                        },
                      },
                {
                  $unwind: { path: "$partnerreviews", preserveNullAndEmptyArrays: true },
                },
                {
                  $group: {
                    _id: "$_id",
                    avgStar: {
                      $avg: { $toDouble: "$partnerreviews.star" },
                    },
                    companyName: { $first: "$companyName" },
                    companyLogo: { $first: "$companyLogo" },
                    darkCompanyLogo: { $first: "$darkCompanyLogo" },
                    isMDSPartner: { $first: "$isMDSPartner" },
                    MDSType: { $first: "$MDSType" },
                    status: { $first: "$status" },
                    partnerType: { $first: "$typeData.name" },
                    pageView: { $first: "$pageView" },
                    claims: { $first: "$claims" },
                    rating: { $first: "$rating" },
                    badgeOrder: { $first: "$badgeOrder" },
                    createdAt: { $first: "$createdAt" },
                    updatedAt: { $first: "$updatedAt" },
                    sortFieldLower: { $first: "$sortFieldLower" },
                    approvedReviews: { $first: "$approvedReviews" },
                    userViews: { $first: "$userViews" },
                    userOfferViews: { $first: "$userOfferViews" },
                    },
                },
                {
                    $project: {
                        _id: 1,
                        companyName: 1,
                        companyLogo: 1,
                        darkCompanyLogo: 1,
                        isMDSPartner: 1,
                        MDSType: 1,
                        status: 1,
                        partnerType: "$typeData.name",
                        pageView: 1,
                        claims: 1,
                       //   rating: 1,
                        rating: "$avgStar",
                        badgeOrder: 1,
                        createdAt: 1,
                        updatedAt: 1,
                        sortFieldLower: 1,
                        approvedReviews: 1,
                    },
                },
                { $sort: req.query.badge ? badgeSort : { sortFieldLower: sortType } },
            ];
        }

        const partnerList = await Partner.aggregate([
            ...aggregatePipeline,
            { $skip: skip },
            { $limit: limit },
        ]);
        const totalCount = await Partner.aggregate(aggregatePipeline);
        count = totalCount.length;

        const countAllData = await Partner.countDocuments({ relation_id: ObjectId(req.relation_id), isDelete: false });

        if (partnerList.length > 0) {
            return res.status(200).json({
                status: true,
                message: `Partner list retrive successfully.`,
                data: {
                    partnerList: partnerList,
                    totalPages: Math.ceil(count / limit),
                    currentPage: page,
                    totalMessages: count,
                    countAllData: countAllData
                },
            });
        } else {
            return res.status(200).json({
                status: true,
                message: `Something went wrong while getting partner list!`,
                data: {
                    partnerList: [],
                    totalPages: Math.ceil(count / limit),
                    currentPage: page,
                    totalMessages: count,
                    countAllData: countAllData
                },
            });
        }
    } catch (error) {
        return res.status(200).json({ status: false, message: `${error.message}` });
    }
};
// get Partner Suggestion List for MDSPartner 
exports.getPartnerSuggestionList = async (req, res) => {
    try {
        var match = {
            isDelete: false,
            isMDSPartner: true,
            relation_id: ObjectId(req.relation_id),
        };
        const partnerList = await Partner.find(match, { _id: 0, companyName: 1, partnerType: 0, category: 0, subcategory: 0, tag: 0 }).sort({ companyName: 1 }).lean();
        if (partnerList.length > 0) {
            return res.status(200).json({
                status: true, message: `Partner list retrive successfully.`,
                data: partnerList
            });
        } else {
            return res.status(200).json({
                status: true, message: `No Data Found!`,
                data: []
            });
        }
    } catch (error) {
        return res.status(200).json({ status: false, message: `${error.message}` });
    }
};



// Partner list re-order Badge Filter Wise 
exports.badgeFilterWiseReOrderPartner = async (req, res) => {
    try {
        const ids = req.body.ids
        const objectPartnerIds = [];

        if (ids.length > 0) {
            let resOrder = ids.map(async (item, i) => {
                await Partner.findByIdAndUpdate(ObjectId(item), { badgeOrder: i + 1 }, { new: true }).select("_id")
                objectPartnerIds.push(ObjectId(item))
            });
            await Promise.all([...resOrder]);
        }

        const aggregatePipeline = [
            { $sort: { badgeOrder: 1 } },
            {
                $match: {
                    isDelete: false,
                    isMDSPartner: true,
                    _id: { $in: objectPartnerIds },
                    relation_id: ObjectId(req.relation_id),
                }
            },
            {
                $lookup: {
                    from: 'partnerbadges',
                    localField: 'partnerType',
                    foreignField: '_id',
                    as: 'typeData'
                }
            },

            { $unwind: { path: "$typeData", preserveNullAndEmptyArrays: true } },
        ];

        const reorderedPartner = await Partner.aggregate([
            ...aggregatePipeline,
            {
                $project: {
                    _id: 1,
                    companyName: 1,
                    companyLogo: 1,
                    darkCompanyLogo: 1,
                    isMDSPartner: 1,
                    status: 1,
                    partnerType: "$typeData.name",
                    pageView: 1,
                    claims: 1,
                    rating: 1,
                    createdAt: 1,
                    updatedAt: 1,
                    badgeOrder: 1,
                }
            }
        ])

        if (reorderedPartner.length > 0)
            return res.status(200).json({ status: true, message: "Reordered partners retrieved!", data: reorderedPartner });
        else
            return res.status(200).json({ status: false, message: "Partners not found!" });
    } catch (error) {
        return res.status(200).json({ status: false, message: `${error.message}` });
    }
};

// partner detail api
exports.getPartnerById = async (req, res) => {
    try {
        const obj = { relation_id: req.relation_id }
        if (req.params.id !== undefined && req.params.id !== null && req.params.id !== "") {
            let partnerDetailTemp = await Partner.findOne({ _id: ObjectId(req.params.id) }, { _id: 1, companyName: 1, tag: 1, companyLogo: 1, darkCompanyLogo: 1, description: 1, isMDSPartner: 1, status: 1, MDSType: 1, partnerType: 1, category: 1, subcategory: 1, webBanner: 1, thumbnail: 1, mobileBanner: 1, OfferInstructions: 1, offerValue: 1, OfferDescription: 1, contactInfo: 1, isDelete: 1, shortDescription: 1, restrictionAccess: 1, restrictedAccessGroupId: 1, restrictedAccessMembershipPlanId: 1, restrictedAccessUserId: 1,restrictedAccessTagId:1, restrictedAccessTierId: 1 })
                .populate({ path: "restrictedAccessGroupId", select: "groupTitle groupInfo " })
                .populate({ path: "restrictedAccessMembershipPlanId", select: "plan_name" })
                .populate({ path: "restrictedAccessTagId", select: "name" })
                .populate({ path: "restrictedAccessUserId", select: { first_name: { '$ifNull': ['$first_name', ''] }, last_name: { '$ifNull': ['$last_name', ''] } ,display_name:{ '$ifNull': ['$display_name', ''] }, 'Preferred Email': 1, attendeeDetail: 1 } }).lean();

                let partnerDetail = {...partnerDetailTemp}

                const tiers = await getAllTiersfromBilling( obj, expand = true );
                const restrictedTierIds = Array.isArray(partnerDetail.restrictedAccessTierId)
                ? partnerDetail.restrictedAccessTierId.map(id => id.toString())
                : [];
                const filteredTiers = tiers.filter(tier => 
                restrictedTierIds.includes(tier._id.toString())
                ).map(tier => ({
                _id: tier._id,
                name: tier.name
                }));
                partnerDetail.restrictedAccessTierId = filteredTiers;

                if (partnerDetail)
                return res.status(200).json({ status: true, message: `Partner  detail`, partnerDetail: partnerDetail });
            else
                return res.status(200).json({ status: false, message: `No data found for this partner id!` });
        } else {
            return res.status(200).json({ status: false, message: `Partner not found!`, });
        }
    } catch (error) {         
        return res.status(200).json({ status: false, message: `${error.message}` });
    }
};

// get all filter partner list
exports.getAllPartnerList = async (req, res) => {
    try {
        const sortType = req.query.sortType === "Asc" ? 1 : -1;
        const mdsType = (req.query.mdstype) ? req.query.mdstype : "all";
        const tag = (req.query.tag) ? req.query.tag : "";

        var match = {
            isDelete: false,
            relation_id: ObjectId(req.relation_id),
        }

        var search = "";
        if (req.query.search) {
            search = req.query.search;
            match = {
                ...match,
                companyName: { $regex: ".*" + search + ".*", $options: "i" },
            };
        }

        if (mdsType !== "all") {
            match = {
                ...match,
                MDSType: mdsType,
            };
        }
        if (tag !== "") {
            match = {
                ...match,
                tag: { $in: [ObjectId(tag)] },
            };
        }

        const partnerList = await Partner.aggregate([
            {
                $match: match,
            },
            {
                $lookup: {
                    from: 'partnerbadges',
                    localField: 'partnerType',
                    foreignField: '_id',
                    as: 'typeData'
                }
            },
            { $unwind: { path: "$typeData", preserveNullAndEmptyArrays: true } },
            {
                $project: {
                    _id: 1,
                    companyName: 1,
                    companyLogo: 1,
                    darkCompanyLogo: 1,
                    isMDSPartner: 1,
                    MDSType: 1,
                    status: 1,
                    partnerType: "$typeData.name",
                    pageView: 1,
                    claims: 1,
                    rating: 1,
                    createdAt: 1,
                    updatedAt: 1,
                },
            },
            {
                $addFields: {
                    sortFieldLower: (req.query.sortField === "companyName" ? { $toLower: "$companyName" } : req.query.sortField === "partnerType" ? { $toLower: "$partnerType" } : req.query.sortField === "status" ? { $toLower: "$status" } : req.query.sortField === "pageView" ? { $toInt: "$pageView" } : req.query.sortField === "claims" ? { $toInt: "$claims" } : req.query.sortField === "rating" ? { $toLower: "$rating" } : "createdAt")
                },
            },
            { $sort: (req.query.sortField === "isMDSPartner" ? { isMDSPartner: sortType } : req.query.sortField === "createdAt" ? { "createdAt": sortType } : { sortFieldLower: sortType }) },
            {
                $project: {
                    sortFieldLower: 0,
                },
            },
        ]);

        if (partnerList.length > 0) {
            return res.status(200).json({ status: true, message: `Partner list retrive successfully.`, data: partnerList, });
        } else {
            return res.status(200).json({ status: true, message: `Partner list not found!`, data: [], });
        }
    } catch (error) {
        return res.status(200).json({ status: false, message: `${error.message}` });
    }
};

// partner listing with selected fields for selecting feature partner or freshdeal -admin side
exports.getPublishedPartnersList = async (req, res) => {
    try {
        const partnerList = await Partner.find({ isDelete: false, status: "published", isMDSPartner: true, MDSType: "offer", relation_id: ObjectId(req.relation_id), }, {
            companyName: 1,
            companyLogo: 1,
            darkCompanyLogo: 1,
            featuredPartner: 1,
            freshDealPartner: 1,
            featuredPartnerOrder: 1,
            freshDealPartnerOrder: 1,
            MDSType: 1,
            category: 0,
            subcategory: 0
        });
        if (partnerList)
            return res.status(200).json({ status: true, message: "Successfully retrived partners list", data: partnerList })
        else
            return res.status(200).json({ status: false, message: "Something went wrong while retriving partners list", data: partnerList })
    } catch (e) {
        return res.status(200).json({ status: false, message: "Something went wrong!", error: e })
    }
}

// get featured partner list -admin side
exports.getFeaturedOrFreshdealPartnersList = async (req, res) => {
    try {

        const type = req.query.type
        const field = type !== undefined && type !== null && type === "freshdeal" ? "freshDealPartner" : "featuredPartner"
        const orderField = (type !== undefined && type !== null && type === "freshdeal" ? "freshDealPartnerOrder" : "featuredPartnerOrder")
        const sortType = req.query.sortType === "Desc" ? -1 : 1;

        const partnerList = await Partner.find({ isDelete: false, status: "published", isMDSPartner: true, relation_id: ObjectId(req.relation_id), [`${field}`]: true }, {
            companyName: 1,
            companyLogo: 1,
            darkCompanyLogo: 1,
            freshDealPartner: 1,
            featuredPartner: 1,
            featuredPartnerOrder: 1,
            freshDealPartnerOrder: 1,
            category: 0,
            subcategory: 0
        })
            // .collation({ locale: "en" })
            .sort(req.query.sortField === "companyName" ? { companyName: sortType } : { [`${orderField}`]: sortType });

        if (partnerList)
            return res.status(200).json({ status: true, message: `Successfully retrived ${type} partners list!`, data: partnerList })
        else
            return res.status(200).json({ status: false, message: `Something went wrong while retriving ${type} partners list`, data: partnerList })
    } catch (e) {
        return res.status(200).json({ status: false, message: "Something went wrong!", error: e })
    }
}
// get Featured Or Freshdeal Partners Suggestion List -admin side
exports.getFeaturedOrFreshdealPartnersSuggestionList = async (req, res) => {
    try {

        const type = req.query.type
        const field = type !== undefined && type !== null && type === "freshdeal" ? "freshDealPartner" : "featuredPartner"
        const partnerList = await Partner.find({ isDelete: false, status: "published", isMDSPartner: true, relation_id: ObjectId(req.relation_id), [`${field}`]: true }, {
            _id: 0,
            companyName: 1,
            partnerType: 0,
            category: 0,
            subcategory: 0,
            tag: 0
        });

        if (partnerList)
            return res.status(200).json({ status: true, message: `Successfully retrived ${type} partners list!`, data: partnerList })
        else
            return res.status(200).json({ status: false, message: `No data Found!`, data: partnerList })
    } catch (e) {
        return res.status(200).json({ status: false, message: "Something went wrong!", error: e })
    }
}

// add partners in featured partner list
exports.addFeaturedOrFreshDealPartners = async (req, res) => {
    try {
        var partnersIds = req.body.partnersIds;
        const type = req.body.type
        const field = (type !== undefined && type !== null && type === "freshdeal" ? "freshDealPartner" : "featuredPartner")
        const maxCount = (type === "freshdeal" ? 5 : 10)
        if (partnersIds) {

            if ((partnersIds.length > maxCount)) {
                return res.status(200).json({ status: false, message: `${type === "freshdeal" ? "Freshdeal" : "Featured"} partners can't be more then ${maxCount}!` });
            } else {
                partnersIds = partnersIds.map((ids) => {
                    return ObjectId(ids);
                })
                const checkValidIds = await Partner.find({ isDelete: false, [`${field}`]: true, isMDSPartner: true, status: "published", relation_id: ObjectId(req.relation_id), }, { _id: 1, partnerType: 0, category: 0, subcategory: 0, });


                await Partner.updateMany({ _id: { $in: checkValidIds } }, { [`${field}`]: false });
                if (type === "freshdeal") {
                    let resOrder = partnersIds.map(async (item, i) => {
                        await Partner.findOneAndUpdate({ _id: item }, { freshDealPartnerOrder: i + 1, [`${field}`]: true }, { new: true });
                    });
                    await Promise.all([...resOrder]);
                } else {
                    let resOrder = partnersIds.map(async (item, i) => {
                        await Partner.findOneAndUpdate({ _id: item }, { featuredPartnerOrder: i + 1, [`${field}`]: true }, { new: true });
                    });
                    await Promise.all([...resOrder]);
                }

                const addFeaturedPartner = await Partner.find({ isDelete: false, [`${field}`]: true, isMDSPartner: true, status: "published",relation_id: ObjectId(req.relation_id), });

                if (addFeaturedPartner) {
                    return res.status(200).json({ status: true, message: `${type === "freshdeal" ? "Freshdeal" : "Featured"} partners added successfully!`, data: addFeaturedPartner });
                } else {
                    return res.status(200).json({ status: false, message: `Something went wrong while adding ${type} partner!` });
                }

            }
        } else {
            return res.status(200).json({ status: false, message: "Please add partner ids!" })
        }
    } catch (e) {
        return res.status(200).json({ status: false, message: "Something went wrong!", error: e })
    }
}

// remove featured/freshdeal partner
exports.removeFeaturedOrFreshDealPartner = async (req, res) => {
    try {
        const type = req.query.type
        const field = (type !== undefined && type !== null && type === "freshdeal" ? "freshDealPartner" : "featuredPartner")
        const updatePartner = await Partner.findByIdAndUpdate(req.params.partnerId, { [`${field}`]: false }).select("_id");
        if (updatePartner)
            return res.status(200).json({ status: true, message: `Removed ${type} partner successfully!`, data: updatePartner })
        else
            return res.status(200).json({ status: false, message: `Something went wrong while removing ${type} partner!`, data: updatePartner })
    } catch (e) {
        return res.status(200).json({ status: false, message: "Something went wrong!", error: e })
    }
}

// Reorder featured partner
exports.reorderFeaturedOrFreshDealPartner = async (req, res) => {
    try {
        const ids = req.body.ids
        const type = req.body.type
        const field = (type !== undefined && type !== null && type === "freshdeal" ? "freshDealPartnerOrder" : "featuredPartnerOrder")

        const objectPartnerIds = [];

        if (ids.length > 0) {
            let resOrder = ids.map(async (item, i) => {
                await Partner.findByIdAndUpdate(ObjectId(item), { [`${field}`]: i + 1 }, { new: true }).select("_id")
                objectPartnerIds.push(ObjectId(item))
            });
            await Promise.all([...resOrder]);
        }
        const reorderedFeaturedPartner = await Partner.find({ isDelete: false, _id: { $in: objectPartnerIds } }).sort({ [`${field}`]: 1 }).select({
            companyName: 1,
            companyLogo: 1,
            darkCompanyLogo: 1,
            freshDealPartner: 1,
            featuredPartner: 1,
            category: 0,
            subcategory: 0
        });
        if (reorderedFeaturedPartner.length > 0)
            return res.status(200).json({ status: true, message: "Reordered featured partners retrieved!", data: reorderedFeaturedPartner });
        else
            return res.status(200).json({ status: false, message: "Partners not found!" });
    } catch (error) {
        return res.status(200).json({ status: false, message: `${error.message}` });
    }
};

// category listing
exports.getCategoryListForPartner = async (req, res) => {
    try {

        const categoryList = await ContentCategory.aggregate([
            {
                $match: {
                    isDelete: false,
                    relation_id: ObjectId(req.relation_id),
                }
            },
            {
                $lookup: {
                    from: "partners",
                    localField: "_id",
                    foreignField: "category",
                    pipeline: [
                        {
                            $match: {
                                isDelete: false,
                            },
                        },
                        {
                            $project: {
                                _id: 1,
                                partnerCount: { $sum: 1 },
                                pageView: 1,
                                claims: 1,
                                rating: 1,
                            }
                        }
                    ],
                    as: "partner",
                },
            },
            {
                $project: {
                    _id: 1,
                    name: 1,
                    categoryImage: 1,
                    partnerCount: { $cond: [{ $eq: ["$partner", []] }, 0, { $sum: "$partner.partnerCount" }] },
                    TotalPageViews: { $cond: [{ $eq: ["$partner", []] }, 0, { $sum: "$partner.pageView" }] },
                    TotalClaims: { $cond: [{ $eq: ["$partner", []] }, 0, { $sum: "$partner.claims" }] },
                    TotalRating: { $cond: [{ $eq: ["$partner", []] }, 0, { $sum: "$partner.rating" }] }
                }
            }
        ]);

        if (categoryList)
            return res.status(200).json({ status: true, message: "Successfully retrived category list", data: categoryList })
        else
            return res.status(200).json({ status: false, message: "Something went wrong while retriving category list", data: categoryList })
    } catch (e) {
        return res.status(200).json({ status: false, message: "Something went wrong!", error: e })
    }
}

// partner listing by category id
exports.getCategoryWisePartnerList = async (req, res) => {
    try {

        const partnerList = await Partner.aggregate([
            {
                $match: {
                    isDelete: false,
                    category: ObjectId(req.params.categoryId),
                    relation_id: ObjectId(req.relation_id),
                }
            }
        ]);

        if (partnerList)
            return res.status(200).json({ status: true, message: "Successfully retrived partners list", data: partnerList })
        else
            return res.status(200).json({ status: false, message: "Something went wrong while retriving partners list", data: partnerList })
    } catch (e) {
        return res.status(200).json({ status: false, message: "Something went wrong!", error: e })
    }
}

// update status of partner
exports.updateStatusPartner = async (req, res) => {
    try {
        if (req.params.id !== undefined && req.params.id !== null && req.params.id !== "" && req.query.status && req.query.status !== undefined) {
            const partnerExist = await Partner.findById(req.params.id);
            if (!partnerExist)
                return res.status(200).json({ status: false, message: "Partner details not found!" });

            const updatePartnerStatus = await Partner.findByIdAndUpdate(req.params.id, { status: req.query.status }, { new: true }).select("_id companyName status");
            if (updatePartnerStatus)
                return res.status(200).json({ status: true, message: `Partner status updated successfully.`, data: updatePartnerStatus });
            else
                return res.status(200).json({ status: false, message: `Something went wrong while updating status of partner!`, });
        }
    } catch (error) {
        return res.status(200).json({ status: false, message: `${error.message}` });
    }
};

// End of Admin apis

// Start of User-frontend side apis

// partner  detail api for user
exports.getPartnerByIdForUser = async (req, res) => {
    try {

        const partnerDetail = await Partner.aggregate([
            {
                $match: {
                    _id: ObjectId(req.params.id),
                    isDelete: false,
                    status: "published",
                    relation_id: ObjectId(req.relation_id),  
                }
            },
            {
                $lookup: {
                    from: "partnerhelpfullinks",
                    localField: "_id",
                    foreignField: "partnerId",
                    as: "helpfulLinks",
                },
            },
            {
                $lookup: {
                    from: "partnerposts",
                    localField: "_id",
                    foreignField: "partnerId",
                    as: "partnerPosts",
                },
            },
            {
                $lookup: {
                    from: "partnerreviews",
                    localField: "_id",
                    foreignField: "partnerId",
                    as: "partnerReviews",
                },
            },
            {
                $lookup: {
                    from: "contentarchive_categories",
                    localField: "_id",
                    foreignField: "category",
                    as: "partnerCategory",
                },
            },
            {
                $lookup: {
                    from: "contentarchive_videos",
                    localField: "_id",
                    foreignField: "category",
                    as: "partnerCategory",
                },
            },
            {
                $project: {
                    description: 1,
                    webBanner: 1,
                    thumbnail: 1,
                    mobileBanner: 1,
                    category: 1,
                    urlToAllPosts: 1,
                    contactInfo: 1,
                    helpfulLinks: 1,
                    partnerPosts: 1,
                    partnerReviews: 1,
                    partnerCategory: 1,
                    shortDescription: 1,
                }
            }
        ]);

        if (partnerDetail)
            return res.status(200).json({ status: true, message: `Partner  detail`, partnerDetail: partnerDetail });
        else
            return res.status(200).json({ status: false, message: `No data found for this partner id!` });
    } catch (error) {
        return res.status(200).json({ status: false, message: `${error.message}` });
    }
};

// get featured partner list for user
exports.getFeaturedOrFreshDealPartnersListForUser = async (req, res) => {
    try {
        const field = req.query.type !== undefined && req.query.type !== null && req.query.type === "freshdeal" ? "freshDealPartner" : "featuredPartner";
        const orderField = req.query.type !== undefined && req.query.type !== null && req.query.type === "freshdeal" ? "freshDealPartnerOrder" : "featuredPartnerOrder";
        const filter = req.query.filter !== undefined && req.query.filter !== null ? req.query.filter : "offer";
        var matchDefault = {
            isDelete: false,
            status: "published",
            isMDSPartner: true,
        };

        var match = {};
        if (req.query.type) {
            match = {
                ...matchDefault,
                [`${field}`]: true,
            };
        }

        const partnerList = await Partner.aggregate([
            { $sort: { [`${orderField}`]: 1 } },
            { $match: match },
            {
                $lookup: {
                    from: 'partnerbadges',
                    localField: 'partnerType',
                    foreignField: '_id',
                    as: 'typeData'
                }
            },
            { $unwind: { path: "$typeData", preserveNullAndEmptyArrays: true } },
            {
                $project: {
                    companyName: 1,
                    companyLogo: 1,
                    darkCompanyLogo: 1,
                    featuredPartnerOrder: 1,
                    freshDealPartnerOrder: 1,
                    thumbnail: 1,
                    MDSType: 1,
                    rating: 1,
                    description: 1,
                    shortDescription: 1,
                    offerValue: 1,
                    partnerType: {
                        $cond: [
                            {
                                "$ifNull":
                                    ["$typeData", false]
                            },
                            {
                                _id: "$typeData._id",
                                name: "$typeData.name",
                                badgeColor: "$typeData.badgeColor",
                            }, null
                        ]
                    },
                },
            }
        ]);

        const partnerCount = await Partner.countDocuments({ ...matchDefault, MDSType: filter });

        if (partnerList.length > 0)
            return res.status(200).json({ status: true, message: `Successfully retrived ${req.query.type} partners list`, data: { partnerList: partnerList, partnerCount: partnerCount } });
        else
            return res.status(200).json({ status: false, message: `${req.query.type} partners list not found!`, data: partnerList });
    } catch (e) {
        return res.status(200).json({ status: false, message: "Something went wrong!", error: e })
    }
}

// category listing
exports.getFrontendCategoryListForPartner = async (req, res) => {
    try {
        const mdsType = (req.query.type !== undefined && req.query.type !== null) ? req.query.type : "offer"
        let pipeline = [
            {
              $match: {
                isDelete: false,
                relation_id: ObjectId(req.relation_id),
              },
            },
            {
              $lookup: {
                from: "partners",
                localField: "_id",
                foreignField: "category",
                pipeline: [
                    {
                        $match: {
                            isDelete: false,
                            isMDSPartner: true,
                            MDSType: mdsType,
                            status: "published"
                        },
                    },
                    {
                        $project: {
                            _id: 1,
                            partnerCount: { $sum: 1 },
                        }
                    }
                ],
                as: "partners_result",
              },
            },
            {
              $match: {
                $expr: {
                  $gt: [
                    {
                      $size: "$partners_result",
                    },
                    0,
                  ],
                },
              },
            },
            {
              $lookup: {
                from: "partner_subcategories",
                localField: "subcategory",
                foreignField: "_id",
                pipeline: [
                  {
                    $match: {
                      isDelete: false,
                    },
                  },
                  {
                    $lookup: {
                      from: "partners",
                      localField: "_id",
                      foreignField: "subcategory",
                      pipeline: [
                        {
                          $match: {
                            $expr: {
                              $and: [
                                {
                                  $eq: ["$isDelete", false],
                                },
                                {
                                  $eq: ["$status", "published"],
                                },
                              ],
                            },
                          },
                        },
                      ],
                      as: "subcategoryWisePartners",
                    },
                  },
                  {
                    $project: {
                      _id: 1,
                      name: 1,
                      subcategoryWisePartnersCount: {
                        $cond: {
                          if: {
                            $isArray: "$subcategoryWisePartners",
                          },
                          then: {
                            $size: "$subcategoryWisePartners",
                          },
                          else: 0,
                        },
                      },
                    },
                  },
                  {
                    $match: {
                      $expr: {
                        $gt: ["$subcategoryWisePartnersCount", 0],
                      },
                    },
                  },
                ],
                as: "subcategory",
              },
            },
            {
              $lookup: {
                from: "partners",
                localField: "_id",
                foreignField: "category",
                pipeline: [
                  {
                    $match: {
                      isDelete: false,
                    },
                  },
                ],
                as: "docData",
              },
            },
            {
              $project: {
                _id: 1,
                name: 1,
                isDelete: 1,
                createdAt: 1,
                subcategory: 1,
                categoryImage: 1,
                partnerCount: { $cond: [{ $eq: ["$partners_result", []] }, 0, { $sum: "$partners_result.partnerCount" }] },
                docCount: {
                  $cond: {
                    if: {
                      $isArray: "$docData",
                    },
                    then: {
                      $size: "$docData",
                    },
                    else: 0,
                  },
                },
                subCategoryCount: {
                  $cond: {
                    if: {
                      $isArray: "$subcategory",
                    },
                    then: {
                      $size: "$subcategory",
                    },
                    else: 0,
                  },
                },
              },
            },
            {
              $sort: {
                name: 1,
              },
            },
          ];

        let categoryList = await ContentCategory.aggregate(pipeline);
        if (categoryList.length > 0 ){
            return res.status(200).json({ status: true, message: "Successfully retrived category list", data: categoryList })
        }else{
            return res.status(200).json({ status: false, message: "No categories found!", })
        }

    } catch (e) {
        return res.status(200).json({ status: false, message: "Something went wrong!", error: e })
    }
}

// select partner list for user
exports.getSelectPartnerList = async (req, res) => {
    try {
        var match = {
            isDelete: false,
            isMDSPartner: true,
            MDSType: "offer",
            status: "published"
        }

        var search = "";
        if (req.query.search) {
            search = req.query.search;
            match = {
                ...match,
                companyName: { $regex: ".*" + search + ".*", $options: "i" },
            };
        }

        const partnerList = await Partner.aggregate([
            { $match: match },
            {
                $lookup: {
                    from: 'partnerreviews',
                    localField: '_id',
                    foreignField: 'partnerId',
                    as: 'partnerReviews'
                }
            },
            {
                $project: {
                    _id: 1,
                    companyName: 1,
                    thumbnail: 1,
                    isMDSPartner: 1,
                    description: 1,
                    shortDescription: 1,
                    status: 1,
                    offerValue: 1,
                    countPartnerReviews: { $cond: { if: { $isArray: "$partnerReviews" }, then: { $size: "$partnerReviews" }, else: "NA" } },
                    "partnerReviews": { _id: 1, status: 1 }
                },
            },
            { $sort: { countPartnerReviews: 1 } },
        ]);

        if (partnerList.length > 0) {
            return res.status(200).json({ status: true, message: `Partner list retrive successfully.`, data: partnerList, });
        } else {
            return res.status(200).json({ status: true, message: `Partner list not found!`, data: [], });
        }
    } catch (error) {
        return res.status(200).json({ status: false, message: `${error.message}` });
    }
};

// get Review Count By UserId
exports.getReviewCountByUserId = async (req, res) => {
    try {
        const authUser = req.authUserId;
        var match = { userId: authUser, relation_id: ObjectId(req.relation_id),}
        const UserReview = await partnerReview.find(match).select({ userId: 0, updatedAt: 0, IsNew: 0, reasonId: 0, rejectNotes: 0, __v: 0 })
        if (UserReview.length < 3) {
            return res.status(200).json({ status: true, message: `User Review List.`, data: { countStatus: true, reviewCount: UserReview.length, UserReview: UserReview } });
        } else {
            return res.status(200).json({ status: true, message: `No Need to give more Review`, data: { countStatus: false }, });
        }
    } catch (error) {
        return res.status(200).json({ status: false, message: `${error.message}` });
    }
};

// get All Partner Suggestion List
exports.getAllPartnerSuggestionList = async (req, res) => {
    try {

        var match = { isDelete: false, relation_id: ObjectId(req.relation_id), }

        const partnerList = await Partner.find(match, { _id: 0, companyName: 1, partnerType: 0, category: 0, subcategory: 0, tag: 0 });
        if (partnerList.length > 0) {
            return res.status(200).json({ status: true, message: `Partner list retrive successfully.`, data: partnerList, });
        } else {
            return res.status(200).json({ status: true, message: `Partner list not found!`, data: [], });
        }
    } catch (error) {
        return res.status(200).json({ status: false, message: `${error.message}` });
    }
};

exports.averageStar = async (req, res) => {
    try {
        const relation_id = new ObjectId(req.relation_id);       
        const partners = await Partner.find({ isDelete: false, relation_id: relation_id });
        let updatedPartners = partners.map(async (partner) => {           
            const avgRatings = await partnerReview.aggregate([
                {
                    $match: {
                        partnerId: mongoose.Types.ObjectId(partner._id),
                        relation_id: relation_id,
                        isDelete: false,
                        status: "approved"
                    }
                },
                {
                    $group: {
                        _id: 0,
                        rating: { $avg: { $toInt: "$star" } }
                    }
                }
            ]);
            let newRating;
            if (avgRatings.length > 0) {
                newRating = parseFloat(avgRatings[0].rating).toFixed(1);
            }
            await Partner.findByIdAndUpdate(
                partner._id ,
                { $set: { rating: newRating } },
                { new: true }
            )
            return partner
        })
        await Promise.all([...updatedPartners]);
        return res.status(200).send({ status: true, message: "Partner Rating Update succefully!" });   
    } catch (error) {
        return res.status(500).json({ status: false, message: "Internal server error!", error: error })
    }
}
  