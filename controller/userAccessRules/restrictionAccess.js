const { ObjectId } = require("mongodb");
const User = require("../../database/models/airTableSync");
const eventParticipantAttendees = require("../../database/models/eventParticipantAttendees");
const group = require("../../database/models/group");
const membershipPlan = require("../../database/models/membershipPlanManagement/membership_plan");
const contentArchive_tag = require("../../database/models/contentArchive_tag");
const event = require("../../database/models/event");
const { user_edges } = require("../../microservices/user/components/user-edges/database/models");
const { getAllEventIdOfMember } = require("../../controller/userAccessRules/event");
const { getAllTiersfromBilling, getSubscriptionById, getTiersById} = require("./tiers");
const { toFloat } = require("validator");
const inviteCollaborators = require("../../microservices/user/components/users/database/models/invite-collaborators");

exports.checkValidIds = async (obj) => {
  try {
    // check groupId valid or not
    if (obj.restrictedAccessGroupId) {
      let count = await group.count({
        _id: { $in: obj.restrictedAccessGroupId },
        isDelete: false,
      });
      if (count !== obj.restrictedAccessGroupId.length)
        return { status: false, msg: `Invalid group ids!` };
    }

    // check MembershipPlanId valid or not
    if (obj.restrictedAccessMembershipPlanId) {
      let count = await membershipPlan.count({
        _id: { $in: obj.restrictedAccessMembershipPlanId },
        isDelete: false,
      });
      if (count !== obj.restrictedAccessMembershipPlanId.length)
        return { status: false, msg: `Invalid membership ids!` };
    }

    // check userId valid or not
    if (obj.restrictedAccessUserId) {
      let count = await User.count({
        _id: { $in: obj.restrictedAccessUserId },
        isDelete: false,
      });
      if (count !== obj.restrictedAccessUserId.length)
        return { status: false, msg: `Invalid user ids!` };
    }

    // check tagId valid or not
    if (obj.restrictedAccessTagId) {
      let count = await contentArchive_tag.count({
        _id: { $in: obj.restrictedAccessTagId },
        isDelete: false,
      });
      if (count !== obj.restrictedAccessTagId.length)
        return { status: false, msg: `Invalid tag ids!` };
    }

     // check tiersId valid or not
     if (obj.restrictedAccessTierId) {

      let tiers = await getAllTiersfromBilling( obj, expand = true)
      const validTiers = tiers.filter(tier => 
        obj.restrictedAccessTierId.includes(tier._id) && !tier.isDelete == true
      );
      const count = validTiers.length;
      if (count !== obj.restrictedAccessTierId.length) {
        return { status: false, msg: `Invalid Tier ids!` };
      }
    }

    // check eventId valid or not
    if (obj.restrictedAccessEventId) {
      let count = await event.count({
        _id: { $in: obj.restrictedAccessEventId },
        isDelete: false,
      });
      if (count !== obj.restrictedAccessEventId.length)
        return { status: false, msg: `Invalid event ids!` };
    } 
    return true;
  } catch (err) {
    console.log(err, "error in valid id's for ruling system added");
    return false;
  }
};

// user Access Rules Common Condition
exports.userAccessRulesCommonCondition = async (obj) => {
  try {
    if (obj && obj.userId && ObjectId.isValid(obj.userId) && obj.relation_id && ObjectId.isValid(obj.relation_id)) {
      let userId = new ObjectId(obj.userId);
      let relation_id = new ObjectId(obj.relation_id);
      let purchasedPlan = [];
      let accessibleGroups = [];
      let tagIds = [];
      let eventsIds = [];
      let tiersIds = [];
      let restrictedAccessUserIdTemp = {
        restrictionAccess: "restricted",
        restrictedAccessUserId: userId,
      };

      let userdata = await User.findById(userId)
        .select({
          purchased_plan: 1,
          accessible_groups: 1,
          tagId: 1,
          "Preferred Email": 1,
        })
        .lean();

      let userEdgesData = await user_edges
        .findOne({ user_id: userId, relation_id: relation_id, isDelete: false })
        .select("tagId accessible_groups subscription_id type");

      let subscription_id = userEdgesData.subscription_id
        ? ObjectId(userEdgesData.subscription_id)
        : "";
      let eventsIdData = await getAllEventIdOfMember({
        userId: userId,
        relationId: relation_id,
      });

      if (userdata && userEdgesData) {
        if (
          userdata.purchased_plan &&
          ObjectId.isValid(userdata.purchased_plan)
        ) {
          purchasedPlan = [userdata.purchased_plan];
        }
        if (userEdgesData.accessible_groups && userEdgesData.accessible_groups.length) {
          accessibleGroups = userEdgesData.accessible_groups;
        }
        if (userEdgesData.tagId && userEdgesData.tagId.length) {
          tagIds = userEdgesData.tagId;
        }
        if (eventsIdData && eventsIdData.length) {
          eventsIds = eventsIdData;
        }

        if (userEdgesData.type === "CU") {
          let getInviteCollaboratorUser = await inviteCollaborators
            .findOne({
              email: userdata["Preferred Email"],
              relation_id,
              status: { $ne: "REVOKED" },
              isDelete: false,
            })
            .select({ invitee_id: 1, _id: 0 })
            .lean();

          let collaboratorUserData = await User.findById(
            getInviteCollaboratorUser?.invitee_id
          )
            .select({
              purchased_plan: 1,
              accessible_groups: 1,
              tagId: 1,
              "Preferred Email": 1,
            })
            .lean();

          let collaboratorUserEdgesData = await user_edges
            .findOne({
              user_id: collaboratorUserData._id,
              relation_id: relation_id,
              isDelete: false,
            })
            .select("tagId accessible_groups subscription_id type");

            if(collaboratorUserEdgesData?.subscription_id) {
              subscription_id = collaboratorUserEdgesData?.subscription_id
            }

          let collaboratorUserEventsIdData = await getAllEventIdOfMember({
            userId: collaboratorUserData._id,
            relationId: relation_id,
          });

          restrictedAccessUserIdTemp = {
            restrictionAccess: "restricted",
            restrictedAccessUserId: {
              $in: [ObjectId(collaboratorUserData._id), userId],
            },
          };

          if (
            collaboratorUserData &&
            collaboratorUserData.purchased_plan &&
            ObjectId.isValid(collaboratorUserData.purchased_plan)
          ) {
            purchasedPlan = Array.from(
              new Set([
                ...purchasedPlan,
                ...collaboratorUserData.purchased_plan,
              ])
            );
          }

          if (
            collaboratorUserEdgesData &&
            collaboratorUserEdgesData.accessible_groups &&
            collaboratorUserEdgesData.accessible_groups.length
          ) {
            accessibleGroups = Array.from(
              new Set([
                ...accessibleGroups,
                ...collaboratorUserEdgesData.accessible_groups,
              ])
            );
          }
          if (
            collaboratorUserEdgesData &&
            collaboratorUserEdgesData.tagId &&
            collaboratorUserEdgesData.tagId.length
          ) {
            tagIds = Array.from(
              new Set([...tagIds, ...collaboratorUserEdgesData.tagId])
            );
          }

          if (
            collaboratorUserEventsIdData &&
            collaboratorUserEventsIdData.length
          ) {
            eventsIds = Array.from(
              new Set([...eventsIds, ...collaboratorUserEventsIdData])
            );
          }
        }

        if (subscription_id) {
          let getSubscriptionData = await getTiersById(subscription_id);
          if (getSubscriptionData && ObjectId.isValid(getSubscriptionData._id)) {
            tiersIds.push(ObjectId(getSubscriptionData._id));
          }
        }

        let condition = {
          $or: [
            ...(userEdgesData && userEdgesData?.type == "GU"
              ? [{ isPublic: true }]
              : [{ restrictionAccess: "public" }]),
            // { restrictionAccess: "public" },
            { restrictionAccess: "admin/staff" },
            // { restrictionAccess: "restricted", restrictedAccessUserId: userId },
            restrictedAccessUserIdTemp,
            {
              restrictionAccess: "restricted",
              restrictedAccessGroupId: { $in: accessibleGroups },
            },
            {
              restrictionAccess: "restricted",
              restrictedAccessMembershipPlanId: { $in: purchasedPlan },
            },
            {
              restrictionAccess: "restricted",
              restrictedAccessTagId: { $in: tagIds },
            },
            {
              restrictionAccess: "restricted",
              restrictedAccessEventId: { $in: eventsIds },
            },
            {
              restrictionAccess: "restricted",
              restrictedAccessTierId: { $in: tiersIds },
            },
          ],
        };
        return condition;
      } else {
        return {};
      }
    } else {
      return {};
    }
  } catch (err) {
    console.log(err, "error in commom condition for access rules");
    return {};
  }
};

exports.getUserCount = async (req, res) => {
  try {
    let body = req.body;
    let match, uniqueUserIds, uniqueEventUserIds;

    //match
    match = {
      isDelete: false,
      //firebaseId: { $nin: ["", null] },
      $or: [{ blocked: false }, { blocked: { $exists: false } }],
    };

    let matchTemp = {
      relation_id: ObjectId(req.relation_id),
      isDelete: false,
      type: "M",
      owner: false,
      subscription_id: {
        $ne: null,  
        $regex: /^[a-fA-F0-9]{24}$/
      }
    };

    if (body.restrictionAccess === "restricted") {
      //get event Attendee users
      let matchAttendees;
      if (body.restrictedAccessEventId) {
        const eventIds = body.restrictedAccessEventId.map(
          (id) => new ObjectId(id)
        );

        matchAttendees = {
          event: { $in: eventIds },
          isDelete: false,
          relation_id: ObjectId(req.relation_id),
        };

        let list = await eventParticipantAttendees.aggregate([
          {
            $match: matchAttendees,
          },
          {
            $lookup: {
              from: "event_wise_participant_types",
              localField: "role",
              foreignField: "_id",
              pipeline: [
                {
                  $match: {
                    isDelete: false,
                  },
                },
              ],
              as: "roleData",
            },
          },
          {
            $match: { "roleData.role": "Member" },
          },
        ]);

        const eventUserIds = list.map((item) => item.user);
        const uniqueUserIds = [...new Set(eventUserIds)];
        uniqueEventUserIds = Array.from(new Set(uniqueUserIds));
      }
      matchTemp["$or"] = [
        { tagId: { $in: body.restrictedAccessTagId } },
        { accessible_groups: { $in: body.restrictedAccessGroupId } },
      ]
    }


    let getUserData = await user_edges.aggregate([
      {
        '$match': matchTemp
      }, {
        '$lookup': {
          'from': 'airtable-syncs', 
          'localField': 'user_id', 
          'foreignField': '_id', 
          'pipeline': [
            {
              '$match': {
                'isDelete': false
              }
            }
          ], 
          'as': 'user'
        }
      }, {
        '$unwind': {
          'path': '$user', 
          'preserveNullAndEmptyArrays': false
        }
      },
      {
        '$project': {
          'user_id': 1
        }
      }])
      //get tag and accessible_groups users
      // let getUserData = await user_edges
      //   .find(matchTemp)        
      //   .select("user_id");

    let getUserIds = getUserData.map((item) => item.user_id);
    let getUniqueUserIds = [...new Set(getUserIds)];

    if (body.restrictedAccessTierId && body.restrictedAccessTierId.length) {
      let getSubscriptionData = await getSubscriptionById(
        body.restrictedAccessTierId
      );
      let subscriptionUserIds = getSubscriptionData.map(
        (subscription) => subscription.user_id
      );
      getUniqueUserIds = [
        ...new Set(subscriptionUserIds),
        ...new Set(getUserIds),
      ];
    }
      uniqueUserIds = Array.from(new Set(getUniqueUserIds));

      const uniqueAllUserIds = [
        ...new Set([
          ...(body.restrictedAccessUserId || []),
          ...(uniqueUserIds || []),
          ...(uniqueEventUserIds || []),
        ]),
      ];

      match = { ...match, _id: { $in: uniqueAllUserIds } };
    const userData = await User.find(match, {
      _id: 1,
      first_name: { $ifNull: ["$first_name", ""] },
      last_name: { $ifNull: ["$last_name", ""] },
      display_name: { $ifNull: ["$display_name", ""] },
      "Preferred Email": 1,
      display_name:1,
    })

    if (userData.length !== 0) {
      return res.status(200).json({
        status: true,
        message: "Get notification list successfully!",
        data: userData,
      });
    } else {
      return res.status(200).json({
        status: false,
        message: "Data not found!",
      });
    }
  } catch (error) {
    return res.status(500).json({ status: false, message: error.message });
  }
};

exports.eventDateAndTimeCommonCondition = async () => {
  try {
    let condition = {
      $addFields: {
        startDate: {
          $cond: [
            {
              $or: [
                { $in: ["$startDate", ["", "Invalid date"]] },
                { $eq: ["$startDate", null] }
              ]
            },
            "01-01-1970",
            "$startDate"
          ]
        },
        startTime: {
          $cond: [
            {
              $or: [
                { $in: ["$startTime", ["", "Invalid date"]] },
                { $eq: ["$startTime", null] }
              ]
            },
            "00:01 am",
            "$startTime"
          ]
        },
        endDate: {
          $cond: [
            {
              $or: [
                { $in: ["$endDate", ["", "Invalid date"]] },
                { $eq: ["$endDate", null] }
              ]
            },
            {
              $dateToString: {
                format: "%m-%d-%Y",
                date: { $toDate : new Date() },
              }
            },
            "$endDate"
          ]
        },
        endTime: {
          $cond: [
            {
              $or: [
                { $in: ["$endTime", ["", "Invalid date"]] },
                { $eq: ["$endTime", null] }
              ]
            },
            "11:59 pm",
            "$endTime"
          ]
        }
      } 
    };
    return condition;
  } catch (err) {
    return {};
  }
};

exports.dateValidation = async () => {
  try {
    let condition = [
      {
        $addFields: {
          startDate: {
            $cond: [
              {
                $or: [
                  { $in: ["$startDate", ["", "Invalid date"]] },
                  { $eq: ["$startDate", null] }
                ]
              },
              "01-01-1970",
              "$startDate"
            ]
          },
          startTime: {
            $cond: [
              {
                $or: [
                  { $in: ["$startTime", ["", "Invalid date"]] },
                  { $eq: ["$startTime", null] }
                ]
              },
              "00:01 am",
              "$startTime"
            ]
          },
          endDate: {
            $cond: [
              {
                $or: [
                  { $in: ["$endDate", ["", "Invalid date"]] },
                  { $eq: ["$endDate", null] }
                ]
              },
              {
                $dateToString: {
                  format: "%m-%d-%Y",
                  date: { $toDate : new Date() },
                }
              },
              "$endDate"
            ]
          },
          endTime: {
            $cond: [
              {
                $or: [
                  { $in: ["$endTime", ["", "Invalid date"]] },
                  { $eq: ["$endTime", null] }
                ]
              },
              "11:59 pm",
              "$endTime"
            ]
          }
        } 
      },      
      {
      $match: {
        $expr: {
          $and: [
            {
              $regexMatch: {
                input: "$startDate",
                regex:
                  "^(0[1-9]|1[0-2])-(0[1-9]|[12][0-9]|3[01])-(\\d{4})$"
              }
            },
            {
              $regexMatch: {
                input: "$startTime",
                regex:
                  "^(0?[1-9]|1[0-2]):[0-5][0-9] (am|pm)$",
                options: "i"
              }
            },
            {
              $regexMatch: {
                input: "$endDate",
                regex:
                  "^(0[1-9]|1[0-2])-(0[1-9]|[12][0-9]|3[01])-(\\d{4})$"
              }
            },
            {
              $regexMatch: {
                input: "$endTime",
                regex:
                  "^(0?[1-9]|1[0-2]):[0-5][0-9] (am|pm)$",
                options: "i"
              }
            }
          ]
        }
      }
    }];
    return condition;
  } catch (err) {
    return [];
  }
};