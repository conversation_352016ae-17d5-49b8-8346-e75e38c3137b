const { ObjectId } = require("mongodb");
const AppleAuth = require("apple-auth");
const jwt = require("jsonwebtoken");
const airtable_sync = require("../../database/models/airTableSync");
const User = require("../../database/models/merged-users");
const UserEdge = require("../../microservices/user/components/user-edges/database/models/user-edges");

const chatUser = require("../../database/models/chatUser");
const contactUsUser = require("../../database/models/contactUsUser");
const AuthUserEmail = require("../../database/models/authUserEmail");
const ContentArchive_tag = require("../../database/models/contentArchive_tag");
const { JWT_SECRET, GATEWAY_DOMAIN, APPLE_PEMCERT, JWT_PEMCERT } = require("../../config/config");
const csv = require("csv-parser");
const { Readable } = require("stream");
//by ZP ********************************
const GoogleJSON = require("../../pc-api-8893606168182108109-208-586050a90383.json");
const MembershipPlan = require("../../database/models/membershipPlanManagement/membership_plan");
const Payment = require("../../database/models/payment");
const PlanResource = require("../../database/models/plan_resource");
const CustomRegistrationForm = require("../../database/models/customregistrationform");
const GroupMember = require("../../database/models/groupMember");
const Group = require("../../database/models/group");
const Post = require("../../database/models/post");
const Questionnaire = require("../../database/models/questionnaire");
const QuestionAnswer_byUser = require("../../database/models/questionanswerbyuser");
const {
  userRoles,
} = require("../../microservices/user/components/roles/database/models/user-roles");
const ContentEvent = require("../../database/models/contentArchive_event");
const { AdminUser } = require("../../database/models/adminuser");
const https = require("https");
const { manageUserLog } = require("../../middleware/userActivity");
const { sendEmail, sendEmailAdmin } = require("../../config/common");
const event = require("../../database/models/event");
const userSocialAccount = require("../../database/models/userSocialAccount");
const userChatGroup = require("../../database/models/userChatGroup");
const eventParticipantAttendees = require("../../database/models/eventParticipantAttendees");
const productDataMap = require("../../productDataMap");
const excelJS = require("exceljs");
const groupBy = require("lodash");
var request = require("superagent");
var FormData = require("form-data");
var fs = require("fs");
const http = require("http");
const moment = require("moment");
// const iap = require('in-app-purchase');
const appleReceiptVerify = require("node-apple-receipt-verify");
const APP_STORE_INAPP_SECRET = process.env.APP_STORE_INAPP_SECRET;
const crypto = require("crypto");
const forge = require("node-forge");
const { PubSub } = require("@google-cloud/pubsub");
const {
  send_notification,
  notification_template,
} = require("../../utils/notification");
const { checkIfMsgReadSocket } = require("../chatcontroller");
require("dotenv").config();
const AWS = require("aws-sdk");
const config = require("config");
const stripe_sk = config.get("stripe");
const stripe = require("stripe")(stripe_sk.secret_key);
const appletoken = config.get("appletoken");
require("moment-timezone");
const inviteCollaborator = require("../../database/models/collaborator/inviteCollaborator");
const importCsvUser = require("../../database/models/users/importCsvUser")
var s3 = new AWS.S3({
  accessKeyId: process.env.AWS_ID,
  secretAccessKey: process.env.AWS_SECRET,
  Bucket: process.env.AWS_BUCKET,
});

const path = require("path");
const plan_resource = require("../../database/models/plan_resource");
const payment = require("../../database/models/payment");
const membership_plan = require("../../database/models/membershipPlanManagement/membership_plan");
const auth0 = config.get("auth0");
var axios = require("axios").default;
var OAUTH_TOKEN_API = auth0.oauth_token;
var CLIENT_ID = auth0.client_id;
var CLIENT_SECRET = auth0.client_secret;
var AUDIENCE = auth0.audience;
const { google } = require("googleapis");
const { OAuth2Client } = require("google-auth-library");
const playDeveloper = google.androidpublisher("v3");
const GoogleKey = require("../../pc-api-8893606168182108109-208-586050a90383.json");
const { user_edges } = require("../../microservices/user/components/user-edges/database/models");
const { default: mongoose } = require("mongoose");
// const importUser = require("../../database/models/users/importUserData");

const GOOGLE_CLIENT_ID = GoogleKey.client_id;
const GOOGLE_CLIENT_SECRET = GoogleKey.private_key;
const oauth2Client = new OAuth2Client(GOOGLE_CLIENT_ID, GOOGLE_CLIENT_SECRET);
const nodeApiDocumentsApiUrl = process.env.NODE_API_DOCUMENTS_API_URL;

const playDeveloperAPI = google.androidpublisher({
  version: "v3",
  auth: oauth2Client,
});


let auth = new AppleAuth(appletoken, APPLE_PEMCERT, "text");


async function getAuth0Token() {
  return new Promise(async (resolve, reject) => {
    try {
      var options = {
        method: "POST",
        url: OAUTH_TOKEN_API,
        headers: { "content-type": "application/json" },
        data: {
          grant_type: "client_credentials",
          client_id: CLIENT_ID,
          client_secret: CLIENT_SECRET,
          audience: AUDIENCE,
        },
      };

      axios
        .request(options)
        .then(function (response) {
          var token = "Bearer " + response.data.access_token;
          resolve(token);
        })
        .catch(function (error) {
          reject(`Something wrong. ${error}`);
        });
    } catch (error) {
      reject(`Something wrong. ${error}`);
    }
  });
}

exports.checkUserbyEmail = async (req, res) => {
  try {
    const { email } = req.body;
    const user_data = await airtable_sync.findOne({
      email: email.toLowerCase(),
    });
    if (!user_data)
      return res
        .status(200)
        .json({ status: false, message: "User doesn't exist, please signup!" });

    return res
      .status(200)
      .json({ status: true, message: "User data.", data: user_data });
  } catch (error) {
    return res.status(200).json({ status: false, message: error.message });
  }
};

exports.checkUserbySocialId = async (req, res) => {
  try {
    const { facebookLinkedinId, socialEmail, mdsEmail, mdsEmailExists } =
      req.body;
    var user = await airtable_sync
      .findOne({
        $or: [
          { facebookLinkedinId: facebookLinkedinId },
          { firebaseId: facebookLinkedinId },
        ],
      })
      .lean();

    if (user) {
      const InviteCollaborator = await inviteCollaborator
        .findOne(
          {
            email: user["Preferred Email"],
            isDelete: false,
          },
          {
            _id: 1,
            email: 1,
            firstName: 1,
            lastName: 1,
            name: 1,
            memberShipPlanDetails: 1,
            sharedUserDetails: 1,
            teamMateInvitationStatus: 1,
            collaboratorPlan: 1,
            isOTPVerified: 1,
          }
        )
        .lean();

      if (InviteCollaborator && InviteCollaborator.collaboratorPlan === false) {
        return res.status(201).json({
          status: false,
          message: "Your collaborator access is revoked.",
          data: [],
        });
      } else {
        if (user.isCollaborator !== undefined && user.isCollaborator === true) {
          if (
            InviteCollaborator.sharedUserDetails !== undefined &&
            InviteCollaborator.sharedUserDetails !== null
          ) {
            const memberUserData = await airtable_sync.findOne(
              {
                _id: InviteCollaborator.sharedUserDetails.userId,
              },
              { _id: 1, "# of Days Since MDS Only Census": 1 }
            );

            const planDetails = await MembershipPlan.findOne({
              _id: InviteCollaborator.memberShipPlanDetails.planId,
            }).populate("accessResources");

            user = {
              ...user,
              "# of Days Since MDS Only Census": memberUserData[
                "# of Days Since MDS Only Census"
              ]
                ? memberUserData["# of Days Since MDS Only Census"]
                : 0,
            };
            user = {
              ...user,
              accessResources: planDetails.accessResources
                ? planDetails.accessResources
                : [],
            };
          }
        } else {
          user.isCollaborator = false;
          user = { ...user, accessResources: [] };
        }
        return res
          .status(200)
          .json({ status: true, message: "User data.", data: user });
      }
    } else {
      if (!mdsEmailExists) {
        if (socialEmail) {
          var socialUser = await airtable_sync
            .findOne({ "Preferred Email": socialEmail })
            .lean();
          let socialUserDBExists = false;
          socialUserDBExists =
            socialUser !== undefined && socialUser !== null ? true : false;
          if (socialUserDBExists) {
            const verificaitonURL = socialUser["Verification URL"]; 
            const helloText = `Hello,`;
            if (verificaitonURL && verificaitonURL !== "") {
              const mailData = getUserMigrationEmailContent(
                helloText,
                socialEmail,
                verificaitonURL
              );
              await sendEmail(mailData);
            }

            return res
              .status(200)
              .json({
                status: false,
                socialEmailDBExists: true,
                message: "Social user exists!",
                data: socialUser,
              });
          } else {
            return res
              .status(200)
              .json({
                status: false,
                socialEmailDBExists: false,
                message: "Social user not exist!",
              });
          }
        } else {
          return res
            .status(200)
            .json({
              status: false,
              message: "Input parameter social email is missing!",
            });
        }
      } else {
        if (mdsEmail !== undefined && mdsEmail !== null && mdsEmail !== "") {
          var mdsUser = await airtable_sync
            .findOne({ "Preferred Email": mdsEmail })
            .lean();
          let mdsEmailDBExists = false;
          mdsEmailDBExists =
            mdsUser !== undefined && mdsUser !== null ? true : false;
          if (mdsEmailDBExists) {
            const verificaitonURL = mdsUser["Verification URL"];
            const helloText = `Hello,`;
            if (verificaitonURL && verificaitonURL !== "") {
              const mailData = getUserMigrationEmailContent(
                helloText,
                mdsEmail,
                verificaitonURL
              );
              await sendEmail(mailData);
            }
            return res
              .status(200)
              .json({
                status: false,
                socialEmailDBExists: false,
                mdsEmailDBExists: true,
                message: "Mds email exists!",
                data: mdsUser,
              });
          } else {
            return res
              .status(200)
              .json({
                status: false,
                socialEmailDBExists: false,
                mdsEmailDBExists: false,
                message: "Mds email not found in db!",
              });
          }
        } else {
          return res
            .status(200)
            .json({
              status: false,
              message: "Input parameter mds email is missing!",
            });
        }
      }
    }
  } catch (error) {
    return res.status(200).json({ status: false, message: error.message });
  }
};

exports.userAddAuth0Step = async (req, res) => {
  try {
    const { email, secondary_email, provider, firebaseId, isSocial, webOrApp } =
      req.body;

    const user_data = await airtable_sync.findOne(
      { firebaseId: firebaseId, isDelete: false },
      {
        migrate_user: 1,
        email: 1,
        firebaseId: 1,
        profileImg: 1,
        active: 1,
        blocked: 1,
        verified: 1,
        star_chat: 1,
        blocked_chat: 1,
        blocked_by_who_chat: 1,
        accessible_groups: 1,
        isSocial: 1,
        last_login: 1,
        last_activity_log: 1,
        isDelete: 1,
        register_status: 1,
        payment_status: 1,
        QA_status: 1,
        personalDetail_status: 1,
        migrate_user_status: 1,
        purchased_plan: 1,
        forgot_ticket: 1,
        payment_id: 1,
        idtoken: 1,
        user_role: 1,
        "Preferred Email": 1,
        first_name: 1,
        last_name: 1,
        display_name: 1,
      }
    );

    if (user_data) {
      return res.status(200).json({
        status: false,
        message: `This user is Inactive, please wait while admin is verifying the details.`,
        data: user_data,
      });
    } else {
      if (webOrApp === "web") {
        const newUser = new airtable_sync({
          "Preferred Email": email,
          email,
          secondary_email,
          firebaseId,
          provider,
          isSocial,
          register_status: true,
          facebookLinkedinId: firebaseId,
        });

        const savedata = await newUser.save();

        if (!savedata)
          return res
            .status(200)
            .json({ status: false, message: `User not created.` });

        return res
          .status(200)
          .json({ status: true, message: `User created.`, data: savedata });
      } else {
        return res.status(200).json({
          status: false,
          message: `User doesn't exist, please signup!`,
        });
      }
    }
  } catch (error) {
    if (error.name === "ValidationError") {
      let errors;
      Object.keys(error.errors).forEach((key) => {
        errors = error.errors[key].message;
      });
      return res.status(200).send({ status: false, message: errors });
    }
    return res
      .status(200)
      .json({ status: false, message: "Something went wrong." });
  }
};

exports.userAddAuth0Step_flutter = async (req, res) => {
  try {
    const { firebaseId } = req.body;
    const user_data = await airtable_sync.findOne({
      firebaseId: firebaseId,
      isDelete: false,
    });

    if (user_data) {
      return res
        .status(200)
        .json({ status: true, message: `User exist.`, data: user_data });
    } else {
      return res
        .status(200)
        .json({ status: false, message: `User doesn't exist, please signup!` });
    }
  } catch (error) {
    if (error.name === "ValidationError") {
      let errors;
      Object.keys(error.errors).forEach((key) => {
        errors = error.errors[key].message;
      });
      return res.status(200).send({ status: false, message: errors });
    }
    return res
      .status(200)
      .json({ status: false, message: "Something went wrong." });
  }
};

exports.savePersonaldetails_userStep = async (req, res) => {
  try {
    const body = req.body;
    const user_data = await airtable_sync.findById(body.userId, {
      isDelete: false,
    });
    if (user_data) {
      if (!user_data.register_status)
        return res
          .status(200)
          .json({ status: false, message: `You have not registered yet.` });

      if (user_data.personalDetail_status)
        return res.status(200).json({
          status: false,
          message: `You have already perform  this step please move further and choose membership plan and do payment.`,
        });

      const updated_data = await airtable_sync.findByIdAndUpdate(
        body.userId,
        {
          personalDetail_status: true,
        },
        { runValidators: true, new: true }
      );

      return res.status(200).send({
        status: true,
        message: "User personal details saved!",
        data: updated_data,
      });
    } else {
      return res
        .status(200)
        .json({ status: false, message: `User not found.` });
    }
  } catch (error) {
    return res.status(200).json({ status: false, message: error.message });
  }
};

exports.payment_step = async (req, res) => {
  try {
    const {
      userId,
      purchased_plan,
      name_on_card,
      payment_id,
      country,
      postal_code,
      card_last4,
      card_expiry_date,
      card_brand,
    } = req.body;

    const user_data = await airtable_sync.findById(userId, { isDelete: false });
    if (!user_data)
      return res
        .status(200)
        .json({ status: false, message: `User doesn't exist, please signup!` });

    if (!user_data.register_status)
      return res
        .status(200)
        .json({ status: false, message: "User has not register yet!" });

    if (!user_data.personalDetail_status)
      return res.status(200).json({
        status: false,
        message: "User has not completed personal details step yet!",
      });

    if (user_data.payment_status)
      return res
        .status(200)
        .json({ status: false, message: "User has already purchased plan." });

    if (!purchased_plan)
      return res
        .status(200)
        .json({ status: false, message: "Please choose any plan." });

    const plan_data = await MembershipPlan.findById(purchased_plan).select(
      "plan_name stripe_price_id plan_resource recurring_timeframe"
    );

    if (!plan_data)
      return res
        .status(200)
        .json({ status: false, message: "This plan is not valid." });

    const get_resource = await PlanResource.findById(
      plan_data.plan_resource
    ).select("group_ids");

    var expire_date = new Date();
    if (plan_data.recurring_timeframe === "day") {
      expire_date.setDate(expire_date.getDate() + 1);
    } else if (plan_data.recurring_timeframe === "month") {
      expire_date.setMonth(expire_date.getMonth() + 1);
    } else if (plan_data.recurring_timeframe === "year") {
      expire_date.setYear(expire_date.getYear() + 1);
    }

    const update_membershipPlan_purchase_user =
      await MembershipPlan.findByIdAndUpdate(purchased_plan, {
        $addToSet: { total_member_who_purchased_plan: userId },
      });
    // ***
    // *    for saving live stripe customers payment
    // ***
    const customerInfo = {
      name: name_on_card,
      plan_price_id: plan_data.stripe_price_id,
    };
    const paymentMethodId = payment_id;

    /* Create customer and set default payment method */
    const customer = await stripe.customers.create({
      payment_method: paymentMethodId,
      name: customerInfo.name,
      invoice_settings: {
        default_payment_method: paymentMethodId,
      },
    });

    if (!customer)
      return res.status(200).json({
        status: false,
        message: "Something wrong, customer not created.",
      });

    /* Create subscription and expand the latest invoice's Payment Intent
     * We'll check this Payment Intent's status to determine if this payment needs SCA
     */
    const subscription = await stripe.subscriptions.create({
      customer: customer.id,
      items: [
        {
          plan: customerInfo.plan_price_id,
        },
      ],
      trial_from_plan: false /* Use trial period specified in plan */,
      expand: ["latest_invoice.payment_intent"],
    });
    if (!subscription)
      return res.status(200).json({
        status: false,
        message: "Something wrong, subscription not created.",
      });

    const payment_entry = new Payment({
      membership_plan_id: plan_data._id,
      user_id: userId,
      name_on_card: name_on_card,
      country: country,
      postal_code: postal_code,
      subscriptionId: subscription.id,
      paymentMethodId: paymentMethodId,
      customerId: customer.id,
      card_number: "**** **** **** " + card_last4,
      card_expiry_date: card_expiry_date,
      card_brand: card_brand,
      invoice_payment_intent_status:
        subscription.latest_invoice.payment_intent.status,
      expire_date: expire_date,
    });

    if (!payment_entry)
      return res
        .status(200)
        .json({ status: false, message: "Something went wrong !!" });
    const savedEntry = await payment_entry.save();

    const data = {
      purchased_plan,
      payment_id: savedEntry._id,
      accessible_groups: get_resource.group_ids,
      payment_status: true,
    };
    const update_user_data = await airtable_sync
      .findByIdAndUpdate(userId, data, {
        new: true,
      })
      .select("-__v -password");

    return res.status(200).send({
      status: true,
      message: "Payment successful!",
      data: [
        { id: savedEntry._id, subscription, savedEntry, update_user_data },
      ],
    });
  } catch (error) {
    return res.status(200).json({ status: false, message: error.message });
  }
};

exports.questionAnswerStep = async (req, res) => {
  try {
    const get_user = await airtable_sync
      .findById(req.body.userId, {
        isDelete: false,
      })
      .select(
        "email register_status personalDetail_status payment_status QA_status"
      );
    if (!get_user)
      return res
        .status(200)
        .json({ status: false, message: `User not found.` });
    if (
      !get_user.register_status ||
      !get_user.personalDetail_status ||
      !get_user.payment_status
    )
      return res.status(200).json({
        status: false,
        message: "Please complete your registeration profile first.",
      });

    const get_QA = await QuestionAnswer_byUser.findOne({
      userId: req.body.userId,
      question: req.body.question,
      status: true,
    });
    if (get_QA)
      return res.status(200).json({
        status: false,
        message: "You have already given answer for this question.",
      });

    const newentry = new QuestionAnswer_byUser({
      userId: req.body.userId,
      question: req.body.question,
      answer_object: req.body.answer_object,
      status: true,
    });

    if (req.questions_file.length > 0) {
      newentry.answer_object = { data: req.questions_file };
    }

    const add_QA = await newentry.save();
    const total_que = await Questionnaire.find({
      isDelete: false,
      order: { $gt: 0 },
    }).select("order isDelete");

    const que_ids = total_que.map((item) => {
      return item._id;
    });

    const total_fill_answer = await QuestionAnswer_byUser.countDocuments({
      userId: req.body.userId,
      question: { $in: que_ids },
      status: true,
    });

    if (total_fill_answer === total_que.length) {
      const user_data = await airtable_sync.findByIdAndUpdate(
        req.body.userId,
        { QA_status: true },
        { new: true }
      );

      return res.status(200).send({
        status: true,
        message: "All question answer completed.",
        data: { next_ques: false, user: user_data },
      });
    } else {
      return res.status(200).send({
        status: true,
        message: "Answer for this question is submitted.",
        data: { next_ques: true, QnA: add_QA, user: get_user },
      });
    }
  } catch (error) {
    return res.status(200).json({ status: false, message: error.message });
  }
};

exports.userLoginby_oauth = async (req, res) => {
  try {
    const { email, password } = req.body;
    const user = await airtable_sync
      .findOne({ email: email.toLowerCase(), isDelete: false })
      .select("-__v -createdAt -updatedAt -password");
    if (!user)
      return res
        .status(200)
        .json({ status: false, message: "User doesn't exist, please signup!" });
    if (
      !user.register_status ||
      !user.personalDetail_status ||
      !user.payment_status ||
      !user.QA_status
    )
      return res.status(200).json({
        status: false,
        message: "Registration flow is incomplete.",
        data: { user: user },
      });
    var options = {
      method: "POST",
      url: OAUTH_TOKEN_API,
      headers: { "content-type": "application/json" },
      data: {
        grant_type: "password",
        scope: "openid email",
        username: email,
        password: password,
        client_id: CLIENT_ID,
        client_secret: CLIENT_SECRET,
        audience: AUDIENCE,
      },
    };
    axios
      .request(options)
      .then(async function (result) {
        return res.status(200).json({
          status: true,
          message: "User login.",
          data: { user, idtoken: result.data.id_token },
        });
      })
      .catch((error) => {
        console.log(error);
        return res.status(200).json({
          status: false,
          message: `${error.message} Wrong email or password.`,
        });
      });
  } catch (error) {
    return res.status(200).json({ status: false, message: `${error.message}` });
  }
};

exports.getUserQuestionAnswerList = async (req, res) => {
  try {
    const { id } = req.params;
    const total_que = await Questionnaire.find({
      isDelete: false,
      $gte: { order: 1 },
    });
    const que_ids = total_que.map((item) => {
      return item._id;
    });
    const answerList = await QuestionAnswer_byUser.find({
      userId: id,
      $in: { question: que_ids },
    })
      .populate("userId", "-__v")
      .populate("question", "-__v");

    if (answerList.length > 0) {
      return res.status(200).json({
        status: true,
        message: "User given questions answers.",
        data: answerList,
      });
    } else {
      return res
        .status(200)
        .json({ status: false, message: "Data not found.", data: [] });
    }
  } catch (error) {
    return res.status(200).json({ status: false, message: error.message });
  }
};

exports.getUserProfile_forAdmin = async (req, res) => {
  try {
    const { userId } = req.params;
    // get question and their answer
    const questionNanswer = await QuestionAnswer_byUser.aggregate([
      {
        $lookup: {
          from: "questionnaires",
          localField: "question",
          foreignField: "_id",
          as: "question",
        },
      },
      { $unwind: "$question" },
      {
        $match: {
          userId: ObjectId(userId),
          status: true,
          "question.isDelete": false,
        },
      },
      {
        $project: {
          userId: 0,
          createdAt: 0,
          updatedAt: 0,
          status: 0,
          "question.createdAt": 0,
          "question.updatedAt": 0,
          "question.isDelete": 0,
          "question.__v": 0,
          __v: 0,
        },
      },
    ]);

    // get user info.
    const profile = await airtable_sync.aggregate([
      {
        $lookup: {
          from: "invitecollaborators",
          localField: "_id",
          foreignField: "sharedUserDetails.userId",
          pipeline: [
            {
              $match: {
                isDelete: false,
              },
            },
            {
              $lookup: {
                from: "airtable-syncs",
                localField: "email",
                foreignField: `${"Preferred Email"}`,
                pipeline: [
                  {
                    $match: {
                      isDelete: false,
                      isCollaborator: true,
                    },
                  },
                  {
                    $project: {
                      _id: 1,
                      first_name: 1,
                      last_name: 1,
                      display_name: 1
                    },
                  },
                ],
                as: "CollaboratorUserDetails",
              },
            },
            {
              $project: {
                email: 1,
                CollaboratorUserDetails: {
                  $cond: [
                    { $gt: [{ $size: "$CollaboratorUserDetails" }, 0] },
                    {
                      $let: {
                        vars: {
                          test: {
                            $arrayElemAt: ["$CollaboratorUserDetails", 0],
                          },
                        },
                        in: {
                          userId: `$$test._id`,
                          firstname: `$$test.first_name`,
                          lastname: `$$test.last_name`,
                        },
                      },
                    },
                    "",
                  ],
                },
              },
            },
          ],

          as: "collaborators",
        },
      },
      {
        $lookup: {
          from: "groups",
          let: { localField: "$accessible_groups" },
          pipeline: [
            {
              $match: {
                $expr: { $eq: ["$_id", "$$localField"] },
                isDelete: false,
              },
            },
            {
              $project: {
                createdAt: 0,
                updatedAt: 0,
                isDelete: 0,
                __v: 0,
              },
            },
          ],
          as: "accessible_groups",
        },
      },
      {
        $lookup: {
          from: "membership_plans",
          localField: "purchased_plan",
          foreignField: "_id",
          as: "purchased_plan",
        },
      },
      {
        $unwind: { path: "$purchased_plan", preserveNullAndEmptyArrays: true },
      },
      {
        $lookup: {
          from: "userroles",
          localField: "user_role",
          foreignField: "_id",
          as: "role",
        },
      },
      { $unwind: { path: "$role", preserveNullAndEmptyArrays: true } },
      {
        $match: {
          _id: ObjectId(userId),
          $or: [{ isDelete: false }, { isDelete: { $exists: false } }],
        },
      },
      {
        $project: {
          __v: 0,
        },
      },
    ]);

    let totalNoOfTeamMate = profile[0]["no_of_team_mate"]
      ? profile[0]["no_of_team_mate"]
      : 0;
    let totalNoOfRevokedInvite = 0;
    let totalNoOfAcceptedInvite = 0;
    let totalNoOfPendingInvite = 0;

    if (profile[0]["purchased_plan"]) {
      const existCollaboratorCota = await inviteCollaborator
        .find(
          {
            "memberShipPlanDetails.planId": new ObjectId(
              profile[0]["purchased_plan"]["_id"]
            ),
            "sharedUserDetails.userId": new ObjectId(profile[0]["_id"]),
            isDelete: false,
          },
          { _id: 1, email: 1, teamMateInvitationStatus: 1 }
        )
        .lean();
      for (let i = 0; i < existCollaboratorCota.length; i++) {
        if (existCollaboratorCota[i]["teamMateInvitationStatus"] == "pending") {
          totalNoOfPendingInvite = totalNoOfPendingInvite + 1;
        } else if (
          existCollaboratorCota[i]["teamMateInvitationStatus"] == "accepted"
        ) {
          totalNoOfAcceptedInvite = totalNoOfAcceptedInvite + 1;
        } else if (
          existCollaboratorCota[i]["teamMateInvitationStatus"] == "revoked"
        ) {
          totalNoOfRevokedInvite = totalNoOfRevokedInvite + 1;
        }
      }
    }

    if (
      profile[0]["purchased_plan"] !== undefined &&
      profile[0]["purchased_plan"] !== null
    ) {
      if (profile[0]["purchased_plan"]["isTeamMate"] === true) {
        totalNoOfTeamMate =
          totalNoOfTeamMate +
          parseInt(profile[0]["purchased_plan"]["no_of_team_mate"]);
      }
    }

    profile[0]["totalNoOfTeamMate"] = totalNoOfTeamMate;
    profile[0]["totalNoOfRevokedInvite"] = totalNoOfRevokedInvite;
    profile[0]["totalNoOfAcceptedInvite"] = totalNoOfAcceptedInvite;
    profile[0]["totalNoOfPendingInvite"] = totalNoOfPendingInvite;
    profile[0]["totalNoOfAvailableInvite"] =
      totalNoOfTeamMate - (totalNoOfPendingInvite + totalNoOfAcceptedInvite);

    profile[0]["inviteCollaboratorDetail"] = {
      total: totalNoOfTeamMate,
      revoked: totalNoOfRevokedInvite,
      accepted: totalNoOfAcceptedInvite,
      pending: totalNoOfPendingInvite,
      available:
        totalNoOfTeamMate - (totalNoOfPendingInvite + totalNoOfAcceptedInvite),
    };
    return res.status(200).json({
      status: true,
      message: "Get user profile",
      data: { profile, questionNanswer },
    });
  } catch (error) {
    return res.status(200).json({ status: false, message: error.message });
  }
};

exports.getAttendeeProfile_forAdmin = async (req, res) => {
  try {
    const { userId } = req.params;
    const profile = await airtable_sync.findById(userId, {
      attendeeDetail: 1,
      "Preferred Email": 1,
      firebaseId: 1,
      profileImg: 1,
      partnerIcon: 1,
      passcode: 1,
      first_name: 1,
      last_name: 1,
      display_name: 1,
    });

    let pipeline = [
      {
        $match: {
          user: userId,
        },
      },
      {
        $lookup: {
          from: "event_wise_participant_types",
          localField: "role",
          foreignField: "_id",
          as: "event_wise_participant_types",
        },
      },
      {
        $unwind: {
          path: "$event_wise_participant_types",
          preserveNullAndEmptyArrays: false,
        },
      },
      {
        $group: {
          _id: "$event_wise_participant_types.role",
          role: {
            $first: "$event_wise_participant_types.role",
          },
          type_icon: {
            $first: "$type_icon",
          },
        },
      },
      {
        $sort: {
          _id: 1,
        },
      },
      {
        $unset: "_id",
      },
    ];
    let roles = await eventParticipantAttendees.aggregate(pipeline);
    profile.roles = roles;
    return res.status(200).json({
      status: true,
      message: "Get user profile",
      data: profile,
    });
  } catch (error) {
    return res.status(200).json({ status: false, message: error.message });
  }
};

/** create new user **/
exports.createUser = async (req, res) => {
  if (Object.keys(req.body.questions).length > 0) {
    const newuser = new airtable_sync(req.body);
    airtable_sync.findOne(
      { email: newuser.email.toLowerCase() },
      function (err, user) {
        if (user)
          return res
            .status(400)
            .json({ success: false, message: "email exist" });

        newuser.save(async (err, doc) => {
          if (err) {
            console.log(err);
            return res
              .status(400)
              .json({ success: false, message: "Registration failed" + err });
          } else {
            if (doc.payment_id) {
              const payment_data = await Payment.findById(doc.payment_id);
              const plan_data = await MembershipPlan.findByIdAndUpdate(
                payment_data.membership_plan_id,
                { $push: { total_member_who_purchased_plan: doc._id } },
                { new: true }
              );
              const resource_data = await PlanResource.findById(
                plan_data.plan_resource
              );
              await airtable_sync.findByIdAndUpdate(doc._id, {
                accessible_groups: resource_data.group_ids,
              });
            }
            res
              .status(200)
              .json({ succes: true, user: doc, message: "User created." });
          }
        });
      }
    );
  } else {
    res.status(200).json({ succes: false, message: "Provide proper data" });
  }
};

/** user already exist */
exports.userExist = async (req, res) => {
  try {
    const { firebaseId } = req.body;

    var user = await airtable_sync
      .findOne({
        $or: [{ firebaseId: firebaseId }, { facebookLinkedinId: firebaseId }],
      })
      .lean();

    if (user) {
      const InviteCollaborator = await inviteCollaborator
        .findOne(
          {
            email: user["Preferred Email"],
            isDelete: false,
          },
          {
            _id: 1,
            email: 1,
            firstName: 1,
            lastName: 1,
            name: 1,
            memberShipPlanDetails: 1,
            sharedUserDetails: 1,
            teamMateInvitationStatus: 1,
            collaboratorPlan: 1,
            isOTPVerified: 1,
          }
        )
        .lean();

      if (
        InviteCollaborator !== null &&
        InviteCollaborator.collaboratorPlan === false
      ) {
        return res.status(401).json({
          status: false,
          message: "Your collaborator access is revoked.",
          data: [],
        });
      } else {
        if (user.isCollaborator !== undefined && user.isCollaborator === true) {
          if (
            InviteCollaborator !== null &&
            InviteCollaborator.sharedUserDetails !== undefined &&
            InviteCollaborator.sharedUserDetails !== null
          ) {
            const memberUserData = await airtable_sync.findOne(
              {
                _id: InviteCollaborator.sharedUserDetails.userId,
                isDelete: false,
              },
              { _id: 1, "# of Days Since MDS Only Census": 1 }
            );

            const planDetails = await MembershipPlan.findOne({
              _id: InviteCollaborator.memberShipPlanDetails.planId,
            }).populate("accessResources");

            user = {
              ...user,
              "# of Days Since MDS Only Census": memberUserData[
                "# of Days Since MDS Only Census"
              ]
                ? memberUserData["# of Days Since MDS Only Census"]
                : 0,
            };
            user = {
              ...user,
              accessResources: planDetails.accessResources
                ? planDetails.accessResources
                : [],
            };
          }
        } else {
          user.isCollaborator = false;
          user = { ...user, accessResources: [] };
        }
        return res
          .status(200)
          .json({ status: true, message: "User data.", data: user });
      }
    } else {
      return res
        .status(200)
        .json({ status: false, message: "user not exist." });
    }
  } catch (e) {
    return res
      .status(200)
      .json({ status: false, message: "Something went wrong!" });
  }
};

exports.userExistV2 = async (req, res) => {
  try {
    const { firebaseId, socialEmail, mdsEmail, mdsEmailExists } = req.body;

    var user = await airtable_sync
      .findOne({
        $or: [{ firebaseId: firebaseId }, { facebookLinkedinId: firebaseId }],
        // isDelete: false,
      })
      .lean();

    if (user) {
      const InviteCollaborator = await inviteCollaborator
        .findOne(
          {
            email: user["Preferred Email"],
            isDelete: false,
          },
          {
            _id: 1,
            email: 1,
            firstName: 1,
            lastName: 1,
            name: 1,
            memberShipPlanDetails: 1,
            sharedUserDetails: 1,
            teamMateInvitationStatus: 1,
            collaboratorPlan: 1,
            isOTPVerified: 1,
          }
        )
        .lean();

      if (
        InviteCollaborator !== null &&
        InviteCollaborator.collaboratorPlan === false
      ) {
        return res.status(401).json({
          status: false,
          message: "Your collaborator access is revoked.",
          data: [],
        });
      } else {
        if (user.isCollaborator !== undefined && user.isCollaborator === true) {
          if (
            InviteCollaborator !== null &&
            InviteCollaborator.sharedUserDetails !== undefined &&
            InviteCollaborator.sharedUserDetails !== null
          ) {
            const memberUserData = await airtable_sync.findOne(
              {
                _id: InviteCollaborator.sharedUserDetails.userId,
                isDelete: false,
              },
              { _id: 1, "# of Days Since MDS Only Census": 1 }
            );

            const planDetails = await MembershipPlan.findOne({
              _id: InviteCollaborator.memberShipPlanDetails.planId,
            }).populate("accessResources");

            user = {
              ...user,
              "# of Days Since MDS Only Census": memberUserData[
                "# of Days Since MDS Only Census"
              ]
                ? memberUserData["# of Days Since MDS Only Census"]
                : 0,
            };
            user = {
              ...user,
              accessResources: planDetails.accessResources
                ? planDetails.accessResources
                : [],
            };
          }
        } else {
          user.isCollaborator = false;
          user = { ...user, accessResources: [] };
        }
        return res
          .status(200)
          .json({ status: true, message: "User data.", data: user });
      }
    } else {
      if (!mdsEmailExists) {
        if (socialEmail) {
          var socialUser = await airtable_sync
            .findOne({ "Preferred Email": socialEmail })
            .lean();
          let socialUserDBExists = false;
          socialUserDBExists =
            socialUser !== undefined && socialUser !== null ? true : false;
          if (socialUserDBExists) {
            const verificaitonURL = socialUser["Verification URL"];
            const helloText = `Hello,`;
            if (verificaitonURL && verificaitonURL !== "") {
              const mailData = getUserMigrationEmailContent(
                helloText,
                socialEmail,
                verificaitonURL
              );
              await sendEmail(mailData);
            }
            return res
              .status(200)
              .json({
                status: false,
                socialEmailDBExists: true,
                message: "Social user exists!",
                data: socialUser,
              });
          } else {
            return res
              .status(200)
              .json({
                status: false,
                socialEmailDBExists: false,
                message: "Social user not exist!",
              });
          }
        } else {
          return res
            .status(200)
            .json({
              status: false,
              message: "Input parameter social email is missing!",
            });
        }
      } else {
        if (mdsEmail !== undefined && mdsEmail !== null && mdsEmail !== "") {
          var mdsUser = await airtable_sync
            .findOne({ "Preferred Email": mdsEmail })
            .lean();
          let mdsEmailDBExists = false;

          mdsEmailDBExists =
            mdsUser !== undefined && mdsUser !== null ? true : false;

          if (mdsEmailDBExists) {
            const verificaitonURL = mdsUser["Verification URL"]; 
            const helloText = `Hello,`;
            if (verificaitonURL && verificaitonURL !== "") {
              const mailData = getUserMigrationEmailContent(
                helloText,
                mdsEmail,
                verificaitonURL
              );
              await sendEmail(mailData);
            }
            return res
              .status(200)
              .json({
                status: false,
                socialEmailDBExists: false,
                mdsEmailDBExists: true,
                message: "Mds email exists!",
                data: mdsUser,
              });
          } else {
            return res
              .status(200)
              .json({
                status: false,
                socialEmailDBExists: false,
                mdsEmailDBExists: false,
                message: "Mds email not found in db!",
              });
          }
        } else {
          return res
            .status(200)
            .json({
              status: false,
              message: "Input parameter mds email is missing!",
            });
        }
      }
    }
  } catch (e) {
    return res
      .status(200)
      .json({ status: false, message: "Something went wrong!" });
  }
};

exports.userExistV2 = async (req, res) => {
  try {
    const { firebaseId, socialEmail, mdsEmail, mdsEmailExists } = req.body;

    var user = await airtable_sync
      .findOne({
        $or: [{ firebaseId: firebaseId }, { facebookLinkedinId: firebaseId }],
      })
      .lean();

    if (user) {
      const InviteCollaborator = await inviteCollaborator
        .findOne(
          {
            email: user["Preferred Email"],
            isDelete: false,
          },
          {
            _id: 1,
            email: 1,
            firstName: 1,
            lastName: 1,
            name: 1,
            memberShipPlanDetails: 1,
            sharedUserDetails: 1,
            teamMateInvitationStatus: 1,
            collaboratorPlan: 1,
            isOTPVerified: 1,
          }
        )
        .lean();

      if (
        InviteCollaborator !== null &&
        InviteCollaborator.collaboratorPlan === false
      ) {
        return res.status(401).json({
          status: false,
          message: "Your collaborator access is revoked.",
          data: [],
        });
      } else {
        if (user.isCollaborator !== undefined && user.isCollaborator === true) {
          if (
            InviteCollaborator !== null &&
            InviteCollaborator.sharedUserDetails !== undefined &&
            InviteCollaborator.sharedUserDetails !== null
          ) {
            const memberUserData = await airtable_sync.findOne(
              {
                _id: InviteCollaborator.sharedUserDetails.userId,
                isDelete: false,
              },
              { _id: 1, "# of Days Since MDS Only Census": 1 }
            );

            const planDetails = await MembershipPlan.findOne({
              _id: InviteCollaborator.memberShipPlanDetails.planId,
            }).populate("accessResources");

            user = {
              ...user,
              "# of Days Since MDS Only Census": memberUserData[
                "# of Days Since MDS Only Census"
              ]
                ? memberUserData["# of Days Since MDS Only Census"]
                : 0,
            };
            user = {
              ...user,
              accessResources: planDetails.accessResources
                ? planDetails.accessResources
                : [],
            };
          }
        } else {
          user.isCollaborator = false;
          user = { ...user, accessResources: [] };
        }
        return res
          .status(200)
          .json({ status: true, message: "User data.", data: user });
      }
    } else {
      if (!mdsEmailExists) {
        if (socialEmail) {
          var socialUser = await airtable_sync
            .findOne({ "Preferred Email": socialEmail })
            .lean();
          let socialUserDBExists = false;
          socialUserDBExists =
            socialUser !== undefined && socialUser !== null ? true : false;
          if (socialUserDBExists) {
            const verificaitonURL = socialUser["Verification URL"];
            const helloText = `Hello,`;
            if (verificaitonURL && verificaitonURL !== "") {
              const mailData = getUserMigrationEmailContent(
                helloText,
                socialEmail,
                verificaitonURL
              );
              await sendEmail(mailData);
            }
            return res
              .status(200)
              .json({
                status: false,
                socialEmailDBExists: true,
                message: "Social user exists!",
                data: socialUser,
              });
          } else {
            return res
              .status(200)
              .json({
                status: false,
                socialEmailDBExists: false,
                message: "Social user not exist!",
              });
          }
        } else {
          return res
            .status(200)
            .json({
              status: false,
              message: "Input parameter social email is missing!",
            });
        }
      } else {
        if (mdsEmail !== undefined && mdsEmail !== null && mdsEmail !== "") {
          var mdsUser = await airtable_sync
            .findOne({ "Preferred Email": mdsEmail })
            .lean();
          let mdsEmailDBExists = false;

          mdsEmailDBExists =
            mdsUser !== undefined && mdsUser !== null ? true : false;

          if (mdsEmailDBExists) {
            const verificaitonURL = mdsUser["Verification URL"];
            const helloText = `Hello,`;
            if (verificaitonURL && verificaitonURL !== "") {
              const mailData = getUserMigrationEmailContent(
                helloText,
                mdsEmail,
                verificaitonURL
              );
              await sendEmail(mailData);
            }
            return res
              .status(200)
              .json({
                status: false,
                socialEmailDBExists: false,
                mdsEmailDBExists: true,
                message: "Mds email exists!",
                data: mdsUser,
              });
          } else {
            return res
              .status(200)
              .json({
                status: false,
                socialEmailDBExists: false,
                mdsEmailDBExists: false,
                message: "Mds email not found in db!",
              });
          }
        } else {
          return res
            .status(200)
            .json({
              status: false,
              message: "Input parameter mds email is missing!",
            });
        }
      }
    }
  } catch (e) {
    console.log(e, "error");
    return res
      .status(200)
      .json({ status: false, message: "Something went wrong!" });
  }
};

function getUserMigrationEmailContent(helloText, email, verificationURL) {
  const communityLogo = req.currentEdge.relation_id.logo ? req.currentEdge.relation_id.logo : ""
  const communityName = req.currentEdge.relation_id.name ? req.currentEdge.relation_id.name: "";
  let communityId =  req.currentEdge.relation_id?._id ? ObjectId(req.currentEdge.relation_id?._id) : "";
  let customSMTP =  req.currentEdge.relation_id?.customSMTP ? req.currentEdge.relation_id?.customSMTP : false;
  const mailData = {
    email: `${email}`,
    subject: `Verify Your Account to Access the ${communityName} App!`,
    html: `<html xmlns:v="urn:schemas-microsoft-com:vml" xmlns:o="urn:schemas-microsoft-com:office:office" lang="en">

    <head>
        <title></title>
        <meta http-equiv="Content-Type" content="text/html; charset=utf-8">
        <meta name="viewport" content="width=device-width, initial-scale=1.0">
        <!--[if mso]><xml><o:OfficeDocumentSettings><o:PixelsPerInch>96</o:PixelsPerInch><o:AllowPNG/></o:OfficeDocumentSettings></xml><![endif]-->
        <style>
            * {
                box-sizing: border-box;
            }

            body {
                margin: 0;
                padding: 0;
            }

            a[x-apple-data-detectors] {
                color: inherit !important;
                text-decoration: inherit !important;
            }

            #MessageViewBody a {
                color: inherit;
                text-decoration: none;
            }

            p {
                line-height: inherit
            }

            .desktop_hide,
            .desktop_hide table {
                mso-hide: all;
                display: none;
                max-height: 0px;
                overflow: hidden;
            }

            .image_block img+div {
                display: none;
            }

            @media (max-width:700px) {
                .desktop_hide table.icons-inner {
                    display: inline-block !important;
                }

                .icons-inner {
                    text-align: center;
                }

                .icons-inner td {
                    margin: 0 auto;
                }

                .row-content {
                    width: 100% !important;
                }

                .mobile_hide {
                    display: none;
                }

                .stack .column {
                    width: 100%;
                    display: block;
                }

                .mobile_hide {
                    min-height: 0;
                    max-height: 0;
                    max-width: 0;
                    overflow: hidden;
                    font-size: 0px;
                }

                .desktop_hide,
                .desktop_hide table {
                    display: table !important;
                    max-height: none !important;
                }

                .row-2 .column-1 .block-2.text_block td.pad {
                    padding: 15px 10px !important;
                }

                .row-2 .column-1 .block-1.heading_block h1 {
                    text-align: center !important;
                    font-size: 20px !important;
                }

                .row-2 .column-1 .block-1.heading_block td.pad {
                    padding: 40px 10px 5px !important;
                }

                .row-1 .column-1 .block-2.image_block td.pad {
                    padding: 0 0 0 20px !important;
                }

                .row-2 .column-1 {
                    padding: 0 !important;
                }
            }
        </style>
    </head>

    <body style="margin: 0; background-color: #fbfbfb; padding: 0; -webkit-text-size-adjust: none; text-size-adjust: none;">
        <table class="nl-container" width="100%" border="0" cellpadding="0" cellspacing="0" role="presentation"
            style="mso-table-lspace: 0pt; mso-table-rspace: 0pt; background-color: #fbfbfb;">
            <tbody>
                <tr>
                    <td>
                        <table class="row row-1" align="center" width="100%" border="0" cellpadding="0" cellspacing="0"
                            role="presentation"
                            style="mso-table-lspace: 0pt; mso-table-rspace: 0pt; background-color: #fbfbfb;">
                            <tbody>
                                <tr>
                                    <td>
                                        <table class="row-content stack" align="center" border="0" cellpadding="0"
                                            cellspacing="0" role="presentation"
                                            style="mso-table-lspace: 0pt; mso-table-rspace: 0pt; background-color: #fbfbfb; color: #000000; width: 680px;"
                                            width="680">
                                            <tbody>
                                                <tr>
                                                    <td class="column column-1" width="100%"
                                                        style="mso-table-lspace: 0pt; mso-table-rspace: 0pt; font-weight: 400; text-align: left; padding-bottom: 5px; padding-top: 5px; vertical-align: top; border-top: 0px; border-right: 0px; border-bottom: 0px; border-left: 0px;">
                                                        <div class="spacer_block block-1"
                                                            style="height:30px;line-height:30px;font-size:1px;">&#8202;
                                                        </div>
                                                        <table class="image_block block-2" width="100%" border="0"
                                                            cellpadding="0" cellspacing="0" role="presentation"
                                                            style="mso-table-lspace: 0pt; mso-table-rspace: 0pt;">
                                                            <tr>
                                                                <td class="pad"
                                                                    style="width:100%;padding-right:0px;padding-left:0px;">
                                                                    <div class="alignment" align="left"
                                                                        style="line-height:10px"><img
                                                                            src=${communityLogo}
                                                                            style="display: block; height: auto; border: 0; width: 136px; max-width: 100%;"
                                                                            width="136" alt="Web Logo" title="Logo">
                                                                    </div>
                                                                </td>
                                                            </tr>
                                                        </table>
                                                        <div class="spacer_block block-3"
                                                            style="height:15px;line-height:15px;font-size:1px;">&#8202;
                                                        </div>
                                                        <div class="spacer_block block-4"
                                                            style="height:15px;line-height:15px;font-size:1px;">&#8202;
                                                        </div>
                                                    </td>
                                                </tr>
                                            </tbody>
                                        </table>
                                    </td>
                                </tr>
                            </tbody>
                        </table>
                        <table class="row row-2" align="center" width="100%" border="0" cellpadding="0" cellspacing="0"
                            role="presentation"
                            style="mso-table-lspace: 0pt; mso-table-rspace: 0pt; background-color: #fbfbfb; background-position: center top;">
                            <tbody>
                                <tr>
                                    <td>
                                        <table class="row-content stack" align="center" border="0" cellpadding="0"
                                            cellspacing="0" role="presentation"
                                            style="mso-table-lspace: 0pt; mso-table-rspace: 0pt; background-color: #ffffff; border-bottom: 1px solid #CCD6DD; border-left: 1px solid #CCD6DD; border-radius: 4px; border-right: 1px solid #CCD6DD; border-top: 1px solid #CCD6DD; color: #000000; width: 680px;"
                                            width="680">
                                            <tbody>
                                                <tr>
                                                    <td class="column column-1" width="100%"
                                                        style="mso-table-lspace: 0pt; mso-table-rspace: 0pt; font-weight: 400; text-align: left; vertical-align: top; border-top: 0px; border-right: 0px; border-bottom: 0px; border-left: 0px;">
                                                        <table class="heading_block block-1" width="100%" border="0"
                                                            cellpadding="0" cellspacing="0" role="presentation"
                                                            style="mso-table-lspace: 0pt; mso-table-rspace: 0pt;">
                                                            <tr>
                                                                <td class="pad"
                                                                    style="padding-bottom:5px;padding-left:20px;padding-right:15px;padding-top:40px;text-align:center;width:100%;">
                                                                    <h1
                                                                        style="margin: 0; color: #171719; direction: ltr; font-family: 'Helvetica Neue', Helvetica, Arial, sans-serif; font-size: 30px; font-weight: 400; letter-spacing: normal; line-height: 120%; text-align: center; margin-top: 0; margin-bottom: 0;">
                                                                        Just one step away!</h1>
                                                                </td>
                                                            </tr>
                                                        </table>
                                                        <table class="text_block block-2" width="100%" border="0"
                                                            cellpadding="0" cellspacing="0" role="presentation"
                                                            style="mso-table-lspace: 0pt; mso-table-rspace: 0pt; word-break: break-word;">
                                                            <tr>
                                                                <td class="pad"
                                                                    style="padding-bottom:15px;padding-left:20px;padding-right:35px;padding-top:15px;">
                                                                    <div style="font-family: Arial, sans-serif">
                                                                        <div class
                                                                            style="font-size: 14px; font-family: 'Helvetica Neue', Helvetica, Arial, sans-serif; mso-line-height-alt: 21px; color: #171719; line-height: 1.5;">
                                                                            <p
                                                                                style="margin: 0; text-align: center; mso-line-height-alt: 24px;">
                                                                                <span style="font-size:16px;">Please verify your account to access the ${communityName} App.</span>
                                                                            </p>
                                                                        </div>
                                                                    </div>
                                                                </td>
                                                            </tr>
                                                        </table>
                                                        <table class="button_block block-3" width="100%" border="0"
                                                            cellpadding="0" cellspacing="0" role="presentation"
                                                            style="mso-table-lspace: 0pt; mso-table-rspace: 0pt;">
                                                            <tr>
                                                                <td class="pad"
                                                                    style="padding-bottom:40px;padding-left:25px;padding-right:25px;padding-top:25px;text-align:center;">
                                                                    <div class="alignment" align="center">
                                                                        <span>
                                                                            <span
                                                                                style="padding-left:60px;padding-right:60px;font-size:21px; display:inline-block;letter-spacing:1px;">
                                                                              <span dir="ltr"  style="word-break: break-word; line-height: 42px;">
                                                                                    <a style="background-position: initial;background-size: initial;background-repeat: initial;background-attachment: initial;background-origin: initial;background-clip: initial;background-color: rgba(53, 27, 206, 1);border: 1px solid rgba(53, 27, 206, 1);border-radius: 6px;box-shadow: none;box-sizing: border-box;color: rgba(255, 255, 255, 1);font-size: 16px;font-style: normal;font-weight: 700;line-height: 24px;margin: 0;padding: 10px 40px;text-align: center;" href="${verificationURL}">Verify now</a>
                                                                            </span>
                                                                        </span>
                                                                        </span>
                                                                    </div>
                                                                </td>
                                                            </tr>
                                                        </table>
                                                    </td>
                                                </tr>
                                            </tbody>
                                        </table>
                                    </td>
                                </tr>
                            </tbody>
                        </table>
                        <table class="row row-3" align="center" width="100%" border="0" cellpadding="0" cellspacing="0"
                            role="presentation"
                            style="mso-table-lspace: 0pt; mso-table-rspace: 0pt; background-color: #fbfbfb;">
                            <tbody>
                                <tr>
                                    <td>
                                        <table class="row-content stack" align="center" border="0" cellpadding="0"
                                            cellspacing="0" role="presentation"
                                            style="mso-table-lspace: 0pt; mso-table-rspace: 0pt; background-color: #fbfbfb; color: #000000; width: 680px;"
                                            width="680">
                                            <tbody>
                                                <tr>
                                                    <td class="column column-1" width="100%"
                                                        style="mso-table-lspace: 0pt; mso-table-rspace: 0pt; font-weight: 400; text-align: left; vertical-align: top; border-top: 0px; border-right: 0px; border-bottom: 0px; border-left: 0px;">
                                                        <div class="spacer_block block-1"
                                                            style="height:55px;line-height:55px;font-size:1px;">&#8202;
                                                        </div>
                                                    </td>
                                                </tr>
                                            </tbody>
                                        </table>
                                    </td>
                                </tr>
                            </tbody>
                        </table>
                    </td>
                </tr>
            </tbody>
        </table>
    </body>

    </html>`,
    relationId:communityId,
    customsmtp:customSMTP
  }
  return mailData;
}

exports.addLeadUserData = async (req, res) => {
  try {
    const communityName = req.currentEdge.relation_id.name ? req.currentEdge.relation_id.name: "";
    let communityId =  req.currentEdge.relation_id?._id ? ObjectId(req.currentEdge.relation_id?._id) : "";
    let customSMTP =  req.currentEdge.relation_id?.customSMTP ? req.currentEdge.relation_id?.customSMTP : false;
    if (req.body) {
      const existLeadUserData = await leadUsers.find({
        socialEmail: req.body.socialEmail,
        isDelete: false,
      });
      if (existLeadUserData.length === 0) {
        const leadUserObj = {
          socialEmail: req.body.socialEmail,
          socialEmailDBExists: req.body.socialEmailDBExists,
          mdsEmail: req.body.mdsEmail,
          mdsEmailDBExists: req.body.mdsEmailDBExists,
          migrateUserStatus: req.body.migrateUserStatus,
          firstName: req.body.firstName,
          lastName: req.body.lastName,
          name: req.body.name,
          nickname: req.body.nickname,
          socialProfileURL: req.body.socialProfileURL,
          firebaseId: req.body.firebaseId,
          provider: req.body.provider,
          userId: req.body.userId,
        };

        const leadUserData = new leadUsers(leadUserObj);
        const addleadUserData = await leadUserData.save();
        if (addleadUserData) {
          const mailData = {
            email: `${process.env.AdminEmail}`,
            subject: `New Lead user request`,
            html: `<div style="max-width: 500px; width: 100%; margin: 30px; font-family: arial; line-height: 24px;">
            <div style="margin-bottom: 25px;">Hello,</div>
            <div style="margin-bottom: 25px;">You have a new user's lead to join the ${communityName} platform. Please <a style="text-decoration: underline" href="${process.env.ADMIN_URL}/user/leaduserslist">Click here</a> to check that.</div>
            <div>Best regards,</div>
            <div>Team ${communityName}</div></div>`,
            relationId:communityId,
            customsmtp:customSMTP
          };
          await sendEmail(mailData);

          res
            .status(200)
            .json({
              status: true,
              message: "Admin will verify email address!",
              data: addleadUserData,
            });
        } else
          res
            .status(200)
            .json({
              status: false,
              message: "Error in saving lead user data!",
            });
      } else {
        const updateLeadUserData = await leadUsers.findOneAndUpdate(
          { socialEmail: req.body.socialEmail, isDelete: false },
          {
            mdsEmail: req.body.mdsEmail,
            mdsEmailDBExists: req.body.mdsEmailDBExists,
          },
          { new: true }
        );
        if (updateLeadUserData)
          res
            .status(200)
            .json({
              status: true,
              message: "Admin will verify email address!",
              data: updateLeadUserData,
            });
        else
          res
            .status(200)
            .json({
              status: false,
              message: "Error in saving lead user data!",
            });
      }
    } else {
      res
        .status(200)
        .json({
          status: false,
          message: "Input request body parameters are missing!",
        });
    }
  } catch (e) {
    console.log(e, "error");
    return res
      .status(200)
      .json({ status: false, message: "Something went wrong!" });
  }
};

exports.approveUserMigrationRequest = async (req, res) => {
  try {
    if (req.body) {
      const existLeadUserData = await leadUsers.findOne({
        _id: ObjectId(req.body.id),
        isDelete: false,
      });
      if (existLeadUserData) {
        const checkForEmail = await airtable_sync.find({
          "Preferred Email": req.body.email,
          purchased_plan: { $ne: null },
        });
        if (checkForEmail.length > 0) {
          res
            .status(200)
            .json({
              status: false,
              message:
                "Error in updating lead user data! User with this email already exists!",
            });
        } else {
          const updateLeadUserData = await leadUsers.findByIdAndUpdate(
            ObjectId(req.body.id),
            {
              firstName: req.body.firstName,
              lastName: req.body.lastName,
              approveEmail: req.body.email,
              purchasedPlan: req.body.purchasedPlan,
            },
            { new: true }
          );
          if (updateLeadUserData) {
            var fields_array = {};
            var plan_data = await MembershipPlan.findOne({
              isDelete: false,
              _id: updateLeadUserData.purchasedPlan._id,
            });

            if (!plan_data) {
              return res
                .status(200)
                .json({ status: false, message: "Plan not found!" });
            }
            var resources = await plan_resource.findById(
              plan_data.plan_resource
            );
            const communityName = req.currentEdge.relation_id.name || "";
            let communityId =  req.currentEdge.relation_id?._id ? ObjectId(req.currentEdge.relation_id?._id) : "";
            let customSMTP =  req.currentEdge.relation_id?.customSMTP ? req.currentEdge.relation_id?.customSMTP : false;
            const mail_data = {
              email: updateLeadUserData.approveEmail,
              subject: `Welcome! You've Successfully Created an Account`,
              html: `<html xmlns:v="urn:schemas-microsoft-com:vml" xmlns:o="urn:schemas-microsoft-com:office:office" lang="en">
  
                                    <head>
                                        <title></title>
                                        <meta http-equiv="Content-Type" content="text/html; charset=utf-8">
                                        <meta name="viewport" content="width=device-width, initial-scale=1.0">
                                        <style>
                                            * {
                                                box-sizing: border-box;
                                            }
  
                                            body {
                                                margin: 0;
                                                padding: 0;
                                            }
  
                                            a[x-apple-data-detectors] {
                                                color: inherit !important;
                                                text-decoration: inherit !important;
                                            }
  
                                            #MessageViewBody a {
                                                color: inherit;
                                                text-decoration: none;
                                            }
  
                                            p {
                                                line-height: inherit
                                            }
  
                                            .desktop_hide,
                                            .desktop_hide table {
                                                mso-hide: all;
                                                display: none;
                                                max-height: 0px;
                                                overflow: hidden;
                                            }
  
                                            .image_block img+div {
                                                display: none;
                                            }
  
                                            @media (max-width:620px) {
  
                                                .fullMobileWidth,
                                                .image_block img.big,
                                                .row-content {
                                                    width: 100% !important;
                                                }
  
                                                .mobile_hide {
                                                    display: none;
                                                }
  
                                                .stack .column {
                                                    width: 100%;
                                                    display: block;
                                                }
  
                                                .mobile_hide {
                                                    min-height: 0;
                                                    max-height: 0;
                                                    max-width: 0;
                                                    overflow: hidden;
                                                    font-size: 0px;
                                                }
  
                                                .desktop_hide,
                                                .desktop_hide table {
                                                    display: table !important;
                                                    max-height: none !important;
                                                }
                                            }
                                        </style>
                                    </head>
  
                                    <body style="background-color: #ffffff; margin: 0; padding: 0; -webkit-text-size-adjust: none; text-size-adjust: none;">
                                        <table class="nl-container" width="100%" border="0" cellpadding="0" cellspacing="0" role="presentation" style="mso-table-lspace: 0pt; mso-table-rspace: 0pt; background-color: #ffffff;">
                                            <tbody>
                                                <tr>
                                                    <td>
                                                        <table class="row row-1" align="center" width="100%" border="0" cellpadding="0" cellspacing="0" role="presentation" style="mso-table-lspace: 0pt; mso-table-rspace: 0pt;">
                                                            <tbody>
                                                                <tr>
                                                                    <td>
                                                                        <table class="row-content stack" align="center" border="0" cellpadding="0" cellspacing="0" role="presentation" style="mso-table-lspace: 0pt; mso-table-rspace: 0pt; border-radius: 0; color: #000000; width: 600px;" width="600">
                                                                            <tbody>
                                                                                <tr>
                                                                                    <td class="column column-1" width="100%" style="mso-table-lspace: 0pt; mso-table-rspace: 0pt; font-weight: 400; text-align: left; vertical-align: top; border-top: 0px; border-right: 0px; border-bottom: 0px; border-left: 0px;">
                                                                                        <table class="image_block block-1 mobile_hide" width="100%" border="0" cellpadding="0" cellspacing="0" role="presentation" style="mso-table-lspace: 0pt; mso-table-rspace: 0pt;">
                                                                                            <tr>
                                                                                                <td class="pad" style="padding-bottom:10px;padding-left:20px;padding-top:10px;width:100%;padding-right:0px;">
                                                                                                    <div class="alignment" align="left" style="line-height:10px"><img class="fullMobileWidth" src="https://d15k2d11r6t6rl.cloudfront.net/public/users/Integrators/BeeProAgency/974514_959141/Mds%20Grey%20wide.png" style="display: block; height: auto; border: 0; width: 330px; max-width: 100%;" width="330"></div>
                                                                                                </td>
                                                                                            </tr>
                                                                                        </table>
                                                                                        <table class="image_block block-2 desktop_hide" width="100%" border="0" cellpadding="0" cellspacing="0" role="presentation" style="mso-table-lspace: 0pt; mso-table-rspace: 0pt; mso-hide: all; display: none; max-height: 0; overflow: hidden;">
                                                                                            <tr>
                                                                                                <td class="pad" style="padding-bottom:10px;padding-left:20px;padding-top:10px;width:100%;padding-right:0px;">
                                                                                                    <div class="alignment" align="left" style="line-height:10px"><img src="https://d15k2d11r6t6rl.cloudfront.net/public/users/Integrators/BeeProAgency/974514_959141/Mds%20Grey%20wide.png" style="display: block; height: auto; border: 0; width: 240px; max-width: 100%;" width="240"></div>
                                                                                                </td>
                                                                                            </tr>
                                                                                        </table>
                                                                                    </td>
                                                                                </tr>
                                                                            </tbody>
                                                                        </table>
                                                                    </td>
                                                                </tr>
                                                            </tbody>
                                                        </table>
                                                        <table class="row row-2" align="center" width="100%" border="0" cellpadding="0" cellspacing="0" role="presentation" style="mso-table-lspace: 0pt; mso-table-rspace: 0pt;">
                                                            <tbody>
                                                                <tr>
                                                                    <td>
                                                                        <table class="row-content stack" align="center" border="0" cellpadding="0" cellspacing="0" role="presentation" style="mso-table-lspace: 0pt; mso-table-rspace: 0pt; border-radius: 0; color: #000000; width: 600px;" width="600">
                                                                            <tbody>
                                                                                <tr>
                                                                                    <td class="column column-1" width="100%" style="mso-table-lspace: 0pt; mso-table-rspace: 0pt; font-weight: 400; text-align: left; vertical-align: top; border-top: 0px; border-right: 0px; border-bottom: 0px; border-left: 0px;">
                                                                                        <table class="heading_block block-1" width="100%" border="0" cellpadding="20" cellspacing="0" role="presentation" style="mso-table-lspace: 0pt; mso-table-rspace: 0pt;">
                                                                                            <tr>
                                                                                                <td class="pad">
                                                                                                    <h3 style="margin: 0; color: #000000; direction: ltr; font-family: Arial, 'Helvetica Neue', Helvetica, sans-serif; font-size: 30px; font-weight: 400; letter-spacing: -1px; line-height: 120%; text-align: left; margin-top: 0; margin-bottom: 0;">Congratulations on successfully creating an account with Million Dollar Sellers</h3>
                                                                                                </td>
                                                                                            </tr>
                                                                                        </table>
                                                                                        <table class="image_block block-2" width="100%" border="0" cellpadding="0" cellspacing="0" role="presentation" style="mso-table-lspace: 0pt; mso-table-rspace: 0pt;">
                                                                                            <tr>
                                                                                                <td class="pad" style="width:100%;padding-right:0px;padding-left:0px;">
                                                                                                    <div class="alignment" align="center" style="line-height:10px"><img class="big" src="https://d15k2d11r6t6rl.cloudfront.net/public/users/Integrators/BeeProAgency/974514_959141/mds-app-announcement-rev2_v5_1.png" style="display: block; height: auto; border: 0; width: 600px; max-width: 100%;" width="600"></div>
                                                                                                </td>
                                                                                            </tr>
                                                                                        </table>
                                                                                    </td>
                                                                                </tr>
                                                                            </tbody>
                                                                        </table>
                                                                    </td>
                                                                </tr>
                                                            </tbody>
                                                        </table>
                                                        <table class="row row-3" align="center" width="100%" border="0" cellpadding="0" cellspacing="0" role="presentation" style="mso-table-lspace: 0pt; mso-table-rspace: 0pt;">
                                                            <tbody>
                                                                <tr>
                                                                    <td>
                                                                        <table class="row-content stack" align="center" border="0" cellpadding="0" cellspacing="0" role="presentation" style="mso-table-lspace: 0pt; mso-table-rspace: 0pt; color: #000000; width: 600px;" width="600">
                                                                            <tbody>
                                                                                <tr>
                                                                                    <td class="column column-1" width="100%" style="mso-table-lspace: 0pt; mso-table-rspace: 0pt; font-weight: 400; text-align: left; padding-bottom: 5px; padding-top: 5px; vertical-align: top; border-top: 0px; border-right: 0px; border-bottom: 0px; border-left: 0px;">
                                                                                        <table class="paragraph_block block-1" width="100%" border="0" cellpadding="0" cellspacing="0" role="presentation" style="mso-table-lspace: 0pt; mso-table-rspace: 0pt; word-break: break-word;">
                                                                                            <tr>
                                                                                                <td class="pad" style="padding-bottom:25px;padding-left:20px;padding-right:20px;">
                                                                                                    <div style="color:#232c3d;direction:ltr;font-family:Arial, 'Helvetica Neue', Helvetica, sans-serif;font-size:16px;font-weight:400;letter-spacing:0px;line-height:150%;text-align:left;mso-line-height-alt:24px;">
                                                                                                        <p style="margin: 0;">We would like to suggest that you try our mobile app for an even better experience with our platform</p>
                                                                                                    </div>
                                                                                                </td>
                                                                                            </tr>
                                                                                        </table>
                                                                                    </td>
                                                                                </tr>
                                                                            </tbody>
                                                                        </table>
                                                                    </td>
                                                                </tr>
                                                            </tbody>
                                                        </table>
                                                        <table class="row row-4 mobile_hide" align="center" width="100%" border="0" cellpadding="0" cellspacing="0" role="presentation" style="mso-table-lspace: 0pt; mso-table-rspace: 0pt;">
                                                            <tbody>
                                                                <tr>
                                                                    <td>
                                                                        <table class="row-content stack" align="center" border="0" cellpadding="0" cellspacing="0" role="presentation" style="mso-table-lspace: 0pt; mso-table-rspace: 0pt; border-radius: 0; color: #000000; width: 600px;" width="600">
                                                                            <tbody>
                                                                                <tr>
                                                                                    <td class="column column-1" width="33.333333333333336%" style="mso-table-lspace: 0pt; mso-table-rspace: 0pt; font-weight: 400; text-align: left; background-color: #f7f9fb; padding-bottom: 5px; padding-top: 5px; vertical-align: middle; border-top: 0px; border-right: 0px; border-bottom: 0px; border-left: 0px;">
                                                                                        <table class="image_block block-1" width="100%" border="0" cellpadding="10" cellspacing="0" role="presentation" style="mso-table-lspace: 0pt; mso-table-rspace: 0pt;">
                                                                                            <tr>
                                                                                                <td class="pad">
                                                                                                    <div class="alignment" align="center" style="line-height:10px"><img src="https://d15k2d11r6t6rl.cloudfront.net/public/users/Integrators/BeeProAgency/974514_959141/app-logo-mockArtboard-1.png" style="display: block; height: auto; border: 0; width: 180px; max-width: 100%;" width="180"></div>
                                                                                                </td>
                                                                                            </tr>
                                                                                        </table>
                                                                                    </td>
                                                                                    <td class="column column-2" width="66.66666666666667%" style="mso-table-lspace: 0pt; mso-table-rspace: 0pt; font-weight: 400; text-align: left; background-color: #f7f9fb; padding-bottom: 5px; padding-top: 5px; vertical-align: middle; border-top: 0px; border-right: 0px; border-bottom: 0px; border-left: 0px;">
                                                                                        <table class="heading_block block-1" width="100%" border="0" cellpadding="0" cellspacing="0" role="presentation" style="mso-table-lspace: 0pt; mso-table-rspace: 0pt;">
                                                                                            <tr>
                                                                                                <td class="pad" style="padding-left:20px;padding-right:20px;text-align:center;width:100%;">
                                                                                                    <h2 style="margin: 0; color: #232c3d; direction: ltr; font-family: Arial, Helvetica, sans-serif; font-size: 18px; font-weight: 700; letter-spacing: normal; line-height: 120%; text-align: left; margin-top: 0; margin-bottom: 0;"><span class="tinyMce-placeholder">Download the App</span></h2>
                                                                                                </td>
                                                                                            </tr>
                                                                                        </table>
                                                                                        <table class="paragraph_block block-2" width="100%" border="0" cellpadding="0" cellspacing="0" role="presentation" style="mso-table-lspace: 0pt; mso-table-rspace: 0pt; word-break: break-word;">
                                                                                            <tr>
                                                                                                <td class="pad" style="padding-bottom:10px;padding-left:20px;padding-right:20px;padding-top:10px;">
                                                                                                    <div style="color:#232c3d;direction:ltr;font-family:Arial, Helvetica, sans-serif;font-size:16px;font-weight:400;letter-spacing:0px;line-height:150%;text-align:left;mso-line-height-alt:24px;">
                                                                                                        <p style="margin: 0;">Download the MDS app for&nbsp;<a href="https://apps.apple.com/app/id1636838955" rel="noopener" target="_blank" style="text-decoration: underline; color: #296bb7;"><strong>iOS</strong></a>&nbsp;or&nbsp;<a href="https://play.google.com/store/apps/details?id=com.app.mdscommunity" rel="noopener" target="_blank" style="text-decoration: underline; color: #296bb7;"><strong>Android</strong></a>, or access the content on the website.</p>
                                                                                                    </div>
                                                                                                </td>
                                                                                            </tr>
                                                                                        </table>
                                                                                    </td>
                                                                                </tr>
                                                                            </tbody>
                                                                        </table>
                                                                    </td>
                                                                </tr>
                                                            </tbody>
                                                        </table>
                                                        <table class="row row-5 desktop_hide" align="center" width="100%" border="0" cellpadding="0" cellspacing="0" role="presentation" style="mso-table-lspace: 0pt; mso-table-rspace: 0pt; mso-hide: all; display: none; max-height: 0; overflow: hidden;">
                                                            <tbody>
                                                                <tr>
                                                                    <td>
                                                                        <table class="row-content stack" align="center" border="0" cellpadding="0" cellspacing="0" role="presentation" style="mso-table-lspace: 0pt; mso-table-rspace: 0pt; mso-hide: all; display: none; max-height: 0; overflow: hidden; background-color: #f7f9fb; color: #000000; width: 600px;" width="600">
                                                                            <tbody>
                                                                                <tr>
                                                                                    <td class="column column-1" width="100%" style="mso-table-lspace: 0pt; mso-table-rspace: 0pt; font-weight: 400; text-align: left; padding-bottom: 5px; padding-top: 5px; vertical-align: top; border-top: 0px; border-right: 0px; border-bottom: 0px; border-left: 0px;">
                                                                                        <table class="image_block block-1" width="100%" border="0" cellpadding="10" cellspacing="0" role="presentation" style="mso-table-lspace: 0pt; mso-table-rspace: 0pt; mso-hide: all; display: none; max-height: 0; overflow: hidden;">
                                                                                            <tr>
                                                                                                <td class="pad">
                                                                                                    <div class="alignment" align="center" style="line-height:10px"><img src="https://d15k2d11r6t6rl.cloudfront.net/public/users/Integrators/BeeProAgency/974514_959141/app-logo-mockArtboard-1.png" style="display: block; height: auto; border: 0; width: 180px; max-width: 100%;" width="180"></div>
                                                                                                </td>
                                                                                            </tr>
                                                                                        </table>
                                                                                        <table class="heading_block block-2" width="100%" border="0" cellpadding="0" cellspacing="0" role="presentation" style="mso-table-lspace: 0pt; mso-table-rspace: 0pt; mso-hide: all; display: none; max-height: 0; overflow: hidden;">
                                                                                            <tr>
                                                                                                <td class="pad" style="padding-left:20px;padding-right:20px;text-align:center;width:100%;">
                                                                                                    <h2 style="margin: 0; color: #232c3d; direction: ltr; font-family: Arial, Helvetica, sans-serif; font-size: 18px; font-weight: 700; letter-spacing: normal; line-height: 120%; text-align: left; margin-top: 0; margin-bottom: 0;"><span class="tinyMce-placeholder">Download the App</span></h2>
                                                                                                </td>
                                                                                            </tr>
                                                                                        </table>
                                                                                        <table class="paragraph_block block-3" width="100%" border="0" cellpadding="0" cellspacing="0" role="presentation" style="mso-table-lspace: 0pt; mso-table-rspace: 0pt; word-break: break-word; mso-hide: all; display: none; max-height: 0; overflow: hidden;">
                                                                                            <tr>
                                                                                                <td class="pad" style="padding-bottom:10px;padding-left:20px;padding-right:20px;padding-top:10px;">
                                                                                                    <div style="color:#232c3d;direction:ltr;font-family:Arial, Helvetica, sans-serif;font-size:16px;font-weight:400;letter-spacing:0px;line-height:150%;text-align:left;mso-line-height-alt:24px;">
                                                                                                        <p style="margin: 0;">Download the MDS app for&nbsp;<a href="https://apps.apple.com/app/id1636838955" rel="noopener" target="_blank" style="text-decoration: underline; color: #296bb7;"><strong>iOS</strong></a>&nbsp;or&nbsp;<a href="https://play.google.com/store/apps/details?id=com.app.mdscommunity" rel="noopener" target="_blank" style="text-decoration: underline; color: #296bb7;"><strong>Android</strong></a>, or access the content on the website.</p>
                                                                                                    </div>
                                                                                                </td>
                                                                                            </tr>
                                                                                        </table>
                                                                                    </td>
                                                                                </tr>
                                                                            </tbody>
                                                                        </table>
                                                                    </td>
                                                                </tr>
                                                            </tbody>
                                                        </table>
                                                        <table class="row row-6" align="center" width="100%" border="0" cellpadding="0" cellspacing="0" role="presentation" style="mso-table-lspace: 0pt; mso-table-rspace: 0pt;">
                                                            <tbody>
                                                                <tr>
                                                                    <td>
                                                                        <table class="row-content stack" align="center" border="0" cellpadding="0" cellspacing="0" role="presentation" style="mso-table-lspace: 0pt; mso-table-rspace: 0pt; border-radius: 0; color: #000000; width: 600px;" width="600">
                                                                            <tbody>
                                                                                <tr>
                                                                                    <td class="column column-1" width="100%" style="mso-table-lspace: 0pt; mso-table-rspace: 0pt; font-weight: 400; text-align: left; padding-bottom: 5px; padding-top: 5px; vertical-align: top; border-top: 0px; border-right: 0px; border-bottom: 0px; border-left: 0px;">
                                                                                        <div class="spacer_block block-1" style="height:15px;line-height:15px;font-size:1px;">&#8202;</div>
                                                                                    </td>
                                                                                </tr>
                                                                            </tbody>
                                                                        </table>
                                                                    </td>
                                                                </tr>
                                                            </tbody>
                                                        </table>
                                                        <table class="row row-7 mobile_hide" align="center" width="100%" border="0" cellpadding="0" cellspacing="0" role="presentation" style="mso-table-lspace: 0pt; mso-table-rspace: 0pt;">
                                                            <tbody>
                                                                <tr>
                                                                    <td>
                                                                        <table class="row-content stack" align="center" border="0" cellpadding="0" cellspacing="0" role="presentation" style="mso-table-lspace: 0pt; mso-table-rspace: 0pt; border-radius: 0; color: #000000; width: 600px;" width="600">
                                                                            <tbody>
                                                                                <tr>
                                                                                    <td class="column column-1" width="33.333333333333336%" style="mso-table-lspace: 0pt; mso-table-rspace: 0pt; font-weight: 400; text-align: left; background-color: #f7f9fb; padding-bottom: 5px; padding-top: 5px; vertical-align: middle; border-top: 0px; border-right: 0px; border-bottom: 0px; border-left: 0px;">
                                                                                        <table class="image_block block-1" width="100%" border="0" cellpadding="10" cellspacing="0" role="presentation" style="mso-table-lspace: 0pt; mso-table-rspace: 0pt;">
                                                                                            <tr>
                                                                                                <td class="pad">
                                                                                                    <div class="alignment" align="center" style="line-height:10px"><img src="https://d15k2d11r6t6rl.cloudfront.net/public/users/Integrators/BeeProAgency/974514_959141/Group%201000001962_1.png" style="display: block; height: auto; border: 0; width: 180px; max-width: 100%;" width="180"></div>
                                                                                                </td>
                                                                                            </tr>
                                                                                        </table>
                                                                                    </td>
                                                                                    <td class="column column-2" width="66.66666666666667%" style="mso-table-lspace: 0pt; mso-table-rspace: 0pt; font-weight: 400; text-align: left; background-color: #f7f9fb; padding-bottom: 5px; padding-top: 5px; vertical-align: middle; border-top: 0px; border-right: 0px; border-bottom: 0px; border-left: 0px;">
                                                                                        <table class="heading_block block-1" width="100%" border="0" cellpadding="0" cellspacing="0" role="presentation" style="mso-table-lspace: 0pt; mso-table-rspace: 0pt;">
                                                                                            <tr>
                                                                                                <td class="pad" style="padding-left:20px;padding-right:20px;text-align:center;width:100%;">
                                                                                                    <h2 style="margin: 0; color: #232c3d; direction: ltr; font-family: Arial, Helvetica, sans-serif; font-size: 18px; font-weight: 700; letter-spacing: normal; line-height: 120%; text-align: left; margin-top: 0; margin-bottom: 0;"><span class="tinyMce-placeholder">Need help?</span></h2>
                                                                                                </td>
                                                                                            </tr>
                                                                                        </table>
                                                                                        <table class="paragraph_block block-2" width="100%" border="0" cellpadding="0" cellspacing="0" role="presentation" style="mso-table-lspace: 0pt; mso-table-rspace: 0pt; word-break: break-word;">
                                                                                            <tr>
                                                                                                <td class="pad" style="padding-bottom:10px;padding-left:20px;padding-right:20px;padding-top:10px;">
                                                                                                    <div style="color:#232c3d;direction:ltr;font-family:Arial, Helvetica, sans-serif;font-size:16px;font-weight:400;letter-spacing:0px;line-height:150%;text-align:left;mso-line-height-alt:24px;">
                                                                                                        <p style="margin: 0;">Having issues? Drop them in the beta access <a href="https://m.me/j/AbZsGogZIqQohmgO/" target="_blank" rel="noopener" style="text-decoration: underline; color: #296bb7;"><strong>Facebook Chat ➔</strong></a></p>
                                                                                                    </div>
                                                                                                </td>
                                                                                            </tr>
                                                                                        </table>
                                                                                    </td>
                                                                                </tr>
                                                                            </tbody>
                                                                        </table>
                                                                    </td>
                                                                </tr>
                                                            </tbody>
                                                        </table>
                                                        <table class="row row-8 desktop_hide" align="center" width="100%" border="0" cellpadding="0" cellspacing="0" role="presentation" style="mso-table-lspace: 0pt; mso-table-rspace: 0pt; mso-hide: all; display: none; max-height: 0; overflow: hidden;">
                                                            <tbody>
                                                                <tr>
                                                                    <td>
                                                                        <table class="row-content stack" align="center" border="0" cellpadding="0" cellspacing="0" role="presentation" style="mso-table-lspace: 0pt; mso-table-rspace: 0pt; mso-hide: all; display: none; max-height: 0; overflow: hidden; background-color: #f7f9fb; color: #000000; width: 600px;" width="600">
                                                                            <tbody>
                                                                                <tr>
                                                                                    <td class="column column-1" width="100%" style="mso-table-lspace: 0pt; mso-table-rspace: 0pt; font-weight: 400; text-align: left; padding-bottom: 5px; padding-top: 5px; vertical-align: top; border-top: 0px; border-right: 0px; border-bottom: 0px; border-left: 0px;">
                                                                                        <table class="image_block block-1" width="100%" border="0" cellpadding="10" cellspacing="0" role="presentation" style="mso-table-lspace: 0pt; mso-table-rspace: 0pt; mso-hide: all; display: none; max-height: 0; overflow: hidden;">
                                                                                            <tr>
                                                                                                <td class="pad">
                                                                                                    <div class="alignment" align="center" style="line-height:10px"><img src="https://d15k2d11r6t6rl.cloudfront.net/public/users/Integrators/BeeProAgency/974514_959141/Group%201000001962_1.png" style="display: block; height: auto; border: 0; width: 180px; max-width: 100%;" width="180"></div>
                                                                                                </td>
                                                                                            </tr>
                                                                                        </table>
                                                                                        <table class="heading_block block-2" width="100%" border="0" cellpadding="0" cellspacing="0" role="presentation" style="mso-table-lspace: 0pt; mso-table-rspace: 0pt; mso-hide: all; display: none; max-height: 0; overflow: hidden;">
                                                                                            <tr>
                                                                                                <td class="pad" style="padding-left:20px;padding-right:20px;text-align:center;width:100%;">
                                                                                                    <h2 style="margin: 0; color: #232c3d; direction: ltr; font-family: Arial, Helvetica, sans-serif; font-size: 18px; font-weight: 700; letter-spacing: normal; line-height: 120%; text-align: left; margin-top: 0; margin-bottom: 0;"><span class="tinyMce-placeholder">Need help?</span></h2>
                                                                                                </td>
                                                                                            </tr>
                                                                                        </table>
                                                                                        <table class="paragraph_block block-3" width="100%" border="0" cellpadding="0" cellspacing="0" role="presentation" style="mso-table-lspace: 0pt; mso-table-rspace: 0pt; word-break: break-word; mso-hide: all; display: none; max-height: 0; overflow: hidden;">
                                                                                            <tr>
                                                                                                <td class="pad" style="padding-bottom:10px;padding-left:20px;padding-right:20px;padding-top:10px;">
                                                                                                    <div style="color:#232c3d;direction:ltr;font-family:Arial, Helvetica, sans-serif;font-size:16px;font-weight:400;letter-spacing:0px;line-height:150%;text-align:left;mso-line-height-alt:24px;">
                                                                                                        <p style="margin: 0;">Having issues? Drop them in the beta access <a href="https://m.me/j/AbZsGogZIqQohmgO/" target="_blank" rel="noopener" style="text-decoration: underline; color: #296bb7;"><strong>Facebook Chat ➔</strong></a></p>
                                                                                                    </div>
                                                                                                </td>
                                                                                            </tr>
                                                                                        </table>
                                                                                    </td>
                                                                                </tr>
                                                                            </tbody>
                                                                        </table>
                                                                    </td>
                                                                </tr>
                                                            </tbody>
                                                        </table>
                                                        <table class="row row-9" align="center" width="100%" border="0" cellpadding="0" cellspacing="0" role="presentation" style="mso-table-lspace: 0pt; mso-table-rspace: 0pt;">
                                                            <tbody>
                                                                <tr>
                                                                    <td>
                                                                        <table class="row-content stack" align="center" border="0" cellpadding="0" cellspacing="0" role="presentation" style="mso-table-lspace: 0pt; mso-table-rspace: 0pt; border-radius: 0; color: #000000; width: 600px;" width="600">
                                                                            <tbody>
                                                                                <tr>
                                                                                    <td class="column column-1" width="100%" style="mso-table-lspace: 0pt; mso-table-rspace: 0pt; font-weight: 400; text-align: left; padding-top: 15px; vertical-align: top; border-top: 0px; border-right: 0px; border-bottom: 0px; border-left: 0px;">
                                                                                        <div class="spacer_block block-1" style="height:30px;line-height:30px;font-size:1px;">&#8202;</div>
                                                                                        <table class="heading_block block-2" width="100%" border="0" cellpadding="0" cellspacing="0" role="presentation" style="mso-table-lspace: 0pt; mso-table-rspace: 0pt;">
                                                                                            <tr>
                                                                                                <td class="pad" style="padding-bottom:5px;padding-left:20px;padding-right:20px;padding-top:10px;text-align:center;width:100%;">
                                                                                                    <h1 style="margin: 0; color: #232c3d; direction: ltr; font-family: Arial, Helvetica, sans-serif; font-size: 18px; font-weight: 700; letter-spacing: normal; line-height: 120%; text-align: left; margin-top: 0; margin-bottom: 0;"><span class="tinyMce-placeholder">Join the beta testing</span></h1>
                                                                                                </td>
                                                                                            </tr>
                                                                                        </table>
                                                                                        <table class="paragraph_block block-3" width="100%" border="0" cellpadding="0" cellspacing="0" role="presentation" style="mso-table-lspace: 0pt; mso-table-rspace: 0pt; word-break: break-word;">
                                                                                            <tr>
                                                                                                <td class="pad" style="padding-bottom:10px;padding-left:20px;padding-right:20px;">
                                                                                                    <div style="color:#232c3d;direction:ltr;font-family:Arial, Helvetica, sans-serif;font-size:16px;font-weight:400;letter-spacing:0px;line-height:150%;text-align:left;mso-line-height-alt:24px;">
                                                                                                        <p style="margin: 0;">Explore exclusive content, and help shape the future of MDS!</p>
                                                                                                    </div>
                                                                                                </td>
                                                                                            </tr>
                                                                                        </table>
                                                                                        <table class="divider_block block-4" width="100%" border="0" cellpadding="20" cellspacing="0" role="presentation" style="mso-table-lspace: 0pt; mso-table-rspace: 0pt;">
                                                                                            <tr>
                                                                                                <td class="pad">
                                                                                                    <div class="alignment" align="center">
                                                                                                        <table border="0" cellpadding="0" cellspacing="0" role="presentation" width="100%" style="mso-table-lspace: 0pt; mso-table-rspace: 0pt;">
                                                                                                            <tr>
                                                                                                                <td class="divider_inner" style="font-size: 1px; line-height: 1px; border-top: 1px solid #dddddd;"><span>&#8202;</span></td>
                                                                                                            </tr>
                                                                                                        </table>
                                                                                                    </div>
                                                                                                </td>
                                                                                            </tr>
                                                                                        </table>
                                                                                    </td>
                                                                                </tr>
                                                                            </tbody>
                                                                        </table>
                                                                    </td>
                                                                </tr>
                                                            </tbody>
                                                        </table>
                                                        <table class="row row-10 desktop_hide" align="center" width="100%" border="0" cellpadding="0" cellspacing="0" role="presentation" style="mso-table-lspace: 0pt; mso-table-rspace: 0pt; mso-hide: all; display: none; max-height: 0; overflow: hidden;">
                                                            <tbody>
                                                                <tr>
                                                                    <td>
                                                                        <table class="row-content stack" align="center" border="0" cellpadding="0" cellspacing="0" role="presentation" style="mso-table-lspace: 0pt; mso-table-rspace: 0pt; mso-hide: all; display: none; max-height: 0; overflow: hidden; border-radius: 0; color: #000000; width: 600px;" width="600">
                                                                            <tbody>
                                                                                <tr>
                                                                                    <td class="column column-1" width="50%" style="mso-table-lspace: 0pt; mso-table-rspace: 0pt; font-weight: 400; text-align: left; padding-bottom: 5px; vertical-align: top; border-top: 0px; border-right: 0px; border-bottom: 0px; border-left: 0px;">
                                                                                        <table class="image_block block-1" width="100%" border="0" cellpadding="5" cellspacing="0" role="presentation" style="mso-table-lspace: 0pt; mso-table-rspace: 0pt; mso-hide: all; display: none; max-height: 0; overflow: hidden;">
                                                                                            <tr>
                                                                                                <td class="pad">
                                                                                                    <div class="alignment" align="center" style="line-height:10px"><a href="https://apps.apple.com/app/id1636838955" target="_blank" style="outline:none" tabindex="-1"><img src="https://d15k2d11r6t6rl.cloudfront.net/public/users/Integrators/BeeProAgency/974514_959141/apple.png" style="display: block; height: auto; border: 0; width: 135px; max-width: 100%;" width="135"></a></div>
                                                                                                </td>
                                                                                            </tr>
                                                                                        </table>
                                                                                    </td>
                                                                                    <td class="column column-2" width="50%" style="mso-table-lspace: 0pt; mso-table-rspace: 0pt; font-weight: 400; text-align: left; padding-bottom: 5px; vertical-align: top; border-top: 0px; border-right: 0px; border-bottom: 0px; border-left: 0px;">
                                                                                        <table class="image_block block-1" width="100%" border="0" cellpadding="5" cellspacing="0" role="presentation" style="mso-table-lspace: 0pt; mso-table-rspace: 0pt; mso-hide: all; display: none; max-height: 0; overflow: hidden;">
                                                                                            <tr>
                                                                                                <td class="pad">
                                                                                                    <div class="alignment" align="center" style="line-height:10px"><a href="https://play.google.com/store/apps/details?id=com.app.mdscommunity" target="_blank" style="outline:none" tabindex="-1"><img src="https://d15k2d11r6t6rl.cloudfront.net/public/users/Integrators/BeeProAgency/974514_959141/play.png" style="display: block; height: auto; border: 0; width: 135px; max-width: 100%;" width="135"></a></div>
                                                                                                </td>
                                                                                            </tr>
                                                                                        </table>
                                                                                    </td>
                                                                                </tr>
                                                                            </tbody>
                                                                        </table>
                                                                    </td>
                                                                </tr>
                                                            </tbody>
                                                        </table>
                                                        <table class="row row-11 mobile_hide" align="center" width="100%" border="0" cellpadding="0" cellspacing="0" role="presentation" style="mso-table-lspace: 0pt; mso-table-rspace: 0pt;">
                                                            <tbody>
                                                                <tr>
                                                                    <td>
                                                                        <table class="row-content stack" align="center" border="0" cellpadding="0" cellspacing="0" role="presentation" style="mso-table-lspace: 0pt; mso-table-rspace: 0pt; border-radius: 0; color: #000000; width: 600px;" width="600">
                                                                            <tbody>
                                                                                <tr>
                                                                                    <td class="column column-1" width="50%" style="mso-table-lspace: 0pt; mso-table-rspace: 0pt; font-weight: 400; text-align: left; padding-bottom: 5px; padding-left: 60px; padding-right: 5px; vertical-align: top; border-top: 0px; border-right: 0px; border-bottom: 0px; border-left: 0px;">
                                                                                        <table class="image_block block-1" width="100%" border="0" cellpadding="0" cellspacing="0" role="presentation" style="mso-table-lspace: 0pt; mso-table-rspace: 0pt;">
                                                                                            <tr>
                                                                                                <td class="pad" style="padding-bottom:5px;padding-left:60px;padding-right:5px;padding-top:5px;width:100%;">
                                                                                                    <div class="alignment" align="center" style="line-height:10px"><a href="https://apps.apple.com/app/id1636838955" target="_blank" style="outline:none" tabindex="-1"><img src="https://d15k2d11r6t6rl.cloudfront.net/public/users/Integrators/BeeProAgency/974514_959141/apple.png" style="display: block; height: auto; border: 0; width: 170px; max-width: 100%;" width="170"></a></div>
                                                                                                </td>
                                                                                            </tr>
                                                                                        </table>
                                                                                    </td>
                                                                                    <td class="column column-2" width="50%" style="mso-table-lspace: 0pt; mso-table-rspace: 0pt; font-weight: 400; text-align: left; padding-bottom: 5px; padding-left: 10px; padding-right: 60px; vertical-align: top; border-top: 0px; border-right: 0px; border-bottom: 0px; border-left: 0px;">
                                                                                        <table class="image_block block-1" width="100%" border="0" cellpadding="0" cellspacing="0" role="presentation" style="mso-table-lspace: 0pt; mso-table-rspace: 0pt;">
                                                                                            <tr>
                                                                                                <td class="pad" style="padding-bottom:5px;padding-left:5px;padding-right:60px;padding-top:5px;width:100%;">
                                                                                                    <div class="alignment" align="center" style="line-height:10px"><a href="https://play.google.com/store/apps/details?id=com.app.mdscommunity" target="_blank" style="outline:none" tabindex="-1"><img src="https://d15k2d11r6t6rl.cloudfront.net/public/users/Integrators/BeeProAgency/974514_959141/play.png" style="display: block; height: auto; border: 0; width: 165px; max-width: 100%;" width="165"></a></div>
                                                                                                </td>
                                                                                            </tr>
                                                                                        </table>
                                                                                    </td>
                                                                                </tr>
                                                                            </tbody>
                                                                        </table>
                                                                    </td>
                                                                </tr>
                                                            </tbody>
                                                        </table>
                                                        <table class="row row-12" align="center" width="100%" border="0" cellpadding="0" cellspacing="0" role="presentation" style="mso-table-lspace: 0pt; mso-table-rspace: 0pt;">
                                                            <tbody>
                                                                <tr>
                                                                    <td>
                                                                        <table class="row-content stack" align="center" border="0" cellpadding="0" cellspacing="0" role="presentation" style="mso-table-lspace: 0pt; mso-table-rspace: 0pt; border-radius: 0; color: #000000; width: 600px;" width="600">
                                                                            <tbody>
                                                                                <tr>
                                                                                    <td class="column column-1" width="100%" style="mso-table-lspace: 0pt; mso-table-rspace: 0pt; font-weight: 400; text-align: left; padding-bottom: 5px; padding-top: 5px; vertical-align: top; border-top: 0px; border-right: 0px; border-bottom: 0px; border-left: 0px;">
                                                                                        <div class="spacer_block block-1" style="height:15px;line-height:15px;font-size:1px;">&#8202;</div>
                                                                                    </td>
                                                                                </tr>
                                                                            </tbody>
                                                                        </table>
                                                                    </td>
                                                                </tr>
                                                            </tbody>
                                                        </table>
                                                        <table class="row row-13" align="center" width="100%" border="0" cellpadding="0" cellspacing="0" role="presentation" style="mso-table-lspace: 0pt; mso-table-rspace: 0pt;">
                                                            <tbody>
                                                                <tr>
                                                                    <td>
                                                                        <table class="row-content stack" align="center" border="0" cellpadding="0" cellspacing="0" role="presentation" style="mso-table-lspace: 0pt; mso-table-rspace: 0pt; border-radius: 0; color: #000000; width: 600px;" width="600">
                                                                            <tbody>
                                                                                <tr>
                                                                                    <td class="column column-1" width="100%" style="mso-table-lspace: 0pt; mso-table-rspace: 0pt; font-weight: 400; text-align: left; padding-bottom: 5px; padding-top: 5px; vertical-align: top; border-top: 0px; border-right: 0px; border-bottom: 0px; border-left: 0px;">
                                                                                        <table class="paragraph_block block-1" width="100%" border="0" cellpadding="0" cellspacing="0" role="presentation" style="mso-table-lspace: 0pt; mso-table-rspace: 0pt; word-break: break-word;">
                                                                                            <tr>
                                                                                                <td class="pad" style="padding-bottom:10px;padding-left:10px;padding-right:10px;">
                                                                                                    <div style="color:#232c3d;direction:ltr;font-family:Arial, Helvetica, sans-serif;font-size:11px;font-weight:400;letter-spacing:0px;line-height:120%;text-align:center;mso-line-height-alt:13.2px;">
                                                                                                       <!-- <p style="margin: 0;">Copyright © 2023 ${communityName}, Inc<br>All Rights Reserved</p> -->
                                                                                                    </div>
                                                                                                </td>
                                                                                            </tr>
                                                                                        </table>
                                                                                    </td>
                                                                                </tr>
                                                                            </tbody>
                                                                        </table>
                                                                    </td>
                                                                </tr>
                                                            </tbody>
                                                        </table>
                                                    </td>
                                                </tr>
                                            </tbody>
                                        </table>
                                    </body>
  
                                    </html>`,
              relationId:communityId,
              customsmtp:customSMTP
            };

            const existsUserWithNoPlan = await airtable_sync.find({
              "Preferred Email": updateLeadUserData.approveEmail,
              purchased_plan: { $eq: null },
            });
            if (existsUserWithNoPlan.length > 0) {
              const updated_data = await airtable_sync.findOneAndUpdate(
                { _id: existsUserWithNoPlan[0]._id },
                {
                  first_name: updateLeadUserData.firstName,
                  last_name: updateLeadUserData.lastName,
                  email: updateLeadUserData.approveEmail,
                  firebaseId: updateLeadUserData.firebaseId,
                  provider: updateLeadUserData.provider,
                  isSocial: true,
                  active: true,
                  purchased_plan: plan_data._id,
                  register_status: true,
                  personalDetail_status: true,
                  payment_status: true,
                  QA_status: true,
                  accessible_groups:
                    resources && resources.group_ids ? resources.group_ids : [],
                  migrate_user_status: true,
                  facebookLinkedinId: updateLeadUserData.firebaseId,
                  isDelete: false,
                  blocked: false,
                  createdAt: new Date(),
                  updatedAt: new Date(),
                  joinDate: new Date(),
                },
                { new: true }
              );
              if (updated_data) {
                const plan = await MembershipPlan.findByIdAndUpdate(
                  plan_data._id,
                  {
                    $push: {
                      total_member_who_purchased_plan:
                        existsUserWithNoPlan[0]._id,
                    },
                  },
                  { new: true }
                );

                const updateLeadUserStatus = await leadUsers.findByIdAndUpdate(
                  updateLeadUserData._id,
                  { status: "approved" },
                  { new: true }
                );

                return res.status(200).json({
                  status: true,
                  message: "updated successfully!",
                  data: updated_data,
                  leadUserData: updateLeadUserStatus,
                });
              } else {
                return res
                  .status(200)
                  .json({ status: false, message: "Something went wrong!" });
              }
            } else {
              const updated_data = new airtable_sync({
                "Preferred Email": updateLeadUserData.approveEmail,
                first_name: updateLeadUserData.firstName,
                last_name: updateLeadUserData.lastName,
                email: updateLeadUserData.approveEmail,
                firebaseId: updateLeadUserData.firebaseId,
                provider: updateLeadUserData.provider,
                isSocial: true,
                active: true,
                purchased_plan: plan_data._id,
                register_status: true,
                personalDetail_status: true,
                payment_status: true,
                QA_status: true,
                accessible_groups:
                  resources && resources.group_ids ? resources.group_ids : [],
                migrate_user_status: true,
                facebookLinkedinId: updateLeadUserData.firebaseId,
                profileImg: "",
                isDelete: false,
                blocked: false,
                joinDate: new Date(),
              });

              updated_data.save(async (err, doc) => {
                if (err)
                  return res.status(200).json({
                    status: false,
                    message: "Something went wrong!",
                    error: err,
                  });
                else {
                  const plan = await MembershipPlan.findByIdAndUpdate(
                    plan_data._id,
                    { $push: { total_member_who_purchased_plan: doc._id } },
                    { new: true }
                  );

                  const updateLeadUserStatus =
                    await leadUsers.findByIdAndUpdate(
                      updateLeadUserData._id,
                      { status: "approved" },
                      { new: true }
                    );

                  await sendEmail(mail_data);
                  return res.status(200).json({
                    status: true,
                    message: "updated successfully!",
                    data: doc,
                    leadUserData: updateLeadUserStatus,
                  });
                }
              });
            }
          }
        }
      } else {
        res
          .status(200)
          .json({ status: false, message: "Lead user data not exists!" });
      }
    } else {
      res
        .status(200)
        .json({
          status: false,
          message: "Input request body parameters are missing!",
        });
    }
  } catch (e) {
    console.log(e, "error");
    return res
      .status(200)
      .json({ status: false, message: "Something went wrong!" });
  }
};

exports.rejectUserMigrationRequest = async (req, res) => {
  try {
    if (req.body) {
      const existLeadUserData = await leadUsers.findOne({
        _id: ObjectId(req.body.id),
        isDelete: false,
      });
      if (existLeadUserData) {
        if (
          existLeadUserData.status === "pending" &&
          existLeadUserData.status !== "approved"
        ) {
          const updateLeadUserData = await leadUsers.findByIdAndUpdate(
            ObjectId(req.body.id),
            { status: "rejected", rejectReason: req.body.reason },
            { new: true }
          );
          if (updateLeadUserData)
            res
              .status(200)
              .json({
                status: true,
                message: "Lead user migration request rejected!",
                data: updateLeadUserData,
              });
          else
            res
              .status(200)
              .json({
                status: false,
                message: "Error in rejecting lead user data!",
              });
        } else {
          res
            .status(200)
            .json({
              status: false,
              message: "lead user data aleady approved! ",
            });
        }
      } else {
        res
          .status(200)
          .json({ status: false, message: "Lead user data not found!" });
      }
    } else {
      res
        .status(200)
        .json({
          status: false,
          message: "Input request body parameters are missing!",
        });
    }
  } catch (e) {
    console.log(e, "error");
    return res
      .status(200)
      .json({ status: false, message: "Something went wrong!" });
  }
};

exports.leadUsersList = async (req, res) => {
  try {
    const leadUserList = await leadUsers.find({ isDelete: false });
    if (leadUserList.length > 0)
      return res
        .status(200)
        .json({
          status: true,
          message: "Lead users list retrieved!",
          data: leadUserList,
        });
    else
      return res
        .status(200)
        .json({ status: true, message: "No Lead users found !", data: [] });
  } catch (e) {
    return res
      .status(200)
      .json({ status: false, message: "Something went wrong!" });
  }
};

exports.adminLogin = async (req, res) => {
  try {
    const user = await AdminUser.findOne({
      email: req.body.email.toLowerCase(),
      isDelete: false,
    });
    if (!user)
      return res
        .status(200)
        .json({ status: false, message: "Admin not found." });
    else
      return res
        .status(200)
        .json({ status: true, message: "Admin found.", data: user });
  } catch (e) {
    res.status(400).send(e);
  }
};

//  user login
exports.userLogin = async (req, res) => {
  try {
    const user = await airtable_sync.findOne({
      email: req.body.email.toLowerCase(),
      isDelete: false,
    });
    if (user) {
      if (!user.register_status || !user.payment_status || !user.QA_status) {
        return res.json({
          isAuth: false,
          message: "Not completed registartion process!",
          data: user,
        });
      } else if (user.blocked) {
        return res.json({
          isAuth: false,
          message:
            "Your account is inactive. Once the admin reactivates it, you will be able to login.",
        });
      } else if (!user.active) {
        return res.json({
          isAuth: false,
          message:
            "Your account is inactive. Once the admin reactivates it, you will be able to login.",
        });
      } else {
        return res.json({
          isAuth: true,
          userData: user,
          message: "successfull",
        });
      }
    } else {
      return res.json({
        isAuth: false,
        message: " Auth failed ,email not found",
      });
    }
  } catch (error) {
    return res.json({
      status: false,
      data: error,
      message: "Error in user login!",
    });
  }
};

exports.sociallogin = async (req, res) => {
  try {
    const user = await airtable_sync.findOne({
      email: req.body.email.toLowerCase(),
      isSocial: true,
      isDelete: false,
    });
    if (user) {
      if (!user.register_status || !user.payment_status || !user.QA_status) {
        return res.json({
          isAuth: false,
          data: user,
          message: "Not completed registartion process!",
        });
      } else if (user.blocked) {
        return res.json({
          isAuth: false,
          message:
            "Your account is inactive. Once the admin reactivates it, you will be able to login.",
        });
      } else if (!user.active) {
        return res.json({
          isAuth: false,
          message:
            "Your account is inactive. Once the admin reactivates it, you will be able to login.",
        });
      } else {
        return res.json({
          isAuth: true,
          userData: user,
          message: "successfull",
        });
      }
    } else {
      return res.json({
        isAuth: false,
        message: " Auth failed ,email not found",
      });
    }
  } catch (error) {
    return res.json({
      status: false,
      data: "",
      message: "Error in user login!",
    });
  }
};


exports.getAllAttendeeListForRules = async (req, res) => {
  try {
    const sortField =
      req.query.sortField === "first_name"
        ? "first_name"
        : req.query.sortField === "last_name"
          ? "last_name"
              : req.query.sortField === "email"
                ? "Preferred Email"
                : "createdAt";
    const sortType = req.query.sortType === "Asc" ? 1 : -1;

    var match = {
      $or: [{ isDelete: false }, { isDelete: { $exists: false } }],
      $and: [
        { 'Preferred Email': { $exists: true, $ne: '' } },
        { 'Preferred Email': { $ne: null } }
      ]
    };

    let pipeline = [
      {
        '$match': match
      }, {
        '$project': {
          'Preferred Email': 1,
          'isDelete': 1,
          'first_name': 1,
          'last_name': 1,
          'display_name': 1,
        }
      }, 
      {
        '$sort': { [`${sortField}`]: sortType }
      }
    ]
    const data = await airtable_sync.aggregate(pipeline);
    return res.status(200).send(data);
  } catch (e) {
    return res.status(400).json({ status: false, message: e });
  }
};

/** user forgot pwd **/
exports.forgotPwd = async (req, res) => {
  try {
    const user = await airtable_sync.findOne({
      email: req.body.email.toLowerCase(),
      isDelete: false,
    });
    if (!user)
      return res.status(200).send({
        isAuth: false,
        message: "user with given email doesn't exist",
      });
    else return res.status(200).send({ isAuth: true, message: "email exist" });
  } catch (error) {
    console.log(error);
    return res.status(400).send("An error occured");
  }
};

exports.getallusers = async (req, res) => {
  try {
    const sortField =
      req.query.sortField === "firstName"
        ? "first_name"
        : req.query.sortField === "lastName"
          ? "last_name"
          : req.query.sortField === "email"
            ? "Preferred Email"
            : req.query.sortField === "joined"
              ? "joinDate"
              : "createdAt";
    const sortType = req.query.sortType === "Asc" ? 1 : -1;
    var match = {
      $or: [{ blocked: false }, { blocked: { $exists: false } }],
      isDelete: false,
      firebaseId: { $nin: ["", null] },
    };

    var search = "";
    if (req.query.search) {
      search = req.query.search;
      match = {
        ...match,
        $or: [
          {
            "Preferred Email": { $regex: ".*" + search + ".*", $options: "i" },
          },
          {
            "attendeeDetail.name": {
              $regex: ".*" + search + ".*",
              $options: "i",
            },
          },
          {
            "attendeeDetail.firstName": {
              $regex: ".*" + search + ".*",
              $options: "i",
            },
          },
          {
            "attendeeDetail.lastName": {
              $regex: ".*" + search + ".*",
              $options: "i",
            },
          },
          { first_name: { $regex: ".*" + search + ".*", $options: "i" } },
          { last_name: { $regex: ".*" + search + ".*", $options: "i" } },
          { "display_name": { $regex: ".*" + search + ".*", $options: "i" }, },
        ],
      };
    }

    const data = await airtable_sync
      .find(match, {
        "Preferred Email": 1,
        email: 1,
        firebaseId: 1,
        last_login: 1,
        last_activity_log: 1,
        isDelete: 1,
        migrate_user_status: 1,
        migrate_user: 1,
        State: 1,
        City: 1,
        Country: 1,
        State: 1,
        Zip: 1,
        "About Me": 1,
        "Facebook Profile Link": 1,
        attendeeDetail: 1,
        createdAt: 1,
        joinDate: 1,
        first_name: { $ifNull: ["$first_name", ""] },
        last_name: { $ifNull: ["$last_name", ""] },
        display_name: { $ifNull: ["$display_name", ""] },
        userName: {
          $concat: [
            { $ifNull: ["$first_name", ""] },
            " ",
            { $ifNull: ["$last_name", ""] },
          ],
        },
      })
      .lean()
      .sort({ [`${sortField}`]: sortType });
    return res.status(200).send(data);
  } catch (e) {
    return res.status(400).json({ status: false, message: e });
  }
};

exports.getallusersLimitedFields = async (req, res) => {
  try {
    const communityId = req.currentEdge.relation_id._id

    const data = await user_edges.aggregate([
      {
        $match: {
          relation_id: new ObjectId(communityId)
        }
      },
      {
        $lookup: {
          from: 'airtable-syncs',
          localField: 'user_id',
          foreignField: '_id',
          as: 'user'
        }
      },
      {
        $unwind: '$user'
      },
      {
        $project: {
          _id: '$user._id',
          email: '$user.email',
          secondary_email: '$user.secondary_email',
          firebaseId: '$user.firebaseId',
          profileImg: '$user.profileImg',
          "Preferred Email": '$user.Preferred Email',
          "attendeeDetail.name": '$user.attendeeDetail.name',
          "attendeeDetail.firstName": '$user.attendeeDetail.firstName',
          "attendeeDetail.lastName": '$user.attendeeDetail.lastName',
          first_name: { $ifNull: ['$user.first_name', ''] },
          last_name: { $ifNull: ['$user.last_name', ''] },
          display_name: { $ifNull: ['$user.display_name', ''] }
        }
      }
    ]);

    return res.status(200).send(data);
  } catch (e) {
    return res.status(400).json({ status: false, message: e });
  }
};

exports.getAllAttendeeList = async (req, res) => {
  try {
    const sortField =
      req.query.sortField === "name"
        ? "attendeeDetail.name"
        : req.query.sortField === "firstName"
          ? "attendeeDetail.firstName"
          : req.query.sortField === "lastName"
            ? "attendeeDetail.lastName"
            : req.query.sortField === "company"
              ? "attendeeDetail.company"
              : req.query.sortField === "email"
                ? "attendeeDetail.email"
                : "createdAt";
    const sortType = req.query.sortType === "Asc" ? 1 : -1;

    var match = {
      attendeeDetail: { $ne: null },
      $or: [{ isDelete: false }, { isDelete: { $exists: false } }],
    };

    var search = "";
    if (req.query.search) {
      search = req.query.search;
      match = {
        ...match,
        $and: [
          {
            $or: [
              {
                "Preferred Email": {
                  $regex: ".*" + search + ".*",
                  $options: "i",
                },
              },
              {
                "attendeeDetail.name": {
                  $regex: ".*" + search + ".*",
                  $options: "i",
                },
              },
              {
                "attendeeDetail.firstName": {
                  $regex: ".*" + search + ".*",
                  $options: "i",
                },
              },
              {
                "attendeeDetail.lastName": {
                  $regex: ".*" + search + ".*",
                  $options: "i",
                },
              },
              {
                first_name: { $regex: ".*" + search + ".*", $options: "i" },
              },
              {
                last_name: { $regex: ".*" + search + ".*", $options: "i" },
              },
              {
                "display_name": { $regex: ".*" + search + ".*", $options: "i" },
              },
            ],
          },
        ],
      };
    }

    let pipeline = [
      {
        $match: match,
      },
      {
        $project: {
          "Preferred Email": 1,
          attendeeDetail: 1,
          isDelete: 1,
          first_name: 1,
          last_name: 1,
          'display_name': 1,
        },
      },
      {
        $lookup: {
          from: "event_participant_attendees",
          localField: "_id",
          foreignField: "user",
          pipeline: [
            {
              $match: {
                $and: [
                  {
                    $expr: {
                      $eq: ["$isDelete", false],
                    },
                  },
                ],
              },
            },
            {
              $lookup: {
                from: "events",
                localField: "event",
                foreignField: "_id",
                as: "events_result",
                pipeline: [
                  {
                    $match: {
                      $and: [
                        {
                          $expr: {
                            $eq: ["$isDelete", false],
                          },
                        },
                      ],
                    },
                  },
                ],
              },
            },
            {
              $unwind: {
                path: "$events_result",
                preserveNullAndEmptyArrays: false,
              },
            },
            {
              $lookup: {
                from: "event_wise_participant_types",
                localField: "role",
                foreignField: "_id",
                pipeline: [
                  {
                    $match: {
                      $and: [
                        {
                          $expr: {
                            $eq: ["$isDelete", false],
                          },
                        },
                      ],
                    },
                  },
                ],
                as: "event_wise_participant_types_result",
              },
            },
            {
              $unwind: {
                path: "$event_wise_participant_types_result",
                preserveNullAndEmptyArrays: false,
              },
            },
            {
              $project: {
                _id: "$event",
                type_icon: 1,
                contact_name: 1,
                partner_order: 1,
                description: 1,
                offer: 1,
                isDelete: 1,
                title: "$events_result.title",
                role: "$event_wise_participant_types_result.role",
              },
            },
          ],
          as: "event_participant_attendees_result",
        },
      },
      {
        $sort: { [`${sortField}`]: sortType },
      },
    ];
    const data = await airtable_sync.aggregate(pipeline);
    return res.status(200).send(data);
  } catch (e) {
    return res.status(400).json({ status: false, message: e });
  }
};

exports.getAllSpeakerList = async (req, res) => {
  try {
    const data = await airtable_sync
      .find(
        {
          $or: [{ isDelete: false }, { isDelete: { $exists: false } }],
          $and: [
            { "Preferred Email": { $ne: null } },
            { "Preferred Email": { $ne: "" } },
          ],
          user_edges: { $in: [new ObjectId(req.currentEdge["_id"])] }
        },
        {
          PreferredEmail: "$Preferred Email",
          firebaseId: 1,
          first_name: { $ifNull: ["$first_name", ""] },
          last_name: { $ifNull: ["$last_name", ""] },
          display_name: { $ifNull: ["$display_name", ""] },
          attendeeDetail: {
            name: "$attendeeDetail.name" ? "$attendeeDetail.name" : "",
            firstName: "$attendeeDetail.firstName"
              ? "$attendeeDetail.firstName"
              : "",
            lastName: "$attendeeDetail.lastName"
              ? "$attendeeDetail.lastName"
              : "",
          },
          isDelete: 1,
        }
      )
      .lean();

    return res.status(200).json({
      status: true,
      message: "Speaker list retrive successfully.",
      data: data,
    });
  } catch (error) {
    return res
      .status(400)
      .json({ status: false, message: "Internal server error!", data: error });
  }
};

exports.getAllSpeakerListV2 = async (req, res) => {
  try {
    let { search } = req.query;

    let sortBy =
      req.query.sortBy && req.query.sortBy !== ""
        ? req.query.sortBy
        : "createdAt";
    let sortOrder =
      req.query.sortOrder && req.query.sortOrder != ""
        ? req.query.sortOrder
        : "Desc";

    const page = parseInt(req.query.page) || 1;
    const limit = parseInt(req.query.limit) || 10;
    const skip = (page - 1) * limit;
    search = search ? search.replace(/[-[\]{}()*+?.,\\^$|#\s]/g, "\\$&") : null;

    let searchFilter = req.query.search
      ? {
          $or: [
            { "Preferred Email": { $regex: ".*" + search + ".*", $options: "i" } },
            ...(search.trim().split(" ").length == 2
              ? [
                  {
                    $and: [
                      {
                        first_name: {
                          $regex:".*" +  search.split(" ")[0] + ".*",
                          $options: "i",
                        },
                      },
                      {
                        last_name: {
                          $regex:".*" +  search.split(" ")[1] + ".*",
                          $options: "i",
                        },
                      },
                    ],
                  },
                ]
              : [
                  { first_name: { $regex: ".*" + search + ".*", $options: "i" } },
                  { last_name: { $regex: ".*" + search + ".*", $options: "i" } },
                ]),
          ],
        }
      : null;

    let pipeline = [
      {
        $match: {
          type: {
            $in: ["M", "CO", "GU"],
          },
          isDelete: false,
          relation_id: ObjectId(req.relation_id),
        },
      },
      {
        $lookup: {
          from: "airtable-syncs",
          localField: "user_id",
          foreignField: "_id",
          as: "airtable_syncs",
          pipeline: [
            {
              $match: {
                ...searchFilter,
                isDelete: false,
              },
            },
          ],
        },
      },
      {
        $unwind: {
          path: "$airtable_syncs",
          preserveNullAndEmptyArrays: false,
        },
      },
      {
        $project: {
          _id: "$airtable_syncs._id",
          isDelete: "$airtable_syncs.isDelete",
          "Preferred Email": "$airtable_syncs.Preferred Email",
          firebaseId: "$airtable_syncs.firebaseId",
          first_name: { $ifNull: ["$airtable_syncs.first_name", ""] },
          last_name: { $ifNull: ["$airtable_syncs.last_name", ""] },
          display_name: { $ifNull: ["$airtable_syncs.display_name", ""] },
          type: 1,
          createdAt: "$airtable_syncs.createdAt",
          updatedAt: "$airtable_syncs.updatedAt",
          attendeeDetail: {
            name: {
              $ifNull: ["$airtable_syncs.attendeeDetail.name", ""],
            },
            firstName: {
              $ifNull: ["$airtable_syncs.attendeeDetail.firstName", ""],
            },
            lastName: {
              $ifNull: ["$airtable_syncs.attendeeDetail.lastName", ""],
            },
          },
        },
      },
      {
        $sort: {
          [sortBy === "email" ? "Preferred Email" : sortBy]:
            sortOrder === "Asc" ? 1 : -1,
        },
      },
    ];
    const videoSpeakerData = await user_edges.aggregate([
      ...pipeline,
      { $skip: skip },
      { $limit: limit },
    ]);

    let totalCount = await user_edges.aggregate([
      ...pipeline,
      { $count: "count"}
    ])

    let countAllData = await user_edges.aggregate([
      {
        $match: {
          type: {
            $in: ["M", "CO", "GU"],
          },
          isDelete: false,
          relation_id: ObjectId(req.relation_id),
        },
      },
      {
        $lookup: {
          from: "airtable-syncs",
          localField: "user_id",
          foreignField: "_id",
          as: "airtable_syncs",
          pipeline: [
            {
              $match: {
                isDelete: false,
              },
            },
          ],
        },
      },
      {
        $unwind: {
          path: "$airtable_syncs",
          preserveNullAndEmptyArrays: false,
        },
      },
      {
        $project: {
          _id: "$airtable_syncs._id",
          isDelete: "$airtable_syncs.isDelete",
          "Preferred Email": "$airtable_syncs.Preferred Email",
          firebaseId: "$airtable_syncs.firebaseId",
          first_name: { $ifNull: ["$airtable_syncs.first_name", ""] },
          last_name: { $ifNull: ["$airtable_syncs.last_name", ""] },
          display_name: { $ifNull: ["$airtable_syncs.display_name", ""] },
          createdAt: "$airtable_syncs.createdAt",
          updatedAt: "$airtable_syncs.updatedAt",
          attendeeDetail: {
            name: {
              $ifNull: ["$airtable_syncs.attendeeDetail.name", ""],
            },
            firstName: {
              $ifNull: ["$airtable_syncs.attendeeDetail.firstName", ""],
            },
            lastName: {
              $ifNull: ["$airtable_syncs.attendeeDetail.lastName", ""],
            },
          },
        },
      },
      {
        $count: "count"
      }
    ])

    return res.status(200).json({
      status: true,
      message: "Speaker list retrive successfully.",
      data: {
        data: videoSpeakerData,
        totalPages: totalCount.length ? Math.ceil((totalCount[0]?.count) / limit) : 0,
        currentPage: page,
        totalCount : totalCount.length ? totalCount[0]?.count : 0,
        countAllData: countAllData[0].count
      },
    });
  } catch (error) {
    console.log("🚀 ~ exports.getAllSpeakerList= ~ error:", error);
    return res
      .status(400)
      .json({ status: false, message: "Internal server error!", data: error });
  }
};

exports.getAllSpeakerListSuggestion = async (req, res) => {
  try {
    let pipeline = [
      {
        $match: {
          type: {
            $in: ["M", "CO", "GU"],
          },
          isDelete: false,
          relation_id: ObjectId(req.relation_id),
        },
      },
      {
        $lookup: {
          from: "airtable-syncs",
          localField: "user_id",
          foreignField: "_id",
          as: "airtable_syncs",
          pipeline: [
            {
              $match: {
                isDelete: false,
              },
            },
          ],
        },
      },
      {
        $unwind: {
          path: "$airtable_syncs",
          preserveNullAndEmptyArrays: false,
        },
      },
      {
        $addFields: {
          first_name_lower: {
            $toLower: { $ifNull: ["$airtable_syncs.first_name", ""] },
          },
        },
      },
      {
        $project: {
          _id: "$airtable_syncs._id",
          first_name: { $ifNull: ["$airtable_syncs.first_name", ""] },
          last_name: { $ifNull: ["$airtable_syncs.last_name", ""] },
          display_name: { $ifNull: ["$airtable_syncs.display_name", ""] },
          attendeeDetail: {
            name: { $ifNull: ["$airtable_syncs.attendeeDetail.name", ""] },
            firstName: {
              $ifNull: ["$airtable_syncs.attendeeDetail.firstName", ""],
            },
            lastName: {
              $ifNull: ["$airtable_syncs.attendeeDetail.lastName", ""],
            },
          },
          first_name_lower: 1, 
        },
      },
      {
        $sort: {
          first_name_lower: 1,
        },
      },
      {
        $unset: "first_name_lower",
      },
    ];
    const videoSpeakerData = await user_edges.aggregate([
      ...pipeline,
    ]);

    return res.status(200).json({
      status: true,
      message: "Speaker suggestion list retrive successfully.",
      data: {
        data: videoSpeakerData,
      },
    });
  } catch (error) {
    console.log("🚀 ~ exports.getAllSpeakerListSuggestion= ~ error:", error);
    return res
      .status(400)
      .json({ status: false, message: "Internal server error!", data: error });
  }
};

exports.getVideoSpeakerById = async (req, res) => {
  try {
    const { id } = req?.params;
    if (!id || !mongoose.Types.ObjectId.isValid(id)) {
      return res.status(400).json({
        success: false,
        message: "Invitee ID is not valid!",
        data: {},
      });
    }

    const data = await user_edges.aggregate([
      {
        $match: {
          type: {
            $in: ["M", "CO", "GU"],
          },
          isDelete: false,
          relation_id: ObjectId(req.relation_id),
          user_id: ObjectId(id),
        },
      },
      {
        $lookup: {
          from: "airtable-syncs",
          localField: "user_id",
          foreignField: "_id",
          as: "airtable_syncs",
        },
      },
      {
        $unwind: {
          path: "$airtable_syncs",
          preserveNullAndEmptyArrays: false,
        },
      },
      {
        $project: {
          _id: "$airtable_syncs._id",
          isDelete: "$airtable_syncs.isDelete",
          "Preferred Email": "$airtable_syncs.Preferred Email",
          firebaseId: "$airtable_syncs.firebaseId",
          first_name: { $ifNull: ["$airtable_syncs.first_name", ""] },
          last_name: { $ifNull: ["$airtable_syncs.last_name", ""] },
          display_name: { $ifNull: ["$airtable_syncs.display_name", ""] },
          profileImg: "$airtable_syncs.profileImg",
          attendeeDetail: "$airtable_syncs.attendeeDetail",
        },
      },
    ]);

    return res.status(200).json({
      status: true,
      message: "Video Speaker data retrive successfully.",
      data: data,
    });
  } catch (error) {
    console.log("🚀 ~ exports.getAllSpeakerList= ~ error:", error);
    return res
      .status(400)
      .json({ status: false, message: "Internal server error!", data: error });
  }
};

exports.getblockeduser = async (req, res) => {
  try {
    const sortField =
      req.query.sortField === "firstName"
        ? "first_name"
        : req.query.sortField === "lastName"
          ? "last_name"
          : req.query.sortField === "email"
            ? "Preferred Email"
            : req.query.sortField === "createdAt"
              ? "createdAt"
              : req.query.sortField === "last_activity_log"
                ? "last_activity_log"
                : req.query.sortField === "last_login"
                  ? "last_login"
                  : "createdAt";
    const sortType = req.query.sortType === "Asc" ? 1 : -1;

    var match = {
      blocked: true,
      isDelete: false,
      firebaseId: { $nin: ["", null] },
    };

    var search = "";
    if (req.query.search) {
      search = req.query.search;
      match = {
        ...match,
        $or: [
          {
            "Preferred Email": { $regex: ".*" + search + ".*", $options: "i" },
          },
          {
            "attendeeDetail.name": {
              $regex: ".*" + search + ".*",
              $options: "i",
            },
          },
          {
            "attendeeDetail.firstName": {
              $regex: ".*" + search + ".*",
              $options: "i",
            },
          },
          {
            "attendeeDetail.lastName": {
              $regex: ".*" + search + ".*",
              $options: "i",
            },
          },
          {
            first_name: { $regex: ".*" + search + ".*", $options: "i" },
          },
          {
            last_name: { $regex: ".*" + search + ".*", $options: "i" },
          },
          {
            display_name: { $regex: ".*" + search + ".*", $options: "i" },
          },
        ],
      };
    }

    const data = await airtable_sync
      .find(match)
      .sort({ [`${sortField}`]: sortType });
    return res.status(200).send(data);
  } catch (e) {
    return res.status(400).json({ status: false, message: e });
  }
};

exports.updateprofile = async (req, res) => {
  try {
    const user = await airtable_sync.findById(req.body.id, { isDelete: false });
    if (!user) return res.status(400).send("User not found.");

    let tempFirstName =req.body.first_name ?req.body.first_name :""
    let tempLastName =req.body.last_name ? req.body.last_name :""

    airtable_sync.findByIdAndUpdate(
      req.body.id,
      {
        first_name: req.body.first_name,
        last_name: req.body.last_name,
        display_name: req.body.display_name ? req.body.display_name : (`${tempFirstName} ${tempLastName}`),
        active: req.body.active,
        verified: req.body.verified,
      },
      { new: true },
      (err, user) => {
        if (err) {
          return res.status(400).send("Something went wrong!");
        } else {
          manageUserLog(user._id);
          return res.status(200).send(user);
        }
      }
    );
  } catch (e) {
    return res.status(400).send("Something went wrong!");
  }
};

exports.deactivateuser = async (req, res) => {
  try {
    const user = await airtable_sync.findById(req.body.id, { isDelete: false });
    if (!user) return res.status(400).send("User not found.");

    airtable_sync.findByIdAndUpdate(
      req.body.id,
      {
        isDelete: true,
        firebaseId: "",
        facebookLinkedinId: "",
        socialauth0id: "",
      },
      { new: true },
      (err, user) => {
        if (err) return res.status(400).send("Something went wrong!");
        else {
          return res.status(200).send("successfully deactivated user");
        }
      }
    );
  } catch (e) {
    res.status(400).send("Something went wrong!");
  }
};

exports.blockuser = async (req, res) => {
  try {
    const user = await airtable_sync.findById(req.body.id, { isDelete: false });
    if (!user) return res.status(400).send("User not found.");

    airtable_sync.findByIdAndUpdate(
      req.body.id,
      { blocked: true },
      { new: true },
      (err, user) => {
        if (err) res.status(400).send("something went wrong!");
        else res.status(200).send("successfully blocked user!");
      }
    );
  } catch (e) {
    res.status(400).send(e);
  }
};

exports.unblockuser = async (req, res) => {
  try {
    const user = await airtable_sync.findById(req.body.id, { isDelete: false });
    if (!user) return res.status(400).json({ status: false, message: 'User not found.' });
    airtable_sync.findByIdAndUpdate(
      req.body.id,
      { blocked: false },
      { new: true },
      (err, user) => {
        if (err) res.status(400).send(err);
        else res.status(200).json({ status: true, message: 'Successfully unblocked user!' });
      }
    );
  } catch (e) {
    res.status(400).json({ status: false, error: e });
  }
};

exports.deleteuser = async (req, res) => {
  try {
    const user = await airtable_sync.findById(req.body.id, { isDelete: false });
    if (!user) return res.status(400).send("User not found.");
    airtable_sync.findByIdAndDelete(req.body.id, (err, user) => {
      if (err) res.status(400).send(err);
      else res.status(200).send("Successfully deleted!");
    });
  } catch (e) {
    res.status(400).send(e);
  }
};

/** --------------------------------
 * API created by ZP
 */

exports.getUserbyId = async (req, res) => {
  try {
    const userObj = await airtable_sync.findById(req.authUserId, {
      isDelete: false,
    });
    let userData = userObj.toObject();
    delete userData.questions;

    const userChat = await chatUser.findOne({
      userid: new ObjectId(userData._id),
    });

    if (userChat !== null) {
      userData.onlineStatus = userChat.online;
    } else {
      userData.onlineStatus = false;
    }

    let totalNoOfTeamMate = userData.no_of_team_mate
      ? userData.no_of_team_mate
      : 0;
    let totalNoOfRevokedInvite = 0;
    let totalNoOfAcceptedInvite = 0;
    let totalNoOfPendingInvite = 0;
    let totalNoOfAvailableInvite = 0;
    if (userData.purchased_plan) {
      const existCollaboratorCota = await inviteCollaborator
        .find(
          {
            "memberShipPlanDetails.planId": new ObjectId(
              userData.purchased_plan
            ),
            "sharedUserDetails.userId": new ObjectId(userData._id),
            isDelete: false,
          },
          { _id: 1, email: 1, teamMateInvitationStatus: 1 }
        )
        .lean();
      for (let i = 0; i < existCollaboratorCota.length; i++) {
        if (existCollaboratorCota[i]["teamMateInvitationStatus"] == "pending") {
          totalNoOfPendingInvite = totalNoOfPendingInvite + 1;
        } else if (
          existCollaboratorCota[i]["teamMateInvitationStatus"] == "accepted"
        ) {
          totalNoOfAcceptedInvite = totalNoOfAcceptedInvite + 1;
        } else if (
          existCollaboratorCota[i]["teamMateInvitationStatus"] == "revoked"
        ) {
          totalNoOfRevokedInvite = totalNoOfRevokedInvite + 1;
        }
      }
      let planDeteails = await MembershipPlan.findOne(
        { _id: userData.purchased_plan, isDelete: false },
        { _id: 1, isTeamMate: 1, no_of_team_mate: 1 }
      );
      if (planDeteails !== undefined && planDeteails !== null) {
        if (planDeteails.isTeamMate === true) {
          totalNoOfTeamMate =
            totalNoOfTeamMate + parseInt(planDeteails.no_of_team_mate);
        }
      }
    }
    userData.totalNoOfTeamMate = totalNoOfTeamMate;
    userData.totalNoOfRevokedInvite = totalNoOfRevokedInvite;
    userData.totalNoOfAcceptedInvite = totalNoOfAcceptedInvite;
    userData.totalNoOfPendingInvite = totalNoOfPendingInvite;
    userData.totalNoOfAvailableInvite =
      totalNoOfTeamMate - (totalNoOfPendingInvite + totalNoOfAcceptedInvite);

    userData.inviteCollaboratorDetail = {
      total: totalNoOfTeamMate,
      revoked: totalNoOfRevokedInvite,
      accepted: totalNoOfAcceptedInvite,
      pending: totalNoOfPendingInvite,
      available:
        totalNoOfTeamMate - (totalNoOfPendingInvite + totalNoOfAcceptedInvite),
    };
    return res
      .status(200)
      .json({ status: true, message: "User details.", data: userData });
  } catch (error) {
    return res
      .status(200)
      .json({ status: false, message: "smothing went wrong !!" });
  }
};

exports.editUserOwnProfile = async (req, res) => {
  try {
    const getUser = await airtable_sync.findOne({
      _id: req.authUserId,
      isDelete: false,
    });
    if (req.origi_profile || req.body.remove_profile) {
      getUser.profileImg &&
        (await s3
          .deleteObject({
            Bucket: process.env.AWS_BUCKET,
            Key: getUser.profileImg,
          })
          .promise());

    }
    var updateUserDetails = {
      email: req.body.email
        ? req.body.email.toLowerCase()
        : getUser.email
          ? getUser.email.toLowerCase()
          : getUser.email,
      profileImg: req.origi_profile
        ? req.origi_profile
        : req.body.remove_profile === undefined
          ? ""
          : getUser.profileImg,
      first_name: req.body.first_name
        ? req.body.first_name
        : getUser.first_name,
      last_name: req.body.last_name ? req.body.last_name : getUser.last_name,
      display_name: req.body.display_name ? req.body.display_name : getUser.display_name,
      "Preferred Email": req.body.email
        ? req.body.email
        : getUser["Preferred Email"],
    };

    const updateProfile = await airtable_sync.findOneAndUpdate(
      { _id: req.authUserId },
      { $set: updateUserDetails },
      { new: true }
    );
    if (!updateProfile) {
      return res
        .status(200)
        .json({ status: false, message: "Profile not updated!!" });
    } else {
      manageUserLog(req.authUserId);
      return res.status(200).json({
        status: true,
        message: "Profile updated successfully!",
        data: updateProfile,
      });
    }
  } catch (error) {
    return res.status(200).json({ status: false, message: "error," + error });
  }
};

exports.editUserProfilebyAdmin = async (req, res) => {
  try {
    if (req.body.email) {
      return res.status(200).json({
        status: false,
        message: `you can not update user's email address.`,
      });
    }
    const getUser = await airtable_sync
      .findOne({
        _id: req.body.userId,
        isDelete: false,
      })
      .populate("purchased_plan");

    var updateUserDetails = {
      blocked: req.body.blocked ?? getUser.blocked,
      active: req.body.active ?? getUser.active,
      verified: req.body.verified ?? getUser.verified,
      migrate_user: req.body.migratedata ?? getUser.migrate_user,
      no_of_team_mate:
        req.body.no_of_team_mate !== undefined &&
          req.body.no_of_team_mate !== null
          ? req.body.no_of_team_mate
          : getUser.no_of_team_mate
            ? getUser.no_of_team_mate
            : 0,
    };

    if (req.body !== undefined && req.body !== null) {
      updateUserDetails = {
        ...updateUserDetails,
        "Preferred Email": req.body.email ?? getUser["Preferred Email"],
        "display_name": req.body.display_name ?? getUser["display_name"],
        "first_name": req.body.first_name ?? getUser["first_name"],
        "last_name": req.body.last_name ?? getUser["last_name"],
        "passcode": req.body.passcode ? req.body.passcode : getUser["passcode"],
        attendeeDetail: {
          email: req.body.attendeeDetail && req.body.attendeeDetail.email !== undefined ? req.body.attendeeDetail.email : "",
          firebaseId: req.body.attendeeDetail && req.body.attendeeDetail.firebaseId !== undefined ? req.body.attendeeDetail.firebaseId : "",
          title: getUser.attendeeDetail && getUser.attendeeDetail.title !== undefined ? getUser.attendeeDetail.title : "",
          name: req.body.first_name && req.body.last_name ? (req.body.first_name + ' ' + req.body.last_name) : getUser.attendeeDetail ? getUser.attendeeDetail.name : '',
          firstName: req.body.first_name !== undefined ? req.body.first_name : getUser.attendeeDetail ? getUser.attendeeDetail.firstName : '',
          lastName: req.body.last_name !== undefined ? req.body.last_name : getUser.attendeeDetail ? getUser.attendeeDetail.lastName : '',
          company: req.body.attendeeDetail && req.body.attendeeDetail.company !== undefined ? req.body.attendeeDetail.company : "",
          profession: req.body.attendeeDetail && req.body.attendeeDetail.profession !== undefined ? req.body.attendeeDetail.profession : "",
          phone: req.body.attendeeDetail && req.body.attendeeDetail.phone !== undefined ? req.body.attendeeDetail.phone : "",
          facebook: req.body.attendeeDetail && req.body.attendeeDetail.facebook !== undefined ? req.body.attendeeDetail.facebook : "",
          linkedin: req.body.attendeeDetail && req.body.attendeeDetail.linkedin !== undefined ? req.body.attendeeDetail.linkedin : "",
          description: getUser.attendeeDetail && getUser.attendeeDetail.description && getUser.attendeeDetail.description !== "" ? getUser.attendeeDetail.description : "",
          offer: getUser.attendeeDetail && getUser.attendeeDetail.offer && getUser.attendeeDetail.offer !== "" ? getUser.attendeeDetail.offer : "",
          contactPartnerName: getUser.contactPartnerName === undefined ? "" : getUser.contactPartnerName,
          evntData: req.body.evntData && req.body.evntData.length > 0 ? req.body.evntData : getUser.attendeeDetail && getUser.attendeeDetail.evntData ? getUser.attendeeDetail.evntData : [],
        },
      };
    }

    // check if this groups are already save or not
    const n_grp = req.body.accessible_groups;
    var accessibleGroups = [];
    if (n_grp.length > 0) {
      var temp = n_grp.map(async (id) => {
        const grp = await Group.findById(id).select("groupTitle");
        accessibleGroups.push(grp.groupTitle.trim());
        if (!grp)
          return res
            .status(200)
            .json({ status: false, message: `Group not found.` });
      });
      await Promise.all([...temp]);
      updateUserDetails.accessible_groups = n_grp;
      updateUserDetails["Chapter Affiliation"] = accessibleGroups;
    } else if (n_grp.length === 0) {
      updateUserDetails.accessible_groups = [];
    }
    const updateProfile = await airtable_sync.findByIdAndUpdate(
      req.body.userId,
      updateUserDetails,
      {
        new: true,
      }
    );

    if (!updateProfile) {
      return res
        .status(200)
        .json({ status: false, message: "Profile not updated!!" });
    } else {
      manageUserLog(req.admin_Id);
      return res.status(200).json({
        status: true,
        message: "Profile updated successfully!",
        data: updateProfile,
      });
    }
  } catch (error) {
    return res.status(200).json({ status: false, message: "error," + error });
  }
};

exports.editUserProfileImagebyAdmin = async (req, res) => {
  try {
    const getUser = await airtable_sync.findOne({
      _id: ObjectId(req.body.userId),
      // isDelete: false,
      $or: [{ isDelete: false }, { isDelete: { $exists: false } }],
    });

    if (req.origi_profile || req.body.remove_profile) {
      getUser.profileImg &&
        (await s3
          .deleteObject({
            Bucket: process.env.AWS_BUCKET,
            Key: getUser.profileImg,
          })
          .promise());
    }
    if (req.partnerIcon) {
      getUser.partnerIcon &&
        (await s3
          .deleteObject({
            Bucket: process.env.AWS_BUCKET,
            Key: getUser.partnerIcon,
          })
          .promise());
    }
    var data = {};

    data = {
      profileImg: req.origi_profile
        ? req.origi_profile
        : req.body.remove_profile
          ? ""
          : getUser.profileImg,
      partnerIcon: req.partnerIcon ?? getUser.partnerIcon,
    };

    const updateProfile = await airtable_sync.findByIdAndUpdate(
      req.body.userId,
      data,
      { new: true }
    );
    if (!updateProfile) {
      return res
        .status(200)
        .json({ status: false, message: "Profile Image not updated!!" });
    } else {
      manageUserLog(req.admin_Id);
      return res.status(200).json({
        status: true,
        message: "Profile Image updated successfully!",
        data: updateProfile,
      });
    }
  } catch (error) {
    return res.status(200).json({ status: false, message: "error," + error });
  }
};

exports.editAttendeeProfilebyAdmin = async (req, res) => {
  try {
    const getUser = await airtable_sync
      .findOne({
        _id: req.body.userId,
        $or: [{ isDelete: false }, { isDelete: { $exists: false } }],
      })
      .lean();

    if (getUser) {
      if (req.origi_profile) {
        getUser.profileImg &&
          (await s3
            .deleteObject({
              Bucket: process.env.AWS_BUCKET,
              Key: getUser.profileImg,
            })
            .promise());
      }
      if (req.partnerIcon) {
        getUser.partnerIcon &&
          (await s3
            .deleteObject({
              Bucket: process.env.AWS_BUCKET,
              Key: getUser.partnerIcon,
            })
            .promise());
      }
      const editProfileObject = {
        attendeeDetail: JSON.parse(req.body.attendeeDetail)
          ? {
            ...getUser.attendeeDetail,
            ...JSON.parse(req.body.attendeeDetail),
          }
          : getUser.attendeeDetail,
        passcode: req.body.passcode ?? getUser.passcode,
        profileImg: req.origi_profile ?? getUser.profileImg,
        partnerIcon: req.partnerIcon
          ? req.partnerIcon
          : req.body.partner ?? getUser.partnerIcon,
        first_name: req.body.first_name ?? getUser.first_name,
        last_name: req.body.last_name ?? getUser.last_name,
         display_name: req.body.display_name ?? getUser.display_name,
      };

      const updateProfile = await airtable_sync.findByIdAndUpdate(
        req.body.userId,
        editProfileObject,
        { new: true }
      );
      if (!updateProfile) {
        return res
          .status(200)
          .json({ status: false, message: "Profile not updated!!" });
      } else {
        return res.status(200).json({
          status: true,
          message: "Profile updated successfully!",
          data: updateProfile,
        });
      }
    } else {
      return res
        .status(200)
        .json({ status: false, message: "Attendee not found!" });
    }
  } catch (error) {
    return res.status(200).json({ status: false, message: "error," + error });
  }
};

// save user profile image to AWs S3 bucket
exports.saveSIgnupQuestionsFiles = async (req, res) => {
  return res.status(200).json({
    status: true,
    message: "Questions files save in S3",
    data: req.questions_file,
  });
};

exports.deleteSignUpQuestionFiles = async (req, res) => {
  try {
    const files = req.body.multi_question_files;
    if (files.length > 0) {
      var temp = [];
      temp = files.map(async (file) => {
        await s3
          .deleteObject({
            Bucket: process.env.AWS_BUCKET,
            Key: file,
          })
          .promise();
      });
      await Promise.all([...temp]);
      return res.status(200).json({
        status: true,
        message: "Files deleted from S3 successfully.`",
      });
    } else {
      return res
        .status(200)
        .json({ status: false, message: "Empty files in body." });
    }
  } catch (error) {
    return res.status(200).json({ status: false, message: error.message });
  }
};

// storing user auth0 token into user collection after login
exports.storeUserToken = async (req, res) => {
  try {
    const { userId, idtoken } = req.body;
    if (!req.body)
      return res
        .status(200)
        .json({ status: false, message: "Provide proper user details." });

    const userData = await airtable_sync.findById(userId, { isDelete: false });
    if (!userData)
      return res
        .status(200)
        .json({ status: false, message: "User not found." });

    const updateData = await airtable_sync.findOneAndUpdate(
      { _id: userId },
      { $set: { token: idtoken } },
      { new: true }
    );
    if (!updateData)
      return res
        .status(200)
        .json({ status: false, message: "User token not save." });

    return res
      .status(200)
      .json({ status: true, message: "Token save.", data: updateData });
  } catch (error) {
    return res.status(200).json({ status: false, message: error.message });
  }
};

// get inkedin auth user email address
exports.getlinkedinuserdetails = async (req, res) => {
  var reqAuthCode = req.body.code;
  var reqCallbackUrl = req.body.redirect_uri;

  var data = {
    grant_type: "authorization_code",
    code: reqAuthCode,
    redirect_uri: reqCallbackUrl,
    client_id: "77vqx0j4jty35b",
    client_secret: "4CSIdc6Ra3qcJIY8",
  };

  request
    .post("https://www.linkedin.com/oauth/v2/accessToken")
    .send(data)
    .set("Content-Type", "application/x-www-form-urlencoded")
    .accept("application/json")
    .end(function (err, resp) {
      const accessToken = resp.body.access_token;
      if (accessToken) {
        request
          .get(
            "https://api.linkedin.com/v2/emailAddress?q=members&projection=(elements*(handle~))"
          )
          .set("Authorization", `Bearer ${accessToken}`)
          .end(function (err, resp) {
            const dataFormat = resp.text;
            const linkedinData = JSON.parse(dataFormat);

            return res.status(200).json({
              status: true,
              message: "Linkedin user email address.",
              data: linkedinData.elements[0]["handle~"].emailAddress,
            });
          });
      } else {
        return res
          .status(200)
          .json({ status: false, message: resp.body.error_description });
      }
    });
};

exports.getOtherMemberProfileForLoginUser = async (req, res) => {
  try {
    const { memberId } = req.params;
    const communityId = req.currentEdge.relation_id._id


    const memberProfile_obj = await airtable_sync.findOne({
      _id: memberId,
      isDelete: false,
    }).select({ first_name: 1, "Preferred Email": 1, last_name: 1, "WA Last Login Date": 1, last_login: 1, last_activity_log: 1,display_name:1,profileImg:1 });

    return res.status(200).json({
      status: true,
      message: "Member profile details.",
      data: memberProfile_obj,
    });

  } catch (error) {
    return res.status(200).json({ status: false, message: error.message });
  }
};

exports.getCommonGroupListwithMember = async (req, res) => {
  try {
    const { memberId } = req.params;
    const groupFollowbyMember_id = await GroupMember.find({
      userId: memberId,
      status: 2,
    }).select("groupId -_id");
    const groupFollowbyloginUser_id = await GroupMember.find({
      userId: req.authUserId,
      status: 2,
    }).select("groupId -_id");

    var commonGroup_ids = groupFollowbyMember_id.filter((id1) =>
      groupFollowbyloginUser_id.some(
        (id2) => id1.groupId.toString() === id2.groupId.toString()
      )
    );

    if (commonGroup_ids.length > 0) {
      var commonGroup_list = [];
      const temp = commonGroup_ids.map(async (item) => {
        const result = await Group.findById(item.groupId).select("-__v");
        const group_member_data = await GroupMember.findOne({
          userId: req.authUserId,
          groupId: item.groupId,
          status: 2,
        }).select("groupId -_id updatedAt");
        result["member_updatedAt"] = group_member_data.updatedAt;
        commonGroup_list.push(result);
      });
      await Promise.all([...temp]);
      return res.status(200).json({
        status: true,
        message: "Common group list.",
        data: commonGroup_list,
      });
    } else {
      return res.status(200).json({
        status: false,
        message: "Cann't found common group.",
        data: [],
      });
    }
  } catch (error) {
    return res.status(200).json({ status: false, message: error.message });
  }
};

exports.memberJoinGroupList = async (req, res) => {
  try {
    const { memberId } = req.params;
    const groupFollowbyMember_id = await GroupMember.find({
      userId: memberId,
      status: 2,
    }).select("groupId -_id");
    const groupFollowbyloginUser_id = await GroupMember.find({
      userId: req.authUserId,
      status: 2,
    }).select("groupId -_id");

    const join_group_ids = groupFollowbyMember_id.filter((el) => {
      return !groupFollowbyloginUser_id.find((element) => {
        return element.groupId.toString() === el.groupId.toString();
      });
    });

    if (join_group_ids.length > 0) {
      var group_list = [];
      const temp = join_group_ids.map(async (item) => {
        const result = await Group.findById(item.groupId).select("-__v");
        group_list.push(result);
      });
      await Promise.all([...temp]);
      return res
        .status(200)
        .json({ status: true, message: "Group list.", data: group_list });
    } else {
      return res
        .status(200)
        .json({ status: false, message: "No group found.", data: [] });
    }
  } catch (error) {
    return res.status(200).json({ status: false, message: error.message });
  }
};

exports.getPosts_onlyPostedByGroupMember_forOtherMemberProfile = async (
  req,
  res
) => {
  try {
    const { page, limit } = req.query;
    const { memberId } = req.params;
    const groupFollowbyMember_id = await GroupMember.find({
      userId: memberId,
      status: 2,
    }).select("groupId -_id");

    var posts = [];
    const temp = groupFollowbyMember_id.map(async (item) => {
      const result = await Post.find({
        postedBy: memberId,
        groupId: item.groupId,
        postStatus: "Public",
      })
        .populate("groupId", "groupTitle")
        .sort({ updatedAt: -1 });

      if (result.length > 0) posts.push(result);
    });

    await Promise.all([...temp]);
    return res
      .status(200)
      .json({ status: true, message: "Member all posts list.", data: posts });
  } catch (error) {
    return res.status(200).json({ status: false, message: error.message });
  }
};

exports.verifyUserIDtoken = async (req, res) => {
  try {
    return res.status(200).json({
      status: true,
      message: "To verify idtoken",
      data: { userId: req.authUserId, role: req.userRoles },
    });
  } catch (error) {
    return res.status(200).json({ status: false, message: error.message });
  }
};

// last user login
exports.manageUserlastLogin = async (req, res) => {
  try {
    const { authUserId } = req;
    const userdata = await airtable_sync.findById(authUserId, {
      isDelete: false,
    });
    if (!userdata)
      return res
        .status(200)
        .json({ status: false, message: "User not found." });

    if (userdata.migrate_user_status) {
      const newmetadata = req.body.newmetadata ?? userdata.migrate_user;
      const result = await airtable_sync.findByIdAndUpdate(
        authUserId,
        {
          $set: {
            last_login: Date.now(),
            migrate_user: newmetadata,
            webDeviceToken: [],
          },
        },
        { new: true }
      );
      return res
        .status(200)
        .json({ status: true, message: "Update user login timestamp." });
    } else {
      await airtable_sync.findByIdAndUpdate(authUserId, {
        $set: { last_login: Date.now(), webDeviceToken: [] },
      });
      return res
        .status(200)
        .json({ status: true, message: "Update user login timestamp." });
    }
  } catch (error) {
    return res.status(200).json({ status: false, message: error.message });
  }
};

// delete all user from auth0 and from mongo db users collections EXCEPT ADMIN
exports.deleteAllUsersExceptAdmin = async (req, res) => {
  try {
    await User.remove({ _id: { $ne: "6298419baba3e700705075c9" } });
    return res.status(200).json({
      status: true,
      message: "All users deleted successfully except admin.",
    });
  } catch (error) {
    return res.status(200).json({ status: false, message: error.message });
  }
};

// delete all media files/folders/images/videos  from S3 buckets
exports.deleteFoldersfromS3bucket = async (req, res) => {
  try {
    await emptyBucket(process.env.AWS_BUCKET);
    return res
      .status(200)
      .json({ status: true, message: "Deleted media all." });
  } catch (error) {
    return res.status(200).json({ status: false, message: error.message });
  }
};

async function emptyBucket(bucketName) {
  let currentData;
  let params = {
    Bucket: bucketName,
    Prefix: "uploads/",
  };

  return s3
    .listObjects(params)
    .promise()
    .then((data) => {
      if (data.Contents.length === 0) {
        throw new Error("List of media in this bucket is empty.");
      }

      currentData = data;

      params = { Bucket: bucketName };
      params.Delete = { Objects: [] };

      currentData.Contents.forEach((content) => {
        params.Delete.Objects.push({ Key: content.Key });
      });

      return s3.deleteObjects(params).promise();
    })
    .then(() => {
      if (currentData.Contents.length === 1000) {
        emptyBucket(bucketName, callback);
      } else {
        return true;
      }
    });
}

exports.activeUserStatusBYadmin = async (req, res) => {
  try {
    const { userId } = req.body;
    const data = await airtable_sync
      .findById(userId, { isDelete: false })
      .select("active role");
    if (!data)
      return res.status(200).json({
        status: false,
        message: "User not found!",
      });
    // if (data.role !== 'user') return res.status(200).json({ status: true, message: "You cann't active/deactive who's role is not user." })
    const result = await airtable_sync
      .findByIdAndUpdate(userId, { active: !data.active }, { new: true })
      .select("email active");
    manageUserLog(req.admin_Id);
    return res.status(200).json({
      status: true,
      message: "User status has been changed!",
      data: result,
    });
  } catch (error) {
    return res.status(200).json({ status: false, message: error.message });
  }
};

exports.getallusersname_email_user = async (req, res) => {
  try {
    const { authUserId } = req;
    const data = await airtable_sync.findById(authUserId);
    const alluser = await airtable_sync
      .find({
        email: { $ne: data.email.toLowerCase() },
        register_status: true,
        personalDetail_status: true,
        payment_status: true,
        QA_status: true,
        isDelete: false,
        firebaseId: { $nin: ["", null] },
      })
      .select("email first_name last_name display_name");
    return res.status(200).json({ status: true, data: alluser });
  } catch (error) {
    return res.status(200).json({ status: false, message: error.message });
  }
};

exports.getallusersname_email_admin = async (req, res) => {
  try {
    const alluser = await airtable_sync
      .find({
        register_status: true,
        personalDetail_status: true,
        payment_status: true,
        QA_status: true,
        isDelete: false,
        firebaseId: { $nin: ["", null] },
      })
      .select("email first_name last_name display_name");
    return res.status(200).json({ status: true, data: alluser });
  } catch (error) {
    return res.status(200).json({ status: false, message: error.message });
  }
};

exports.getconnectionid = async (req, res) => {
  try {
    const auth0token = await getAuth0Token();
    var options = {
      method: "GET",
      url: "https://dev-yc4k4-ud.us.auth0.com/api/v2/connections/con_dMoje8kFjEECk1zm",
      headers: {
        authorization: auth0token,
      },
    };
    axios
      .request(options)
      .then(function (response) {
        res.send(response.data);
      })
      .catch(function (error) {
        console.error(error);
      });
  } catch (e) { }
};

exports.checkjobstatus = async (req, res) => {
  try {
    const auth0token = await getAuth0Token();
    var options = {
      method: "GET",
      url: "https://dev-yc4k4-ud.us.auth0.com/api/v2/jobs/job_xYtwVXLZyZDzk7Fv",
      headers: {
        "content-type": "application/json",
        authorization: auth0token,
      },
    };

    axios
      .request(options)
      .then(function (response) {
        res.send(response);
      })
      .catch(function (error) {
        console.error(error);
      });
  } catch (e) {
    res.send(e);
  }
};

exports.deletemigrateduser = async (req, res) => {
  try {
    const user_data = await airtable_sync.findOneAndDelete({
      email: req.params.email.toLowerCase(),
    });
    if (user_data)
      return res
        .status(200)
        .json({ status: true, Message: "User deleted successfully!" });
    else
      return res
        .status(200)
        .json({ status: false, Message: "User not deleted!", user_data });
  } catch (e) {
    console.log(e);
    res.status(200).json({ status: false, message: "Something went wrong!" });
  }
};

exports.deleteauthuser = async (req, res) => {
  try {
    await getAuth0Token()
      .then(async (token) => {
        var options = {
          method: "DELETE",
          url: AUDIENCE + "users/" + req.params.userid,
          headers: {
            "content-type": "application/json",
            authorization: token,
            "cache-control": "no-cache",
          },
        };
        axios
          .request(options)
          .then(async function (response) {
            res.status(200).json({ data: response });
          })
          .catch(function (error) {
            return res.status(200).json({
              status: false,
              message: `Something wrong while deleting user. ${error.message}`,
            });
          });
      })
      .catch(function (error) {
        return res.status(200).json({
          status: false,
          message: `Something wrong in user token. ${error.message}`,
        });
      });
  } catch (e) {
    console.log(e);
    res.status(200).json({ status: false, message: "Something went wrong!" });
  }
};

exports.updatemigrateduserinfo = async (req, res) => {
  try {
    var plan_data = await MembershipPlan.findOne({
      isDelete: false,
      auth0_plan_id: req.body.migrate_user.plan_id,
    });

    if (!plan_data) {
      return res
        .status(200)
        .json({ status: false, message: "Plan not found!" });
    }
    var resources = await plan_resource.findById(plan_data.plan_resource);

    await getAuth0Token().then(async (token) => {
      axios
        .post(
          AUDIENCE + "users/" + req.body.AuthUserId + "/identities",
          {
            provider: req.body.provider,
            user_id: req.body.AuthUserId2,
          },
          {
            headers: {
              authorization: token,
            },
          }
        )
        .then(async function (response) {
          const communityName = req.currentEdge.relation_id.name || "";
          let communityId =  req.currentEdge.relation_id?._id ? ObjectId(req.currentEdge.relation_id?._id) : "";
          let customSMTP =  req.currentEdge.relation_id?.customSMTP ? req.currentEdge.relation_id?.customSMTP : false;
          const mail_data = {
            email: req.body.email,
            subject: `Welcome! You've Successfully Created an Account`,
            html: `<html xmlns:v="urn:schemas-microsoft-com:vml" xmlns:o="urn:schemas-microsoft-com:office:office" lang="en">
                        
                        <head>
                            <title></title>
                            <meta http-equiv="Content-Type" content="text/html; charset=utf-8">
                            <meta name="viewport" content="width=device-width, initial-scale=1.0">
                            <style>
                                * {
                                    box-sizing: border-box;
                                }
                        
                                body {
                                    margin: 0;
                                    padding: 0;
                                }
                        
                                a[x-apple-data-detectors] {
                                    color: inherit !important;
                                    text-decoration: inherit !important;
                                }
                        
                                #MessageViewBody a {
                                    color: inherit;
                                    text-decoration: none;
                                }
                        
                                p {
                                    line-height: inherit
                                }
                        
                                .desktop_hide,
                                .desktop_hide table {
                                    mso-hide: all;
                                    display: none;
                                    max-height: 0px;
                                    overflow: hidden;
                                }
                        
                                .image_block img+div {
                                    display: none;
                                }
                        
                                @media (max-width:620px) {
                        
                                    .fullMobileWidth,
                                    .image_block img.big,
                                    .row-content {
                                        width: 100% !important;
                                    }
                        
                                    .mobile_hide {
                                        display: none;
                                    }
                        
                                    .stack .column {
                                        width: 100%;
                                        display: block;
                                    }
                        
                                    .mobile_hide {
                                        min-height: 0;
                                        max-height: 0;
                                        max-width: 0;
                                        overflow: hidden;
                                        font-size: 0px;
                                    }
                        
                                    .desktop_hide,
                                    .desktop_hide table {
                                        display: table !important;
                                        max-height: none !important;
                                    }
                                }
                            </style>
                        </head>
                        
                        <body style="background-color: #ffffff; margin: 0; padding: 0; -webkit-text-size-adjust: none; text-size-adjust: none;">
                            <table class="nl-container" width="100%" border="0" cellpadding="0" cellspacing="0" role="presentation" style="mso-table-lspace: 0pt; mso-table-rspace: 0pt; background-color: #ffffff;">
                                <tbody>
                                    <tr>
                                        <td>
                                            <table class="row row-1" align="center" width="100%" border="0" cellpadding="0" cellspacing="0" role="presentation" style="mso-table-lspace: 0pt; mso-table-rspace: 0pt;">
                                                <tbody>
                                                    <tr>
                                                        <td>
                                                            <table class="row-content stack" align="center" border="0" cellpadding="0" cellspacing="0" role="presentation" style="mso-table-lspace: 0pt; mso-table-rspace: 0pt; border-radius: 0; color: #000000; width: 600px;" width="600">
                                                                <tbody>
                                                                    <tr>
                                                                        <td class="column column-1" width="100%" style="mso-table-lspace: 0pt; mso-table-rspace: 0pt; font-weight: 400; text-align: left; vertical-align: top; border-top: 0px; border-right: 0px; border-bottom: 0px; border-left: 0px;">
                                                                            <table class="image_block block-1 mobile_hide" width="100%" border="0" cellpadding="0" cellspacing="0" role="presentation" style="mso-table-lspace: 0pt; mso-table-rspace: 0pt;">
                                                                                <tr>
                                                                                    <td class="pad" style="padding-bottom:10px;padding-left:20px;padding-top:10px;width:100%;padding-right:0px;">
                                                                                        <div class="alignment" align="left" style="line-height:10px"><img class="fullMobileWidth" src="https://d15k2d11r6t6rl.cloudfront.net/public/users/Integrators/BeeProAgency/974514_959141/Mds%20Grey%20wide.png" style="display: block; height: auto; border: 0; width: 330px; max-width: 100%;" width="330"></div>
                                                                                    </td>
                                                                                </tr>
                                                                            </table>
                                                                            <table class="image_block block-2 desktop_hide" width="100%" border="0" cellpadding="0" cellspacing="0" role="presentation" style="mso-table-lspace: 0pt; mso-table-rspace: 0pt; mso-hide: all; display: none; max-height: 0; overflow: hidden;">
                                                                                <tr>
                                                                                    <td class="pad" style="padding-bottom:10px;padding-left:20px;padding-top:10px;width:100%;padding-right:0px;">
                                                                                        <div class="alignment" align="left" style="line-height:10px"><img src="https://d15k2d11r6t6rl.cloudfront.net/public/users/Integrators/BeeProAgency/974514_959141/Mds%20Grey%20wide.png" style="display: block; height: auto; border: 0; width: 240px; max-width: 100%;" width="240"></div>
                                                                                    </td>
                                                                                </tr>
                                                                            </table>
                                                                        </td>
                                                                    </tr>
                                                                </tbody>
                                                            </table>
                                                        </td>
                                                    </tr>
                                                </tbody>
                                            </table>
                                            <table class="row row-2" align="center" width="100%" border="0" cellpadding="0" cellspacing="0" role="presentation" style="mso-table-lspace: 0pt; mso-table-rspace: 0pt;">
                                                <tbody>
                                                    <tr>
                                                        <td>
                                                            <table class="row-content stack" align="center" border="0" cellpadding="0" cellspacing="0" role="presentation" style="mso-table-lspace: 0pt; mso-table-rspace: 0pt; border-radius: 0; color: #000000; width: 600px;" width="600">
                                                                <tbody>
                                                                    <tr>
                                                                        <td class="column column-1" width="100%" style="mso-table-lspace: 0pt; mso-table-rspace: 0pt; font-weight: 400; text-align: left; vertical-align: top; border-top: 0px; border-right: 0px; border-bottom: 0px; border-left: 0px;">
                                                                            <table class="heading_block block-1" width="100%" border="0" cellpadding="20" cellspacing="0" role="presentation" style="mso-table-lspace: 0pt; mso-table-rspace: 0pt;">
                                                                                <tr>
                                                                                    <td class="pad">
                                                                                        <h3 style="margin: 0; color: #000000; direction: ltr; font-family: Arial, 'Helvetica Neue', Helvetica, sans-serif; font-size: 30px; font-weight: 400; letter-spacing: -1px; line-height: 120%; text-align: left; margin-top: 0; margin-bottom: 0;">Congratulations on successfully creating an account with Million Dollar Sellers</h3>
                                                                                    </td>
                                                                                </tr>
                                                                            </table>
                                                                            <table class="image_block block-2" width="100%" border="0" cellpadding="0" cellspacing="0" role="presentation" style="mso-table-lspace: 0pt; mso-table-rspace: 0pt;">
                                                                                <tr>
                                                                                    <td class="pad" style="width:100%;padding-right:0px;padding-left:0px;">
                                                                                        <div class="alignment" align="center" style="line-height:10px"><img class="big" src="https://d15k2d11r6t6rl.cloudfront.net/public/users/Integrators/BeeProAgency/974514_959141/mds-app-announcement-rev2_v5_1.png" style="display: block; height: auto; border: 0; width: 600px; max-width: 100%;" width="600"></div>
                                                                                    </td>
                                                                                </tr>
                                                                            </table>
                                                                        </td>
                                                                    </tr>
                                                                </tbody>
                                                            </table>
                                                        </td>
                                                    </tr>
                                                </tbody>
                                            </table>
                                            <table class="row row-3" align="center" width="100%" border="0" cellpadding="0" cellspacing="0" role="presentation" style="mso-table-lspace: 0pt; mso-table-rspace: 0pt;">
                                                <tbody>
                                                    <tr>
                                                        <td>
                                                            <table class="row-content stack" align="center" border="0" cellpadding="0" cellspacing="0" role="presentation" style="mso-table-lspace: 0pt; mso-table-rspace: 0pt; color: #000000; width: 600px;" width="600">
                                                                <tbody>
                                                                    <tr>
                                                                        <td class="column column-1" width="100%" style="mso-table-lspace: 0pt; mso-table-rspace: 0pt; font-weight: 400; text-align: left; padding-bottom: 5px; padding-top: 5px; vertical-align: top; border-top: 0px; border-right: 0px; border-bottom: 0px; border-left: 0px;">
                                                                            <table class="paragraph_block block-1" width="100%" border="0" cellpadding="0" cellspacing="0" role="presentation" style="mso-table-lspace: 0pt; mso-table-rspace: 0pt; word-break: break-word;">
                                                                                <tr>
                                                                                    <td class="pad" style="padding-bottom:25px;padding-left:20px;padding-right:20px;">
                                                                                        <div style="color:#232c3d;direction:ltr;font-family:Arial, 'Helvetica Neue', Helvetica, sans-serif;font-size:16px;font-weight:400;letter-spacing:0px;line-height:150%;text-align:left;mso-line-height-alt:24px;">
                                                                                            <p style="margin: 0;">We would like to suggest that you try our mobile app for an even better experience with our platform</p>
                                                                                        </div>
                                                                                    </td>
                                                                                </tr>
                                                                            </table>
                                                                        </td>
                                                                    </tr>
                                                                </tbody>
                                                            </table>
                                                        </td>
                                                    </tr>
                                                </tbody>
                                            </table>
                                            <table class="row row-4 mobile_hide" align="center" width="100%" border="0" cellpadding="0" cellspacing="0" role="presentation" style="mso-table-lspace: 0pt; mso-table-rspace: 0pt;">
                                                <tbody>
                                                    <tr>
                                                        <td>
                                                            <table class="row-content stack" align="center" border="0" cellpadding="0" cellspacing="0" role="presentation" style="mso-table-lspace: 0pt; mso-table-rspace: 0pt; border-radius: 0; color: #000000; width: 600px;" width="600">
                                                                <tbody>
                                                                    <tr>
                                                                        <td class="column column-1" width="33.333333333333336%" style="mso-table-lspace: 0pt; mso-table-rspace: 0pt; font-weight: 400; text-align: left; background-color: #f7f9fb; padding-bottom: 5px; padding-top: 5px; vertical-align: middle; border-top: 0px; border-right: 0px; border-bottom: 0px; border-left: 0px;">
                                                                            <table class="image_block block-1" width="100%" border="0" cellpadding="10" cellspacing="0" role="presentation" style="mso-table-lspace: 0pt; mso-table-rspace: 0pt;">
                                                                                <tr>
                                                                                    <td class="pad">
                                                                                        <div class="alignment" align="center" style="line-height:10px"><img src="https://d15k2d11r6t6rl.cloudfront.net/public/users/Integrators/BeeProAgency/974514_959141/app-logo-mockArtboard-1.png" style="display: block; height: auto; border: 0; width: 180px; max-width: 100%;" width="180"></div>
                                                                                    </td>
                                                                                </tr>
                                                                            </table>
                                                                        </td>
                                                                        <td class="column column-2" width="66.66666666666667%" style="mso-table-lspace: 0pt; mso-table-rspace: 0pt; font-weight: 400; text-align: left; background-color: #f7f9fb; padding-bottom: 5px; padding-top: 5px; vertical-align: middle; border-top: 0px; border-right: 0px; border-bottom: 0px; border-left: 0px;">
                                                                            <table class="heading_block block-1" width="100%" border="0" cellpadding="0" cellspacing="0" role="presentation" style="mso-table-lspace: 0pt; mso-table-rspace: 0pt;">
                                                                                <tr>
                                                                                    <td class="pad" style="padding-left:20px;padding-right:20px;text-align:center;width:100%;">
                                                                                        <h2 style="margin: 0; color: #232c3d; direction: ltr; font-family: Arial, Helvetica, sans-serif; font-size: 18px; font-weight: 700; letter-spacing: normal; line-height: 120%; text-align: left; margin-top: 0; margin-bottom: 0;"><span class="tinyMce-placeholder">Download the App</span></h2>
                                                                                    </td>
                                                                                </tr>
                                                                            </table>
                                                                            <table class="paragraph_block block-2" width="100%" border="0" cellpadding="0" cellspacing="0" role="presentation" style="mso-table-lspace: 0pt; mso-table-rspace: 0pt; word-break: break-word;">
                                                                                <tr>
                                                                                    <td class="pad" style="padding-bottom:10px;padding-left:20px;padding-right:20px;padding-top:10px;">
                                                                                        <div style="color:#232c3d;direction:ltr;font-family:Arial, Helvetica, sans-serif;font-size:16px;font-weight:400;letter-spacing:0px;line-height:150%;text-align:left;mso-line-height-alt:24px;">
                                                                                            <p style="margin: 0;">Download the MDS app for&nbsp;<a href="https://apps.apple.com/app/id1636838955" rel="noopener" target="_blank" style="text-decoration: underline; color: #296bb7;"><strong>iOS</strong></a>&nbsp;or&nbsp;<a href="https://play.google.com/store/apps/details?id=com.app.mdscommunity" rel="noopener" target="_blank" style="text-decoration: underline; color: #296bb7;"><strong>Android</strong></a>, or access the content on the website.</p>
                                                                                        </div>
                                                                                    </td>
                                                                                </tr>
                                                                            </table>
                                                                        </td>
                                                                    </tr>
                                                                </tbody>
                                                            </table>
                                                        </td>
                                                    </tr>
                                                </tbody>
                                            </table>
                                            <table class="row row-5 desktop_hide" align="center" width="100%" border="0" cellpadding="0" cellspacing="0" role="presentation" style="mso-table-lspace: 0pt; mso-table-rspace: 0pt; mso-hide: all; display: none; max-height: 0; overflow: hidden;">
                                                <tbody>
                                                    <tr>
                                                        <td>
                                                            <table class="row-content stack" align="center" border="0" cellpadding="0" cellspacing="0" role="presentation" style="mso-table-lspace: 0pt; mso-table-rspace: 0pt; mso-hide: all; display: none; max-height: 0; overflow: hidden; background-color: #f7f9fb; color: #000000; width: 600px;" width="600">
                                                                <tbody>
                                                                    <tr>
                                                                        <td class="column column-1" width="100%" style="mso-table-lspace: 0pt; mso-table-rspace: 0pt; font-weight: 400; text-align: left; padding-bottom: 5px; padding-top: 5px; vertical-align: top; border-top: 0px; border-right: 0px; border-bottom: 0px; border-left: 0px;">
                                                                            <table class="image_block block-1" width="100%" border="0" cellpadding="10" cellspacing="0" role="presentation" style="mso-table-lspace: 0pt; mso-table-rspace: 0pt; mso-hide: all; display: none; max-height: 0; overflow: hidden;">
                                                                                <tr>
                                                                                    <td class="pad">
                                                                                        <div class="alignment" align="center" style="line-height:10px"><img src="https://d15k2d11r6t6rl.cloudfront.net/public/users/Integrators/BeeProAgency/974514_959141/app-logo-mockArtboard-1.png" style="display: block; height: auto; border: 0; width: 180px; max-width: 100%;" width="180"></div>
                                                                                    </td>
                                                                                </tr>
                                                                            </table>
                                                                            <table class="heading_block block-2" width="100%" border="0" cellpadding="0" cellspacing="0" role="presentation" style="mso-table-lspace: 0pt; mso-table-rspace: 0pt; mso-hide: all; display: none; max-height: 0; overflow: hidden;">
                                                                                <tr>
                                                                                    <td class="pad" style="padding-left:20px;padding-right:20px;text-align:center;width:100%;">
                                                                                        <h2 style="margin: 0; color: #232c3d; direction: ltr; font-family: Arial, Helvetica, sans-serif; font-size: 18px; font-weight: 700; letter-spacing: normal; line-height: 120%; text-align: left; margin-top: 0; margin-bottom: 0;"><span class="tinyMce-placeholder">Download the App</span></h2>
                                                                                    </td>
                                                                                </tr>
                                                                            </table>
                                                                            <table class="paragraph_block block-3" width="100%" border="0" cellpadding="0" cellspacing="0" role="presentation" style="mso-table-lspace: 0pt; mso-table-rspace: 0pt; word-break: break-word; mso-hide: all; display: none; max-height: 0; overflow: hidden;">
                                                                                <tr>
                                                                                    <td class="pad" style="padding-bottom:10px;padding-left:20px;padding-right:20px;padding-top:10px;">
                                                                                        <div style="color:#232c3d;direction:ltr;font-family:Arial, Helvetica, sans-serif;font-size:16px;font-weight:400;letter-spacing:0px;line-height:150%;text-align:left;mso-line-height-alt:24px;">
                                                                                            <p style="margin: 0;">Download the MDS app for&nbsp;<a href="https://apps.apple.com/app/id1636838955" rel="noopener" target="_blank" style="text-decoration: underline; color: #296bb7;"><strong>iOS</strong></a>&nbsp;or&nbsp;<a href="https://play.google.com/store/apps/details?id=com.app.mdscommunity" rel="noopener" target="_blank" style="text-decoration: underline; color: #296bb7;"><strong>Android</strong></a>, or access the content on the website.</p>
                                                                                        </div>
                                                                                    </td>
                                                                                </tr>
                                                                            </table>
                                                                        </td>
                                                                    </tr>
                                                                </tbody>
                                                            </table>
                                                        </td>
                                                    </tr>
                                                </tbody>
                                            </table>
                                            <table class="row row-6" align="center" width="100%" border="0" cellpadding="0" cellspacing="0" role="presentation" style="mso-table-lspace: 0pt; mso-table-rspace: 0pt;">
                                                <tbody>
                                                    <tr>
                                                        <td>
                                                            <table class="row-content stack" align="center" border="0" cellpadding="0" cellspacing="0" role="presentation" style="mso-table-lspace: 0pt; mso-table-rspace: 0pt; border-radius: 0; color: #000000; width: 600px;" width="600">
                                                                <tbody>
                                                                    <tr>
                                                                        <td class="column column-1" width="100%" style="mso-table-lspace: 0pt; mso-table-rspace: 0pt; font-weight: 400; text-align: left; padding-bottom: 5px; padding-top: 5px; vertical-align: top; border-top: 0px; border-right: 0px; border-bottom: 0px; border-left: 0px;">
                                                                            <div class="spacer_block block-1" style="height:15px;line-height:15px;font-size:1px;">&#8202;</div>
                                                                        </td>
                                                                    </tr>
                                                                </tbody>
                                                            </table>
                                                        </td>
                                                    </tr>
                                                </tbody>
                                            </table>
                                            <table class="row row-7 mobile_hide" align="center" width="100%" border="0" cellpadding="0" cellspacing="0" role="presentation" style="mso-table-lspace: 0pt; mso-table-rspace: 0pt;">
                                                <tbody>
                                                    <tr>
                                                        <td>
                                                            <table class="row-content stack" align="center" border="0" cellpadding="0" cellspacing="0" role="presentation" style="mso-table-lspace: 0pt; mso-table-rspace: 0pt; border-radius: 0; color: #000000; width: 600px;" width="600">
                                                                <tbody>
                                                                    <tr>
                                                                        <td class="column column-1" width="33.333333333333336%" style="mso-table-lspace: 0pt; mso-table-rspace: 0pt; font-weight: 400; text-align: left; background-color: #f7f9fb; padding-bottom: 5px; padding-top: 5px; vertical-align: middle; border-top: 0px; border-right: 0px; border-bottom: 0px; border-left: 0px;">
                                                                            <table class="image_block block-1" width="100%" border="0" cellpadding="10" cellspacing="0" role="presentation" style="mso-table-lspace: 0pt; mso-table-rspace: 0pt;">
                                                                                <tr>
                                                                                    <td class="pad">
                                                                                        <div class="alignment" align="center" style="line-height:10px"><img src="https://d15k2d11r6t6rl.cloudfront.net/public/users/Integrators/BeeProAgency/974514_959141/Group%201000001962_1.png" style="display: block; height: auto; border: 0; width: 180px; max-width: 100%;" width="180"></div>
                                                                                    </td>
                                                                                </tr>
                                                                            </table>
                                                                        </td>
                                                                        <td class="column column-2" width="66.66666666666667%" style="mso-table-lspace: 0pt; mso-table-rspace: 0pt; font-weight: 400; text-align: left; background-color: #f7f9fb; padding-bottom: 5px; padding-top: 5px; vertical-align: middle; border-top: 0px; border-right: 0px; border-bottom: 0px; border-left: 0px;">
                                                                            <table class="heading_block block-1" width="100%" border="0" cellpadding="0" cellspacing="0" role="presentation" style="mso-table-lspace: 0pt; mso-table-rspace: 0pt;">
                                                                                <tr>
                                                                                    <td class="pad" style="padding-left:20px;padding-right:20px;text-align:center;width:100%;">
                                                                                        <h2 style="margin: 0; color: #232c3d; direction: ltr; font-family: Arial, Helvetica, sans-serif; font-size: 18px; font-weight: 700; letter-spacing: normal; line-height: 120%; text-align: left; margin-top: 0; margin-bottom: 0;"><span class="tinyMce-placeholder">Need help?</span></h2>
                                                                                    </td>
                                                                                </tr>
                                                                            </table>
                                                                            <table class="paragraph_block block-2" width="100%" border="0" cellpadding="0" cellspacing="0" role="presentation" style="mso-table-lspace: 0pt; mso-table-rspace: 0pt; word-break: break-word;">
                                                                                <tr>
                                                                                    <td class="pad" style="padding-bottom:10px;padding-left:20px;padding-right:20px;padding-top:10px;">
                                                                                        <div style="color:#232c3d;direction:ltr;font-family:Arial, Helvetica, sans-serif;font-size:16px;font-weight:400;letter-spacing:0px;line-height:150%;text-align:left;mso-line-height-alt:24px;">
                                                                                            <p style="margin: 0;">Having issues? Drop them in the beta access <a href="https://m.me/j/AbZsGogZIqQohmgO/" target="_blank" rel="noopener" style="text-decoration: underline; color: #296bb7;"><strong>Facebook Chat ➔</strong></a></p>
                                                                                        </div>
                                                                                    </td>
                                                                                </tr>
                                                                            </table>
                                                                        </td>
                                                                    </tr>
                                                                </tbody>
                                                            </table>
                                                        </td>
                                                    </tr>
                                                </tbody>
                                            </table>
                                            <table class="row row-8 desktop_hide" align="center" width="100%" border="0" cellpadding="0" cellspacing="0" role="presentation" style="mso-table-lspace: 0pt; mso-table-rspace: 0pt; mso-hide: all; display: none; max-height: 0; overflow: hidden;">
                                                <tbody>
                                                    <tr>
                                                        <td>
                                                            <table class="row-content stack" align="center" border="0" cellpadding="0" cellspacing="0" role="presentation" style="mso-table-lspace: 0pt; mso-table-rspace: 0pt; mso-hide: all; display: none; max-height: 0; overflow: hidden; background-color: #f7f9fb; color: #000000; width: 600px;" width="600">
                                                                <tbody>
                                                                    <tr>
                                                                        <td class="column column-1" width="100%" style="mso-table-lspace: 0pt; mso-table-rspace: 0pt; font-weight: 400; text-align: left; padding-bottom: 5px; padding-top: 5px; vertical-align: top; border-top: 0px; border-right: 0px; border-bottom: 0px; border-left: 0px;">
                                                                            <table class="image_block block-1" width="100%" border="0" cellpadding="10" cellspacing="0" role="presentation" style="mso-table-lspace: 0pt; mso-table-rspace: 0pt; mso-hide: all; display: none; max-height: 0; overflow: hidden;">
                                                                                <tr>
                                                                                    <td class="pad">
                                                                                        <div class="alignment" align="center" style="line-height:10px"><img src="https://d15k2d11r6t6rl.cloudfront.net/public/users/Integrators/BeeProAgency/974514_959141/Group%201000001962_1.png" style="display: block; height: auto; border: 0; width: 180px; max-width: 100%;" width="180"></div>
                                                                                    </td>
                                                                                </tr>
                                                                            </table>
                                                                            <table class="heading_block block-2" width="100%" border="0" cellpadding="0" cellspacing="0" role="presentation" style="mso-table-lspace: 0pt; mso-table-rspace: 0pt; mso-hide: all; display: none; max-height: 0; overflow: hidden;">
                                                                                <tr>
                                                                                    <td class="pad" style="padding-left:20px;padding-right:20px;text-align:center;width:100%;">
                                                                                        <h2 style="margin: 0; color: #232c3d; direction: ltr; font-family: Arial, Helvetica, sans-serif; font-size: 18px; font-weight: 700; letter-spacing: normal; line-height: 120%; text-align: left; margin-top: 0; margin-bottom: 0;"><span class="tinyMce-placeholder">Need help?</span></h2>
                                                                                    </td>
                                                                                </tr>
                                                                            </table>
                                                                            <table class="paragraph_block block-3" width="100%" border="0" cellpadding="0" cellspacing="0" role="presentation" style="mso-table-lspace: 0pt; mso-table-rspace: 0pt; word-break: break-word; mso-hide: all; display: none; max-height: 0; overflow: hidden;">
                                                                                <tr>
                                                                                    <td class="pad" style="padding-bottom:10px;padding-left:20px;padding-right:20px;padding-top:10px;">
                                                                                        <div style="color:#232c3d;direction:ltr;font-family:Arial, Helvetica, sans-serif;font-size:16px;font-weight:400;letter-spacing:0px;line-height:150%;text-align:left;mso-line-height-alt:24px;">
                                                                                            <p style="margin: 0;">Having issues? Drop them in the beta access <a href="https://m.me/j/AbZsGogZIqQohmgO/" target="_blank" rel="noopener" style="text-decoration: underline; color: #296bb7;"><strong>Facebook Chat ➔</strong></a></p>
                                                                                        </div>
                                                                                    </td>
                                                                                </tr>
                                                                            </table>
                                                                        </td>
                                                                    </tr>
                                                                </tbody>
                                                            </table>
                                                        </td>
                                                    </tr>
                                                </tbody>
                                            </table>
                                            <table class="row row-9" align="center" width="100%" border="0" cellpadding="0" cellspacing="0" role="presentation" style="mso-table-lspace: 0pt; mso-table-rspace: 0pt;">
                                                <tbody>
                                                    <tr>
                                                        <td>
                                                            <table class="row-content stack" align="center" border="0" cellpadding="0" cellspacing="0" role="presentation" style="mso-table-lspace: 0pt; mso-table-rspace: 0pt; border-radius: 0; color: #000000; width: 600px;" width="600">
                                                                <tbody>
                                                                    <tr>
                                                                        <td class="column column-1" width="100%" style="mso-table-lspace: 0pt; mso-table-rspace: 0pt; font-weight: 400; text-align: left; padding-top: 15px; vertical-align: top; border-top: 0px; border-right: 0px; border-bottom: 0px; border-left: 0px;">
                                                                            <div class="spacer_block block-1" style="height:30px;line-height:30px;font-size:1px;">&#8202;</div>
                                                                            <table class="heading_block block-2" width="100%" border="0" cellpadding="0" cellspacing="0" role="presentation" style="mso-table-lspace: 0pt; mso-table-rspace: 0pt;">
                                                                                <tr>
                                                                                    <td class="pad" style="padding-bottom:5px;padding-left:20px;padding-right:20px;padding-top:10px;text-align:center;width:100%;">
                                                                                        <h1 style="margin: 0; color: #232c3d; direction: ltr; font-family: Arial, Helvetica, sans-serif; font-size: 18px; font-weight: 700; letter-spacing: normal; line-height: 120%; text-align: left; margin-top: 0; margin-bottom: 0;"><span class="tinyMce-placeholder">Join the beta testing</span></h1>
                                                                                    </td>
                                                                                </tr>
                                                                            </table>
                                                                            <table class="paragraph_block block-3" width="100%" border="0" cellpadding="0" cellspacing="0" role="presentation" style="mso-table-lspace: 0pt; mso-table-rspace: 0pt; word-break: break-word;">
                                                                                <tr>
                                                                                    <td class="pad" style="padding-bottom:10px;padding-left:20px;padding-right:20px;">
                                                                                        <div style="color:#232c3d;direction:ltr;font-family:Arial, Helvetica, sans-serif;font-size:16px;font-weight:400;letter-spacing:0px;line-height:150%;text-align:left;mso-line-height-alt:24px;">
                                                                                            <p style="margin: 0;">Explore exclusive content, and help shape the future of MDS!</p>
                                                                                        </div>
                                                                                    </td>
                                                                                </tr>
                                                                            </table>
                                                                            <table class="divider_block block-4" width="100%" border="0" cellpadding="20" cellspacing="0" role="presentation" style="mso-table-lspace: 0pt; mso-table-rspace: 0pt;">
                                                                                <tr>
                                                                                    <td class="pad">
                                                                                        <div class="alignment" align="center">
                                                                                            <table border="0" cellpadding="0" cellspacing="0" role="presentation" width="100%" style="mso-table-lspace: 0pt; mso-table-rspace: 0pt;">
                                                                                                <tr>
                                                                                                    <td class="divider_inner" style="font-size: 1px; line-height: 1px; border-top: 1px solid #dddddd;"><span>&#8202;</span></td>
                                                                                                </tr>
                                                                                            </table>
                                                                                        </div>
                                                                                    </td>
                                                                                </tr>
                                                                            </table>
                                                                        </td>
                                                                    </tr>
                                                                </tbody>
                                                            </table>
                                                        </td>
                                                    </tr>
                                                </tbody>
                                            </table>
                                            <table class="row row-10 desktop_hide" align="center" width="100%" border="0" cellpadding="0" cellspacing="0" role="presentation" style="mso-table-lspace: 0pt; mso-table-rspace: 0pt; mso-hide: all; display: none; max-height: 0; overflow: hidden;">
                                                <tbody>
                                                    <tr>
                                                        <td>
                                                            <table class="row-content stack" align="center" border="0" cellpadding="0" cellspacing="0" role="presentation" style="mso-table-lspace: 0pt; mso-table-rspace: 0pt; mso-hide: all; display: none; max-height: 0; overflow: hidden; border-radius: 0; color: #000000; width: 600px;" width="600">
                                                                <tbody>
                                                                    <tr>
                                                                        <td class="column column-1" width="50%" style="mso-table-lspace: 0pt; mso-table-rspace: 0pt; font-weight: 400; text-align: left; padding-bottom: 5px; vertical-align: top; border-top: 0px; border-right: 0px; border-bottom: 0px; border-left: 0px;">
                                                                            <table class="image_block block-1" width="100%" border="0" cellpadding="5" cellspacing="0" role="presentation" style="mso-table-lspace: 0pt; mso-table-rspace: 0pt; mso-hide: all; display: none; max-height: 0; overflow: hidden;">
                                                                                <tr>
                                                                                    <td class="pad">
                                                                                        <div class="alignment" align="center" style="line-height:10px"><a href="https://apps.apple.com/app/id1636838955" target="_blank" style="outline:none" tabindex="-1"><img src="https://d15k2d11r6t6rl.cloudfront.net/public/users/Integrators/BeeProAgency/974514_959141/apple.png" style="display: block; height: auto; border: 0; width: 135px; max-width: 100%;" width="135"></a></div>
                                                                                    </td>
                                                                                </tr>
                                                                            </table>
                                                                        </td>
                                                                        <td class="column column-2" width="50%" style="mso-table-lspace: 0pt; mso-table-rspace: 0pt; font-weight: 400; text-align: left; padding-bottom: 5px; vertical-align: top; border-top: 0px; border-right: 0px; border-bottom: 0px; border-left: 0px;">
                                                                            <table class="image_block block-1" width="100%" border="0" cellpadding="5" cellspacing="0" role="presentation" style="mso-table-lspace: 0pt; mso-table-rspace: 0pt; mso-hide: all; display: none; max-height: 0; overflow: hidden;">
                                                                                <tr>
                                                                                    <td class="pad">
                                                                                        <div class="alignment" align="center" style="line-height:10px"><a href="https://play.google.com/store/apps/details?id=com.app.mdscommunity" target="_blank" style="outline:none" tabindex="-1"><img src="https://d15k2d11r6t6rl.cloudfront.net/public/users/Integrators/BeeProAgency/974514_959141/play.png" style="display: block; height: auto; border: 0; width: 135px; max-width: 100%;" width="135"></a></div>
                                                                                    </td>
                                                                                </tr>
                                                                            </table>
                                                                        </td>
                                                                    </tr>
                                                                </tbody>
                                                            </table>
                                                        </td>
                                                    </tr>
                                                </tbody>
                                            </table>
                                            <table class="row row-11 mobile_hide" align="center" width="100%" border="0" cellpadding="0" cellspacing="0" role="presentation" style="mso-table-lspace: 0pt; mso-table-rspace: 0pt;">
                                                <tbody>
                                                    <tr>
                                                        <td>
                                                            <table class="row-content stack" align="center" border="0" cellpadding="0" cellspacing="0" role="presentation" style="mso-table-lspace: 0pt; mso-table-rspace: 0pt; border-radius: 0; color: #000000; width: 600px;" width="600">
                                                                <tbody>
                                                                    <tr>
                                                                        <td class="column column-1" width="50%" style="mso-table-lspace: 0pt; mso-table-rspace: 0pt; font-weight: 400; text-align: left; padding-bottom: 5px; padding-left: 60px; padding-right: 5px; vertical-align: top; border-top: 0px; border-right: 0px; border-bottom: 0px; border-left: 0px;">
                                                                            <table class="image_block block-1" width="100%" border="0" cellpadding="0" cellspacing="0" role="presentation" style="mso-table-lspace: 0pt; mso-table-rspace: 0pt;">
                                                                                <tr>
                                                                                    <td class="pad" style="padding-bottom:5px;padding-left:60px;padding-right:5px;padding-top:5px;width:100%;">
                                                                                        <div class="alignment" align="center" style="line-height:10px"><a href="https://apps.apple.com/app/id1636838955" target="_blank" style="outline:none" tabindex="-1"><img src="https://d15k2d11r6t6rl.cloudfront.net/public/users/Integrators/BeeProAgency/974514_959141/apple.png" style="display: block; height: auto; border: 0; width: 170px; max-width: 100%;" width="170"></a></div>
                                                                                    </td>
                                                                                </tr>
                                                                            </table>
                                                                        </td>
                                                                        <td class="column column-2" width="50%" style="mso-table-lspace: 0pt; mso-table-rspace: 0pt; font-weight: 400; text-align: left; padding-bottom: 5px; padding-left: 10px; padding-right: 60px; vertical-align: top; border-top: 0px; border-right: 0px; border-bottom: 0px; border-left: 0px;">
                                                                            <table class="image_block block-1" width="100%" border="0" cellpadding="0" cellspacing="0" role="presentation" style="mso-table-lspace: 0pt; mso-table-rspace: 0pt;">
                                                                                <tr>
                                                                                    <td class="pad" style="padding-bottom:5px;padding-left:5px;padding-right:60px;padding-top:5px;width:100%;">
                                                                                        <div class="alignment" align="center" style="line-height:10px"><a href="https://play.google.com/store/apps/details?id=com.app.mdscommunity" target="_blank" style="outline:none" tabindex="-1"><img src="https://d15k2d11r6t6rl.cloudfront.net/public/users/Integrators/BeeProAgency/974514_959141/play.png" style="display: block; height: auto; border: 0; width: 165px; max-width: 100%;" width="165"></a></div>
                                                                                    </td>
                                                                                </tr>
                                                                            </table>
                                                                        </td>
                                                                    </tr>
                                                                </tbody>
                                                            </table>
                                                        </td>
                                                    </tr>
                                                </tbody>
                                            </table>
                                            <table class="row row-12" align="center" width="100%" border="0" cellpadding="0" cellspacing="0" role="presentation" style="mso-table-lspace: 0pt; mso-table-rspace: 0pt;">
                                                <tbody>
                                                    <tr>
                                                        <td>
                                                            <table class="row-content stack" align="center" border="0" cellpadding="0" cellspacing="0" role="presentation" style="mso-table-lspace: 0pt; mso-table-rspace: 0pt; border-radius: 0; color: #000000; width: 600px;" width="600">
                                                                <tbody>
                                                                    <tr>
                                                                        <td class="column column-1" width="100%" style="mso-table-lspace: 0pt; mso-table-rspace: 0pt; font-weight: 400; text-align: left; padding-bottom: 5px; padding-top: 5px; vertical-align: top; border-top: 0px; border-right: 0px; border-bottom: 0px; border-left: 0px;">
                                                                            <div class="spacer_block block-1" style="height:15px;line-height:15px;font-size:1px;">&#8202;</div>
                                                                        </td>
                                                                    </tr>
                                                                </tbody>
                                                            </table>
                                                        </td>
                                                    </tr>
                                                </tbody>
                                            </table>
                                            <table class="row row-13" align="center" width="100%" border="0" cellpadding="0" cellspacing="0" role="presentation" style="mso-table-lspace: 0pt; mso-table-rspace: 0pt;">
                                                <tbody>
                                                    <tr>
                                                        <td>
                                                            <table class="row-content stack" align="center" border="0" cellpadding="0" cellspacing="0" role="presentation" style="mso-table-lspace: 0pt; mso-table-rspace: 0pt; border-radius: 0; color: #000000; width: 600px;" width="600">
                                                                <tbody>
                                                                    <tr>
                                                                        <td class="column column-1" width="100%" style="mso-table-lspace: 0pt; mso-table-rspace: 0pt; font-weight: 400; text-align: left; padding-bottom: 5px; padding-top: 5px; vertical-align: top; border-top: 0px; border-right: 0px; border-bottom: 0px; border-left: 0px;">
                                                                            <table class="paragraph_block block-1" width="100%" border="0" cellpadding="0" cellspacing="0" role="presentation" style="mso-table-lspace: 0pt; mso-table-rspace: 0pt; word-break: break-word;">
                                                                                <tr>
                                                                                    <td class="pad" style="padding-bottom:10px;padding-left:10px;padding-right:10px;">
                                                                                        <div style="color:#232c3d;direction:ltr;font-family:Arial, Helvetica, sans-serif;font-size:11px;font-weight:400;letter-spacing:0px;line-height:120%;text-align:center;mso-line-height-alt:13.2px;">
                                                                                           <!-- <p style="margin: 0;">Copyright © 2023 ${communityName}, Inc<br>All Rights Reserved</p> -->
                                                                                        </div>
                                                                                    </td>
                                                                                </tr>
                                                                            </table>
                                                                        </td>
                                                                    </tr>
                                                                </tbody>
                                                            </table>
                                                        </td>
                                                    </tr>
                                                </tbody>
                                            </table>
                                        </td>
                                    </tr>
                                </tbody>
                            </table>
                        </body>
                        
                        </html>`,
            relationId:communityId,
            customsmtp:customSMTP
          };
          const userExist = await airtable_sync.find({
            "Preferred Email": req.body.email,
          });
          if (userExist && userExist.length) {
            let tempFirstName =req.body.first_name ?req.body.first_name :""
            let tempLastName =req.body.last_name ? req.body.last_name :""

            const updated_data = await airtable_sync.findOneAndUpdate(
              { "Preferred Email": req.body.email },
              {
                first_name: req.body.first_name,
                last_name: req.body.last_name,
                display_name: req.body.display_name ? req.body.display_name : (`${tempFirstName} ${tempLastName}`),
                email: req.body.email.toLowerCase(),
                firebaseId: req.body.firebaseId,
                provider: req.body.provider,
                isSocial: true,
                active: true,
                purchased_plan: plan_data._id,
                register_status: true,
                personalDetail_status: true,
                payment_status: true,
                QA_status: true,
                accessible_groups:
                  resources && resources.group_ids ? resources.group_ids : [],
                migrate_user_status: true,
                migrate_user: req.body.migrate_user ?? {},
                socialauth0id: req.body.socialauth0id,
                facebookLinkedinId: req.body.facebookLinkedinId,
                profileImg: req.body.migrate_user
                  ? req.body.migrate_user.picture_url ?? ""
                  : "",
                isDelete: false,
                blocked: false,
                createdAt: new Date(),
                updatedAt: new Date(),
                joinDate: new Date(),
              },
              { new: true }
            );
            if (updated_data) {
              const plan = await MembershipPlan.findByIdAndUpdate(
                plan_data._id,
                {
                  $push: { total_member_who_purchased_plan: userExist[0]._id },
                },
                { new: true }
              );
              return res.status(200).json({
                status: true,
                message: "updated successfully!",
                data: updated_data,
              });
            } else {
              return res
                .status(200)
                .json({ status: false, message: "Something went wrong!" });
            }
          } else {
            var migrationObj = (userEvent = req.body.migrate_user);
            const allEvents = await ContentEvent.find({
              isDelete: false,
              name: { $ne: "others" },
            });

            allEvents.forEach(async (event, key) => {
              const eventName = event.name.toLowerCase();
              if (!migrationObj[eventName]) {
                userEvent[eventName] = false;
              }
            });
            delete userEvent["plan_id"];
            userEvent["others"] = true;

            let tempFirstName =req.body.first_name ?req.body.first_name :""
            let tempLastName =req.body.last_name ? req.body.last_name :""
            
            const updated_data = new airtable_sync({
              "Preferred Email": req.body.email,
              first_name: req.body.first_name,
              last_name: req.body.last_name,
              display_name: req.body.display_name ? req.body.display_name : (`${tempFirstName} ${tempLastName}`),
              email: req.body.email.toLowerCase(),
              firebaseId: req.body.firebaseId,
              provider: req.body.provider,
              isSocial: true,
              active: true,
              purchased_plan: plan_data._id,
              register_status: true,
              personalDetail_status: true,
              payment_status: true,
              QA_status: true,
              accessible_groups:
                resources && resources.group_ids ? resources.group_ids : [],
              migrate_user_status: true,
              migrate_user: req.body.migrate_user ?? {},
              socialauth0id: req.body.socialauth0id,
              facebookLinkedinId: req.body.facebookLinkedinId,
              userEvents: userEvent,
              profileImg: req.body.migrate_user
                ? req.body.migrate_user.picture_url ?? ""
                : "",
              isDelete: false,
              blocked: false,
              joinDate: new Date(),
            });

            updated_data.save(async (err, doc) => {
              if (err)
                return res.status(200).json({
                  status: false,
                  message: "Something went wrong!",
                  error: err,
                });
              else {
                const plan = await MembershipPlan.findByIdAndUpdate(
                  plan_data._id,
                  { $push: { total_member_who_purchased_plan: doc._id } },
                  { new: true }
                );
                await sendEmail(mail_data);
                return res.status(200).json({
                  status: true,
                  message: "updated successfully!",
                  data: doc,
                });
              }
            });
          }
        })
        .catch(function (error) {
          return res.status(200).json({
            status: false,
            message: `Something wrong while linking user. ${error}`,
          });
        });
    });
  } catch (e) {
    return res
      .status(200)
      .json({ status: false, message: "Something went wrong!", error: e });
  }
};

exports.getaccesstoken = async (req, res) => {
  try {
    const token = await getAuth0Token();
    res.status(200).json({ token: token });
  } catch (e) {
    res.status(200).json({ status: false, message: "Something went wrong!" });
  }
};

exports.getuserfromauth0 = async (req, res) => {
  try {
    const { authid } = req.params;
    const auth0token = await getAuth0Token();
    var options = {
      method: "GET",
      url: "https://dev-yc4k4-ud.us.auth0.com/api/v2/users/" + authid,
      headers: {
        "content-type": "application/json",
        authorization: auth0token,
      },
    };

    axios
      .request(options)
      .then(async function (response) {
        if (response.data) {
          res
            .status(200)
            .json({
              status: true,
              message: "User found!",
              data: response.data,
            });
        } else {
          res.status(200).json({ status: false, message: "User not found!" });
        }
      })
      .catch((e) => {
        console.log(e);``
        res
          .status(200)
          .json({ status: false, error: e, message: "User not found!" });
      });
  } catch (e) {
    console.log(e);
    res
      .status(200)
      .json({ status: false, message: "Something went wrong!", error: e });
  }
};

exports.reSendOTP = async (req, res) => {
  try {
    const { email } = req.body;
    const communityLogo = req.currentEdge.relation_id.logo || "";
    const communityName = req.currentEdge.relation_id.name ? req.currentEdge.relation_id.name: "";
    let communityId =  req.currentEdge.relation_id?._id ? ObjectId(req.currentEdge.relation_id?._id) : "";
    let customSMTP =  req.currentEdge.relation_id?.customSMTP ? req.currentEdge.relation_id?.customSMTP : false;

    const random = Math.floor(100000 + Math.random() * 900000);
    const mail_data = {
      email: email,
      subject: `${communityName} verification code`,
      html: `<html xmlns:v="urn:schemas-microsoft-com:vml" xmlns:o="urn:schemas-microsoft-com:office:office" lang="en">

            <head>
                <title></title>
                <meta http-equiv="Content-Type" content="text/html; charset=utf-8">
                <meta name="viewport" content="width=device-width, initial-scale=1.0">
                <style>
                    * {
                        box-sizing: border-box;
                    }
            
                    body {
                        margin: 0;
                        padding: 0;
                    }
            
                    a[x-apple-data-detectors] {
                        color: inherit !important;
                        text-decoration: inherit !important;
                    }
            
                    #MessageViewBody a {
                        color: inherit;
                        text-decoration: none;
                    }
            
                    p {
                        line-height: inherit
                    }
            
                    .desktop_hide,
                    .desktop_hide table {
                        mso-hide: all;
                        display: none;
                        max-height: 0px;
                        overflow: hidden;
                    }
            
                    .image_block img+div {
                        display: none;
                    }
            
                    @media (max-width:700px) {
                        .desktop_hide table.icons-inner {
                            display: inline-block !important;
                        }
            
                        .icons-inner {
                            text-align: center;
                        }
            
                        .icons-inner td {
                            margin: 0 auto;
                        }
            
                        .row-content {
                            width: 100% !important;
                        }
            
                        .mobile_hide {
                            display: none;
                        }
            
                        .stack .column {
                            width: 100%;
                            display: block;
                        }
            
                        .mobile_hide {
                            min-height: 0;
                            max-height: 0;
                            max-width: 0;
                            overflow: hidden;
                            font-size: 0px;
                        }
            
                        .desktop_hide,
                        .desktop_hide table {
                            display: table !important;
                            max-height: none !important;
                        }
            
                        .row-2 .column-1 .block-2.text_block td.pad {
                            padding: 15px 10px !important;
                        }
            
                        .row-2 .column-1 .block-1.heading_block h1 {
                            text-align: center !important;
                            font-size: 20px !important;
                        }
            
                        .row-2 .column-1 .block-1.heading_block td.pad {
                            padding: 40px 10px 5px !important;
                        }
            
                        .row-1 .column-1 .block-2.image_block td.pad {
                            padding: 0 0 0 20px !important;
                        }
            
                        .row-2 .column-1 {
                            padding: 0 !important;
                        }
                    }
                </style>
            </head>
            
            <body style="margin: 0; background-color: #fbfbfb; padding: 0; -webkit-text-size-adjust: none; text-size-adjust: none;">
                <table class="nl-container" width="100%" border="0" cellpadding="0" cellspacing="0" role="presentation" style="mso-table-lspace: 0pt; mso-table-rspace: 0pt; background-color: #fbfbfb;">
                    <tbody>
                        <tr>
                            <td>
                                <table class="row row-1" align="center" width="100%" border="0" cellpadding="0" cellspacing="0" role="presentation" style="mso-table-lspace: 0pt; mso-table-rspace: 0pt; background-color: #fbfbfb;">
                                    <tbody>
                                        <tr>
                                            <td>
                                                <table class="row-content stack" align="center" border="0" cellpadding="0" cellspacing="0" role="presentation" style="mso-table-lspace: 0pt; mso-table-rspace: 0pt; background-color: #fbfbfb; color: #000000; width: 680px;" width="680">
                                                    <tbody>
                                                        <tr>
                                                            <td class="column column-1" width="100%" style="mso-table-lspace: 0pt; mso-table-rspace: 0pt; font-weight: 400; text-align: left; padding-bottom: 5px; padding-top: 5px; vertical-align: top; border-top: 0px; border-right: 0px; border-bottom: 0px; border-left: 0px;">
                                                                <div class="spacer_block block-1" style="height:30px;line-height:30px;font-size:1px;">&#8202;</div>
                                                                <table class="image_block block-2" width="100%" border="0" cellpadding="0" cellspacing="0" role="presentation" style="mso-table-lspace: 0pt; mso-table-rspace: 0pt;">
                                                                    <tr>
                                                                        <td class="pad" style="width:100%;padding-right:0px;padding-left:0px;">
                                                                            <div class="alignment" align="left" style="line-height:10px"><img src=${communityLogo} style="display: block; height: auto; border: 0; width: 136px; max-width: 100%;" width="136" alt="Web Logo" title="Logo">
                                                                            </div>
                                                                        </td>
                                                                    </tr>
                                                                </table>
                                                                <div class="spacer_block block-3" style="height:15px;line-height:15px;font-size:1px;">&#8202;</div>
                                                                <div class="spacer_block block-4" style="height:15px;line-height:15px;font-size:1px;">&#8202;</div>
                                                            </td>
                                                        </tr>
                                                    </tbody>
                                                </table>
                                            </td>
                                        </tr>
                                    </tbody>
                                </table>
                                <table class="row row-2" align="center" width="100%" border="0" cellpadding="0" cellspacing="0" role="presentation" style="mso-table-lspace: 0pt; mso-table-rspace: 0pt; background-color: #fbfbfb; background-position: center top;">
                                    <tbody>
                                        <tr>
                                            <td>
                                                <table class="row-content stack" align="center" border="0" cellpadding="0" cellspacing="0" role="presentation" style="mso-table-lspace: 0pt; mso-table-rspace: 0pt; background-color: #ffffff; border-bottom: 1px solid #CCD6DD; border-left: 1px solid #CCD6DD; border-radius: 4px; border-right: 1px solid #CCD6DD; border-top: 1px solid #CCD6DD; color: #000000; width: 680px;" width="680">
                                                    <tbody>
                                                        <tr>
                                                            <td class="column column-1" width="100%" style="mso-table-lspace: 0pt; mso-table-rspace: 0pt; font-weight: 400; text-align: left; vertical-align: top; border-top: 0px; border-right: 0px; border-bottom: 0px; border-left: 0px;">
                                                                <table class="heading_block block-1" width="100%" border="0" cellpadding="0" cellspacing="0" role="presentation" style="mso-table-lspace: 0pt; mso-table-rspace: 0pt;">
                                                                    <tr>
                                                                        <td class="pad" style="padding-bottom:5px;padding-left:20px;padding-right:15px;padding-top:40px;text-align:center;width:100%;">
                                                                            <h1 style="margin: 0; color: #171719; direction: ltr; font-family: 'Helvetica Neue', Helvetica, Arial, sans-serif; font-size: 30px; font-weight: 400; letter-spacing: normal; line-height: 120%; text-align: center; margin-top: 0; margin-bottom: 0;">Just checking to be sure you're you.</h1>
                                                                        </td>
                                                                    </tr>
                                                                </table>
                                                                <table class="text_block block-2" width="100%" border="0" cellpadding="0" cellspacing="0" role="presentation" style="mso-table-lspace: 0pt; mso-table-rspace: 0pt; word-break: break-word;">
                                                                    <tr>
                                                                        <td class="pad" style="padding-bottom:15px;padding-left:20px;padding-right:35px;padding-top:15px;">
                                                                            <div style="font-family: Arial, sans-serif">
                                                                                <div class style="font-size: 14px; font-family: 'Helvetica Neue', Helvetica, Arial, sans-serif; mso-line-height-alt: 21px; color: #171719; line-height: 1.5;">
                                                                                    <p style="margin: 0; text-align: center; mso-line-height-alt: 24px;"><span style="font-size:16px;">Please copy and paste the following code into the Verification Code field.</span></p>
                                                                                </div>
                                                                            </div>
                                                                        </td>
                                                                    </tr>
                                                                </table>
                                                                <table class="button_block block-3" width="100%" border="0" cellpadding="0" cellspacing="0" role="presentation" style="mso-table-lspace: 0pt; mso-table-rspace: 0pt;">
                                                                    <tr>
                                                                        <td class="pad" style="padding-bottom:40px;padding-left:25px;padding-right:25px;padding-top:25px;text-align:center;">
                                                                            <div class="alignment" align="center">
                                                                                <span style="text-decoration:none;display:inline-block;color:#171719;background-color:transparent;border-radius:10px;width:auto;border-top:1px solid #AFAFAF;font-weight:400;border-right:1px solid #AFAFAF;border-bottom:1px solid #AFAFAF;border-left:1px solid #AFAFAF;padding-top:5px;padding-bottom:5px;font-family:'Helvetica Neue', Helvetica, Arial, sans-serif;font-size:21px;text-align:center;mso-border-alt:none;word-break:keep-all;"><span style="padding-left:60px;padding-right:60px;font-size:21px; display:inline-block;letter-spacing:1px;"><span dir="ltr" style="word-break: break-word; line-height: 42px;"><strong>${random}</strong></span></span></span>
                                                                            </div>
                                                                        </td>
                                                                    </tr>
                                                                </table>
                                                            </td>
                                                        </tr>
                                                    </tbody>
                                                </table>
                                            </td>
                                        </tr>
                                    </tbody>
                                </table>
                                <table class="row row-3" align="center" width="100%" border="0" cellpadding="0" cellspacing="0" role="presentation" style="mso-table-lspace: 0pt; mso-table-rspace: 0pt; background-color: #fbfbfb;">
                                    <tbody>
                                        <tr>
                                            <td>
                                                <table class="row-content stack" align="center" border="0" cellpadding="0" cellspacing="0" role="presentation" style="mso-table-lspace: 0pt; mso-table-rspace: 0pt; background-color: #fbfbfb; color: #000000; width: 680px;" width="680">
                                                    <tbody>
                                                        <tr>
                                                            <td class="column column-1" width="100%" style="mso-table-lspace: 0pt; mso-table-rspace: 0pt; font-weight: 400; text-align: left; vertical-align: top; border-top: 0px; border-right: 0px; border-bottom: 0px; border-left: 0px;">
                                                                <div class="spacer_block block-1" style="height:55px;line-height:55px;font-size:1px;">&#8202;</div>
                                                            </td>
                                                        </tr>
                                                    </tbody>
                                                </table>
                                            </td>
                                        </tr>
                                    </tbody>
                                </table>
                            </td>
                        </tr>
                    </tbody>
                </table>
            </body>
            
            </html>`,
            relationId:communityId,
            customsmtp:customSMTP
    };

    await sendEmail(mail_data);

    const updatedata = await AuthUserEmail.findOneAndUpdate(
      { email: email.toLowerCase() },
      {
        otp: random,
        otpExpireTime: new Date(
          new Date().setMinutes(new Date().getMinutes() + 5)
        ),
        is_otp_verified: false,
      },
      { new: true }
    );

    if (updatedata)
      res.status(200).json({ status: true, message: "OTP sent sucessfully!" });
    else res.status(200).json({ status: false, message: "OTP not sent!" });
  } catch (e) {
    res
      .status(200)
      .json({ status: false, message: "Something went wrong!", error: e });
  }
};

exports.verifyOTP = async (req, res) => {
  try {
    const { email, otp } = req.body;

    const userData = await AuthUserEmail.findOne({
      email: email.toLowerCase(),
    });

    if (new Date(userData.otpExpireTime).getTime() < new Date().getTime())
      return res
        .status(200)
        .json({ status: false, response: {}, message: "OTP expier!" });

    if (userData.otp === otp) {
      const updateData = await AuthUserEmail.findOneAndUpdate(
        { email: email.toLowerCase() },
        { otp: null, otpExpireTime: null, is_otp_verified: true },
        { new: true }
      );
      res
        .status(200)
        .json({ status: true, response: updateData, message: "OTP verified!" });
    } else {
      res.status(200).json({
        status: false,
        response: {},
        message: "You have entered wrong code. Please enter again!",
      });
    }
  } catch (e) {
    res
      .status(200)
      .json({ status: false, message: "Something went wrong!", error: e });
  }
};

exports.appleTokenConvertData = async (req, res) => {
  try {
    const response = await auth.accessToken(req.body.authorization.code);
    const idToken = jwt.decode(response.id_token);
    const user = {};
    user.id = idToken.sub;
    
    // Extract email from idToken if available
    if (idToken.email) {
      user.email = idToken.email;
    }
    
    // Add a flag to indicate if this is a mobile sign-in with hidden email
    user.isEmailHidden = !idToken.email && req.body.isFromMobile;
    
    // For mobile authentication without email, we need to proceed anyway
    // The frontend will need to handle this appropriately
    
    // Add extra debugging information in development
    if (process.env.NODE_ENV !== 'production') {
      user.tokenInfo = {
        hasEmail: !!idToken.email,
        isFromMobile: !!req.body.isFromMobile,
        sub: idToken.sub
      };
    }

    res.json(user); // Respond with the user, even if email is missing
  } catch (e) {
    console.error("Apple authentication error:", e);
    res
      .status(200)
      .json({ status: false, message: "Something went wrong with Apple authentication!", error: e.message });
  }
};

/**
 * Mobile-specific Apple authentication endpoint
 * Handles the case where users choose to hide their email during Apple Sign In
 * on mobile devices, which is a common issue due to Apple's privacy policies
 */
exports.mobileAppleAuth = async (req, res) => {
  try {
    // Validate required parameters
    if (!req.body.authorization || !req.body.authorization.code) {
      return res.status(400).json({ 
        status: false, 
        message: "Missing authorization code"
      });
    }
    
    // Get the Apple ID token
    const response = await auth.accessToken(req.body.authorization.code);
    const idToken = jwt.decode(response.id_token);
    
    if (!idToken || !idToken.sub) {
      return res.status(400).json({
        status: false,
        message: "Invalid Apple authentication response"
      });
    }
    
    // Create user object with minimal required info
    const user = {
      id: idToken.sub,
      isMobileAuth: true,
      provider: "apple"
    };
    
    // Add email if available (but not required)
    if (idToken.email) {
      user.email = idToken.email;
      user.emailHidden = false;
    } else {
      user.emailHidden = true;
      // Since email is hidden, we'll use the Apple ID as the identifier
      console.log(`Mobile Apple auth with hidden email, using Apple ID: ${idToken.sub}`);
    }
    
    // Look up user in the database by Apple ID
    const existingUser = await User.findOne({
      $or: [
        { socialauth0id: idToken.sub },
        { firebaseId: idToken.sub }
      ]
    }).lean();
    
    // If user exists in our system, return success with user data
    if (existingUser) {
      user.exists = true;
      user.userData = {
        _id: existingUser._id,
        email: existingUser.email || existingUser["Preferred Email"],
        name: existingUser.first_name ? 
          `${existingUser.first_name} ${existingUser.last_name}` : 
          existingUser.name || ""
      };
      
      console.log(`Mobile Apple auth successful for existing user with ID: ${existingUser._id}`);
    } else {
      user.exists = false;
      console.log(`Mobile Apple auth for new user with Apple ID: ${idToken.sub}`);
    }

    // Return successful response with user information
    return res.status(200).json({
      status: true,
      message: "Apple authentication successful",
      data: user
    });
    
  } catch (error) {
    console.error("Mobile Apple authentication error:", error);
    return res.status(200).json({
      status: false,
      message: "Error during Apple authentication",
      error: error.message
    });
  }
};

/** code by SJ start **/
exports.sendDeactivateRequest = async (req, res) => {
  try {
    const { authUserId } = req;
    const userData = await airtable_sync.findByIdAndUpdate(
      authUserId,
      { deactivate_account_request: true },
      { new: true }
    );

    if (userData)
      return res.status(200).json({
        status: true,
        message: `User account deactivated reuested successfully!`,
        data: userData,
      });
  } catch (error) {
    return res.status(200).json({ status: false, message: error.message });
  }
};

exports.getDeactivateRequestedUsers = async (req, res) => {
  try {
    const userData = await airtable_sync.find({
      deactivate_account_request: true,
      isDelete: false,
      firebaseId: { $nin: ["", null] },
    });

    if (userData)
      return res.status(200).json({
        status: true,
        message: `Deactivated account reuested user list retrive successfully!`,
        data: userData,
      });
  } catch (error) {
    return res.status(200).json({ status: false, message: error.message });
  }
};

exports.getAllValidUser = async (req, res) => {
  try {
    const userId = req.authUserId;
    let match = {
      _id: { $ne: userId },
      email: { $ne: "<EMAIL>" },
      register_status: true,
      personalDetail_status: true,
      payment_status: true,
      isDelete: false,
      blocked: false,
      active: true,
      QA_status: true,
    };

    const userData = await airtable_sync.aggregate([
      {
        $match: match,
      },
      {
        $lookup: {
          from: "chat_users",
          let: { userId: "$_id" },
          pipeline: [
            {
              $match: {
                $expr: {
                  $eq: ["$userid", "$$userId"],
                },
              },
            },
          ],
          as: "userChat",
        },
      },
      {
        $addFields: {
          onlineStatus: {
            $cond: [
              { $gt: [{ $size: "$userChat" }, 0] },
              "$userChat.online",
              false,
            ],
          },
        },
      },
      {
        $project: {
          email: 1,
          profileImg: 1,
          onlineStatus: 1,
          first_name: 1,
          last_name: 1,
          display_name: 1
        },
      },
    ]);

    userData?.map((user) => {
      if (Array.isArray(user.onlineStatus)) {
        user.onlineStatus = user.onlineStatus[0];
      } else {
        user.onlineStatus = user.onlineStatus;
      }
    });

    const sortedUsers = userData.sort((a, b) => {
      if (a.first_name) return a.first_name.localeCompare(b.first_name);
      else return a;
    });

    if (sortedUsers)
      return res.status(200).json({
        status: true,
        message: `All Valid User list retrive successfully!`,
        data: sortedUsers,
      });
  } catch (error) {
    return res.status(200).json({ status: false, message: error.message });
  }
};

exports.verifyInAppPurchase = async (req, res) => {
  try {
    const body = req.body;
    const receipt = req.body;
    const { authUserId } = req;
    const userData = await airtable_sync.findById(authUserId);

    var prchaseDate = new Date(Number(body.startDate));

    if (body.paymentMethod === "ios") {
      const plan_id = await membership_plan.findOne(
        { apple_plan_id: body.planId },
        { _id: 1 }
      );

      appleReceiptVerify.config({
        applePassword: APP_STORE_INAPP_SECRET,
      });

      var receiptData = receipt.token;
      const encodedReceiptData = Buffer.from(receiptData, "base64").toString(
        "base64"
      );

      const receiptUrl = "https://sandbox.itunes.apple.com/verifyReceipt";
      receiptData = encodedReceiptData;

      const requestData = JSON.stringify({
        "receipt-data": receiptData,
      });

      const options = {
        method: "POST",
        headers: {
          "Content-Type": "application/json",
          "Content-Length": requestData.length,
        },
      };

      const reqData = https.request(receiptUrl, options, (respo) => {
        let responseData = "";

        respo.on("data", async (chunk) => {
          responseData += chunk;
        });

        respo.on("end", async () => {
          const response = JSON.parse(responseData);

          const payment_entry = new Payment({
            name_on_card: "",
            country: "",
            postal_code: "",
            subscriptionId: "",
            paymentMethodId: "",
            customerId: "",
            card_number: "",
            card_expiry_date: "",
            card_brand: "",
            membership_plan_id: null,
            expire_date: "",
            invoice_payment_intent_status: "succeeded",
            originalTransactionId: body.transactionId,
            inAppProductId: body.planId,
            originalStartDate: prchaseDate,
            startDate: prchaseDate,
            user_id: userData._id,
            inAppToken: body.token,
          });

          if (!payment_entry)
            return res
              .status(200)
              .json({ status: false, message: "Something went wrong !!" });

          const savedEntry = await payment_entry.save();

          if (!savedEntry)
            return res.status(201).json({
              status: false,
              message: "Something wrong while updating payment data.",
            });

          await airtable_sync.findByIdAndUpdate(
            authUserId,
            {
              payment_status: true,
              payment_id: savedEntry._id,
              purchased_plan: plan_id,
            },
            { new: true }
          );
          return res.status(200).json({
            status: true,
            message: "Subscription created successfully.",
            data: savedEntry,
          });
        });
      });

      reqData.on("error", async (error) => {
        console.error(error, "error");
        console.error(error.message);
      });

      reqData.write(requestData);
      reqData.end();
    } else if (paymentMethod === "android") {
      /** Google Setup Code **/

      const plan_id = await membership_plan.findOne({
        play_store_plan_id: body.planId,
      });
      const productId = body.planId; // Replace with the ID of the product you want to verify
      const purchaseToken = body.token; // Replace with the purchase token of the purchase you want to verify

      const jwtClient = new google.auth.JWT(
        GoogleKey.client_email,
        null,
        GoogleKey.private_key,
        "[https://www.googleapis.com/auth/androidpublisher]",
        null
      );

      jwtClient.authorize((err, tokens) => {
        if (err) {
          console.error(err, "jwtClientErr");
          return;
        }

        playDeveloper.purchases.products.get(
          {
            auth: jwtClient,
            // packageName: packageName,
            productId: productId,
            token: purchaseToken,
          },
          async (err, response) => {
            if (err) {
              console.error(err, "err");
              return;
            }
          }
        );
      });
    }
  } catch (error) {
    return res.status(200).json({ status: false, message: error.message });
  }
};

exports.GooglePlayPurchase = async (req, res) => {
  let projectId = "pc-api-8893606168182108109-208";
  let topicNameOrId =
    "projects/pc-api-8893606168182108109-208/topics/play_billing";
  let subscriptionName = "my-sub";

  const { message } = req.body;
  const { subscriptionNotification } = message;

  /** 
            INITIAL_PURCHASE
            NON_RENEWING_PURCHASE
            RENEWAL
            PRODUCT_CHANGE
            CANCELLATION
            UNCANCELLATION
            BILLING_ISSUE
            SUBSCRIBER_ALIAS
            SUBSCRIPTION_PAUSED
            TRANSFER
            EXPIRATION
        
            BILLING_ERROR
            DEVELOPER_INITIATED
            PRICE_INCREASE
        **/

  if (subscriptionNotification) {
    const { version } = subscriptionNotification;
    const { notificationType } = subscriptionNotification;
  }

  res.sendStatus(200);
};

exports.AppStorePurchase = async (req, res) => {
  const eventData = req.body;
  const signedPayload = eventData.signedPayload;
  const decoded = jwt.decode(signedPayload);
  const transactionData = decoded.data.signedTransactionInfo;
  const signedTransaction = jwt.decode(transactionData);
  const renewalData = decoded.data.signedRenewalInfo;
  const signedRenewal = jwt.decode(renewalData);

  const notificationType = decoded.notificationType;

  const cancel_subscription = true;
  const inAppProductId = signedTransaction.productId;
  const originalTransactionId = signedTransaction.originalTransactionId;
  const startDate = new Date(Number(signedTransaction.purchaseDate));
  const expire_date = new Date(Number(signedTransaction.expiresDate));
  const user_id = "";
  const membership_plan_id = await membership_plan.findOne(
    { apple_plan_id: signedTransaction.productId },
    { _id: 1 }
  );
  const autoRenewProductId = signedRenewal.autoRenewProductId;
  var result;

  switch (notificationType) {
    case "CONSUMPTION_REQUEST":
      if (
        await payment.findOne({ originalTransactionId: originalTransactionId })
      )
        result = await payment.findOneAndUpdate(
          { originalTransactionId: originalTransactionId },
          {
            inAppProductId: inAppProductId,
            autoRenewProductId: autoRenewProductId,
            startDate: startDate,
            expire_date: expire_date,
            membership_plan_id: membership_plan_id,
          },
          { new: true }
        );
      break;

    case "DID_CHANGE_RENEWAL_PREF":
      if (
        await payment.findOne({ originalTransactionId: originalTransactionId })
      )
        result = await payment.findOneAndUpdate(
          { originalTransactionId: originalTransactionId },
          {
            inAppProductId: inAppProductId,
            autoRenewProductId: autoRenewProductId,
            startDate: startDate,
            expire_date: expire_date,
            membership_plan_id: membership_plan_id,
          },
          { new: true }
        );
      break;

    case "DID_CHANGE_RENEWAL_STATUS":
      if (
        await payment.findOne({ originalTransactionId: originalTransactionId })
      )
        result = await payment.findOneAndUpdate(
          { originalTransactionId: originalTransactionId },
          {
            inAppProductId: inAppProductId,
            autoRenewProductId: autoRenewProductId,
            startDate: startDate,
            expire_date: expire_date,
            membership_plan_id: membership_plan_id,
          },
          { new: true }
        );
      break;

    case "DID_FAIL_TO_RENEW":
      if (
        await payment.findOne({ originalTransactionId: originalTransactionId })
      )
        result = await payment.findOneAndUpdate(
          { originalTransactionId: originalTransactionId },
          {
            inAppProductId: inAppProductId,
            autoRenewProductId: autoRenewProductId,
            startDate: startDate,
            expire_date: expire_date,
            membership_plan_id: membership_plan_id,
          },
          { new: true }
        );
      break;

    case "DID_RENEW":
      if (
        await payment.findOne({ originalTransactionId: originalTransactionId })
      )
        result = await payment.findOneAndUpdate(
          { originalTransactionId: originalTransactionId },
          {
            inAppProductId: inAppProductId,
            autoRenewProductId: autoRenewProductId,
            startDate: startDate,
            expire_date: expire_date,
            membership_plan_id: membership_plan_id,
          },
          { new: true }
        );
      break;

    case "EXPIRED":
      if (
        await payment.findOne({ originalTransactionId: originalTransactionId })
      )
        result = await payment.findOneAndUpdate(
          { originalTransactionId: originalTransactionId },
          {
            inAppProductId: inAppProductId,
            autoRenewProductId: autoRenewProductId,
            startDate: startDate,
            expire_date: expire_date,
            membership_plan_id: membership_plan_id,
            cancel_subscription: cancel_subscription,
          },
          { new: true }
        );
      break;

    case "GRACE_PERIOD_EXPIRED":
      if (
        await payment.findOne({ originalTransactionId: originalTransactionId })
      )
        result = await payment.findOneAndUpdate(
          { originalTransactionId: originalTransactionId },
          {
            inAppProductId: inAppProductId,
            autoRenewProductId: autoRenewProductId,
            startDate: startDate,
            expire_date: expire_date,
            membership_plan_id: membership_plan_id,
            cancel_subscription: cancel_subscription,
          },
          { new: true }
        );
      break;

    case "OFFER_REDEEMED":
      if (
        await payment.findOne({ originalTransactionId: originalTransactionId })
      )
        result = await payment.findOneAndUpdate(
          { originalTransactionId: originalTransactionId },
          {
            inAppProductId: inAppProductId,
            autoRenewProductId: autoRenewProductId,
            startDate: startDate,
            expire_date: expire_date,
            membership_plan_id: membership_plan_id,
          },
          { new: true }
        );
      break;

    case "PRICE_INCREASE":
      if (
        await payment.findOne({ originalTransactionId: originalTransactionId })
      )
        result = await payment.findOneAndUpdate(
          { originalTransactionId: originalTransactionId },
          {
            inAppProductId: inAppProductId,
            autoRenewProductId: autoRenewProductId,
            startDate: startDate,
            expire_date: expire_date,
            membership_plan_id: membership_plan_id,
          },
          { new: true }
        );
      break;

    case "REFUND":
      if (
        await payment.findOne({ originalTransactionId: originalTransactionId })
      )
        result = await payment.findOneAndUpdate(
          { originalTransactionId: originalTransactionId },
          {
            inAppProductId: inAppProductId,
            autoRenewProductId: autoRenewProductId,
            startDate: startDate,
            expire_date: expire_date,
            membership_plan_id: membership_plan_id,
          },
          { new: true }
        );
      break;

    case "REFUND_DECLINED":
      if (
        await payment.findOne({ originalTransactionId: originalTransactionId })
      )
        result = await payment.findOneAndUpdate(
          { originalTransactionId: originalTransactionId },
          {
            inAppProductId: inAppProductId,
            autoRenewProductId: autoRenewProductId,
            startDate: startDate,
            expire_date: expire_date,
            membership_plan_id: membership_plan_id,
          },
          { new: true }
        );
      break;

    case "RENEWAL_EXTENDED":
      if (
        await payment.findOne({ originalTransactionId: originalTransactionId })
      )
        result = await payment.findOneAndUpdate(
          { originalTransactionId: originalTransactionId },
          {
            inAppProductId: inAppProductId,
            autoRenewProductId: autoRenewProductId,
            startDate: startDate,
            expire_date: expire_date,
            membership_plan_id: membership_plan_id,
          },
          { new: true }
        );
      break;

    case "RENEWAL_EXTENSION":
      if (
        await payment.findOne({ originalTransactionId: originalTransactionId })
      )
        result = await payment.findOneAndUpdate(
          { originalTransactionId: originalTransactionId },
          {
            inAppProductId: inAppProductId,
            autoRenewProductId: autoRenewProductId,
            startDate: startDate,
            expire_date: expire_date,
            membership_plan_id: membership_plan_id,
          },
          { new: true }
        );
      break;

    case "REVOKE":
      if (
        await payment.findOne({ originalTransactionId: originalTransactionId })
      )
        result = await payment.findOneAndUpdate(
          { originalTransactionId: originalTransactionId },
          {
            inAppProductId: inAppProductId,
            autoRenewProductId: autoRenewProductId,
            startDate: startDate,
            expire_date: expire_date,
            membership_plan_id: membership_plan_id,
          },
          { new: true }
        );
      break;

    case "SUBSCRIBED":
      if (
        await payment.findOne({ originalTransactionId: originalTransactionId })
      )
        result = await payment.findOneAndUpdate(
          { originalTransactionId: originalTransactionId },
          {
            inAppProductId: inAppProductId,
            autoRenewProductId: autoRenewProductId,
            startDate: startDate,
            expire_date: expire_date,
            membership_plan_id: membership_plan_id,
          },
          { new: true }
        );
      break;

    case "TEST":
      console.log("it's the TEST method we don't need to do anything here.");
      break;

    default:
      text = "No data found";
  }

  if (!result) {
    console.log("Unauthorized request");
    res.sendStatus(401);
    return;
  }

  // Send a 200 OK response to Apple
  console.log("Received webhook data:", result);
  res.sendStatus(200);
};

exports.getUserPayment = async (req, res) => {
  try {
    const { authUserId } = req;
    const userData = await airtable_sync.findById(authUserId);

    const savedEntry = await payment.findOne(
      { user_id: userData._id },
      { inAppToken: 0, __v: 0 }
    );

    if (!savedEntry)
      return res.status(201).json({
        status: false,
        message: "User payment data not found!",
        data: [],
      });

    return res.status(200).json({
      status: true,
      message: "User payment data retrive successfully.",
      data: savedEntry,
    });
  } catch (error) {
    return res.status(200).json({ status: false, message: error.message });
  }
};

exports.addContactUserData = async (req, res) => {
  try {
    const body = req.body;
    const communityName = req.currentEdge.relation_id.name ? req.currentEdge.relation_id.name: "";
    let communityId =  req.currentEdge.relation_id?._id ? ObjectId(req.currentEdge.relation_id?._id) : "";
    let customSMTP =  req.currentEdge.relation_id?.customSMTP ? req.currentEdge.relation_id?.customSMTP : false;

    var contactUser = {};

    const alreadyAdded = await contactUsUser.findOne({
      email: body.email.toLowerCase(),
    });
    const adminEmail = process.env.AdminEmail;

    if (alreadyAdded !== null) {
      contactUser = await contactUsUser.findOneAndUpdate(
        { email: body.email.toLowerCase() },
        body,
        { new: true }
      );
    } else {
      contactUser = await contactUsUser.create(body);
    }
    const mail_data = {
      email: `${body.email}`,
      subject: `${body.subject}`,
      html: `<h4> Hello Admin,</h4>
              </br>
              <div>Here is new inquiry from ${communityName} community platform.</div>
              </br></br>
              <div>From:</div>
              </br>
              <div>${body.email}</div>
              </br></br>
              <div>Message:</div>
              </br>
              <div>${body.message}</div>
              </br></br>
              <div>Thank You</div>`,
              relationId:communityId,
              customsmtp:customSMTP
    };

    await sendEmailAdmin(mail_data);

    if (contactUser) {
      res.status(200).json({
        status: true,
        message: "User data added successfully!",
        data: contactUser,
      });
    }
  } catch (error) {
    return res.status(200).json({ status: false, message: `${error.message}` });
  }
};

exports.getContactUserData = async (req, res) => {
  try {
    const contactUserList = await contactUsUser.find({});

    if (contactUserList) {
      res.status(200).json({
        status: true,
        message: "User data retive successfully!",
        data: contactUserList,
      });
    }
  } catch (error) {
    return res.status(200).json({ status: false, message: `${error.message}` });
  }
};

exports.addBaseInProfile = async (req, res) => {
  try {
    const AllUser = await airtable_sync.find({});
    AllUser?.forEach(async (item) => {
      if (item.profileImg === process.env.AWS_IMG_VID_PATH) {
        await airtable_sync.findOneAndUpdate(
          { _id: new ObjectId(item._id) },
          { profileImg: "" },
          { new: true }
        );
      } else if (
        item.profileImg !== null &&
        !item.profileImg.startsWith("https://mds-community.s3.amazonaws.com/")
      ) {
        await airtable_sync.findOneAndUpdate(
          { _id: new ObjectId(item._id) },
          { profileImg: process.env.AWS_IMG_VID_PATH + item.profileImg },
          { new: true }
        );
      }
    });
    return res
      .status(200)
      .json({ status: true, message: `Updated all profile IMG.` });
  } catch (error) {
    return res
      .status(200)
      .json({ status: false, message: `Something went wrong. ${error}` });
  }
};

// Admin export user data API
exports.exportUser = async (req, res) => {
  try {
    const AllUsers = await airtable_sync.find(
      { isDelete: false, firebaseId: { $nin: ["", null] },
      isCollaborator: false,
      "AT Database Status": {
        $in: [
          "Staff",
          "Pending Group Entrance",
          "New Member",
          "Current Member",
        ],
      },
      },
      {
        _id: 1,
        email: "$Preferred Email",
        migrate_user_status: 1,
        // migrate_user: 1,
        userEvents: 1,
        first_name: 1,
        last_name: 1,
        display_name: 1
      }
    );

    if (AllUsers) {
      return res
        .status(200)
        .json({ status: true, message: "User data retrive.", data: AllUsers });
    } else {
      return res.status(401).json({
        status: false,
        message: `Something went wrong ${error.message}`,
      });
    }
  } catch (err) {
    return res.status(500).json({
      status: false,
      message: `Internal server error! ${err.message}`,
    });
  }
};

// Admin import user data API
exports.importUser = async (req, res) => {
  try {
    const body = req.body;
    const allUser = body.allUser;
    allUser?.forEach(async (userData, i) => {
      let tempFirstName =userData.first_name ?userData.first_name :""
      let tempLastName =userData.last_name ? userData.last_name :""
      await airtable_sync.findOneAndUpdate(
        { _id: new ObjectId(userData._id) },
        {
          migrate_user_status: userData.migrate_user_status,
          migrate_user: userData.migrate_user,
          first_name: userData.first_name,
          last_name: userData.last_name,
          display_name: userData.display_name ? userData.display_name : (`${tempFirstName} ${tempLastName}`),
          userEvents: userData.userEvents,
        },
        { new: true }
      );
    });
    return res
      .status(200)
      .json({ status: true, message: "User data successfully updated." });
  } catch (err) {
    return res.status(500).json({
      status: false,
      message: `Internal server error! ${err.message}`,
    });
  }
};

// add and remove device token API
exports.addNremoveDeviceToken = async (req, res) => {
  try {
    const body = req.body;
    const { authUserId } = req;

    if (body.isLogin === true) {
      let allUser = await airtable_sync
        .find({
          isDelete: false,
          deviceToken: { $in: body.deviceToken },
          firebaseId: { $nin: ["", null] },
        })
        .lean();
      if (allUser.length > 0) {
        allUser.forEach(async (userData) => {
          await airtable_sync.findOneAndUpdate(
            { _id: userData._id, isDelete: false },
            { $pull: { deviceToken: body.deviceToken } }
          );
        });
      }

      let unReadCount = await checkIfMsgReadSocket(authUserId);
      let userDeviceToken = await user_edges.findOne({ user_id: authUserId, relation_id: req.relation_id }, { deviceToken: 1 })

      const conflictingRecords = await user_edges.find({
        deviceToken: { $in: body.deviceToken },
        isDelete: false,
      });
    
      const removalPromises = conflictingRecords
        .filter(record => record.user_id.toString() !== authUserId.toString())  
        .map(async (record) => {
          const tokensToRemove = Array.isArray(body.deviceToken) ? body.deviceToken : [body.deviceToken];
          
          const result = await user_edges.updateOne(
            {
              user_id: record.user_id, 
              relation_id: record.relation_id, 
              deviceToken: { $in: tokensToRemove },
            },
            { $pull: { deviceToken: { $in: tokensToRemove } } }
          );
          
          return result; 
        });
      
      if (removalPromises.length) {
        await Promise.all(removalPromises);
      }

      if (userDeviceToken.deviceToken.length > 0) {
        let successdata = {
          notification: "",
          description: "",
          device_token: userDeviceToken.deviceToken,
          collapse_key: "",
          badge_count: unReadCount,
          notification_data: {
            type: "",
            content: [],
          },
        };
        send_notification(successdata);
      }
      await airtable_sync.findOneAndUpdate(
        { _id: authUserId, isDelete: false },
        {
          $addToSet: {
            ...(body.deviceToken != null && { deviceToken: body.deviceToken }),
          },
        },
        { new: true }
      );

      return res.status(200).json({
        status: true,
        message: "User device token successfully updated.",
      });
    } else {
      let userData = await user_edges
        .findOne({ user_id: authUserId, relation_id: req.relation_id, isDelete: false })
        .select("deviceToken");
        const conflictingRecords = await user_edges.find({
          deviceToken: { $in: body.deviceToken },
          isDelete: false,
        });
      
        const removalPromises = conflictingRecords
          .filter(record => record.user_id.toString() !== authUserId.toString())  
          .map(async (record) => {
            const tokensToRemove = Array.isArray(body.deviceToken) ? body.deviceToken : [body.deviceToken];
            
            const result = await user_edges.updateOne(
              {
                user_id: record.user_id, 
                relation_id: record.relation_id, 
                deviceToken: { $in: tokensToRemove },
              },
              { $pull: { deviceToken: { $in: tokensToRemove } } }
            );
            
            return result; 
          });
        
        if (removalPromises.length) {
          await Promise.all(removalPromises);
        }

      if (userData.deviceToken.length > 0 && userData.deviceToken) {
        let successdata = {
          notification: "",
          description: "",
          device_token: userData.deviceToken,
          collapse_key: "",
          badge_count: 0,
          notification_data: {
            type: "",
            content: [],
          },
        };
        send_notification(successdata);
      }
      await airtable_sync.findOneAndUpdate(
        { _id: authUserId, isDelete: false },
        { $pull: { deviceToken: body.deviceToken } }
      );

      return res
        .status(200)
        .json({ status: true, message: "User Logout successfully." });
    }
  } catch (err) {
    return res.status(500).json({
      status: false,
      message: `Internal server error! ${err.message}`,
    });
  }
};

// get all users and attendees
exports.getAllValidUserAndAttendees = async (req, res) => {
  try {
    let match = {
      email: { $ne: "<EMAIL>" },
      isDelete: false,
      $or: [
        { firebaseId: { $exists: true } },
        { attendeeDetail: { $exists: true } },
      ],
    };

    const userData = await airtable_sync.aggregate([
      {
        $match: match,
      },
      {
        $lookup: {
          from: "chat_users",
          let: { userId: "$_id" },
          pipeline: [
            {
              $match: {
                $expr: {
                  $eq: ["$userid", "$$userId"],
                },
              },
            },
          ],
          as: "userChat",
        },
      },
      {
        $addFields: {
          onlineStatus: {
            $cond: [
              { $gt: [{ $size: "$userChat" }, 0] },
              "$userChat.online",
              false,
            ],
          },
        },
      },
      {
        $project: {
          email: 1,
          profileImg: 1,
          onlineStatus: 1,
          firebaseId: 1,
          attendeeDetail: {
            name: "$attendeeDetail.name" ? "$attendeeDetail.name" : "",
            photo: "$attendeeDetail.photo" ? "$attendeeDetail.photo" : "",
          },
          first_name: 1,
          last_name: 1,
        },
      },
    ]);

    userData?.map((user) => {
      if (Array.isArray(user.onlineStatus)) {
        user.onlineStatus = user.onlineStatus[0];
      } else {
        user.onlineStatus = user.onlineStatus;
      }
    });

    const sortedUsers = userData.sort((a, b) => {
      if (
        a.firebaseId !== "" &&
        b.firebaseId !== "" &&
        a.first_name &&
        b.first_name
      ) {
        return a.first_name.localeCompare(b.first_name);
      } else {
        return a;
      }
    });

    if (sortedUsers)
      return res.status(200).json({
        status: true,
        message: `All Valid User list retrive successfully!`,
        data: sortedUsers,
      });
  } catch (error) {
    return res.status(200).json({ status: false, message: error.message });
  }
};

exports.getAllUsersMembersAndAttendees = async (req, res) => {
  try {
    const communityId = req.currentEdge.relation_id._id
    const pipeline =
      [
        {
          $match: {
            isDelete: false,
            relation_id: ObjectId(communityId),
            type: { $in: ["M", "GU", "CU"] }
          },
        },
        {
          $lookup:
          {
            from: "airtable-syncs",
            localField: "user_id",
            foreignField: "_id",
            as: "result",
            pipeline: [
              {
                $match: {
                  $and: [
                    {
                      $or: [
                        {
                          isDelete: false,
                        },
                        {
                          isDelete: {
                            $exists: false,
                          },
                        },
                      ],
                    },
                    {
                      $or: [
                        {
                          attendeeDetail: {
                            $ne: null,
                          },
                        },
                        {
                          firebaseId: {
                            $nin: ["", null],
                          },
                        },
                      ],
                    },
                  ],
                },
              },
              {
                $project: {
                  "Preferred Email": 1,
                  email: 1,
                  attendeeDetail: 1,
                  profileImg: 1,
                  partnerIcon: 1,
                  first_name: 1,
                  last_name: 1,
                  display_name: 1,
                },
              },
            ],
          },
        },
        {
          $unwind:
          {
            path: "$result",
            preserveNullAndEmptyArrays: false,
          },
        },
        {
          $replaceRoot:
          {
            newRoot: "$result",
          },
        },
      ]

    const allUsersMembersAndAttendees = await UserEdge.aggregate([...pipeline])
    if (allUsersMembersAndAttendees && allUsersMembersAndAttendees.length)
      return res.status(200).send(allUsersMembersAndAttendees);
    else return res.status(200).send([]);
  } catch (error) {
    return res.status(200).json({ status: false, message: error.message });
  }
};

exports.getUserProfile = async (req, res) => {
  try {
    const authUserId = req.authUserId;
    const userData = await airtable_sync.findOne({ _id: ObjectId(authUserId) });
    if (userData !== null) {
      return res.status(201).send({
        status: true,
        message: "User data retrive successfully.",
        data: userData,
      });
    } else {
      return res
        .status(401)
        .send({ status: false, message: "User details not found!" });
    }
  } catch (error) {
    return res
      .status(500)
      .json({ status: false, message: `Internal server error. ${error}` });
  }
};
/** code by SJ end **/

// update single users attendee details for event sync up fields
exports.airTableEventSyncUpForSingleUser = async (req, res) => {
  try {
    const authUserId = req.authUserId;
    let matchedEvents = [];
    let unMatchedEvents = [];
    if (req.body.localDate) {
      var localDate = new Date(req.body.localDate);
      localDate = moment(localDate, "YYYY-MM-DD").toDate();
      const activeUser = await airtable_sync
        .findById(ObjectId(authUserId), {
          _id: 1,
          firebaseId: 1,
          "Preferred Email": 1,
          "Upcoming Events Registered": 1,
          attendeeDetail: 1,
          first_name: 1,
          last_name: 1,
          "AT Database Status": { $in: [ "Pending Group Entrance", "Current Member", "New Member" ] },
        })
        .lean();
      if (activeUser) {
        const allEventList = await event.aggregate([
          {
            $addFields: {
              Date: {
                $let: {
                  vars: {
                    year: { $substr: ["$startDate", 6, 10] },
                    month: { $substr: ["$startDate", 0, 2] },
                    dayOfMonth: { $substr: ["$startDate", 3, 5] },
                  },
                  in: {
                    $toDate: {
                      $concat: ["$$year", "-", "$$month", "-", "$$dayOfMonth"],
                    },
                  },
                },
              },
            },
          },
          {
            $match: {
              isDelete: false,
              status: "published",
              Date: { $gt: localDate },
            },
          },
          {
            $project: {
              _id: 1,
              title: 1,
              airTableEventName: { $ifNull: ["$airTableEventName", ""] },
            },
          },
        ]);

        if (
          activeUser["Upcoming Events Registered"] &&
          activeUser["Upcoming Events Registered"] !== undefined &&
          activeUser["Upcoming Events Registered"] !== null &&
          activeUser["Upcoming Events Registered"] !== "" &&
          activeUser["Upcoming Events Registered"].length > 0
        ) {
          const attendetailExists =
            activeUser.attendeeDetail &&
              Object.keys(activeUser.attendeeDetail).length > 0
              ? true
              : false;

          const eventDataExists =
            activeUser.attendeeDetail &&
              Object.keys(activeUser.attendeeDetail).length > 0 &&
              activeUser.attendeeDetail.evntData
              ? true
              : false;

          var eventData =
            activeUser.attendeeDetail &&
              Object.keys(activeUser.attendeeDetail).length > 0 &&
              activeUser.attendeeDetail.evntData
              ? activeUser.attendeeDetail.evntData
              : [];

          var eventAttended = activeUser["Upcoming Events Registered"];

          allEventList.map((event) => {
            const eventMatch = eventAttended.filter((item) => {
              let tmpItem = item.replaceAll('"', "").trim();
              return tmpItem === event.airTableEventName.trim();
            });
            if (eventMatch.length > 0) {
              matchedEvents.push({
                event: event._id,
                privateProfile: false,
                member: true,
                speaker: false,
                partner: false,
                guest: false,
                partnerOrder: 0,
              });
            } else {
              unMatchedEvents.push(event);
            }
          });

          if (matchedEvents.length > 0) {
            if (attendetailExists && eventDataExists) {
              let memberEventDetails = activeUser.attendeeDetail;
              matchedEvents.map((matchEvnt) => {
                let userAttendeeExists = eventData.filter(
                  (eventObject) =>
                    eventObject.event.toString() === matchEvnt.event.toString()
                );
                if (userAttendeeExists.length === 0) {
                  memberEventDetails.evntData.push(matchEvnt);
                }
              });
              const upDateAttendeeData = await airtable_sync.findByIdAndUpdate(
                activeUser._id,
                { $set: { attendeeDetail: memberEventDetails } }
              );
            } else {
              if (attendetailExists && !eventDataExists) {
                let memberEventDetails = activeUser.attendeeDetail;
                memberEventDetails.evntData = matchedEvents;
                const upDateAttendeeData =
                  await airtable_sync.findByIdAndUpdate(activeUser._id, {
                    $set: { attendeeDetail: memberEventDetails },
                  });
              } else {
                let firstname = activeUser.first_name
                  ? activeUser.first_name
                  : "";
                let lastname = activeUser.last_name ? activeUser.last_name : "";
                let fullname = firstname + lastname;
                const upDateAttendeeData =
                  await airtable_sync.findByIdAndUpdate(activeUser._id, {
                    $set: {
                      attendeeDetail: {
                        email: activeUser["Preferred Email"],
                        name: fullname,
                        firstName: firstname,
                        lastName: lastname,
                        firebaseId: activeUser.firebaseId,
                        evntData: matchedEvents,
                      },
                    },
                  });
              }
            }
          }

          if (unMatchedEvents.length > 0) {
            let resDeleteEvents = unMatchedEvents.map(async (event) => {
              const userUnRegisteredEvent =
                await airtable_sync.findOneAndUpdate(
                  {
                    _id: ObjectId(authUserId),
                    "attendeeDetail.evntData": {
                      $elemMatch: { event: event._id },
                    },
                  },
                  {
                    $pull: { "attendeeDetail.evntData": { event: event._id } },
                  },
                  { new: true }
                );
            });
            await Promise.all([...resDeleteEvents]);
          }

          return res
            .status(200)
            .json({ status: true, message: "Attendees details updated." });
        } else {
          const allEventListWithOutAirTable = await event.aggregate([
            {
              $addFields: {
                Date: {
                  $let: {
                    vars: {
                      year: { $substr: ["$startDate", 6, 10] },
                      month: { $substr: ["$startDate", 0, 2] },
                      dayOfMonth: { $substr: ["$startDate", 3, 5] },
                    },
                    in: {
                      $toDate: {
                        $concat: [
                          "$$year",
                          "-",
                          "$$month",
                          "-",
                          "$$dayOfMonth",
                        ],
                      },
                    },
                  },
                },
              },
            },
            {
              $match: {
                isDelete: false,
                status: "published",
                Date: { $gt: localDate },
              },
            },
            {
              $project: {
                _id: 1,
              },
            },
          ]);

          allEventListWithOutAirTable.map(async (event) => {
            const userUnRegisteredEvent = await airtable_sync.findOneAndUpdate(
              {
                _id: ObjectId(authUserId),
                "attendeeDetail.evntData": {
                  $elemMatch: { event: event._id },
                },
              },
              { $pull: { "attendeeDetail.evntData": { event: event._id } } },
              { new: true }
            );
          });

          return res
            .status(200)
            .json({ status: true, message: "Attendees details updated." });
        }
      } else {
        return res
          .status(200)
          .json({ status: false, message: "No user found!" });
      }
    } else {
      return res
        .status(200)
        .json({ status: false, message: "Date query parameter are missing!" });
    }
  } catch (e) {
    return res
      .status(500)
      .json({ status: false, message: `Internal server error. ${e}` });
  }
};

/** user collaborator exists */
exports.userCollaboratorResources = async (req, res) => {
  try {
    const authUserId = req.authUserId;
    var user = await airtable_sync.findById(ObjectId(authUserId)).lean();

    if (user) {
      const InviteCollaborator = await inviteCollaborator
        .findOne(
          {
            email: user["Preferred Email"],
            isDelete: false,
          },
          { _id: 1, memberShipPlanDetails: 1 }
        )
        .lean();

      if (InviteCollaborator) {
        const planDetails = await MembershipPlan.findOne({
          _id: InviteCollaborator.memberShipPlanDetails.planId,
        }).populate("accessResources");
        const accessResourceList = planDetails.accessResources
          ? planDetails.accessResources
          : [];
        if (accessResourceList.length > 0) {
          return res.status(200).json({
            status: true,
            message: "Access resources list fetched",
            accessResourceList,
          });
        } else {
          return res.status(200).json({
            status: false,
            message: "Acces resources are not available for this user.",
          });
        }
      } else {
        return res
          .status(200)
          .json({ status: false, message: "Collaborator not exists." });
      }
    } else {
      return res
        .status(200)
        .json({ status: false, message: "user not exist." });
    }
  } catch (e) {
    console.log(e, "error");
    return res
      .status(200)
      .json({ status: false, message: "Something went wrong!" });
  }
};

// get all users Suggestion List
exports.getallusersSuggestionList = async (req, res) => {
  try {
    var match = {
      $or: [{ blocked: false }, { blocked: { $exists: false } }],
      isDelete: false,
      firebaseId: { $nin: ["", null] },
    };

    const data = await airtable_sync
      .find(match, {
        _id: 0,
        // "Preferred Email": 1,
        // email: 1,
        firstName: "$first_name",
        lastName: "$last_name",
        email: "$Preferred Email",
        first_name: 1,
        last_name: 1,
        display_name: 1,
      })
      .lean();
    return res.status(200).send(data);
  } catch (e) {
    return res.status(400).json({ status: false, message: e });
  }
};

exports.getAllAttendeeSuggestionList = async (req, res) => {
  try {
    var match = {
      attendeeDetail: { $ne: null },
      $or: [{ isDelete: false }, { isDelete: { $exists: false } }],
    };

    const data = await airtable_sync
      .find(match, {
        _id: 0,
        "Preferred Email": 1,
        name: "$attendeeDetail.name",
        firstName: "$attendeeDetail.firstName",
        lastName: "$attendeeDetail.lastName",
        first_name: 1,
        last_name: 1,
        display_name: 1,
      })
      .lean();
    return res.status(200).send(data);
  } catch (e) {
    return res.status(400).json({ status: false, message: e });
  }
};

exports.getBlockedUserSuggestionList = async (req, res) => {
  try {
    var match = {
      blocked: true,
      isDelete: false,
      firebaseId: { $nin: ["", null] },
    };
    const data = await airtable_sync
      .find(match, {
        _id: 0,
        "Preferred Email": 1,
        name: "$attendeeDetail.name",
        firstName: "$attendeeDetail.firstName",
        lastName: "$attendeeDetail.lastName",
        first_name: 1,
        last_name: 1,
        display_name: 1,
      })
      .lean();
    return res.status(200).send(data);
  } catch (e) {
    return res.status(400).json({ status: false, message: e });
  }
};

exports.getUserInfoByJWTToken = async (req, res) => {
  try {
    let token = req.headers.authorization;
    if (!token || token.length === 0) {
      return res.status(200).send({
        status: false,
        message: "No token provided!",
        invalidToken: true,
      });
    }

    var tokenCrop = token.replace("Bearer ", "");
    jwt.verify(
      tokenCrop,
      JWT_PEMCERT,
      { algorithm: "RS256" },
      async (err, decoded) => {
        if (err) {
          return res.status(200).send({
            status: false,
            message: "Invalid token!",
            err: err,
            invalidToken: true,
          });
        }

        const authDetail = decoded.sub.split("|");
        const authId = authDetail[1];
        const auth_provider = authDetail[0];
        let adminData = await AdminUser.findOne({
          oauthId: decoded.sub,
          isDelete: false,
        }).select("first_name last_name username email");

        if (adminData) {
          return res.status(200).json({
            status: true,
            message: "User details retrieved.",
            userInfo: adminData,
            userRole: "admin",
          });
        } else {
          let userData = await airtable_sync
            .findOne({
              $or: [{ firebaseId: authId }, { facebookLinkedinId: authId }],
              isDelete: false,
            })
            .select(
              "purchased_plan accessible_groups"
            );

          //get event Attendees
          matchAttendees = {
            user: userData._id,
            isDelete: false,
          };

          const list = await eventParticipantAttendees.aggregate([
            {
              $match: matchAttendees,
            },
            {
              $lookup: {
                from: "event_wise_participant_types",
                localField: "role",
                foreignField: "_id",
                pipeline: [
                  {
                    $match: {
                      isDelete: false,
                    },
                  },
                ],
                as: "roleData",
              },
            },
            {
              $match: { "roleData.role": "Member" },
            },
          ]);

          const userAllEventIds = list.map((item) => item.event);

          if (!userData) {
            return res
              .status(200)
              .json({
                status: false,
                message: "User not found.",
                invalidToken: true,
              });
          } else {
            return res
              .status(200)
              .json({
                status: true,
                message: "User details retrieved.",
                userInfo: userData,
                userRole: "user",
                userAllEventIds: userAllEventIds,
              });
          }
        }
      }
    );
  } catch (error) {
    return res.status(200).json({ status: false, message: error.message });
  }
};

exports.getUserInfoByJWTTokenV2 = async (req, res) => {
  try {
    const token = req.headers["authorization"].split(" ")[1];
    if (!token) {
      return res.status(401).send("Access denied!");
    }

    const tokenDecoded = jwt.verify(token, JWT_SECRET);

    if (tokenDecoded.currentEdge?.relation_id?._id) {
      req.relation_id = tokenDecoded.currentEdge.relation_id._id;
    }
    req.userId = tokenDecoded.userId;
    req.authUserId = tokenDecoded.userId;
    req.userName = tokenDecoded.userName;
    req.userEmail = tokenDecoded.userEmail;
    req.userAvatar = tokenDecoded.userAvatar;
    req.userStatus = tokenDecoded.userStatus;
    req.currentEdge = tokenDecoded.currentEdge;
    let userRole = "user";

    if (tokenDecoded?.currentEdge && tokenDecoded.currentEdge.owner && tokenDecoded.currentEdge.owner == true && tokenDecoded.currentEdge.type == "CO") {
      req.admin_Id = tokenDecoded.userId;
      userRole = "admin";
    }

    //for user
    let getUserData = await airtable_sync
      .findOne({
        _id: req.userId,
        isDelete: false
      })
      .select(
        "purchased_plan accessible_groups display_name"
      );

    //get event Attendees
    matchAttendees = {
      user: getUserData._id,
      isDelete: false,
    };

    const list = await eventParticipantAttendees.aggregate([
      {
        $match: matchAttendees,
      },
      {
        $lookup: {
          from: "event_wise_participant_types",
          localField: "role",
          foreignField: "_id",
          pipeline: [
            {
              $match: {
                isDelete: false,
              },
            },
          ],
          as: "roleData",
        },
      },
      {
        $match: { "roleData.role": "Member" },
      },
    ]);

    const userAllEventIds = list.map((item) => item.event);

    //tag data
    let getUserTag = await user_edges
      .findOne({
        user_id: req.userId,
        relation_id:req.relation_id,
        isDelete: false
      })
      .select(
        "tagId"
      );

    let userData = {
      relation_id: req.relation_id ? req.relation_id : "",
      userId: req.userId ? req.userId : "",
      authUserId: req.authUserId ? req.authUserId : "",
      userName: req.userName ? req.userName : "",
      userEmail: req.userEmail ? req.userEmail : "",
      userAvatar: req.userAvatar ? req.userAvatar : "",
      userStatus: req.userStatus ? req.userStatus : "",
      currentEdge: req.currentEdge ? req.currentEdge : "",
      admin_Id: req.admin_Id ? req.admin_Id : "",
      userRole: userRole,
      getUserData: getUserData ? getUserData : "",
      getUserTag: getUserTag ? getUserTag : "",
      userAllEventIds: userAllEventIds ? userAllEventIds : "",

    }

    if (!userData) {
      return res
        .status(200)
        .json({
          status: false,
          message: "User not found.",
          invalidToken: true,
        });
    } else {
      return res
        .status(200)
        .json({
          status: true,
          message: "User details retrieved.",
          userInfo: userData,
        });
    }
  } catch (error) {
    return res.status(200).json({ status: false, message: error.message });
  }
};


exports.getUsersListByIds = async (req, res) => {
  try {
    if (!req.params.ids) {
      return res
        .status(200)
        .json({ status: false, message: "Input parameters are missing!" });
    } else {
      const ids = req.params.ids.split(",");
      let objIds = [];
      ids.map((id) => {
        objIds.push(ObjectId(id));
      });
      const userData = await airtable_sync
        .find({
          _id: { $in: objIds },
          isDelete: false,
        })
        .select({
          attendeeDetail: 1,
          profileImg: 1,
          first_name: 1,
          last_name: 1,
          display_name: 1,
        })
        .populate({
          path: "accessible_groups",
          select: "groupTitle groupInfo ",
        })
        .populate({
          path: "purchased_plan",
          select: "plan_name -accessResources",
        });

      if (userData && userData.length > 0)
        return res.status(200).json({
          status: true,
          message: "User lists retrieved.",
          usersList: userData,
        });
      else
        return res
          .status(200)
          .json({
            status: false,
            message: "User lists not found.",
            usersList: [],
          });
    }
  } catch (error) {
    return res.status(200).json({ status: false, message: error.message });
  }
};

// get Edit Document Detail(group name )(admin Api)
exports.getEditDocDetail = async (req, res) => {
  try {
    const body = req.body;
    const userData = await airtable_sync.find(
      { _id: { $in: body.restrictedAccessUserId }, isDelete: false },
      {
        _id: 1,
        attendeeDetail: 1,
        first_name: 1,
        last_name: 1,
        'Preferred Email': 1,
        display_name: 1,
      }
    );
    const membershipPlanData = await membership_plan.find(
      { _id: { $in: body.restrictedAccessMembershipPlanId }, isDelete: false },
      { _id: 1, plan_name: 1, accessResources: 0 }
    );
    const groupData = await Group.find(
      { _id: { $in: body.restrictedAccessGroupId }, isDelete: false },
      { _id: 1, groupTitle: 1, groupInfo: 1 }
    );
    const eventData = await event.find(
      { _id: { $in: body.restrictedAccessEventId }, isDelete: false },
      { _id: 1, title: 1, subcategory: 0, category: 0, tag: 0 }
    );
    const tagData = await ContentArchive_tag.find(
      { _id: { $in: body.restrictedAccessTagId }, isDelete: false }, 
      { _id: 1, name: 1 }
    );

    const documentDetail = {
      userData: userData,
      membershipPlanData: membershipPlanData,
      groupData: groupData,
      eventData: eventData,
      tagData: tagData,
    };
    if (documentDetail)
      return res
        .status(200)
        .json({
          status: true,
          message: "User lists retrieved.",
          data: documentDetail,
        });
    else
      return res
        .status(200)
        .json({ status: false, message: "User lists not found.", data: [] });
  } catch (error) {
    return res.status(200).json({ status: false, message: error.message });
  }
};

// get Users we want to add rules
exports.getDocUserCount = async (req, res) => {
  try {

    let body = req.body;
    let match;

    //get event Attendee users
    let matchAttendees, uniqueEventUserIds;
    if (body.restrictedAccessEventId) {
      const eventIds = body.restrictedAccessEventId.map((id) => new ObjectId(id));

      matchAttendees = {
        event: { $in: eventIds },
        isDelete: false,
      };

      const list = await eventParticipantAttendees.aggregate([
        {
          $match: matchAttendees
        },
        {
          $lookup: {
            from: "event_wise_participant_types",
            localField: "role",
            foreignField: "_id",
            pipeline: [
              {
                $match: {
                  isDelete: false,
                },
              },
            ],
            as: "roleData",
          },
        },
        {
          $match: { "roleData.role": "Member" }
        },
      ]);

      const eventUserIds = list.map((item) => item.user);
      const uniqueUserIds = [...new Set(eventUserIds)];
      uniqueEventUserIds = Array.from(new Set(uniqueUserIds));
    }

    match = { isDelete: false, $or: [{ blocked: false }, { blocked: { $exists: false } }], firebaseId: { $nin: ["", null] }, };
    if (body.documentAccessType === "restricted") {
      const uniqueAllUserIds = [...new Set([...body.restrictedAccessUserId, ...(uniqueEventUserIds || [])]),];

      match = {
        ...match,
        $or: [
          { _id: { $in: uniqueAllUserIds } },
          { purchased_plan: { $in: body.restrictedAccessMembershipPlanId } },
          { accessible_groups: { $in: body.restrictedAccessGroupId } },
        ],
      };
    } else {
      match = {
        ...match,
        isCollaborator: false
      };
    }

    const userData = await airtable_sync.find(match, {
      _id: 1,
      first_name: { '$ifNull': ['$first_name', ''] },
      last_name: { '$ifNull': ['$last_name', ''] },
      'Preferred Email': 1,
    })
      .populate({ path: "accessible_groups", select: "groupTitle groupInfo " })
      .populate({
        path: "purchased_plan",
        select: "plan_name -accessResources",
      })
      .populate({
        path: "attendeeDetail",
        populate: {
          path: "evntData",
          populate: {
            path: "event",
            select: { _id: 1, title: 1 },
          },
        },
      });

    if (userData.length !== 0) {
      return res.status(200).json({
        status: true,
        message: "Get user count list successfully!",
        data: userData,
      });
    } else {
      return res.status(200).json({
        status: false,
        message: "Data not found!",
      });
    }
  } catch (error) {
    return res.status(500).json({ status: false, message: error.message });
  }
};

/** auth user already exist */
exports.isAuthUserExists = async (req, res) => {
  try {
    const { firebaseId } = req.body;

    var user = await airtable_sync
      .findOne({
        $and: [
          { firebaseId: { $exists: true } },
          { firebaseId: { $ne: null } },
          { $or: [{ firebaseId: firebaseId }, { facebookLinkedinId: firebaseId }] },
        ],
      })
      .lean();

    if (user) {
      return res
        .status(200)
        .json({
          status: true,
          message: "Auth user already exists!",
          data: user,
        });
    } else {
      return res
        .status(200)
        .json({ status: false, message: "Auth user not exists." });
    }
  } catch (e) {
    console.log(e, "error");
    return res
      .status(200)
      .json({ status: false, message: "Something went wrong!" });
  }
};

exports.getuserInfofromauth0 = async (req, res) => {
  try {
    const { authid } = req.params;
    const auth0token = await getAuth0Token();
    var options = {
      method: "GET",
      url: "https://dev-yc4k4-ud.us.auth0.com/api/v2/users/" + authid,
      headers: {
        "content-type": "application/json",
        authorization: auth0token,
      },
    };

    axios
      .request(options)
      .then(async function (response) {
        if (response.data) {
          const data = response.data;
          res
            .status(200)
            .json({ status: true, message: "User found!", data: data });
        } else {
          res.status(200).json({ status: false, message: "User not found!" });
        }
      })
      .catch((e) => {
        res
          .status(200)
          .json({ status: false, error: e, message: "User not found!" });
      });
  } catch (e) {
    console.log(e);
    res
      .status(200)
      .json({ status: false, message: "Something went wrong!", error: e });
  }
};

exports.getallusersForMap = async (req, res) => {
  try {
    const sortField =
      req.query.sortField === "firstName"
        ? "first_name"
        : req.query.sortField === "lastName"
          ? "last_name"
          : req.query.sortField === "email"
            ? "Preferred Email"
            : "createdAt";
    const sortType = req.query.sortType === "Asc" ? 1 : -1;
    var match = {
      $or: [{ blocked: false }, { blocked: { $exists: false } }],
      isDelete: false,
      firebaseId: { $nin: ["", null] },
    };

    const allUsersMembers = await UserEdge.find({type:"M", relation_id: new ObjectId(req.relation_id), isDelete: false }, { user_id: 1 });
    if(allUsersMembers.length){
      let allUsersMembersTemp = allUsersMembers.map(id => (new ObjectId(id.user_id)));
      match._id = {$in : allUsersMembersTemp };
    }
    var search = "";
    if (req.query.search) {
      search = req.query.search;
      match = {
        ...match,
        $or: [
          {
            "Preferred Email": { $regex: ".*" + search + ".*", $options: "i" },
          },
          {
            "attendeeDetail.name": {
              $regex: ".*" + search + ".*",
              $options: "i",
            },
          },
          {
            "attendeeDetail.firstName": {
              $regex: ".*" + search + ".*",
              $options: "i",
            },
          },
          {
            "attendeeDetail.lastName": {
              $regex: ".*" + search + ".*",
              $options: "i",
            },
          },
          { first_name: { $regex: ".*" + search + ".*", $options: "i" } },
          { last_name: { $regex: ".*" + search + ".*", $options: "i" } },
          { "display_name": { $regex: ".*" + search + ".*", $options: "i" }, },
        ],
      };
    }

    const data = await airtable_sync
      .find(match, {
        isDelete: 1,
        State: 1,
        City: 1,
        Country: 1,
        State: 1,
        Zip: 1,
        Hobbies: 1,
        "About Me": 1,
        "Facebook Profile Link": 1,
        Categories: 1,
        profileImg: 1,
        first_name: { $ifNull: ["$first_name", ""] },
        last_name: { $ifNull: ["$last_name", ""] },
        display_name: { $ifNull: ["$last_name", ""] },
        userName: {
          $concat: [
            { $ifNull: ["$first_name", ""] },
            " ",
            { $ifNull: ["$last_name", ""] },
          ],
        },
      })
      .lean()
      // .collation({ locale: "en" })
      .sort({ [`${sortField}`]: sortType });

    var finalArr = [];

    let updateUserArr = data.map(async (user) => {
      var address = "";
      if (user.Country && user.State && user.City && user.Zip) {
        address = `${user.State} ${user.City} ${user.Country} ${user.Zip}`;
      } else if (user.State && user.City && user.Zip) {
        address = `${user.State} ${user.City} ${user.Zip}`;
      } else if (user.City && user.Zip) {
        address = `${user.City} ${user.Zip}`;
      } else if (user.City) {
        address = `${user.City}`;
      } else {
        address = "";
      }

      if (address && address !== "") {
        let lat = "";
        let lng = "";
        let response = await axios
          .get(
            `https://maps.googleapis.com/maps/api/geocode/json?address=${encodeURIComponent(
              address
            )}&key=${process.env.GOOGLE_PLACE_KEY}`
          )
          .catch((e) => {
            console.log(e);
          });

        if (response) {
          const location =
            response.data &&
              response.data.results &&
              response.data.results[0] &&
              response.data.results[0].geometry
              ? response.data.results[0].geometry.location
              : "";
          if (location != "") {
            lat = parseFloat(location.lat);
            lng = parseFloat(location.lng);
          }
        }
        if (lat !== "" && lng !== "") {
          finalArr.push({
            id: user._id,
            name: user.userName,
            AboutMe: user["About Me"],
            profileImg: user.profileImg,
            goodAt: user.Categories,
            FacebookProfileLink: user["Facebook Profile Link"],
            address: address,
            lat: lat,
            lng: lng,
            hobbies:
              user.Hobbies && user.Hobbies.length > 0
                ? user.Hobbies.join(", ")
                : "",
          });
        }
      }
    });

    await Promise.all([...updateUserArr]);

    return res
      .status(200)
      .send({ status: true, message: "user found", data: finalArr });
  } catch (e) {
    return res.status(400).json({ status: false, message: e });
  }
};

exports.addDeviceToken = async (req, res) => {
  try {
    const { authUserId } = req;
    const userDetail = await airtable_sync
      .findById(authUserId)
      .select("deviceToken");
    if (
      userDetail &&
      userDetail.deviceToken &&
      userDetail.deviceToken.includes(req.body.deviceToken)
    ) {
      return res
        .status(200)
        .json({ status: true, message: "Device token already exist!" });
    } else if (
      userDetail &&
      userDetail.deviceToken &&
      userDetail.deviceToken.length > 0
    ) {
      let result = {};
      if (userDetail.deviceToken.length >= 5) {
        let deviceToken = userDetail.deviceToken;
        let newTokenArray = [req.body.deviceToken];
        for (let j = deviceToken.length - 1; j >= 0; j--) {
          let token = deviceToken[j];
          if (token.length > 10 && newTokenArray.length < 5) {
            newTokenArray.push(token);
          }
        }
        var newTokenArrayTemp = newTokenArray.reverse();
        result = await airtable_sync.findByIdAndUpdate(
          authUserId,
          { deviceToken: newTokenArrayTemp },
          { new: true }
        );
      } else {
        result = await airtable_sync.findByIdAndUpdate(
          authUserId,
          { $push: { deviceToken: req.body.deviceToken } },
          { new: true }
        );
      }
      return res
        .status(200)
        .json({ status: true, message: "Device token added!", data: result });
    } else if (userDetail) {
      let result = await airtable_sync.findByIdAndUpdate(
        authUserId,
        {
          deviceToken: [req.body.deviceToken],
        },
        { new: true }
      );
      return res
        .status(200)
        .json({ status: true, message: "Device token added!", data: result });
    } else {
      return res
        .status(200)
        .json({ status: false, message: "User not found!" });
    }
  } catch (e) {
    return res.status(200).json({ status: false, message: e });
  }
};

exports.storeWebDeviceToken = async (req, res) => {
  try {
    const { authUserId } = req;
    const userDetail = await airtable_sync
      .findById(authUserId)
      .select("webDeviceToken");
    if (
      userDetail &&
      userDetail.webDeviceToken &&
      userDetail.webDeviceToken.includes(req.body.deviceToken)
    ) {
      return res
        .status(200)
        .json({ status: true, message: "Device token already exist!" });
    } else if (
      userDetail &&
      userDetail.webDeviceToken &&
      userDetail.webDeviceToken.length > 0
    ) {
      let result = {};
      if (userDetail.webDeviceToken.length >= 5) {
        let webDeviceToken = userDetail.webDeviceToken;
        let newTokenArray = [req.body.deviceToken];
        for (let j = webDeviceToken.length - 1; j >= 0; j--) {
          let token = webDeviceToken[j];
          if (token.length > 10 && newTokenArray.length < 5) {
            newTokenArray.push(token);
          }
        }
        var newTokenArrayTemp = newTokenArray.reverse();
        result = await airtable_sync.findByIdAndUpdate(
          authUserId,
          { webDeviceToken: newTokenArrayTemp },
          { new: true }
        );
      } else {
        result = await airtable_sync.findByIdAndUpdate(
          authUserId,
          { $push: { webDeviceToken: req.body.deviceToken } },
          { new: true }
        );
      }
      return res
        .status(200)
        .json({ status: true, message: "Device token added!", data: result });
    } else if (userDetail) {
      let result = await airtable_sync.findByIdAndUpdate(
        authUserId,
        {
          webDeviceToken: [req.body.deviceToken],
        },
        { new: true }
      );
      return res
        .status(200)
        .json({ status: true, message: "Device token added!", data: result });
    } else {
      return res
        .status(200)
        .json({ status: false, message: "User not found!" });
    }
  } catch (e) {
    return res.status(200).json({ status: false, message: e });
  }
};

//function to update all users detail into MDS-Docment
exports.updateAllUsersDetailInMDSDocumentAPI = async (req, res) => {
  try {
    let ownerList = [];
    try {
      const resTemp = await axios({
        method: "get",
        url: GATEWAY_DOMAIN+"/api/document/api/v1/documents/get-all-owner-ids",
        // url: "http://localhost:8083/api/document/api/v1/documents/get-all-owner-ids",
        data: {relation_id:req.relation_id},
      });

      ownerList = resTemp.data.data.data;
    } catch (error) {
      ownerList = [];
    }
    ownerList = ownerList.map((ids) => ObjectId(ids));

    let condition = {
      $or: [{ isDelete: false }, { isDelete: { $exists: false } }],
      // auth0Id: { $nin: ["", null] }
    };
    if (ownerList.length > 0) {
      condition["_id"] = { $in: ownerList };
    }
    const allActiveUsers = await airtable_sync.find(condition).lean();
    let tokenPayload = [];
    for (let i = 0; i < allActiveUsers.length; i++) {
      let ownerName = "";
      let display_name = "";
      let user = allActiveUsers[i];
      if (user && user.first_name && user.last_name) {
        ownerName = user.first_name + " " + user.last_name;
      } else if (
        user &&
        user.attendeeDetail &&
        user.attendeeDetail["firstName"] &&
        user.attendeeDetail["lastName"]
      ) {
        ownerName =
          user.attendeeDetail["firstName"] +
          " " +
          user.attendeeDetail["lastName"];
      } else if (user && user.attendeeDetail && user.attendeeDetail["name"]) {
        ownerName = user.attendeeDetail["name"];
      }
      if (ownerName != "") {
        let payload = {
          id: allActiveUsers[i]["_id"],
          profileImg: allActiveUsers[i]["profileImg"]
            ? allActiveUsers[i]["profileImg"]
            : "",
          name: ownerName,
          display_name : user.display_name ? user.display_name : "",
          email: user["Preferred Email"] ? user["Preferred Email"] : "",
        };
        let token = jwt.sign(payload, process.env.JWT_SECRET, {
          expiresIn: "5m",
        });
        tokenPayload.push(token);
      }
    }
    try {
      await axios({
        method: "post",
        url: GATEWAY_DOMAIN+"/api/document/api/v1/documents/update-user-info-V2",
        // url: "http://localhost:8083/api/document/api/v1/documents/update-user-info-V2",
        data: {
          tokens: tokenPayload,
        },
      });
    } catch (error) {
      console.log({ error });
    }
    return res.status(200).json({ status: true, message: "User data synced!" });
  } catch (e) {
    console.log("updateAllUsersDetailInMDSDocumentAPI", { e });
    return res.status(200).json({ status: false, message: e });
  }
};

exports.ssoLoginUser = async (req, res) => {
  try {
    const { email } = req;

    if (email !== undefined && email !== null) {
      const userData = await airtable_sync
        .findOne({ "Preferred Email": email })
        .select({
          "Preferred Email": 1,
          attendeeDetail: 1,
          first_name: 1,
          last_name: 1,
          display_name: 1,
        });
      if (userData) {
      } else {
        return res
          .status(200)
          .json({ status: false, message: "User not exists!" });
      }
    } else {
      return res
        .status(200)
        .json({ status: false, message: "Input payload missing!" });
    }
    //return res.status(200).json({ status: true, message: "Device token added!", data: result });
  } catch (e) {
    return res.status(200).json({ status: false, message: e });
  }
};

//script for add first_name and last_name
exports.syncNamingStructure = async (req, res) => {
  try {
    const allActiveUsers = await airtable_sync
      .find({
        // _id: ObjectId("64b64e65bbb9f3efe15af00e"),
        // $or: [{ isDelete: false }, { isDelete: { $exists: false } }],
        //  auth0Id: { $nin: ["", null] }
      }).lean();
    let success = 0;
    let fail = 0;
    for (let i = 0; i < allActiveUsers.length; i++) {
      let first_name = "";
      let last_name = "";
      let user = allActiveUsers[i]; 
      if (user && user.attendeeDetail && user.attendeeDetail["firstName"] && first_name == "") {
        first_name = user.attendeeDetail["firstName"];
      }
      if (user && user.attendeeDetail && user.attendeeDetail["lastName"] && last_name == "") {
        last_name = user.attendeeDetail["lastName"];
      }
      console.log({ i, total: allActiveUsers.length });

      if (first_name != "" || last_name != "") {
        let update = await airtable_sync.findOneAndUpdate(
          { _id: ObjectId(user._id) },
          { $set: { first_name: first_name, last_name: last_name } },
          { new: true }
        );
        console.log({ first_name, last_name, i, id: user._id });
        success = success + 1;
      }
    }
    return res
      .status(200)
      .json({
        status: true,
        message: "User data synced!",
        data: { success, fail },
      });
  } catch (e) {
    console.log("updateAllUsersDetailInMDSDocumentAPI", { e });
    return res.status(200).json({ status: false, message: e });
  }
};

exports.convertCsvBufferToJson = async (csvBuffer) => {
  const jsonArray = [];
  return new Promise((resolve, reject) => {
    // Create a readable stream from the CSV buffer
    const stream = Readable.from([csvBuffer.toString()]);
    stream
      .pipe(csv())
      .on("data", (row) => {
        jsonArray.push(row);
      })
      .on("end", () => {
        resolve(jsonArray);
      })
      .on("error", (err) => {
        reject(err);
      });
  });
};


exports.importUserFromCSV = async (req, res) => {
  try {
    let jsonArrayNew = [];
    let addedEmail = [];
    let skipEmail = [];
    let BooleanField = [
      "active",
      "blocked",
      "verified",
      "isSocial",
      "isDelete",
      "register_status",
      "personalDetail_status",
      "payment_status",
      "QA_status",
      "deleteConversation",
      "migrate_user_status",
      "deactivate_account_request",
      "isCollaborator",
    ];
    let DBField = [
      "# of Days Since MDS Only Census",
      "AT Database Status",
      "About Me",
      "Chapter Affiliation",
      "City",
      "Country",
      "Events Attended",
      "Facebook Profile Link",
      "Preferred Email",
      "State",
      "Verification URL",
      "Zip",
      "attendeeDetail",
      // "updatedAt",
      "auth0Id",
      "email",
      "passcode",
      "profileImg",
      "deviceToken",
      "isDelete",
      "facebookLinkedinId",
      "socialauth0id",
      "notificationFor",
      "deleted_group_of_user",
      "partnerIcon",
      "userEvents",
      "Upcoming Events",
      "Upcoming Events Registered",
      "QA_status",
      "accessible_groups",
      "active",
      "blocked",
      "blocked_by_who_chat",
      "blocked_chat",
      "clear_chat_data",
      "deactivate_account_request",
      "forgot_ticket",
      "isSocial",
      "last_activity_log",
      "last_login",
      "migrate_user",
      "migrate_user_status",
      "payment_status",
      "personalDetail_status",
      "provider",
      "register_status",
      "savePosts",
      "saveVideos",
      "secondary_email",
      "star_chat",
      "user_role",
      "verified",
      "video_history_data",
      "createdAt",
      "payment_id",
      "purchased_plan",
      "__v",
      "joinDate",
      "muteNotification",
      "no_of_team_mate",
      "webDeviceToken",
      "isCollaborator",
      "invitationAccepted",
      "sharedUserDetails",
      "memberShipPlanDetails",
      "% in Other Asia Manufacture",
      "notificationBadgeNotDisplay",
      "refresh_token",
      "status",
      "user_edges",
      "First Name",
      "Last Name",

    ];
    // Example usage:
    try {
      let buf = req.file.buffer;
      const csvBuffer = Buffer.from(buf);
      const jsonArray = await this.convertCsvBufferToJson(csvBuffer);
      jsonArrayNew = jsonArray;
    } catch (error) {
      jsonArrayNew = [];
    }

    for (let i = 0; i < jsonArrayNew.length; i++) {
      if (jsonArrayNew[i] && jsonArrayNew[i]["Preferred Email"]) {
        let userDataTemp = {};
        let user = jsonArrayNew[i];
        let email = user["Preferred Email"];
        let verification_url = user["Verification URL"];

        const userData = await airtable_sync.findOne({
          "Preferred Email": email,
        });
        if (!userData) {
          let keys = Object.keys(user);
          for (let i = 0; i < keys.length; i++) {
            let keyTemp = keys[i];
            let valueTemp = user[keys[i]];
            if (DBField.includes(keyTemp)) {
              if (BooleanField.includes(keyTemp)) {
                if ( valueTemp == "" || valueTemp == "false" || valueTemp == "False" || valueTemp == "FALSE" || valueTemp == false ) {
                  userDataTemp[keyTemp] = false;                
                } else if ( valueTemp == "checked" ) {
                  userDataTemp[keyTemp] = true;
                } else if (  valueTemp == "true" || valueTemp == "True" || valueTemp == "TRUE" || valueTemp == true ) {
                  userDataTemp[keyTemp] = true;
                } else {
                  userDataTemp[keyTemp] = valueTemp;
                }
              } else {
                  userDataTemp[keyTemp] = valueTemp;
              }
            }
          }
          let first_name = userDataTemp["First Name"] ? userDataTemp["First Name"] : "";
          let last_name = userDataTemp["Last Name"] ? userDataTemp["Last Name"] : "";
          userDataTemp.first_name = first_name;
          userDataTemp.last_name = last_name
          userDataTemp.display_name = (first_name + " " + last_name).trim()
          userDataTemp.isDelete = false;
          userDataTemp.active = true;

          let createdUser = await airtable_sync.create(userDataTemp);

          addedEmail.push({ email, verification_url });
        } else {
          const updatedUser = await airtable_sync.updateOne( { "Preferred Email": email, }, { $set: { "Verification URL": verification_url } },  { new: true } );
          skipEmail.push({ email, verification_url, old_verification_url:userData["Verification URL"] });
      
        }
      }
    }
    if(addedEmail.length > 0 || skipEmail.length > 0){
      let importUserData = await importCsvUser.create({addedEmail:addedEmail,skipEmail:skipEmail});

    }
   
    return res.status(200).json({
      status: true,
      message: "User data synced!",
      addedEmail,
      skipEmail,
      add: addedEmail.length,
      skip: skipEmail.length,
      total: jsonArrayNew.length,
    });
  } catch (e) {
    await debugErrorLogs.createErrorLogs(e, "importUserFromCSV", {});
    return res.status(200).json({ status: false, message: e });
  }
};

// update single users attendee details for event sync up fields
exports.airTableEventSyncUpForSingleUserV2 = async (req, res) => {
  try {
    const authUserId = ObjectId(req.authUserId);
    let newAttendeeList = [];
    let removeAttendeeList = [];
    const community = await script.getMdsCommunity();
    const allAttendeeList = await eventParticipantAttendees.find({
      isDelete: false,
      relation_id: community._id,
      user: authUserId,
    });
    const filterAttendeeList = await user_edges.find({
      relation_id: community._id,
      type: "M",
      isDelete: false,
      user_id: authUserId,
    });
    let userIds = filterAttendeeList.map((attendee) => attendee.user_id);
    if (userIds && userIds.length > 0) {
      const allActiveUsers = await airtable_sync
        .find(
          {
            _id: { $in: userIds.map((id) => new ObjectId(id)) },
            $or: [{ isDelete: false }, { isDelete: { $exists: false } }],
            $and: [
              {
                $or: [
                  {
                    "Events Attended": {
                      $exists: true,
                      $ne: null,
                      $not: { $size: 0 },
                      $type: "array",
                    },
                  },
                  {
                    "Upcoming Events Registered": {
                      $exists: true,
                      $ne: null,
                      $not: { $size: 0 },
                      $type: "array",
                    },
                  },
                ],
              },
              {
                "AT Database Status": {
                  $in: [
                    "Pending Group Entrance",
                    "Current Member",
                    "New Member",
                  ],
                },
              },
            ],
          },
          {
            _id: 1,
            first_name: 1,
            last_name: 1,
            auth0Id: 1,
            "Preferred Email": 1,
            "Upcoming Events Registered": 1,
            "Events Attended": 1,
            attendeeDetail: 1,
          }
        )
        .lean();

      const allUsersUpdatedData = [];
      let unMatchedEvents = [];
      const allEventList = await Event.aggregate([
        {
          $match: {
            isDelete: false,
            locationType: "inPerson",
            $and: [
              { airTableEventName: { $exists: true } },
              { airTableEventName: { $ne: null } },
              { airTableEventName: { $ne: "" } },
            ],
            relation_id: ObjectId(community._id),
          },
        },
        {
          $lookup: {
            from: "event_wise_participant_types",
            let: { eventId: "$_id" },
            pipeline: [
              {
                $match: {
                  $expr: {
                    $and: [
                      { $eq: ["$event", "$$eventId"] },
                      { $eq: ["$isDelete", false] },
                    ],
                  },
                },
              },
              {
                $project: { _id: 1, role: 1 },
              },
            ],
            as: "roleDetails",
          },
        },
        {
          $lookup: {
            from: "event_participant_attendees",
            let: { eventId: "$_id", roleId: "$roleDetails._id" },
            pipeline: [
              {
                $match: {
                  $expr: {
                    $and: [
                      { $eq: ["$event", "$$eventId"] },
                      { $eq: ["$isDelete", false] },
                    ],
                  },
                },
              },
              {
                $project: { _id: 1, user: 1, role: 1 },
              },
            ],
            as: "participantDetails",
          },
        },
        // Stage 7: Project final fields
        {
          $project: {
            _id: 1,
            airTableEventName: 1,
            participantDetails: 1,
            roleDetails: 1,
          },
        },
      ]);

      if (allActiveUsers) {
        for (let index = 0; index < allActiveUsers.length; index++) {
          let activeUser = allActiveUsers[index];
          if (activeUser) {
            let arrUserEvents = [];
            if (
              activeUser["Upcoming Events Registered"] &&
              activeUser["Upcoming Events Registered"] !== undefined &&
              activeUser["Upcoming Events Registered"] !== null &&
              activeUser["Upcoming Events Registered"] !== "" &&
              activeUser["Upcoming Events Registered"].length > 0
            ) {
              const upCommingStr =
                activeUser["Upcoming Events Registered"][0] !== null &&
                activeUser["Upcoming Events Registered"][0] !== ""
                  ? activeUser["Upcoming Events Registered"][0].toString()
                  : "";
              if (upCommingStr !== "") {
                arrUserEvents = upCommingStr.split(",");
              }
            }

            if (
              activeUser["Events Attended"] &&
              activeUser["Events Attended"] !== undefined &&
              activeUser["Events Attended"] !== null &&
              activeUser["Events Attended"] !== "" &&
              activeUser["Events Attended"].length > 0
            ) {
              const eventAttendedStr =
                activeUser["Events Attended"][0] !== null &&
                activeUser["Events Attended"][0] !== ""
                  ? activeUser["Events Attended"][0].toString()
                  : "";
              if (eventAttendedStr !== "") {
                arrUserEvents = arrUserEvents.concat(
                  eventAttendedStr.split(",")
                );
              }
            }

            arrUserEvents = arrUserEvents.map((item) => {
              return item.replaceAll('"', "").trim();
            });

            let eventAttended = arrUserEvents;
            const matchingObjects = allEventList.filter((obj) =>
              eventAttended.includes(obj.airTableEventName)
            );
            unMatchedEvents = allEventList.filter(
              (obj) => !eventAttended.includes(obj.airTableEventName)
            );
            matchingObjects.map((obj) => {
              const memberRole =
                obj.roleDetails.filter((roleObj) => roleObj.role === "Member")
                  .length > 0
                  ? obj.roleDetails.filter(
                      (roleObj) => roleObj.role === "Member"
                    )[0]
                  : null;
              if (
                obj.participantDetails.filter(
                  (user) => user.user.toString() === activeUser._id.toString()
                ).length === 0
              ) {
                //add isManuallyAdded=false flag
                if (memberRole !== null) {
                  newAttendeeList.push({
                    user: activeUser._id,
                    event: obj._id,
                    role: memberRole._id,
                    isDelete: false,
                    isManuallyAdded: false,
                  });
                }
              }
            });
            unMatchedEvents.map((obj) => {
              const memberRole =
                obj.roleDetails.filter((roleObj) => roleObj.role === "Member")
                  .length > 0
                  ? obj.roleDetails.filter(
                      (roleObj) => roleObj.role === "Member"
                    )[0]
                  : null;
              if (memberRole !== null) {
                if (
                  obj.participantDetails.filter(
                    (userObj) =>
                      userObj.role.toString() === memberRole._id.toString() &&
                      userObj.user.toString() === activeUser._id.toString()
                  ).length > 0
                ) {
                  removeAttendeeList.push({
                    user: activeUser._id,
                    event: obj._id,
                    role: memberRole._id,
                  });
                }
              }
            });
          }
        }

        if (newAttendeeList.length > 0) {
          const eventParticipantAttendeesCreated =
            await eventParticipantAttendees.insertMany(newAttendeeList);
        }

        if (removeAttendeeList.length > 0) {
          for (let j = 0; j < removeAttendeeList.length; j++) {
            let attendee = removeAttendeeList[j];
            //check attendee isManuallyAdded=false flag
            const alreadyExists = allAttendeeList.find(
              (item) =>
                item.user.toString() === attendee.user.toString() &&
                item.event.toString() === attendee.event.toString() &&
                item.role.toString() === attendee.role.toString()
            );

            if (
              alreadyExists &&
              alreadyExists.isManuallyAdded !== undefined &&
              alreadyExists.isManuallyAdded !== null &&
              !alreadyExists.isManuallyAdded
            ) {
              const userUnRegisteredEvent =
                await eventParticipantAttendees.findOneAndUpdate(
                  {
                    user: attendee.user,
                    event: attendee.event,
                    role: attendee.role,
                    isManuallyAdded: false,
                    isDelete: false,
                  },
                  {
                    isDelete: true,
                  },
                  { new: true }
                );
            }
          }
        }
        return res
          .status(200)
          .json({ status: true, message: "Attendees details updated." });
      } else {
        return res
          .status(200)
          .json({ status: false, message: "No user found!" });
      }
    } else {
      return res.status(200).json({ status: false, message: "No user found!" });
    }
  } catch (error) {
    return res
      .status(500)
      .json({ status: false, message: `Internal server error. ${error}` });
  }
};