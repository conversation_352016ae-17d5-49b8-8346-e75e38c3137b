require("dotenv").config();
const userCustomTable = require("../../database/models/userCustomField");
const customFieldSubmission = require("../../database/models/userCustomFieldSubmission");
const User = require("../../database/models/airTableSync");
const debugErrorLogs = require("../../middleware/debugErrorLogs");
const { user_edges } = require("../../microservices/user/components/user-edges/database/models");
const publishMessage=require("../../microservices/user/components/users/entry-points/message-queue/publisher")
const { fetchSubscriptionData } = require("../../microservices/user/utils/comman");
const { ObjectId } = require("mongodb");
const AWS = require("aws-sdk");
const { AddorRemoveFromChannelOnuserProfileUpdate } = require("../ChatChannelHelperFunction");
let s3 = new AWS.S3({
  accessKeyId: process.env.AWS_ID,
  secretAccessKey: process.env.AWS_SECRET,
  Bucket: process.env.AWS_BUCKET,
});

// create user custom field from admin side
exports.createUserCustomField = async (req, res) => {
    try {
        let { label, placeholder, type, choices, info, is_user_editable, is_user_visible, required, error_message } = req.body;
        const ids = await userCustomTable.find({ relation_id: ObjectId(req.relation_id), isDelete: false }, { _id: 1, label: 1, order: 1}).sort({ order: -1 });
        let fieldOrder = (ids && ids.length > 0) ? ids[0].order + 1 : 1;

        if (!label) {
            return res.status(200).json({ status: false, message: `Custom field name is required!` });
        } else {
            const uniqueName = ids.some(name => name.label === label);
            if (uniqueName) {
                return res.status(400).json({ status: false, message: "This field name already exists.", })
            } else {
                const newCustomField = new userCustomTable({
                    label: label,
                    placeholder: placeholder,
                    type: type,
                    choices: choices,
                    info: info,
                    is_user_editable: is_user_editable,
                    is_user_visible: is_user_visible,
                    required: required,
                    error_message: error_message,
                    order: fieldOrder,
                    relation_id: ObjectId(req.relation_id),
                });

                const customFieldData = await newCustomField.save();
                if (!customFieldData) {
                    return res.status(200).send({
                        status: false,
                        message: `Something went wrong while creating custom field!`,
                    });
                } else {
                    return res.status(200).send({
                        message: `User custom field created successfully`,
                        data: customFieldData,
                    });
                }
            }
        }
    } catch (error) {
        await debugErrorLogs.createErrorLogs(error, "createUserCustomField", {});
        return res.status(500).json({ status: false, message: `${error.message}` });
    }
};

//Edit user custom field (By admin)
exports.updateUserCustomField = async (req, res) => {
    try {
        const { id } = req.params;
        let { label, placeholder, type, choices, info, is_user_editable, is_user_visible, required, error_message } = req.body;

        // fetch user field from that id 
        const existCustomField = await userCustomTable.findOne({ _id: new ObjectId(id), isDelete: false }).lean();
        if (!existCustomField) {
            return res.status(200).send({ status: false, message: `Field is not found!` });
        } else {
            let isExist = await userCustomTable.aggregate([
                { $addFields: { nameLower: { $toLower: "$label" } } },
                {
                    $match: {
                        _id: { $nin: [ObjectId(id)] },
                        isDelete: false,
                        nameLower: label.toLowerCase(),
                        relation_id: ObjectId(req.relation_id),
                    },
                },
            ]);
            if (isExist && isExist.length != 0) {
                return res.status(200).json({ status: false, message: "This field name already exists.", });
            } else {
                // Edit user field by Id get data from body if not then it saved already exist field data
                const editFieldData = await userCustomTable.findByIdAndUpdate(
                    id,
                    {
                        label: label ?? existCustomField.label,
                        placeholder: placeholder ?? existCustomField.placeholder,
                        type: type ?? existCustomField.type,
                        choices: choices ?? existCustomField.choices,
                        info: info ?? existCustomField.info,
                        is_user_editable: is_user_editable ?? existCustomField.is_user_editable,
                        is_user_visible: is_user_visible ?? existCustomField.is_user_visible,
                        required: required ?? existCustomField.required,
                        error_message: error_message ?? existCustomField.error_message,
                    },
                    { new: true });
                if (!editFieldData) {
                    return res.status(200).send({
                        status: false,
                        message: `Something went wrong while edit user field data!`,
                    });
                } else {
                    return res.status(200).send({
                        message: `User custom field edited successfully`,
                        data: editFieldData,
                    });
                }
            }
        }
    } catch (error) {
        await debugErrorLogs.createErrorLogs(error, "UpdateUserCustomField", {});
        return res.status(500).json({ status: false, message: `${error.message}` });
    }
};

// delete user custom field (By admin - soft delete)
exports.deleteUserCustomField = async (req, res) => {
    try {
        const { id } = req.params;
        const existCustomField = await userCustomTable.findOne({ _id: new ObjectId(id), isDelete: false }).lean();
        if (!existCustomField) {
            return res.status(200).send({ status: false, message: `custom field is not found!` });
        } else {
            const deleteCustomField = await userCustomTable.findByIdAndUpdate(
                id,
                { isDelete: true },
                { new: true }
            );
            if (!deleteCustomField) {
                return res.status(200).send({
                    status: false,
                    message: "Something went wrong while deleteing user custom field!",
                });
            } else {
                return res.status(200).send({
                    status: true,
                    message: "Custom field deleted successfully!",
                });
            }
        }
    } catch (error) {
        await debugErrorLogs.createErrorLogs(error, "deleteUserCustomField", {});
        return res.status(500).json({ status: false, message: `${error.message}` });
    }
};

// Reorder custom field by admin
exports.reorderCustomField = async (req, res) => {
    try {
      const { ids } = req.body;
      // Check if each id is a valid ObjectId
      for (const item of ids) {
        if (!ObjectId.isValid(item)) {
          return res.status(400).send({ status: false, message: "Invalid ID!" });
        }
      }
      // Count total number of custom fields where isDelete is false
      const customFieldData = await userCustomTable.countDocuments({relation_id: ObjectId(req.relation_id), isDelete: false });
      if (ids.length !== customFieldData) {
        return res.status(400).json({ status: false, message: "Please give valid data!" });
      } else {
        let reOrder = ids.map(async (item, i) => {
          await userCustomTable.findByIdAndUpdate({ _id: ObjectId(item)}, { order: i + 1 }, { new: true })
        });
        await Promise.all([...reOrder]);
        if (!reOrder) { 
          return res.status(200).json({ status: false, message: "Something went wrong while reorder field!" });
        } else {
          return res.status(200).send({
            status: true,
            message: "Custom field rearrange succesfully!",
          });
        }
      }
    } catch (error) {
      await debugErrorLogs.createErrorLogs(error, "reorderCustomField", {});
      return res.status(500).json({ status: false, message: `${error.message}` });
    }
  };

// get all custom field by admin with filter
exports.getAllUserCustomField = async (req, res) => {
    try {
        let search = req.query.search;
        let page = req.query.page ? +req.query.page : 1;
        let limit = req.query.limit ? +req.query.limit : 10;
        let skip = (page - 1) * limit;
        const filter = req.query.type ? req.query.type : "All";
        const sortField = req.query.sortField ? req.query.sortField : "order";
        const sortType = req.query.sortType === "Desc" ? -1 : 1;
        let sortTemp = { [sortField]: sortType };
        let match = { isDelete: false, relation_id: ObjectId(req.relation_id), };

        // Apply filter for type
        if (filter !== "All") {
            match = {
                ...match,
                type: { $eq: filter },
            };
        };

        // Apply search
        if (search) {
            match = {
                ...match,
                label: { $regex: ".*" + search + ".*", $options: "i" },
            };
        };

        //pipeline  for aggregate function
        let pipeline = [
            { $match: match },
            { $sort: sortTemp },
            // { $skip: skip },
            // { $limit: limit },
            {
                $project: {
                    updatedAt: 0,
                    __v: 0,
                },
            },
        ];

        let [all, count] = await Promise.all([
            userCustomTable.aggregate(pipeline),
            userCustomTable.countDocuments(match)
        ]);
        return res.status(200).send({
            status: true,
            message: "User custom field get successfully!",
            field: all,
            // totalPages: Math.ceil(count / limit),
            // currentPage: page,
            // totalField: count
        })
    }
    catch (error) {
        await debugErrorLogs.createErrorLogs(error, "getAllUserCustomField", {});
        return res.status(500).json({ status: false, message: `${error.message}` });
    }
};

//get user custom field detail api
exports.getUserCustomFieldById = async (req, res) => {
    try {
        const userCustomFieldDetail = await userCustomTable.findById(req.params.id,{updatedAt:0,__v:0});
        if (!userCustomFieldDetail) {
            return res.status(200).send({
                status: false,
                message: `User field data is not found!`,
                data:{}
            });
        } else {
            return res.status(200).send({
                status:true,
                message: `User custom field edited successfully`,
                data: userCustomFieldDetail,
            });
        }
    } catch (error) {
        await debugErrorLogs.createErrorLogs(error, "getUserCustomFieldById", {});
        return res.status(200).json({ status: false, message: `${error.message}` });
    }
};

// update user profile and partner image by admin
exports.updateProfileImageByAdmin = async (req, res) => {
    try {
        const { userId, } = req.body;

        // Fetch user data
        const userData = await User.findById({ _id: new ObjectId(userId), isDelete: false });
        if (!userData) {
            return res.status(404).json({ status: false, message: 'User not found!' });
        };

        if (req.origi_profile) {
            // Delete profile image if it exists
            if (userData.profileImg) {
                await s3.deleteObject({
                    Bucket: process.env.AWS_BUCKET,
                    Key: userData.profileImg,
                }).promise();
            }
        };

        if (req.partnerIcon) {
            // Delete partner profile image if it exists
            if (userData.partnerIcon) {
                await s3.deleteObject({
                    Bucket: process.env.AWS_BUCKET,
                    Key: userData.partnerIcon,
                }).promise();
            }
        };

        // Prepare user update details
        let updateUserDetails = {
            profileImg: req.origi_profile ?? userData.profileImg,
            partnerIcon: req.partnerIcon ?? userData.partnerIcon,
        };

        // Update user profile
        const updatedProfile = await User.findByIdAndUpdate(new ObjectId(userId), updateUserDetails, { new: true }).select('_id profileImg partnerIcon first_name last_name display_name');
        if (!updatedProfile) {
            return res
                .status(200)
                .json({ status: false, message: "Profile Images not updated!!" });
        } else {
            return res.status(200).json({
                status: true,
                message: "Profile Image updated successfully!",
                data: updatedProfile,
            });
        }
    } catch (error) {
        await debugErrorLogs.createErrorLogs(error, "updateProfileImageByAdmin", {});
        return res.status(500).json({ status: false, message: `An error occurred: ${error.message}` });
    }
};

// update user custom field changes by admin
exports.updateUserProfileByAdmin = async (req, res) => {
    try {
        const { relation_id } = req;
        const { userId, first_name, last_name, display_name, status, tagId, accessible_groups, custom_field_data } = req.body;

        // Fetch user data
        const userData = await User.findById({ _id: new ObjectId(userId), isDelete: false });
        if (!userData) {
            return res.status(404).json({ status: false, message: 'User not found!' });
        };

        // Prepare user update details
        let updateUserDetails = {
            first_name: first_name ?? userData.first_name,
            last_name: last_name ?? userData.last_name,
            status: status ?? userData.userData,
            display_name: display_name ? display_name : (first_name && last_name) ? `${first_name} ${last_name}` : userData.display_name,
        };

        // Update user profile
        const updatedProfile = await User.findByIdAndUpdate(new ObjectId(userId), updateUserDetails, { new: true }).select('_id first_name last_name status display_name profileImg status');
        if (!updatedProfile) {
            return res.status(500).json({ status: false, message: 'Failed to update user profile.' });
        }

        // Handle tag and groupid
        if (tagId || accessible_groups) {
            const userEdge = await user_edges.findOne({
                user_id: new ObjectId(userId),
                relation_id: new ObjectId(relation_id),
                isDelete: false
            });

            if (userEdge) {
                const updateData = {};
                if (Array.isArray(tagId)) {
                    updateData.tagId = tagId.map(id => ObjectId(id));
                }
                if (Array.isArray(accessible_groups)) {
                    updateData.accessible_groups = accessible_groups.map(id => ObjectId(id));
                }
                await user_edges.findByIdAndUpdate(userEdge._id, { $set: updateData }, { new: true });
            }
        };

        // Handle custom field data
        let updatedCustomFields = [];
        if (Array.isArray(custom_field_data) && custom_field_data.length > 0) {
            const updatePromises = custom_field_data.map(async (customField) => {
                const { custom_field_id, type, response } = customField;
                const existingCustomField = await customFieldSubmission.findOne({
                    user_id: new ObjectId(userId),
                    custom_field_id: new ObjectId(custom_field_id),
                    relation_id: new ObjectId(relation_id),
                    isDelete: false
                });
                if (existingCustomField) {
                    const updatedCustomField = await customFieldSubmission.findByIdAndUpdate(
                        existingCustomField._id,
                        { type, response, is_admin_edited: true }, { new: true })
                        .select('_id type response status is_admin_edited');
                    updatedCustomFields.push(updatedCustomField);
                } else {
                    const createdCustomField = await customFieldSubmission.create({
                        user_id: new ObjectId(userId),
                        custom_field_id: new ObjectId(custom_field_id),
                        relation_id: new ObjectId(relation_id),
                        is_admin_edited: true,
                        type,
                        response,
                    })
                    const customField = await customFieldSubmission.findById(createdCustomField._id)
                        .select('_id type response status is_admin_edited');

                    // Add to the array
                    updatedCustomFields.push(customField);
                }
            });
            await Promise.all(updatePromises);
        };


        const payload = {
            event:  "UPDATE_USER_PROFILE",
            data:{
                userId:userId,
                relation_id:relation_id,
            }
          }
      
          await publishMessage(JSON.stringify(payload), "CHAT_USER_MODIFICATION")
          
        // Send response with user profile and updated custom fields
        return res.status(200).json({
            status: true,
            message: 'Custom fields updated successfully.',
            data: {
                user: updatedProfile,
                customFields: updatedCustomFields.length > 0 ? updatedCustomFields : 'No custom fields were updated'
            }
        });
    } catch (error) {
        await debugErrorLogs.createErrorLogs(error, "updateUserProfileByAdmin", {});
        return res.status(500).json({ status: false, message: `An error occurred: ${error.message}` });
    }
};

exports.updateProfileByAdmin = async(req,res) => {
    try {
        const { relation_id } = req;
        const { userId, first_name, last_name, display_name, status, tagId, accessible_groups, custom_field_data, no_of_team_mate } = req.body;
        // Fetch user data
        const userData = await User.findById({ _id: new ObjectId(userId), isDelete: false });
        if (!userData) {
            return res.status(404).json({ status: false, message: 'User not found!' });
        };

        if (req.origi_profile) {
            // Delete profile image if it exists
            if (userData.profileImg) {
                await s3.deleteObject({
                    Bucket: process.env.AWS_BUCKET,
                    Key: userData.profileImg,
                }).promise();
            }
        };

        if (req.partnerIcon) {
            // Delete partner profile image if it exists
            if (userData.partnerIcon) {
                await s3.deleteObject({
                    Bucket: process.env.AWS_BUCKET,
                    Key: userData.partnerIcon,
                }).promise();
            }
        };

        // Prepare user update details
        let updateUserDetails = {
            profileImg: req.origi_profile ?? userData.profileImg,
            partnerIcon: req.partnerIcon ?? userData.partnerIcon,
            first_name: first_name ?? userData.first_name,
            last_name: last_name ?? userData.last_name,
            status: status ?? userData.userData,
            display_name: display_name ? display_name : (first_name && last_name) ? `${first_name} ${last_name}` : userData.display_name,
        };
        console.log("🚀 ~ exports.updateProfileByAdmin=async ~ updateUserDetails:", updateUserDetails)  

        // Update user profile
        const updatedProfile = await User.findByIdAndUpdate(new ObjectId(userId), updateUserDetails, { new: true }).select('_id first_name last_name status display_name profileImg status');
        if (!updatedProfile) {
            return res.status(500).json({ status: false, message: 'Failed to update user profile.' });
        }

        // Handle tag and groupid
        if (tagId || accessible_groups || no_of_team_mate) {
            const userEdge = await user_edges.findOne({
                user_id: new ObjectId(userId),
                relation_id: new ObjectId(relation_id),
                isDelete: false
            });

            if (userEdge) {
                ChanneltagId=tagId ? tagId.map(id => ObjectId(id)) :[]
                AddorRemoveFromChannelOnuserProfileUpdate(ChanneltagId,userId,req.userId,relation_id)
                const updateData = {};
                if (Array.isArray(tagId)) {
                    updateData.tagId = tagId.map(id => ObjectId(id));
                }else{
                    updateData.tagId = []
                }
                if (Array.isArray(accessible_groups)) {
                    updateData.accessible_groups = accessible_groups.map(id => ObjectId(id));
                }else{
                    updateData.accessible_groups = []
                }
                if (no_of_team_mate){
                    updateData.no_of_team_mate = no_of_team_mate
                }
                await user_edges.findByIdAndUpdate(userEdge._id, { $set: updateData }, { new: true });
            }
        };

        // Handle custom field data
        let updatedCustomFields = [];
        if (Array.isArray(custom_field_data) && custom_field_data.length > 0) {
            const updatePromises = custom_field_data.map(async (customField) => {
                const { custom_field_id, type, response } = customField;
                const existingCustomField = await customFieldSubmission.findOne({
                    user_id: new ObjectId(userId),
                    custom_field_id: new ObjectId(custom_field_id),
                    relation_id: new ObjectId(relation_id),
                    isDelete: false
                });
                if (existingCustomField) {
                    const updatedCustomField = await customFieldSubmission.findByIdAndUpdate(
                        existingCustomField._id,
                        { type, response, is_admin_edited: true }, { new: true })
                        .select('_id type response status is_admin_edited');
                    updatedCustomFields.push(updatedCustomField);
                } else {
                    const createdCustomField = await customFieldSubmission.create({
                        user_id: new ObjectId(userId),
                        custom_field_id: new ObjectId(custom_field_id),
                        relation_id: new ObjectId(relation_id),
                        is_admin_edited: true,
                        type,
                        response,
                    })
                    const customField = await customFieldSubmission.findById(createdCustomField._id)
                        .select('_id type response status is_admin_edited');

                    // Add to the array
                    updatedCustomFields.push(customField);
                }
            });
            await Promise.all(updatePromises);
        };


        const payload = {
            event:  "UPDATE_USER_PROFILE",
            data:{
                userId:userId,
                relation_id:relation_id,
            }
          }
      
          await publishMessage(JSON.stringify(payload), "CHAT_USER_MODIFICATION")
          
        // Send response with user profile and updated custom fields
        return res.status(200).json({
            status: true,
            message: 'Custom fields updated successfully.',
            data: {
                user: updatedProfile,
                customFields: updatedCustomFields.length > 0 ? updatedCustomFields : 'No custom fields were updated'
            }
        });
    }catch(error){
        await debugErrorLogs.createErrorLogs(error, "updateUserProfileByAdmin", {});
        return res.status(500).json({ status: false, message: `An error occurred: ${error.message}` });
    }
}

// get custom field and user data by admin for indivisual user
exports.getUserProfileByAdmin = async (req, res) => {
    try {
        let { relation_id } = req;
        let { id } = req.params;
        let match = { _id: ObjectId(id), isDelete: false, };
        let fieldMatch = { isDelete: false, relation_id: new ObjectId(relation_id) };
        let search = req.query.search;
        let page = req.query.page ? +req.query.page : 1;
        let limit = req.query.limit ? +req.query.limit : 20;
        let skip = (page - 1) * limit;
        let filter = req.query.type ? req.query.type : "All";
        let sortField = req.query.sortField ? req.query.sortField : "order";
        let sortType = req.query.sortType === "Desc" ? -1 : 1;
        let sortTemp = { [sortField]: sortType };
        
        // Apply filter for type
        if (filter !== "All") {
            fieldMatch = {
                ...fieldMatch,
                type: { $eq: filter },
            };
        };

        // Apply search
        if (search) {
            fieldMatch = {
                ...fieldMatch,
                label: { $regex: ".*" + search + ".*", $options: "i" },
            };
        };

        //pipeline  for aggregate function
        let pipeline = [
            {
                '$match': match
            }, {
                '$lookup': {
                    'from': 'user_edges',
                    'localField': '_id',
                    'foreignField': 'user_id',
                    'pipeline': [
                        {
                            '$match': {
                                'relation_id': new ObjectId(relation_id),
                                'user_id': new ObjectId(id),
                                'isDelete': false
                            }
                        }, {
                            '$lookup': {
                                'from': 'groups',
                                'localField': 'accessible_groups',
                                'foreignField': '_id',
                                'pipeline': [
                                    {
                                        '$match': {
                                            'isDelete': false
                                        }
                                    }, {
                                        '$project': {
                                            'isDelete': 0,
                                            'createdAt': 0,
                                            'updatedAt': 0,
                                            '__v': 0
                                        }
                                    }
                                ],
                                'as': 'accessible_groups'
                            }
                        }, {
                            '$lookup': {
                                'from': 'contentarchive_tags',
                                'localField': 'tagId',
                                'foreignField': '_id',
                                'pipeline': [
                                    {
                                        '$match': {
                                            'isDelete': false
                                        }
                                    }, {
                                        '$project': {
                                            'name': 1
                                        }
                                    }
                                ],
                                'as': 'tagId'
                            }
                        }
                    ],
                    'as': 'user_data'
                }
            }, {
                '$unwind': {
                    'path': '$user_data',
                    'preserveNullAndEmptyArrays': true
                }
            },
            {
                '$lookup': {
                  'from': 'invite_collaborators', 
                  'localField': '_id', 
                  'foreignField': 'invitee_id', 
                  'as': 'invite_collaborators', 
                  'pipeline': [
                    {
                      '$match': {
                        'relation_id': new ObjectId(relation_id),
                        'isDelete': false, 
                        'status': {
                          '$nin': [
                            'REVOKED'
                          ]
                        }
                      }
                    },
                    {
                        '$project': {
                          'first_name': 1,
                          'last_name': 1,
                          'email': 1,
                          'status': 1
                        }
                    }
                  ]
                }
            }, {
                '$addFields': {
                  'number_of_extra_seat': {
                    '$ifNull': [
                      '$user_data.no_of_team_mate', 0
                    ]
                  }, 
                  'number_of_used_seat': {
                    '$cond': {
                      'if': {
                        '$isArray': '$invite_collaborators'
                      }, 
                      'then': {
                        '$size': '$invite_collaborators'
                      }, 
                      'else': 0
                    }
                  }
                }
            },
            {
                '$project': {
                    'first_name': 1,
                    'last_name': 1,
                    'display_name': 1,
                    'status': 1,
                    'partnerIcon': 1,
                    'createdAt': 1,
                    'isDelete': 1,
                    'profileImg': 1,
                    'Preferred Email': 1,
                    'Upcoming Events Registered': 1,
                    'Events Attended': 1,
                    'accessible_groups': '$user_data.accessible_groups',
                    'tagId': '$user_data.tagId',
                    'no_of_team_mate': '$user_data.no_of_team_mate',
                    'type': '$user_data.type',
                    'subscription_id': '$user_data.subscription_id',
                    'number_of_extra_seat': '$number_of_extra_seat',
                    'number_of_used_seat': '$number_of_used_seat',
                    'invite_collaborators': '$invite_collaborators',
                    'providers': 1,
                    'provider': 1,
                }
            }
        ];

        let customPipeline = [
            {
                '$match': fieldMatch
            }, {
                '$lookup': {
                    'from': 'user_custom_form_submissions',
                    'localField': '_id',
                    'foreignField': 'custom_field_id',
                    'pipeline': [
                        {
                            '$match': {
                                'user_id': new ObjectId(id),
                                'isDelete': false
                            }
                        }, {
                            '$project': {
                                'createdAt': 0,
                                'updatedAt': 0,
                                '__v': 0
                            }
                        }
                    ],
                    'as': 'field_data'
                }
            }, 
            { '$sort': sortTemp },
            { '$skip': skip },
            { '$limit': limit },
            {
                '$project': {
                    'createdAt': 0,
                    'updatedAt': 0,
                    '__v': 0
                }
            }
        ];

        let [profile, customField, customFieldCount] = await Promise.all([
            User.aggregate([...pipeline]),
            userCustomTable.aggregate([...customPipeline]),
            userCustomTable.countDocuments(fieldMatch),
          ]);
          
        if (!profile[0]) {
            return res
                .status(200)
                .json({ status: false, message: "User profile not found!!" });
        } else {
            let number_of_tier_seat = 0;
            let number_of_extra_seat = 0;
            let number_of_total_seat = 0;
            let number_of_used_seat = 0;
            let number_of_available_seat = 0;

            if(profile[0].subscription_id){
                const subscriptionData = await fetchSubscriptionData({ subscription_id: profile[0].subscription_id });
                if(subscriptionData && subscriptionData.tier_id && subscriptionData.tier_id.linked_subscription_number){
                    number_of_tier_seat = subscriptionData.tier_id.linked_subscription_number;
                }
            }
            if(profile[0].number_of_extra_seat){
                number_of_extra_seat = profile[0].number_of_extra_seat;
            }
            if(profile[0].number_of_used_seat){
                number_of_used_seat = profile[0].number_of_used_seat;
            }
            number_of_total_seat = number_of_tier_seat + number_of_extra_seat;
            number_of_available_seat = number_of_total_seat - number_of_used_seat;

            profile[0].number_of_total_seat = number_of_total_seat;
            profile[0].number_of_available_seat = number_of_available_seat;
            profile[0].number_of_tier_seat = number_of_tier_seat;
            return res.status(200).json({
                status: true,
                message: "Get user profile successfully!",
                data: { profile: profile, customField: customField },
                fieldPages: Math.ceil(customFieldCount / limit),
                currentPage: page,
                totalCustomField: customFieldCount
            });
        }
    }
    catch (error) {
        await debugErrorLogs.createErrorLogs(error, "getUserProfileByAdmin", {});
        return res.status(500).json({ status: false, message: `${error.message}` });
    }
};

// update user custom field submission from user side
exports.updateUserCustomFieldSubmission = async (req, res) => {
    try {
        const { authUserId, relation_id } = req;
        const { first_name, last_name, display_name, custom_field_data } = req.body;

        // Fetch user data
        const userData = await User.findById({ _id: new ObjectId(authUserId), isDelete: false });
        if (!userData) {
            return res.status(404).json({ status: false, message: 'User not found!' });
        }

        // Prepare user update details
        const updateUserDetails = {
            first_name: first_name ?? userData.first_name,
            last_name: last_name ?? userData.last_name,
            display_name: display_name ?? userData.display_name,
        };

        // Update user profile
        const updatedProfile = await User.findByIdAndUpdate(new ObjectId(authUserId), updateUserDetails, { new: true });
        if (!updatedProfile) {
            return res.status(500).json({ status: false, message: 'Failed to update user profile.' });
        }

        // Handle custom field data
        let updatedCustomFields = [];
        if (Array.isArray(custom_field_data) && custom_field_data.length > 0) {
            const updatePromises = custom_field_data.map(async (customField) => {
                const { custom_field_id, type, response } = customField;
                const existingCustomField = await customFieldSubmission.findOne({
                    user_id: new ObjectId(authUserId),
                    custom_field_id: new ObjectId(custom_field_id),
                    relation_id: new ObjectId(relation_id),
                    isDelete: false
                });

                if (existingCustomField) {
                    const updatedCustomField = await customFieldSubmission.findByIdAndUpdate(existingCustomField._id, { type, response }, { new: true });
                    updatedCustomFields.push(updatedCustomField);
                } else {
                    const createdCustomField = await customFieldSubmission.create({
                        user_id: new ObjectId(authUserId),
                        custom_field_id: new ObjectId(custom_field_id),
                        relation_id: new ObjectId(relation_id),
                        type,
                        response
                    });
                    updatedCustomFields.push(createdCustomField);
                }
            });
            await Promise.all(updatePromises);
        };
        // Send response with user profile and updated custom fields
        return res.status(200).json({
            status: true,
            message: 'Custom fields updated successfully.',
            data: {
                user: updatedProfile,
                customFields: updatedCustomFields.length > 0 ? updatedCustomFields : 'No custom fields were updated'
            }
        });

    } catch (error) {
        await debugErrorLogs.createErrorLogs(error, "updateUserCustomFieldSubmission", {});
        return res.status(500).json({ status: false, message: `An error occurred: ${error.message}` });
    }
};

// update user profile pic by user
exports.updateProfilePicByUser = async (req, res) => {
    try {
        const { userId,relation_id } = req;
        
        // Fetch user data
        const userData = await User.findById({ _id: new ObjectId(userId), isDelete: false });
        if (!userData) {
            return res.status(404).json({ status: false, message: 'User not found!' });
        };

        if (req.origi_profile) {
            // Delete profile image if it exists
            if (userData.profileImg) {
                await s3.deleteObject({
                    Bucket: process.env.AWS_BUCKET,
                    Key: userData.profileImg,
                }).promise();
            }
        };

        // Prepare user update details
        let updateUserDetails = {
            profileImg: req.origi_profile ?? userData.profileImg,
        };

        // Update user profile
        const updatedProfile = await User.findByIdAndUpdate(new ObjectId(userId), updateUserDetails, { new: true }).select('_id profileImg partnerIcon first_name last_name display_name');
        if (!updatedProfile) {
            return res
                .status(200)
                .json({ status: false, message: "Profile Images not updated!!" });
        } else {
            return res.status(200).json({
                status: true,
                message: "Profile Image updated successfully!",
                data: updatedProfile,
            });
        }
    } catch (error) {
        await debugErrorLogs.createErrorLogs(error, "updateProfilePicByUser", {});
        return res.status(500).json({ status: false, message: `An error occurred: ${error.message}` });
    }
};

exports.updateUserProfileByUser = async (req,res) => {
    try {
        const { authUserId, relation_id } = req;
        const { first_name, last_name, display_name, custom_field_data } = req.body;

        // Fetch user data
        const userData = await User.findById({ _id: new ObjectId(authUserId), isDelete: false });
        if (!userData) {
            return res.status(404).json({ status: false, message: 'User not found!' });
        }


        if (req.origi_profile) {
            // Delete profile image if it exists
            if (userData.profileImg) {
                await s3.deleteObject({
                    Bucket: process.env.AWS_BUCKET,
                    Key: userData.profileImg,
                }).promise();
            }
        };

        // Prepare user update details
        const updateUserDetails = {
            first_name: first_name ?? userData.first_name,
            last_name: last_name ?? userData.last_name,
            display_name: display_name ?? userData.display_name,
            profileImg: req.origi_profile ?? userData.profileImg,
        };

        // Update user profile
        const updatedProfile = await User.findByIdAndUpdate(new ObjectId(authUserId), updateUserDetails, { new: true });
        if (!updatedProfile) {
            return res.status(500).json({ status: false, message: 'Failed to update user profile.' });
        }

        // Handle custom field data
        let updatedCustomFields = [];
        if (Array.isArray(custom_field_data) && custom_field_data.length > 0) {
            const updatePromises = custom_field_data.map(async (customField) => {
                const { custom_field_id, type, response } = customField;
                const existingCustomField = await customFieldSubmission.findOne({
                    user_id: new ObjectId(authUserId),
                    custom_field_id: new ObjectId(custom_field_id),
                    relation_id: new ObjectId(relation_id),
                    isDelete: false
                });

                if (existingCustomField) {
                    const updatedCustomField = await customFieldSubmission.findByIdAndUpdate(existingCustomField._id, { type, response }, { new: true });
                    updatedCustomFields.push(updatedCustomField);
                } else {
                    const createdCustomField = await customFieldSubmission.create({
                        user_id: new ObjectId(authUserId),
                        custom_field_id: new ObjectId(custom_field_id),
                        relation_id: new ObjectId(relation_id),
                        type,
                        response
                    });
                    updatedCustomFields.push(createdCustomField);
                }
            });
            await Promise.all(updatePromises);
        };
        const payload = {
            event:  "UPDATE_USER_PROFILE",
            data:{
                userId:authUserId,
                relation_id:relation_id,
            }
          }
      
          await publishMessage(JSON.stringify(payload), "CHAT_USER_MODIFICATION")
        // Send response with user profile and updated custom fields
        return res.status(200).json({
            status: true,
            message: 'Custom fields updated successfully.',
            data: {
                user: updatedProfile,
                customFields: updatedCustomFields.length > 0 ? updatedCustomFields : 'No custom fields were updated'
            }
        });


    }catch(e){
        await debugErrorLogs.createErrorLogs(error, "updateUserCustomFieldSubmission", {});
        return res.status(500).json({ status: false, message: `An error occurred: ${error.message}` });
    }
}

// get all custom field by user 
exports.getAllUserCustomFieldForUser = async (req, res) => {
    try {
        let match = { isDelete: false, relation_id: ObjectId(req.relation_id), is_user_visible: true };

        //pipeline  for aggregate function
        let customPipeline = [
            {
                '$match': match
            }, {
                '$lookup': {
                    'from': 'user_custom_form_submissions',
                    'localField': '_id',
                    'foreignField': 'custom_field_id',
                    'pipeline': [
                        {
                            '$match': {
                                'user_id': new ObjectId(req.userId),
                                'isDelete': false
                            }
                        }, {
                            '$project': {
                                'createdAt': 0,
                                'updatedAt': 0,
                                '__v': 0
                            }
                        }
                    ],
                    'as': 'field_data'
                }
            },
            { '$sort': { order: 1 } },
            {
                '$project': {
                    'createdAt': 0,
                    'updatedAt': 0,
                    '__v': 0
                }
            }
        ];

        // Retrieve user data
        let [userData, customField ] = await Promise.all([
            User.findOne(
                {_id: ObjectId(req.userId),$or: [{ isDelete: false }, { isDelete: { $exists: false } }],}, 
                { "Preferred Email": 1, "first_name": 1, "last_name": 1, "display_name": 1, "profileImg": 1, _id: 0 }).exec(),
            userCustomTable.aggregate([...customPipeline]),
        ]);

        return res.status(200).send({
            status: true,
            message: "User custom field get successfully!",
            field: customField,
            profileData: userData,
        })
    }
    catch (error) {
        await debugErrorLogs.createErrorLogs(error, "getAllUserCustomFieldForUser", {});
        return res.status(500).json({ status: false, message: `${error.message}` });
    }
};

// get all custom field by user 
exports.getUserCustomFieldForUser = async (req, res) => {
    try {
        if(!req.query || !req.query.userId){
            return res.status(400).json({ status: false, message: "user id is required!" });
        }

        let match = { isDelete: false, relation_id: ObjectId(req.relation_id), is_user_visible: true };
        let userIdTemp = ObjectId(req.query.userId);
       
        //pipeline  for aggregate function
        let customPipeline = [
            {
                '$match': match
            }, {
                '$lookup': {
                    'from': 'user_custom_form_submissions',
                    'localField': '_id',
                    'foreignField': 'custom_field_id',
                    'pipeline': [
                        {
                            '$match': {
                                'user_id': userIdTemp,
                                'isDelete': false
                            }
                        }, {
                            '$project': {
                                'createdAt': 0,
                                'updatedAt': 0,
                                '__v': 0
                            }
                        }
                    ],
                    'as': 'field_data'
                }
            },
            { '$sort': { order: 1 } },
            {
                '$project': {
                    'createdAt': 0,
                    'updatedAt': 0,
                    '__v': 0
                }
            }
        ];

        // Retrieve user data
        let [userData, customField ] = await Promise.all([
            User.findOne(
                {_id: userIdTemp, $or: [{ isDelete: false }, { isDelete: { $exists: false } }],}, 
                { "Preferred Email": 1, "first_name": 1, "last_name": 1, "display_name": 1, "profileImg": 1, _id: 0 }).exec(),
            userCustomTable.aggregate([...customPipeline]),
        ]);

        return res.status(200).send({
            status: true,
            message: "User custom field get successfully!",
            field: customField,
            profileData: userData,
        })
    }
    catch (error) {
        await debugErrorLogs.createErrorLogs(error, "getAllUserCustomFieldForUser", {});
        return res.status(500).json({ status: false, message: `${error.message}` });
    }
};

exports.syncProfilefields = async (req, res) => {
  try {
    const { relation_id } = req;
    const { airtable_field, customFieldId } = req.body;

    let match = { relation_id: ObjectId(relation_id), isDelete: false };

    const pipeline = [
      {
        $match: match,
      },
      {
        $lookup: {
          from: "airtable-syncs",
          localField: "user_id",
          foreignField: "_id",
          as: "airtable",
          pipeline: [
            {
              $match: {
                isDelete: false,
              },
            },
          ],
        },
      },

      {
        $unwind: {
          path: "$airtable",
          preserveNullAndEmptyArrays: false,
        },
      },
      {
        $replaceRoot: {
          newRoot: "$airtable",
        },
      },
    ];

    const getUser = await user_edges.aggregate(pipeline);

    // console.log(getUser.length, "length");

    const getCustomForm = await userCustomTable.find({
      _id: ObjectId(customFieldId),
      isDelete: false,
      relation_id: ObjectId(relation_id),
    });

    // console.log(getCustomForm.length)

    if (!getCustomForm || getCustomForm.length <= 0) {
      return res
        .status(500)
        .json({ status: false, message: "No Custom Form Exists" });
    }

    let updatedCustomFields = [];

  

    const updatePromises = getUser.map(async (user) => {
      const userId = new ObjectId(user._id);

      const fieldPromises = getCustomForm.map(async (customField) => {
        const customFieldId = new ObjectId(customField._id);

        const existingCustomField = await customFieldSubmission.findOne({
          user_id: userId,
          custom_field_id: customFieldId,
          relation_id: new ObjectId(relation_id),
          isDelete: false,
        });

        if (existingCustomField) {
          // console.log(userId, "*///////////////////*********");
          const updatedCustomField = await customFieldSubmission
            .findByIdAndUpdate(
              existingCustomField._id,
              { is_admin_edited: true },
              { new: true }
            )
            .select("_id type response status is_admin_edited");

          updatedCustomFields.push(updatedCustomField);
         
        } else {
          const createdCustomField = await customFieldSubmission.create({
            user_id: userId,
            custom_field_id: customFieldId,
            relation_id: new ObjectId(relation_id),
            is_admin_edited: true,
            type: "text",
            response: "",
          });

          const newCustomField = await customFieldSubmission
            .findById(createdCustomField._id)
            .select("_id type response status is_admin_edited");

          updatedCustomFields.push(newCustomField);

        
        }
      });

      await Promise.all(fieldPromises);
    });

    await Promise.all(updatePromises);

    return res.status(200).json({
      status: true,
      message: "Custom fields Added Successfully.",
      data: {
      
        updatedCustomFields
      },
    });
  } catch (error) {
    await debugErrorLogs.createErrorLogs(error, "syncProfilefields", {});
    return res.status(500).json({ status: false, message: `${error.message}` });
  }
};

// syncProfilefields();