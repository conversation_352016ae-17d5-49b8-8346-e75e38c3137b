const Room = require("../../database/models/eventRoom");
const Session = require("../../database/models/eventSession");
const eventActivity = require("../../database/models/eventActivity");
const { ObjectId } = require("mongodb");
const User = require("../../database/models/airTableSync");
const event = require("../../database/models/event");
const eventParticipantAttendees = require("../../database/models/eventParticipantAttendees");
const eventParticipantTypes = require("../../database/models/eventParticipantTypes");
const eventWiseParticipantTypes = require("../../database/models/eventWiseParticipantTypes");
const scheduleNotification = require("../../database/models/notification/scheduleNotification");
const { sendNotificationForNotifyUserV2 } = require("./eventActivityController");
const { schedule, reScheduleNotificationForActivitySession, scheduleNotificationFormAdmin, reScheduleNotificationFormAdmin, reScheduleNotificationFormAdminV2, scheduleJobNotification } = require("./eventAttendeeManageController");

const ScheduledNotification = require("../../database/models/scheduledNotification");
const moment = require("moment");
require('moment-timezone');
const scheduleLib = require("node-schedule");
const { validationResult } = require('express-validator');
const { createNotification, updateNotification, updateUserNotification } = require("../../microservices/user/utils/comman");
const { notification_template, send_notification_v2 } = require("../../utils/notification");

/** CURD opreation of rooms start **/

// create room
exports.createRoom = async (req, res) => {
    try {
        const body = req.body;
        if (body) {

            const roomExist = await Room.find({ name: req.body.name, location: ObjectId(req.body.location), isDelete: false })
            if (roomExist && roomExist.length > 0) {
                return res
                    .status(200)
                    .json({
                        status: false,
                        message: "This room and location already exist!",
                    });
            }
            const ids = await Room.find({ event:body.event,isDelete: false }, { _id: 1, order: 1 }).sort({ order: -1 });
            let newOrder = (ids && ids.length > 0) ? ids[0].order + 1 : 1
            const newRoom = new Room({ ...body,order:newOrder });
            const roomData = await newRoom.save();
            if (roomData)
                return res
                    .status(200)
                    .json({
                        status: true,
                        message: "Room added successfully!",
                        data: roomData,
                    });
            else
                return res
                    .status(200)
                    .json({
                        status: false,
                        message: "Something went wrong while adding room!",
                    });
        } else {
            return res
                .status(200)
                .json({ status: false, message: "All fields are required!" });
        }
    } catch (error) {
        return res
            .status(500)
            .json({ status: false, message: "Internal server error!", error: error });
    }
};

// update room
exports.editRoom = async (req, res) => {
    try {
        const body = req.body;
        const getRoom = await Room.findOne({
            _id: new ObjectId(req.params.id),
            isDelete: false,
        }).lean();
        if (getRoom) {
            const roomExist = await Room.find({
                _id: { $ne: ObjectId(req.params.id) },
                name: body.name ?? getRoom.name,
                location: ObjectId(body.location) ?? getRoom.location,
            },)
            if (roomExist && roomExist.length > 0) {
                return res
                    .status(200)
                    .json({
                        status: false,
                        message: "This room and location already exist!",
                    });
            }
            const roomData = await Room.findByIdAndUpdate(
                req.params.id,
                {
                    name: body.name ?? getRoom.name,
                    location: body.location ?? getRoom.location,
                    event: body.event ?? getRoom.event,
                    notifyChanges: body?.notifyChanges !== null && body?.notifyChanges !== "null" ? body?.notifyChanges : getRoom.notifyChanges === undefined ? false : getRoom.notifyChanges,
                    notifyChangeText: body?.notifyChangeText !== null && body?.notifyChangeText !== "null" ? body?.notifyChangeText : getRoom.notifyChangeText === undefined ? "" : getRoom.notifyChangeText,
                },
                { new: true }
            );
            if (roomData.notifyChanges === true) {
                await sendNotificationForNotifyUserV2(roomData.event, roomData.notifyChangeText, "room", roomData._id);
            }
            if (roomData)
                return res
                    .status(200)
                    .json({
                        status: true,
                        message: "Room updated successfully!",
                        data: roomData
                    });
            else
                return res
                    .status(200)
                    .json({
                        status: false,
                        message: "Something went wrong while updating room!",
                    });
        } else {
            return res
                .status(200)
                .json({ status: false, message: "Room not found!" });
        }
    } catch (error) {
        return res
            .status(500)
            .json({ status: false, message: "Internal server error!", error: error });
    }
};

// delete room
exports.deleteRoom = async (req, res) => {
    try {
        const getRoom = await Room.findOne({ _id: new ObjectId(req.params.id), isDelete: false, }).lean();
        if (getRoom) {
            const alreadyAssignSession = await Session.find({ room: new ObjectId(req.params.id), isDelete: false }).lean();
            if (alreadyAssignSession && alreadyAssignSession.length > 0) {
                var sessionList = [];
                if (alreadyAssignSession.length > 0) {
                    alreadyAssignSession.map((itemSession, i) => {
                        sessionList.push(itemSession.title);
                    });
                }

                return res.status(200).json({ status: false, message: "You cannot delete this room because it is assigned to following sessions: ", data: { sessionList }, });
            } else {
                const roomData = await Room.findByIdAndUpdate(
                    req.params.id,
                    { isDelete: true },
                    { new: true }
                );
                if (roomData)
                    return res.status(200).json({ status: true, message: "Room deleted successfully!", data: roomData, });
                else
                    return res.status(200).json({ status: false, message: "Something went wrong while deleteing room!", });
            }
        } else {
            return res.status(200).json({ status: false, message: "Room not found!" });
        }
    } catch (error) {
        console
        return res.status(500).json({ status: false, message: "Internal server error!", error: error });
    }
};

// listing of room
exports.getAllRooms = async (req, res) => {
    try {
        const allRoomData = await Room.find({ isDelete: false }).sort({ createdAt: -1 });
        if (allRoomData)
            return res
                .status(200)
                .json({
                    status: true,
                    message: "All rooms retrive!",
                    data: allRoomData,
                });
        else
            return res
                .status(200)
                .json({
                    status: false,
                    message: "Something went wrong while getting all rooms!",
                });
    } catch (error) {
        return res
            .status(500)
            .json({ status: false, message: "Internal server error!", error: error });
    }
};

// listing of room by event id
exports.getAllRoomsByEventId = async (req, res) => {
    try {
        const sortType = req.query.sortType ==="Asc"? -1 : 1;
    
        // const allRoomData = await Room.find({
        //     isDelete: false,
        //     event: req.params.eventId,
        // }).sort({ [`${sortField}`]: sortType });
        const allRoomData = await Room.aggregate([
              {
                $match: {
                    isDelete: false,
                    event: ObjectId(req.params.eventId),
                },
              },
              {
                $lookup: {
                  from: "eventlocations",
                  localField: "location",
                  foreignField: "_id",
                  as: "LocationsData",
                },
              },
              {
                $project: {
                  _id: 1,
                  name:1,
                  event:1,
                  isDelete:1,
                  createdAt:1,
                  order:1,
                  locations:{name:{$arrayElemAt: ["$LocationsData.name", 0]}},
                  locationsName: { $arrayElemAt: ["$LocationsData.name", 0] }
                },
              },
              {
                $addFields: {
                  sortFieldLower: (req.query.sortField === "name" ?{ $toLower: "$name" } : req.query.sortField === "location" ? { $toLower: "$locationsName" } : "$order")
                },
              },
              { $sort: { sortFieldLower: sortType } },
              {
                $project: {
                  sortFieldLower: 0,
                },
              },
        ]);
        if (allRoomData)
            return res
                .status(200)
                .json({
                    status: true,
                    message: "All rooms retrive!",
                    data: allRoomData,
                });
        else
            return res
                .status(200)
                .json({
                    status: false,
                    message: "Something went wrong while getting all rooms!",
                });
    } catch (error) {
        return res
            .status(500)
            .json({ status: false, message: "internal server error!", error: error });
    }
};

// listing of room suggestion by event id
exports.getAllRoomsSuggestion = async (req, res) => {
  try {
      const allRoomData = await Room.aggregate([
            {
              $match: {
                  isDelete: false,
                  event: ObjectId(req.params.eventId),
              },
            },
            {
              $addFields: {
                namelower: {
                  $toLower: "$name"
                }
              }
            },
            {
              $sort: {
                namelower: 1
              }
            },
            {
              $unset: ["namelower"]
            },
            {
              $project: {
                name: 1,
              },
            }
      ]);
      if (allRoomData.length){
          return res.status(200).json({status: true,message: "All rooms suggestion retrive!",data: allRoomData,});
      }else{
          return res.status(200).json({status: false,message: "Something went wrong while getting all rooms!",});
      }
  } catch (error) {
      return res
          .status(500)
          .json({ status: false, message: "internal server error!", error: error });
  }
};

// listing of room by event id
exports.reorderRoomsByEventId = async (req, res) => {
    try {
        const sortType = req.query.sortType ==="Asc"? -1 : 1;
        // reOrder code
        const ids = req.body.ids
        if (ids.length > 0) {
            let resOrder = ids.map(async (item, i) => {
                await Room.findByIdAndUpdate(ObjectId(item), { order: i + 1 }, { new: true })
            });
            await Promise.all([...resOrder]);
        }
        
        const allRoomData = await Room.aggregate([
              {
                $match: {
                    isDelete: false,
                    event: ObjectId(req.params.eventId),
                },
              },
              {
                $lookup: {
                  from: "eventlocations",
                  localField: "location",
                  foreignField: "_id",
                  as: "LocationsData",
                },
              },
              {
                $project: {
                  _id: 1,
                  name:1,
                  event:1,
                  isDelete:1,
                  createdAt:1,
                  order:1,
                  locations:{name:{$arrayElemAt: ["$LocationsData.name", 0]}},
                  locationsName: { $arrayElemAt: ["$LocationsData.name", 0] }
                },
              },
              {
                $addFields: {
                  sortFieldLower: (req.query.sortField === "name" ?{ $toLower: "$name" } : req.query.sortField === "location" ? { $toLower: "$locationsName" } : "$order")
                },
              },
              { $sort: { sortFieldLower: sortType } },
              {
                $project: {
                  sortFieldLower: 0,
                },
              },
        ]);
        if (allRoomData)
            return res
                .status(200)
                .json({
                    status: true,
                    message: "Reorder all rooms retrive!",
                    data: allRoomData,
                });
        else
            return res
                .status(200)
                .json({
                    status: false,
                    message: "Something went wrong while getting all rooms!",
                });
    } catch (error) {
        return res
            .status(500)
            .json({ status: false, message: "internal server error!", error: error });
    }
};

// room details data
exports.getRoomDetails = async (req, res) => {
    try {
        const roomData = await Room.findOne({
            _id: new ObjectId(req.params.id),
            isDelete: false,
        });
        if (roomData)
            return res
                .status(200)
                .json({
                    status: true,
                    message: "Rooms detail retrive!",
                    data: roomData,
                });
        else
            return res
                .status(200)
                .json({ status: false, message: "Rooms detail not found!" });
    } catch (error) {
        return res
            .status(500)
            .json({ status: false, message: "Internal server error!", error: error });
    }
};

/** CURD opreation of rooms end **/

/** =========================== **/

/** CURD opreation of session start **/

// delete session
exports.deleteSession = async (req, res) => {
    try {

        const { eventId } = req.body;
        const getSession = await Session.findOne({
            _id: new ObjectId(req.params.id),
            isDelete: false,
        }).lean();
        if (getSession) {
            const alreadyAssignActivity = await eventActivity.find({ event:eventId,session: { $in: [new ObjectId(req.params.id)] }, isDelete: false ,status: "published"}).lean();
            if (alreadyAssignActivity && alreadyAssignActivity.length > 0) {

                var activityList = [];
                if (alreadyAssignActivity.length > 0) {
                    alreadyAssignActivity.map((itemActivity, i) => {
                        activityList.push(itemActivity.name);
                    });
                }

                return res.status(200).json({ status: false, message: "You cannot delete this session because it is assigned to following activities: ", data: { activityList } });
            } else {

               await scheduleNotification.deleteMany({ sessionId: req?.params?.id, relationId: req?.relation_id, type: "session", isDelete: false });
                // const alreadyAddedUsers = await User.find({ notificationFor: { $elemMatch: { id: getSession._id }, }, }, { "notificationFor.$": 1 });

                // if (alreadyAddedUsers.length > 0) {
                //     const NotificationFor = {
                //         id: getSession._id,
                //         type: "session",
                //         setBy: "user",
                //     };

                //     const NotificationForAdmin = {
                //         id: getSession._id,
                //         type: "session",
                //         setBy: "admin",
                //     };
                //     let resCancel = alreadyAddedUsers.map(async (user, i) => {
                //         const scheduleData = await ScheduledNotification.findOne({ createdFor: user._id, idsFor: getSession._id, createdBy: "user" });
                //         if (scheduleData !== null) {
                //             await User.findOneAndUpdate(
                //                 { _id: user._id },
                //                 { $pull: { notificationFor: NotificationFor } },
                //                 { new: true }
                //             );
                //             await ScheduledNotification.findByIdAndRemove(scheduleData._id);
                //         }
                //         const scheduleDataAdmin = await ScheduledNotification.findOne({ createdFor: user._id, idsFor: getSession._id, createdBy: "admin" });
                //         if (scheduleDataAdmin !== null) {
                //             await User.findOneAndUpdate(
                //                 { _id: user._id },
                //                 { $pull: { notificationFor: NotificationForAdmin } },
                //                 { new: true }
                //             );
                //             await ScheduledNotification.findByIdAndRemove(scheduleDataAdmin._id);
                //         }
                //     });
                //     await Promise.all([...resCancel]);
                // }

                const sessionData = await Session.findByIdAndUpdate(
                    req.params.id,
                    { isDelete: true },
                    { new: true }
                );
                if (sessionData)
                    return res.status(200).json({ status: true, message: "Session deleted successfully!", data: sessionData, });
                else
                    return res.status(200).json({ status: false, message: "Something went wrong while deleteing session!", });
            }
        } else {
            return res.status(200).json({ status: false, message: "Session not found!" });
        }
    } catch (error) {
        return res.status(500).json({ status: false, message: "Internal server error!", error: error });
    }
};

// listing of session
exports.getAllSessions = async (req, res) => {
    try {
        const allSessionData = await Session.find({ isDelete: false }).sort({ createdAt: -1 });
        if (allSessionData)
            return res
                .status(200)
                .json({
                    status: true,
                    message: "All sessions retrive!",
                    data: allSessionData,
                });
        else
            return res
                .status(200)
                .json({
                    status: false,
                    message: "Something went wrong while getting all sessions!",
                });
    } catch (error) {
        return res
            .status(500)
            .json({ status: false, message: "Internal server error!", error: error });
    }
};

// listing of session by event id
exports.getAllSessionsByEventId = async (req, res) => {
    try {
        // const allSessionData = await Session.find({
        //     isDelete: false,
        //     event: req.params.eventId,
        // }).collation({ locale: "en" }).sort({ [`${sortField}`]: 1 });
        const sortType = req.query.sortType ==="Asc"? 1 : -1;
        const allSessionData = await Session.aggregate([
            {
                $match: {
                    isDelete: false,
                    event: ObjectId(req.params.eventId),
                },
            },
            {
              $lookup: {
                from: "rooms",
                localField: "room",
                foreignField: "_id",
                as: "RoomData",
              },
            },
            {
              $addFields: {
                  StartDateAndTime: {
                      $cond: {
                          if: { $eq: ["$date", ""] }, // Check if date field is empty
                          then: null, // If empty, set StartDateAndTime to null or handle it accordingly
                          else: {
                              $let: {
                                  vars: {
                                      year_: { $substr: ["$date", 6, 4] },
                                      month_: { $substr: ["$date", 0, 2] },
                                      dayOfMonth_: { $substr: ["$date", 3, 2] },
                                  },
                                  in: {
                                      $toDate: {
                                          $concat: ["$$year_", "-", "$$month_", "-", "$$dayOfMonth_", " ", "$startTime"]
                                      },
                                  },
                              },
                          }
                      }
                  },
              },
            },
            {
                $addFields: {
                    EndDateAndTime: {
                        $cond: {
                            if: { $eq: ["$date", ""] }, // Check if date field is empty
                            then: null, // If empty, set EndDateAndTime to null or handle it accordingly
                            else: {
                                $let: {
                                    vars: {
                                        year_: { $substr: ["$date", 6, 4] },
                                        month_: { $substr: ["$date", 0, 2] },
                                        dayOfMonth_: { $substr: ["$date", 3, 2] },
                                    },
                                    in: {
                                        $toDate: {
                                            $concat: ["$$year_", "-", "$$month_", "-", "$$dayOfMonth_", " ", "$endTime"]
                                        },
                                    },
                                },
                            }
                        }
                    },
                },
            }, 
            {
              $project: {
                  _id: 1,
                  title:1,
                  date: 1,
                  startTime:1,
                  endTime:1,
                  StartDateAndTime:1,
                  EndDateAndTime:1,
                  order:1,
                  room:{name:{$arrayElemAt: ["$RoomData.name", 0]}},
                  RoomName: { $arrayElemAt: ["$RoomData.name", 0] }
                }
            },
            {
              $addFields:{
                sortFieldLower:(req.query.sortField === "title"? { $toLower: "$title" }: req.query.sortField === "room"? { $toLower:"$RoomName"}:{ $toInt: "$order" })
              }
            },
            { $sort: (req.query.sortField === "date"? { "date" : sortType}:req.query.sortField === "startTime"? { "StartDateAndTime" : sortType}: req.query.sortField === "endTime"? { "EndDateAndTime" : sortType}:{ sortFieldLower: sortType })
            },
            {
              $project: {
                sortFieldLower: 0,
              },
            },
        ]);
        if (allSessionData)
            return res
                .status(200)
                .json({
                    status: true,
                    message: "All sessions retrive!",
                    data: allSessionData,
                });
        else
            return res
                .status(200)
                .json({
                    status: false,
                    message: "Something went wrong while getting all sessions!",
                });
    } catch (error) {
        return res
            .status(500)
            .json({ status: false, message: "internal server error!", error: error });
    }
};

// listing of session suggestion by event id
exports.getAllSessionsSuggestion = async (req, res) => {
  try {
      const allSessionData = await Session.aggregate([
          {
              $match: {
                  isDelete: false,
                  event: ObjectId(req.params.eventId),
              },
          },
          {
            $addFields: {
              titleLower: { $toLower: "$title" }
            }
          },
          {
            $sort: {
              titleLower: 1
            }
          },
          {
            $project: {title:1,}
          }
      ]);
      if (allSessionData.length)
          return res.status(200).json({status: true,message: "All sessions retrive!",data: allSessionData,});
      else
          return res.status(200).json({status: false,message: "Something went wrong while getting all sessions!",});
  } catch (error) {
      return res.status(500).json({ status: false, message: "internal server error!", error: error });
  }
};

// listing of session by event id
exports.reorderSessionsOfEventId = async (req, res) => {
    try {
        const sortType = req.query.sortType ==="Asc"? -1 : 1;
        // reOrder code
        const ids = req.body.ids
        if (ids.length > 0) {
            let resOrder = ids.map(async (item, i) => {
                await Session.findByIdAndUpdate(ObjectId(item), { order: i + 1 }, { new: true })
            });
            await Promise.all([...resOrder]);
        }
        
        const allSessionData = await Session.aggregate([
            {
                $match: {
                    isDelete: false,
                    event: ObjectId(req.params.eventId),
                },
              },
              {
                $lookup: {
                  from: "rooms",
                  localField: "room",
                  foreignField: "_id",
                  as: "RoomData",
                },
              },
              {
                $addFields: {
                  StartDateAndTime: {
                    $let: {
                      vars: {
                        year_: { $substr: ["$date", 6, 4] },
                        month_: { $substr: ["$date", 0, 2] },
                        dayOfMonth_: { $substr: ["$date", 3, 2] },
                      },
                      in: {
                        $toDate: {
                          $concat: ["$$year_", "-", "$$month_", "-", "$$dayOfMonth_"," ","$startTime"]
                        },
                      },
                    },
                  },
                },
              },
              {
                $addFields: {
                  EndDateAndTime: {
                    $let: {
                      vars: {
                        year_: { $substr: ["$date", 6, 4] },
                        month_: { $substr: ["$date", 0, 2] },
                        dayOfMonth_: { $substr: ["$date", 3, 2] },
                      },
                      in: {
                        $toDate: {
                          $concat: ["$$year_", "-", "$$month_", "-", "$$dayOfMonth_"," ","$endTime"]
                        },
                      },
                    },
                  },
                },
              },
              {
                $project: {
                    _id: 1,
                    title:1,
                    date: 1,
                    startTime:1,
                    endTime:1,
                    StartDateAndTime:1,
                    EndDateAndTime:1,
                    order:1,
                    room:{name:{$arrayElemAt: ["$RoomData.name", 0]}},
                    RoomName: { $arrayElemAt: ["$RoomData.name", 0] }
                  }
              },
              {
                $addFields:{
                  sortFieldLower:(req.query.sortField === "title"? { $toLower: "$title" }: req.query.sortField === "room"? { $toLower:"$RoomName"}:{ $toInt: "$order" })
                }
              },
              { $sort: (req.query.sortField === "date"? { "date" : sortType}:req.query.sortField === "startTime"? { "StartDateAndTime" : sortType}: req.query.sortField === "endTime"? { "EndDateAndTime" : sortType}:{ sortFieldLower: sortType })
              },
              {
                $project: {
                  sortFieldLower: 0,
                },
              },
        ]);
        if (allSessionData)
            return res
                .status(200)
                .json({
                    status: true,
                    message: "All sessions retrive!",
                    data: allSessionData,
                });
        else
            return res
                .status(200)
                .json({
                    status: false,
                    message: "Something went wrong while getting all sessions!",
                });
    } catch (error) {
        return res
            .status(500)
            .json({ status: false, message: "internal server error!", error: error });
    }
};

// session list by date and event Id
exports.getSessionListByDate = async (req, res) => {
    try {
        const date = req.query.date;
        const eventId = ObjectId(req.query.eventId);
        const sessionData = await Session.find({
            date: date,
            event: eventId,
            isDelete: false,
        });

        if (sessionData)
            return res.status(200).json({ status: true, message: "Session detail retrive!", data: sessionData, });
        else
            return res.status(200).json({ status: false, message: "Something went wrong while getting session!", });
    } catch (error) {
        return res.status(500).json({ status: false, message: "Internal server error!", error: error });
    }
};

// session details data
exports.getSessionDetails = async (req, res) => {
    try {
        const sessionData = await Session.findOne({
            _id: new ObjectId(req.params.id),
            isDelete: false,
        });
        if (sessionData)
            return res
                .status(200)
                .json({
                    status: true,
                    message: "Session detail retrive!",
                    data: sessionData,
                });
        else
            return res
                .status(200)
                .json({
                    status: false,
                    message: "Something went wrong while getting session!",
                });
    } catch (error) {
        return res
            .status(500)
            .json({ status: false, message: "Internal server error!", error: error });
    }
};

/** CURD opreation of session end **/

// session listing based on activity
exports.getSessionListByActivity = async (req, res) => {
    try {
        const authUser = req.authUserId;
        const role = req.query.role;
        var userData;
        const activityId = new ObjectId(req.params.id);
        const activityData = await eventActivity.findOne({ _id: activityId, isDelete: false,status: "published" }, { _id: 1, name: 1, event: 1 });
        if (activityData)
            userData = await User.findOne({ _id: authUser, "attendeeDetail.evntData": { $elemMatch: { event: activityData.event._id } }, }, { firebaseId: 1, email: 1, accessible_groups: 1, purchased_plan: 1, notificationFor: 1, "attendeeDetail.evntData.$": 1 });
        else
            return res.status(200).json({ status: true, message: "Activity not found!", data: [] });
        if (userData !== null && userData !== undefined) {
            const aggregateObj = [
                {
                    $lookup: {
                        from: "airtable-syncs",
                        localField: "speakerId",
                        foreignField: "_id",
                        pipeline: [
                            {
                                $project: {
                                    _id: 1,
                                    profileImg: {
                                        $cond: [
                                            {
                                                "$ifNull": [
                                                    "$profileImg",
                                                    false
                                                ]
                                            },
                                        ]
                                    },
                                    attendeeDetail: 1,
                                },
                            },
                        ],
                        as: "speakerId",
                    },
                },
                {
                    $lookup: {
                        from: "rooms",
                        localField: "room",
                        foreignField: "_id",
                        pipeline: [
                            {
                                $lookup: {
                                    from: "events",
                                    localField: "event",
                                    foreignField: "_id",
                                    pipeline: [
                                        {
                                            $project: {
                                                title: 1,
                                                thumbnail: 1,
                                                shortDescription: 1,
                                                longDescription: 1,
                                                eventUrl: 1,
                                                type: 1,
                                                timeZone: 1,
                                                startDate: 1,
                                                startTime: 1,
                                                endDate: 1,
                                                endTime: 1,
                                                restrictionAccess: 1,
                                                restrictedAccessGroupId: 1,
                                                restrictedAccessMembershipPlanId: 1,
                                                restrictedAccessTierId: 1,
                                                photos: 1,
                                                _id: 1,
                                            },
                                        },
                                    ],
                                    as: "event",
                                },
                            },
                            {
                                $lookup: {
                                    from: "eventlocations",
                                    localField: "location",
                                    foreignField: "_id",
                                    pipeline: [
                                        {
                                            $project: {
                                                name: 1,
                                                address: 1,
                                                country: 1,
                                                city: 1,
                                                latitude: 1,
                                                longitude: 1,
                                                locationImages: 1,
                                                locationVisible: 1,
                                                _id: 1,
                                            },
                                        },
                                    ],
                                    as: "location",
                                },
                            },
                            {
                                $unwind: "$location",
                            },
                            {
                                $project: {
                                    _id: 1,
                                    name: 1,
                                    location: 1,
                                    event: 1,
                                    order:1,
                                },
                            },
                        ],
                        as: "room",
                    },
                },
                {
                    $unwind: "$room"
                },
                {
                    $project: {
                        _id: 1,
                        title: 1,
                        description: "$shortDescription" ?? "",
                        shortDescription: 1,
                        longDescription: 1,
                        date: 1,
                        startTime: 1,
                        endDate: 1,
                        endTime: 1,
                        room: 1,
                        speakerId: 1,
                        reserved: 1,
                        reserved_URL: 1,
                        reservedLabelForDetail: 1,
                        reservedLabelForListing: 1,
                        member: 1,
                        speaker: 1,
                        partner: 1,
                        guest: 1,
                        createdAt: 1,
                        updatedAt: 1,
                        notifyChanges: 1,
                        notifyChangeText: 1,
                        event: 1,
                        notificationFlag: {
                            $let: {
                                vars: {
                                    test: {
                                        $filter: {
                                            input: userData.notificationFor,
                                            cond: {
                                                $and: [
                                                    { $eq: ["$_id", "$$this.id"] },
                                                    { $eq: ["session", "$$this.type"] },
                                                    { $eq: ["user", "$$this.setBy"] },
                                                ],
                                            },
                                        },
                                    },
                                },
                                in: {
                                    $gt: [{ $size: "$$test" }, 0],
                                },
                            },
                        },
                    },
                },
            ]
            if (userData.attendeeDetail.evntData[0].member === true && role === "member") {

                let attendeeData = await User.findOne({
                    _id: authUser,
                    "attendeeDetail.evntData": { $elemMatch: { event: activityData.event._id, [`member`]: true } },
                }, { _id: 1, email: 1, firebaseId: 1, "attendeeDetail.evntData.$": 1 }).lean();

                if (attendeeData !== null) {
                    const activitySessions = await eventActivity.aggregate([{ $match: { _id: ObjectId(req.params.id), member: true, isDelete: false, } }]);

                    if (activitySessions.length) {
                        const sessionList = await Session.aggregate([
                            {
                                $match: {
                                    _id: { $in: activitySessions[0].session },
                                    member: true,
                                    isDelete: false,
                                },
                            },
                            ...aggregateObj
                        ]);
                        if (sessionList.length > 0)
                            return res.status(200).json({ status: true, message: "Session list retrived based on activity!", data: sessionList, });
                        else
                            return res.status(200).json({ status: true, message: "Session list not found for this member!", data: [] });
                    } else {
                        return res.status(200).json({ status: false, message: "Something went wrong while getting sessions of activity!", });
                    }
                } else
                    return res.status(200).json({ status: true, message: "Session not found!", data: [] });
            } else if (userData.attendeeDetail.evntData[0].member === false && role === "nonMember") {
                let attendeeData = await User.findOne({
                    _id: authUser,
                    "attendeeDetail.evntData": { $elemMatch: { event: activityData.event._id, } },
                }, { _id: 1, email: 1, firebaseId: 1, "attendeeDetail.evntData.$": 1 }).lean();

                if (attendeeData !== null) {

                    if (userData.attendeeDetail.evntData[0].speaker === true && userData.attendeeDetail.evntData[0].member === true) {
                        const activitySessions = await eventActivity.aggregate([{ $match: { _id: ObjectId(req.params.id), member: true, isDelete: false, } }]);

                        if (activitySessions.length) {
                            const sessionList = await Session.aggregate([
                                {
                                    $match: {
                                        _id: { $in: activitySessions[0].session },
                                        member: true,
                                        isDelete: false,
                                    },
                                },
                                ...aggregateObj
                            ]);
                            if (sessionList.length > 0)
                                return res.status(200).json({ status: true, message: "Session list retrived based on activity!", data: sessionList, });
                            else
                                return res.status(200).json({ status: true, message: "Session list not found for this member!", data: [] });
                        } else {
                            return res.status(200).json({ status: false, message: "Something went wrong while getting sessions of activity!", });
                        }

                    } else if (userData.attendeeDetail.evntData[0].speaker === true && userData.attendeeDetail.evntData[0].partner === true) {
                        const activitySessions = await eventActivity.aggregate([{ $match: { _id: ObjectId(req.params.id), partner: true, isDelete: false } }]);

                        if (activitySessions.length) {
                            const sessionList = await Session.aggregate([
                                {
                                    $match:
                                    {
                                        _id: { $in: activitySessions[0].session },
                                        partner: true,
                                        isDelete: false,
                                    },
                                },
                                ...aggregateObj
                            ]);
                            if (sessionList.length > 0)
                                return res.status(200).json({ status: true, message: "Session list retrived based on activity!", data: sessionList, });
                            else
                                return res.status(200).json({ status: true, message: "Session list not found for this member!", data: [] });
                        } else {
                            return res.status(200).json({ status: false, message: "Something went wrong while getting sessions of activity!", });
                        }

                    } else if (userData.attendeeDetail.evntData[0].member === true && userData.attendeeDetail.evntData[0].guest === true) {
                        const activitySessions = await eventActivity.aggregate([{ $match: { _id: ObjectId(req.params.id), guest: true, isDelete: false } }]);

                        if (activitySessions.length) {
                            const sessionList = await Session.aggregate([
                                {
                                    $match: {
                                        _id: { $in: activitySessions[0].session },
                                        guest: true,
                                        isDelete: false,
                                    }
                                },
                                ...aggregateObj
                            ]);
                            if (sessionList.length > 0)
                                return res.status(200).json({ status: true, message: "Session list retrived based on activity!", data: sessionList, });
                            else
                                return res.status(200).json({ status: true, message: "Session list not found for this member!", data: [] });
                        } else {
                            return res.status(200).json({ status: false, message: "Something went wrong while getting sessions of activity!", });
                        }

                    } else if (userData.attendeeDetail.evntData[0].speaker === true) {
                        const activitySessions = await eventActivity.aggregate([{ $match: { _id: ObjectId(req.params.id), speaker: true, isDelete: false } }]);

                        if (activitySessions.length > 0) {
                            const sessionList = await Session.aggregate([
                                {
                                    $match:
                                    {
                                        _id: { $in: activitySessions[0].session },
                                        speaker: true,
                                        isDelete: false,
                                    },
                                },
                                ...aggregateObj
                            ]);
                            if (sessionList.length > 0)
                                return res.status(200).json({ status: true, message: "Session list retrived based on activity!", data: sessionList, });
                            else
                                return res.status(200).json({ status: true, message: "Session list not found for this member!", data: [] });
                        } else {
                            return res.status(200).json({ status: false, message: "Something went wrong while getting sessions of activity!", });
                        }

                    } else if (userData.attendeeDetail.evntData[0].partner === true) {
                        const activitySessions = await eventActivity.aggregate([{ $match: { _id: ObjectId(req.params.id), partner: true, isDelete: false } }]);

                        if (activitySessions.length) {
                            const sessionList = await Session.aggregate([
                                {
                                    $match:
                                    {
                                        _id: { $in: activitySessions[0].session },
                                        partner: true,
                                        isDelete: false,
                                    },
                                },
                                ...aggregateObj
                            ]);
                            if (sessionList.length > 0)
                                return res.status(200).json({ status: true, message: "Session list retrived based on activity!", data: sessionList, });
                            else
                                return res.status(200).json({ status: true, message: "Session list not found for this member!", data: [] });
                        } else {
                            return res.status(200).json({ status: false, message: "Something went wrong while getting sessions of activity!", });
                        }

                    } else if (userData.attendeeDetail.evntData[0].guest === true) {
                        const activitySessions = await eventActivity.aggregate([{ $match: { _id: ObjectId(req.params.id), guest: true, isDelete: false } }]);

                        if (activitySessions.length) {
                            const sessionList = await Session.aggregate([
                                {
                                    $match: {
                                        _id: { $in: activitySessions[0].session },
                                        guest: true,
                                        isDelete: false,
                                    }
                                },
                                ...aggregateObj
                            ]);
                            if (sessionList.length > 0)
                                return res.status(200).json({ status: true, message: "Session list retrived based on activity!", data: sessionList, });
                            else
                                return res.status(200).json({ status: true, message: "Session list not found for this member!", data: [] });
                        } else {
                            return res.status(200).json({ status: false, message: "Something went wrong while getting sessions of activity!", });
                        }

                    }
                } else
                    return res.status(200).json({ status: true, message: "Session not found!", data: [] });
            }
        } else
            return res.status(200).json({ status: true, message: "Session list not found for this member!", data: [] });
    } catch (error) {
        return res.status(500).json({ status: false, message: "Internal server error!", error: error });
    }
};

// get session detail by id
exports.getSessionDetailsById = async (req, res) => {
    try {
        const authUserId = req.authUserId;
        const userData = await User.findOne({ _id: ObjectId(authUserId) });
        const notificationFor = userData.notificationFor;
        const sessionDetail = await Session.aggregate([
            {
                $match: {
                    _id: ObjectId(req.params.id),
                    isDelete: false,
                },
            },
            {
                $lookup: {
                    from: "rooms",
                    let: { session_rooms_id: "$room" },
                    pipeline: [
                        {
                            $match: {
                                $expr: {
                                    $eq: ["$_id", "$$session_rooms_id"],
                                },
                            },
                        },
                        {
                            $lookup: {
                                from: "eventlocations",
                                let: { location_id: "$location" },
                                pipeline: [
                                    {
                                        $match: {
                                            $expr: {
                                                $eq: ["$_id", "$$location_id"],
                                            },
                                        },
                                    },
                                    { $project: { name: 1, address: 1, country: 1, city: 1, latitude: 1, longitude: 1, locationVisible: 1, locationImages: 1 } },
                                ],
                                as: "location"
                            },
                        },
                        {
                            $unwind: "$location",
                        },
                        { $project: { name: 1, location: 1, } },
                    ],
                    as: "room"
                }
            },
            {
                $lookup: {
                    from: "events",
                    let: { event_id: "$event" },
                    pipeline: [
                        {
                            $match: {
                                $expr: {
                                    $eq: ["$_id", "$$event_id"],
                                },
                            },
                        },
                        { $project: { title: 1, thumbnail: 1, description: "$longDescription", eventUrl: 1, type: 1, timeZone: 1, startDate: 1, startTime: 1, endDate: 1, endTime: 1, restrictionAccess: 1, restrictedAccessGroupId: 1, restrictedAccessMembershipPlanId: 1, restrictedAccessTierId: 1, photos: 1, } },
                    ],
                    as: "events"
                }
            },
            {
                '$lookup': {
                  'from': 'airtable-syncs', 
                  'let': {
                    'speakerId': '$speakerId', 
                    'eventId': '$event'
                  }, 
                  'pipeline': [
                    {
                      '$match': {
                        '$expr': {
                          '$in': [
                            '$_id', '$$speakerId'
                          ]
                        }
                      }
                    }, {
                      '$lookup': {
                        'let': {
                          'user': '$_id'
                        }, 
                        'from': 'event_participant_attendees', 
                        'pipeline': [
                          {
                            '$match': {
                              '$expr': {
                                '$and': [
                                  {
                                    '$eq': [
                                      '$user', '$$user'
                                    ]
                                  }, {
                                    '$eq': [
                                      '$event', '$$eventId'
                                    ]
                                  }, {
                                    '$eq': [
                                      '$isDelete', false
                                    ]
                                  }
                                ]
                              }
                            }
                          }, {
                            '$lookup': {
                              'from': 'event_wise_participant_types', 
                              'localField': 'role', 
                              'foreignField': '_id', 
                              'as': 'event_wise_participant_types', 
                              'pipeline': [
                                {
                                  '$match': {
                                    '$expr': {
                                      '$eq': [
                                        '$role', 'Speaker'
                                      ]
                                    }
                                  }
                                }
                              ]
                            }
                          }, {
                            '$unwind': {
                              'path': '$event_wise_participant_types', 
                              'preserveNullAndEmptyArrays': false
                            }
                          }, {
                            '$project': {
                              '_id': 0, 
                              'roleId': '$role', 
                              'roleType': '$event_wise_participant_types.role'
                            }
                          }
                        ], 
                        'as': 'roleTypes'
                      }
                    }, {
                      '$unwind': {
                        'path': '$roleTypes', 
                        'preserveNullAndEmptyArrays': false
                      }
                    }, {
                      '$project': {
                        'roleId': {
                          '$ifNull': [
                            '$roleTypes.roleId', ''
                          ]
                        }, 
                        'roleType': {
                          '$ifNull': [
                            '$roleTypes.roleType', ''
                          ]
                        }, 
                        '_id': {
                          '$ifNull': [
                            '$_id', ''
                          ]
                        }, 
                        'title': {
                          '$ifNull': [
                            '$attendeeDetail.title', ''
                          ]
                        }, 
                        'first_name': {
                          '$ifNull': [
                            '$first_name', ''
                          ]
                        }, 
                        'last_name': {
                          '$ifNull': [
                            '$last_name', ''
                          ]
                        }, 
                        'display_name': {
                          '$ifNull': [
                            '$display_name', ''
                          ]
                        }, 
                        'name': {
                          '$ifNull': [
                            '$display_name', ''
                          ]
                        }, 
                        'company': {
                          '$ifNull': [
                            '$attendeeDetail.company', ''
                          ]
                        }, 
                        'profession': {
                          '$ifNull': [
                            '$attendeeDetail.profession', ''
                          ]
                        }, 
                        'phone': {
                          '$ifNull': [
                            '$attendeeDetail.phone', ''
                          ]
                        }, 
                        'facebook': {
                          '$ifNull': [
                            '$attendeeDetail.facebook', ''
                          ]
                        }, 
                        'linkedin': {
                          '$ifNull': [
                            '$attendeeDetail.linkedin', ''
                          ]
                        }, 
                        'firebaseId': {
                          '$ifNull': [
                            '$attendeeDetail.firebaseId', ''
                          ]
                        }, 
                        'profileImg': {
                          '$ifNull': [
                            '$profileImg', ''
                          ]
                        }
                      }
                    }
                  ], 
                  'as': 'speakerId'
                }
              },
            {
                $lookup: {
                  from: 'event_wise_participant_types', 
                  localField: 'accessRoles', 
                  foreignField: '_id', 
                  as: 'accessRoles'
                }
            },
            {
                $project: {
                    _id: 1,
                    title: 1,
                    description: "$longDescription",
                    shortDescription: 1,
                    longDescription: 1,
                    date: 1,
                    startTime: 1,
                    endDate: 1,
                    endTime: 1,
                    room: 1,
                    speakerId: 1,
                    event: 1,
                    reserved: 1,
                    reserved_URL: 1,
                    reservedLabelForDetail: 1,
                    reservedLabelForListing: 1,
                    member: 1,
                    speaker: 1,
                    partner: 1,
                    guest: 1,
                    notifyChanges: 1,
                    notifyChangeText: 1,
                    accessRoles: 1,
                    notificationFlag: {
                        $let:
                        {
                            vars: {
                                test: {
                                    $filter: {
                                        input: userData.notificationFor,
                                        cond: {
                                            $and: [
                                                { $eq: ["$_id", "$$this.id"] },
                                                { $eq: ["session", "$$this.type"] },
                                                { $eq: ["user", "$$this.setBy"] },
                                            ]
                                        }
                                    },
                                }
                            }, in: {
                                $gt: [{ $size: "$$test" }, 0]
                            }
                        }
                    },
                    isDelete: 1,
                    createdAt: 1,
                    updatedAt: 1,
                },
            },
        ]);

        if (sessionDetail)
            return res.status(200).json({ status: true, message: "Session detailed retrived!", data: sessionDetail[0], });
        else
            return res.status(200).json({ status: false, message: "Something went wrong while getting session detail!", });
    } catch (error) {
        return res.status(500).json({ status: false, message: "Internal server error!", error: error });
    }
};

/** =========================== **/

/** session start version 2 **/

// create session version 2 from admin side
exports.createSessionV2 = async (req, res) => {
    try {
        const errors = validationResult(req);
        if (!errors.isEmpty()) {
            let result = validationResult(req).array({ onlyFirstError: false });
            result = result.map((err) => { return { path: err['path'], msg: err['msg'] }; });
            return res.status(403).json({ status: false, message: "Validation error!", errors: result });
        } else {
            const body = req.body;
            if (body) {
                let description = `<div "font-family: 'Muller';">${body.longDescription}</div>`;
                body.longDescription = description;
                body.isEndOrNextDate = body.isEndOrNextDate !== null && body.isEndOrNextDate !== "" ? body.isEndOrNextDate : false;
                body.endDate = body.endDate !== null && body.endDate !== "" ? body.endDate : body.date;

                const fetchEvent = await event.findOne(
                    { _id: body.event },
                    {
                      timeZone: 1,
                      tag: 0,
                      category: 0,
                      subcategory: 0,
                      relation_id: 0,
                    }
                  );
                  if(!fetchEvent) {
                    return res.status(404).json({ status: false, message: "Event not found!" });
                  }

                const newSession = new Session({ ...body });
                const sessionData = await newSession.save();

                if (sessionData.scheduleNotify === true) {
                    const alreadyAssignActivity = await eventActivity.find({ session: { $in: [new ObjectId(sessionData._id)] }, isDelete: false }).lean();
                    const scheduleNotifyTime = sessionData.scheduleNotifyTime;
                    if (alreadyAssignActivity && alreadyAssignActivity.length > 0) {
                        var allUsers = [], members = [], speakers = [], partners = [], guests = [];
                        if (sessionData.member === true) {
                            members = await User.find({ "attendeeDetail.evntData": { $elemMatch: { event: sessionData.event, [`member`]: true } }, }, { "attendeeDetail.evntData.$": 1 });
                            allUsers = allUsers.concat(members);
                        } else if (sessionData.speaker === true) {
                            speakers = await User.find({ "attendeeDetail.evntData": { $elemMatch: { event: sessionData.event, [`speaker`]: true } }, }, { "attendeeDetail.evntData.$": 1 });
                            allUsers = allUsers.concat(speakers);
                        } else if (sessionData.partner === true) {
                            partners = await User.find({ "attendeeDetail.evntData": { $elemMatch: { event: sessionData.event, [`partner`]: true } }, }, { "attendeeDetail.evntData.$": 1 });
                            allUsers = allUsers.concat(partners);
                        } else if (sessionData.guest === true) {
                            guests = await User.find({ "attendeeDetail.evntData": { $elemMatch: { event: sessionData.event, [`guest`]: true } }, }, { "attendeeDetail.evntData.$": 1 });
                            allUsers = allUsers.concat(guests);
                        }

                        const uniqueUsers = allUsers.filter((value, index, self) =>
                            index === self.findIndex((t) => (
                                t._id === value._id
                            ))
                        )

                        if (uniqueUsers.length > 0) {
                            const eventID = sessionData.event ? sessionData.event : null;
                            const sessionID = sessionData._id ? sessionData._id : null;
                            let schedule = uniqueUsers.map(async (user, i) => {
                                await scheduleNotificationFormAdmin(user._id, eventID, "", sessionID, "session", scheduleNotifyTime)
                            });
                            await Promise.all([...schedule]);
                        }

                    } else {

                      const sessionDate = sessionData.date;
                      const sessionTime = sessionData.startTime;

                      //* Construct notification data
                      const templateData = {
                        sessionName: sessionData?.title || "",
                        scheduleNotifyTime:
                          scheduleNotifyTime === "120"
                            ? "2 hours"
                            : scheduleNotifyTime === "60"
                              ? "1 hours"
                              : `${scheduleNotifyTime} minutes.`,
                      };

                      //* Prepare notification payload
                      const data = {
                        templateData,
                        type: "session",
                        date: sessionDate,
                        time: sessionTime,
                        eventTimeZone: fetchEvent.timeZone,
                        relationId: req.relation_id,
                        eventId: fetchEvent?._id,
                        sessionId: sessionData?._id,
                        duration: scheduleNotifyTime,
                        notificationType: "user_session_reminder",
                        messageType: "sessionReminder",
                        createdBy: "admin"
                      };
            
                      await createNotification({ data });
                        // let accessRoles = body?.accessRoles.map((id) => { return ObjectId(id) });
                        // let pipeline = [
                        //     {
                        //         '$match': {
                        //             'role': { '$in': accessRoles },
                        //             'event': ObjectId(sessionData.event)
                        //         }
                        //     }, {
                        //         '$group': {
                        //             '_id': '$user',
                        //             'items': {
                        //                 '$first': '$$ROOT'
                        //             }
                        //         }
                        //     }, {
                        //         '$replaceRoot': {
                        //             'newRoot': '$items'
                        //         }
                        //     }, {
                        //         '$lookup': {
                        //             'from': 'airtable-syncs',
                        //             'localField': 'user',
                        //             'foreignField': '_id',
                        //             'as': 'airtable-syncs_result'
                        //         }
                        //     }, {
                        //         '$unwind': {
                        //             'path': '$airtable-syncs_result',
                        //             'preserveNullAndEmptyArrays': false
                        //         }
                        //     }, {
                        //         '$project': {
                        //             'airtable-syncs_result': 0
                        //         }
                        //     }
                        // ]
                        // let uniqueUsers = await eventParticipantAttendees.aggregate(pipeline);
                        // if (uniqueUsers.length > 0) {
                        //     const eventID = sessionData.event ? sessionData.event : null;
                        //     const sessionID = sessionData._id ? sessionData._id : null;
                        //     let schedule = uniqueUsers.map(async (user, i) => {
                                // await scheduleNotificationFormAdmin(user.user, eventID, "", sessionID, "session", scheduleNotifyTime)
                        //     });
                        //     await Promise.all([...schedule]);
                        // }
                    }
                }
                if (sessionData)
                    return res.status(200).json({ status: true, message: "Session added successfully!", data: sessionData, });
                else
                    return res.status(200).json({ status: false, message: "Something went wrong while adding session!", });
            } else {
                return res.status(200).json({ status: false, message: "All fields are required!" });
            }
        }
    } catch (error) {
        console.log("🚀 ~ exports.createSessionV2= ~ error:", error);
        return res.status(500).json({ status: false, message: "Internal server error!", error: error });
    }
};

// update session
exports.editSessionV2 = async (req, res) => {
    try {
        const errors = validationResult(req);
        if (!errors.isEmpty()) {
            let result = validationResult(req).array({ onlyFirstError: false });
            result = result.map((err) => { return { path: err['path'], msg: err['msg'] }; });
            return res.status(403).json({ status: false, message: "Validation error!", errors: result });
        } else {
            const body = req.body;
            const getSession = await Session.findOne({ _id: new ObjectId(req.params.id), isDelete: false, }).lean();
            if (getSession) {

                const fetchEvent = await event.findOne(
                  { _id: getSession?.event, isDelete: false },
                  {
                    timeZone: 1,
                    title: 1,
                    tag: 0,
                    category: 0,
                    subcategory: 0,
                    relation_id: 0,
                  }
                );

                if(!fetchEvent) {
                  return res.status(404).status({ status: false, message: "Event not found!", data: {} });
                }

                let description = `<div "font-family: 'Muller';">${body.longDescription}</div>`;
                let shortDescription = `${body.shortDescription}`;

                let accessRoles = [];
                if (body.accessRoles && body.accessRoles.length == 0 ) {
                    accessRoles = [];
                } else if (body.accessRoles && body.accessRoles.length != 0 ) {
                    accessRoles = body.accessRoles.map((id) => { if (ObjectId.isValid(id)) { return ObjectId(id) } });
                } else if (getSession.accessRoles && getSession.accessRoles.length) {
                    // accessRoles = getSession.accessRoles.map((id) => { return ObjectId(id) });
                }
                const sessionData = await Session.findOneAndUpdate(
                    { _id: new ObjectId(req.params.id) },
                    {
                        title: body.title ?? getSession.title,
                        shortDescription: shortDescription ?? getSession.shortDescription,
                        longDescription: description ?? getSession.longDescription,
                        date: body.date ?? getSession.date,
                        startTime: body.startTime ?? getSession.startTime,
                        endTime: body.endTime ?? getSession.endTime,
                        room: body.room ?? getSession.room,
                        speakerId: (body.speakerId.length > 0) ? body.speakerId : getSession.speakerId,
                        reserved: body.reserved ?? getSession.reserved,
                        reserved_URL: body.reserved_URL ?? getSession.reserved_URL,
                        reservedLabelForListing: body.reservedLabelForListing ?? getSession.reservedLabelForListing,
                        reservedLabelForDetail: body.reservedLabelForDetail ?? getSession.reservedLabelForDetail,
                        member: body.member ?? getSession.member,
                        speaker: body.speaker ?? getSession.speaker,
                        partner: body.partner ?? getSession.partner,
                        guest: body.guest ?? getSession.guest,
                        event: body.event ?? getSession.event,
                        notifyChanges: body?.notifyChanges !== null ? body?.notifyChanges : getSession.notifyChanges === undefined ? false : getSession.notifyChanges,
                        notifyChangeText: body?.notifyChangeText !== null && body?.notifyChangeText !== "" ? body?.notifyChangeText : getSession.notifyChangeText === undefined ? "" : getSession.notifyChangeText,
                        isEndOrNextDate: req.body?.isEndOrNextDate !== null && req.body?.isEndOrNextDate !== "" ? req.body?.isEndOrNextDate : getSession.isEndOrNextDate === undefined ? false : getSession.isEndOrNextDate,
                        endDate: req.body?.endDate !== null && req.body.endDate !== "" ? req.body.endDate : getSession.endDate === undefined ? null : body.date ?? getSession.date,
                        scheduleNotify: req.body?.scheduleNotify !== null && req.body?.scheduleNotify !== "" ? req.body?.scheduleNotify : getSession.scheduleNotify === undefined ? false : getSession.scheduleNotify,
                        scheduleNotifyTime: req.body?.scheduleNotifyTime !== null && req.body?.scheduleNotifyTime !== "" ? req.body?.scheduleNotifyTime : getSession.scheduleNotifyTime === undefined ? "" : getSession.scheduleNotifyTime,
                        accessRoles: accessRoles,
                    },
                    { new: true }
                );

                const alreadyAdded = await scheduleNotification.find({ sessionId: req?.params?.id, relationId: req?.relation_id, type: "session", isDelete: false }).lean()

                const alreadyAddedUsers = await User.find({ notificationFor: { $elemMatch: { id: sessionData._id }, }, }, { "notificationFor.$": 1 });
                const alreadyAssignActivity = await eventActivity.find({ session: { $in: [getSession._id] }, event: getSession.event, isDelete: false }).lean();

                if (alreadyAssignActivity && alreadyAssignActivity.length > 0) {
                    // if (body.date !== getSession.date || req.body.startTime !== getSession.startTime) {
                    //     if (alreadyAddedUsers.length > 0) {
                    //         const eventID = getSession.event ? getSession.event : null;
                    //         const sessionID = getSession._id ? getSession._id : null;
                    //         let resOrder = alreadyAddedUsers.map(async (user, i) => {
                    //             const userData = await User.findOne({ _id: ObjectId(user._id) });
                    //             await reScheduleNotificationForActivitySession(userData._id, eventID._id, "", sessionID, "session", req.relation_id,);
                    //         });
                    //         await Promise.all([...resOrder]);
                    //     }
                    // }

                    if (alreadyAssignActivity.length > 0) {
                        alreadyAssignActivity.map(async (getActivity, i) => {
                            const { _id: activityId, date: existingDate, startTime: existingTime, scheduleNotifyTime: existingScheduleTime, name: existingName } = getActivity;
                            const alreadyAdded = await User.find({ notificationFor: { $elemMatch: { id: new ObjectId(getActivity._id) }, }, }, { _id: 1, "notificationFor.$": 1 });
                            var startTimeArr = [], endTimeArr = [], startDateArr = [], endDateArr = [];
                            if (getActivity.session) {
                                if (getActivity.session.length > 1) {

                                    let resOrder = getActivity.session.map(async ids => {
                                        const sessionDetail = await Session.findOne({ _id: new ObjectId(ids._id), isDelete: false, });
                                        startDateArr.push(moment(sessionDetail.date, "MM-DD-YYYY"));
                                        endDateArr.push(moment(sessionDetail.endDate, "MM-DD-YYYY"));
                                        startTimeArr.push(sessionDetail.startTime);
                                        endTimeArr.push(sessionDetail.endTime);
                                    });
                                    await Promise.all([...resOrder]);

                                    // Convert each time string in the array to Date objects
                                    const minTimeObjects = startTimeArr.map(startTimeStr => moment(startTimeStr, "h:mm a"));
                                    const maxTimeObjects = endTimeArr.map(endTimeStr => moment(endTimeStr, "h:mm a"));

                                    // Find the minimum time in the array
                                    const minTime = new Date(Math.min(...minTimeObjects));
                                    const maxTime = new Date(Math.max(...maxTimeObjects));
                                    const minDate = moment.min(startDateArr);
                                    const maxDate = moment.max(endDateArr);

                                    // Format the minimum time as a string
                                    const minTimeString = minTime.toLocaleTimeString([], { hour: '2-digit', minute: '2-digit' });
                                    const maxTimeString = maxTime.toLocaleTimeString([], { hour: '2-digit', minute: '2-digit' });
                                    const minDateString = moment(minDate).format("MM-DD-YYYY");
                                    const maxDateString = moment(maxDate).format("MM-DD-YYYY");

                                    getActivity.startTime = minTimeString;
                                    getActivity.endTime = maxTimeString;
                                    getActivity.date = minDateString;
                                    getActivity.endDate = maxDateString;

                                } else {
                                    const sessionData = await Session.findOne({ _id: new ObjectId(getActivity.session[0]._id), isDelete: false, });
                                    getActivity.startTime = sessionData.startTime;
                                    getActivity.endTime = sessionData.endTime;
                                    getActivity.date = sessionData.date;
                                    getActivity.endDate = sessionData.endDate;
                                }
                            }

                            // if (body.date !== getActivity.date || req.body.startTime !== getActivity.startTime) {
                            //     if (alreadyAdded.length > 0) {
                            //         const eventID = getActivity.event ? getActivity.event : null;
                            //         const activityID = getActivity._id ? getActivity._id : null;
                            //         let resOrder = alreadyAdded.map(async (user, i) => {
                            //             const userData = await User.findOne({ _id: ObjectId(user._id) });
                            //             await reScheduleNotificationForActivitySession(userData._id, eventID._id, activityID, "", "activity", req.relation_id,);
                            //         });
                            //         await Promise.all([...resOrder]);
                            //     }
                            // }

                            const updateActivity = await eventActivity.findByIdAndUpdate(getActivity._id, // eventActivity here
                                {
                                    name: getActivity.name,
                                    icon: getActivity.icon,
                                    shortDescription: getActivity.shortDescription,
                                    longDescription: getActivity.longDescription,
                                    date: getActivity.date,
                                    startTime: getActivity.startTime,
                                    endTime: getActivity.endTime,
                                    member: getActivity.member,
                                    speaker: getActivity.speaker,
                                    partner: getActivity.partner,
                                    guest: getActivity.guest,
                                    session: getActivity.session ?? [],
                                    reserved: getActivity.reserved,
                                    reserved_URL: getActivity.reserved_URL,
                                    event: getActivity.event,
                                    location: getActivity.location,
                                    notifyChanges: getActivity.notifyChanges,
                                    notifyChangeText: getActivity.notifyChangeText,
                                    isEndOrNextDate: getActivity.isEndOrNextDate,
                                    endDate: getActivity.endDate,
                                },
                                { new: true }
                            );

                            //* update the scheduled notification
                            if (updateActivity && updateActivity?.scheduleNotify) {
                              const alreadyAdded = await scheduleNotification.find({ activityId: getActivity?._id, relationId: req?.relation_id, type: "activity", isDelete: false }).lean();
                              const { scheduleNotifyTime, date: activityDate, startTime: activityTime, name } = updateActivity;
                              const { _id: eventId, timeZone: eventTimeZone } = fetchEvent;

                              //* Construct notification data
                              const templateData = {
                                activityName: name || "",
                                scheduleNotifyTime: 
                                  scheduleNotifyTime === "120" ? "2 hours" :
                                  scheduleNotifyTime === "60" ? "1 hour" :
                                  `${scheduleNotifyTime} minutes`,
                              };

                              //* Prepare notification payload
                              const data = {
                                templateData,
                                type: "activity",
                                date: activityDate,
                                time: activityTime,
                                eventTimeZone,
                                relationId: req.relation_id,
                                eventId,
                                activityId,
                                duration: Number(scheduleNotifyTime),
                                notificationType: "user_activity_reminder",
                                messageType: "activityReminder",
                                createdBy: "admin",
                              };

                              //* Check if notification already exists
                              if (alreadyAdded.length) {
                                const isDateUpdated = activityDate !== existingDate;
                                const isTimeUpdated = activityTime !== existingTime;
                                const isDurationUpdated = scheduleNotifyTime !== existingScheduleTime;
                                const isNameUpdated = name !== existingName;

                                if (isDateUpdated || isTimeUpdated || isDurationUpdated || isNameUpdated) {
                                  const filter = { activityId, relationId: req?.relation_id, type: "activity", isDelete: false };
                                  const dataaaa = await updateNotification({ filter, data, isDateUpdated, isTimeUpdated, isDurationUpdated, isNameUpdated });
                                }

                                const getAdminNotificationData = alreadyAdded.find(x => x.createdBy === "admin");
                                if(!getAdminNotificationData) {
                                  await createNotification({ data });
                                }
                              } else {
                                await createNotification({ data });
                              }
                            }

                            if(updateActivity && (updateActivity?.date !== existingDate || updateActivity?.startTime !== existingTime) ) {
                              const { _id: eventId, timeZone: eventTimeZone } = fetchEvent;
          
                              const activityDate = updateActivity?.date;
                              const activityTime = updateActivity?.startTime;
          
                              //* Create a new notification for the session
                              let templateData = {
                                activityName: updateActivity.name ? updateActivity.name : "",
                                scheduleNotifyTime: "15 minutes.",
                              };
                            
                              //* Create a new notification for the session
                              const data = {
                                templateData,
                                type: "activity",
                                date: activityDate,
                                time: activityTime,
                                eventTimeZone,
                                relationId: req?.relation_id,
                                eventId,
                                activityId: updateActivity?._id,
                                duration: 15,
                                notificationType: "user_activity_reminder",
                                messageType: "activityReminder",
                                createdBy: "user",
                              };
          
                              const isDateUpdated = updateActivity?.date !== existingDate;
                              const isTimeUpdated = updateActivity?.startTime !== existingTime;
                              const isDurationUpdated = false;
                              const isNameUpdated = false;
          
                              if(isDateUpdated || isTimeUpdated) {
                                const filter = { activityId: updateActivity?._id, relationId: req?.relation_id, type: "activity", isDelete: false };
                                await updateUserNotification({ filter, data, isDateUpdated, isTimeUpdated, isDurationUpdated, isNameUpdated });
                              }
                            }
                        });
                    }
                }

                const eventID = sessionData.event ? sessionData.event : null;
                const sessionID = sessionData._id ? sessionData._id : null;
                // await scheduleJobNotification(eventID, "", sessionID, "session", {matchCondition});
                if (sessionData.scheduleNotify === true) {
                    // const scheduleNotifyTime = sessionData.scheduleNotifyTime;
                    const { scheduleNotifyTime, date: activityDate, startTime: activityTime, title } = sessionData;
                    const { _id: eventId, timeZone: eventTimeZone } = fetchEvent;
                    const { _id: sessionId, date: existingDate, startTime: existingTime, scheduleNotifyTime: existingScheduleTime, title: existingName } = getSession;

                    const sessionDate = sessionData.date;
                    const sessionTime = sessionData.startTime;

                    //* Construct notification data
                    const templateData = {
                      sessionName: sessionData?.title || "",
                      scheduleNotifyTime:
                        scheduleNotifyTime === "120"
                          ? "2 hours"
                          : scheduleNotifyTime === "60"
                            ? "1 hours"
                            : `${scheduleNotifyTime} minutes.`,
                    };

                    //* Prepare notification payload
                    const data = {
                      templateData,
                      type: "session",
                      date: sessionDate,
                      time: sessionTime,
                      eventTimeZone: eventTimeZone,
                      relationId: req.relation_id,
                      eventId,
                      sessionId,
                      duration: Number(scheduleNotifyTime),
                      notificationType: "user_session_reminder",
                      messageType: "sessionReminder",
                      createdBy: "admin"
                    };

                    //* Check if notification already exists
                    if (alreadyAdded.length) {
                      const isDateUpdated = activityDate !== existingDate;
                      const isTimeUpdated = activityTime !== existingTime;
                      const isDurationUpdated = scheduleNotifyTime !== existingScheduleTime;
                      const isNameUpdated = title !== existingName;

                      if (isDateUpdated || isTimeUpdated || isDurationUpdated || isNameUpdated) {
                        const filter = { sessionId: req?.params?.id, relationId: req?.relation_id, type: "session", isDelete: false };
                        await updateNotification({ filter, data, isDateUpdated, isTimeUpdated, isDurationUpdated, isNameUpdated });
                      }

                      const getAdminNotificationData = alreadyAdded.find(x => x.createdBy === "admin");
                      if(!getAdminNotificationData) {
                        await createNotification({ data });
                      }
                    } else {
                      await createNotification({ data });
                    }

                    // let accessRoles = sessionData.accessRoles.map((id) => { return ObjectId(id) });
                    // let matchCondition = [];
                    // matchCondition.push( { 'role': { '$in': accessRoles } });
                    // if (alreadyAssignActivity && alreadyAssignActivity.length > 0) {
                    //     let pipeline = [
                    //         {
                    //             '$match': {
                    //                 'role': { '$in': accessRoles },
                    //                 'event': ObjectId(sessionData.event)
                    //             }
                    //         }, {
                    //             '$group': {
                    //                 '_id': '$user',
                    //                 'items': {
                    //                     '$first': '$$ROOT'
                    //                 }
                    //             }
                    //         }, {
                    //             '$replaceRoot': {
                    //                 'newRoot': '$items'
                    //             }
                    //         }, {
                    //             '$lookup': {
                    //                 'from': 'airtable-syncs',
                    //                 'localField': 'user',
                    //                 'foreignField': '_id',
                    //                 'as': 'airtable-syncs_result'
                    //             }
                    //         }, {
                    //             '$unwind': {
                    //                 'path': '$airtable-syncs_result',
                    //                 'preserveNullAndEmptyArrays': false
                    //             }
                    //         }, {
                    //             '$project': {
                    //                 'airtable-syncs_result': 0
                    //             }
                    //         }
                    //     ]
                    //     // let uniqueUsers = await eventParticipantAttendees.aggregate(pipeline);
                    //     // if (uniqueUsers.length > 0) {
                    //     //     let schedule = uniqueUsers.map(async (user, i) => {
                    //     //     });
                    //     //     await Promise.all([...schedule]);
                    //     // }
                    // }
                    //     // const eventID = sessionData.event ? sessionData.event : null;
                    //     // const sessionID = sessionData._id ? sessionData._id : null;
                    //     // await scheduleNotificationFormAdmin(user.user, eventID, "", sessionID, "session", scheduleNotifyTime)
                        // reScheduleNotificationFormAdminV2(eventID, "", sessionID, "session", scheduleNotifyTime, {matchCondition}, req.relation_id,);
                } else if (sessionData.scheduleNotify === false) {
                  await scheduleNotification.deleteMany({
                    sessionId: req?.params?.id,
                    relationId: req?.relation_id,
                    type: "session",
                    isDelete: false,
                    createdBy: "admin",
                  });

                    // if (alreadyAddedUsers.length > 0) {
                    //     const NotificationFor = {
                    //         id: sessionData._id,
                    //         type: "session",
                    //         setBy: "admin",
                    //     };
                    //     let resCancel = alreadyAddedUsers.map(async (user, i) => {
                    //         const scheduleData = await ScheduledNotification.findOne({ createdFor: user._id, idsFor: sessionData._id, createdBy: "admin" });
                    //         if (scheduleData !== null) {
                    //             await User.findOneAndUpdate(
                    //                 { _id: user._id },
                    //                 { $pull: { notificationFor: NotificationFor } },
                    //                 { new: true }
                    //             );
                    //             await ScheduledNotification.findByIdAndRemove(scheduleData._id);
                    //         }
                    //     });
                    //     await Promise.all([...resCancel]);
                    // }
                }

                if(sessionData && (sessionData?.date !== getSession?.date || sessionData?.startTime !== getSession?.startTime) ) {
                    const { _id: eventId, timeZone: eventTimeZone } = fetchEvent;

                    const sessionDate = sessionData?.date;
                    const sessionTime = sessionData?.startTime;

                    //* Create a new notification for the session
                    let templateData = {
                      sessionName: sessionData.title ? sessionData.title : "",
                      scheduleNotifyTime: "15 minutes.",
                    };
                  
                    //* Create a new notification for the session
                    const data = {
                      templateData,
                      type: "session",
                      date: sessionDate,
                      time: sessionTime,
                      eventTimeZone,
                      relationId: req?.relation_id,
                      eventId,
                      sessionId: sessionData?._id,
                      duration: 15,
                      notificationType: "user_session_reminder",
                      messageType: "sessionReminder",
                      createdBy: "user",
                    };

                    const isDateUpdated = sessionData?.date !== getSession?.date;
                    const isTimeUpdated = sessionData?.startTime !== getSession?.startTime;
                    const isDurationUpdated = false;
                    const isNameUpdated = false;

                    if(isDateUpdated || isTimeUpdated) {
                      const filter = { sessionId: req?.params?.id, relationId: req?.relation_id, type: "session", isDelete: false };
                      await updateUserNotification({ filter, data, isDateUpdated, isTimeUpdated, isDurationUpdated, isNameUpdated });
                    }
                }

                if (body?.notifyChanges === true) {
                  let pipeline = [
                    {
                      $match: {
                        session: {
                          $in: [
                            ObjectId(sessionData?._id)
                          ]
                        },
                        isDelete: false
                      }
                    },
                    {
                      $lookup: {
                        from: "event_participant_attendees",
                        localField: "accessRoles",
                        foreignField: "role",
                        pipeline: [
                          {
                            $match: {
                              isDelete: false
                            }
                          },
                          {
                            $group: {
                              _id: null,
                              users: {
                                $push: "$user"
                              }
                            }
                          }
                        ],
                        as: "attendee"
                      }
                    },
                    {
                      $addFields: {
                        attendeeUsers: {
                          $ifNull: [
                            {
                              $arrayElemAt: ["$attendee.users", 0]
                            },
                            []
                          ]
                        }
                      }
                    },
                    {
                      $unwind: {
                        path: "$attendeeUsers",
                        preserveNullAndEmptyArrays: true
                      }
                    },
                    {
                      $unwind: {
                        path: "$userId",
                        preserveNullAndEmptyArrays: true
                      }
                    },
                    {
                      $group: {
                        _id: null,
                        attendeeData: {
                          $addToSet: "$attendeeUsers"
                        },
                        userData: {
                          $addToSet: {
                            $ifNull: ["$userId", []]
                          }
                        }
                      }
                    },
                    {
                      $project: {
                        uniqueArray: {
                          $setUnion: [
                            "$attendeeData",
                            {
                              $ifNull: ["$userData", []]
                            }
                          ]
                        }
                      }
                    },
                    {
                      $lookup: {
                        from: "user_edges",
                        let: {
                          userArray: "$uniqueArray",
                          relationId: ObjectId(req?.relation_id)
                        },
                        pipeline: [
                          {
                            $match: {
                              $expr: {
                                $and: [
                                  {
                                    $in: ["$user_id", "$$userArray"]
                                  },
                                  {
                                    $eq: [
                                      "$relation_id",
                                      "$$relationId"
                                    ]
                                  }
                                ]
                              },
                              isDelete: false,
                              deviceToken: {
                                $exists: true,
                                $ne: []
                              }
                            }
                          },
                          {
                            $unwind: "$deviceToken"
                          }
                        ],
                        as: "user_edge_data"
                      }
                    },
                    {
                      $addFields: {
                        deviceToken: {
                          $ifNull: [
                            "$user_edge_data.deviceToken",
                            []
                          ]
                        }
                      }
                    },
                    {
                      $project: {
                        deviceToken: 1,
                        _id: 0
                      }
                    }
                  ]
                  const deviceTokenData = await eventActivity .aggregate(pipeline)

                  if(deviceTokenData[0]?.deviceToken?.length) {
                    const notificationData = {
                      senderId: process.env.ADMIN_ID,
                      senderName: "Admin",
                      eventId: fetchEvent?._id,
                      eventName: fetchEvent?.title,
                      chatType: "nofifyUser",
                      sessionId: sessionData?._id,
                      sessionName: sessionData?.title,
                      sessionCount: sessionData?.sessionCount || 0,
                    }

                    const templateData = {
                      eventName: fetchEvent?.title,
                      chatType: "nofifyUser",
                      notifyMsg: sessionData?.notifyChangeText,
                    }

                    let notificationTemplate = await notification_template.notify_user_for_update(templateData);
                    const data = {
                      title: notificationTemplate?.template?.title,
                      body: notificationTemplate?.template?.body,
                      imageUrl: null,
                      icon: null,
                      device_token: deviceTokenData[0]?.deviceToken || [],
                      collapse_key: null,
                      sub_title: null,
                      notification_data: {
                        type: "notify_user_for_update",
                        content: notificationData
                      }
                    }

                    send_notification_v2(data);
                  }

                  // await sendNotificationForNotifyUserV2(sessionData.event, sessionData.notifyChangeText, "session", sessionData._id);
                }

                if (sessionData)
                    return res.status(200).json({ status: true, message: "Session updated successfully!", data: sessionData, });
                else
                    return res.status(200).json({ status: false, message: "Something went wrong while updating session!", });
            } else {
                return res.status(200).json({ status: false, message: "Session not found!" });
            }
        }
    } catch (error) {
        return res.status(500).json({ status: false, message: "Internal server error!", error: error });
    }
};

// clone event API
exports.cloneSession = async (req, res) => {
  try {
    const { id } = req.body;
    if (id !== undefined && id !== null && id !== "") {
      const objData = await Session
        .findOne({
          _id: ObjectId(id),
          isDelete: false,
        }).select("-_id -__v -updatedAt -createdAt");

      if (!objData) {
        return res
          .status(200)
          .json({ status: false, message: "Event session not Found!" });
      }

      let obj = objData.toObject();
      const ids = await Session.find({ event: objData.event, isDelete: false }, { _id: 1, order: 1 }).sort({ order: -1 });
      let orderData = (ids && ids.length > 0) ? ids[0].order + 1 : 1;
      obj.title = "Copy - " + objData.title;
      obj.order = orderData
      obj.date = ""
      obj.startTime = ""
      obj.endTime = ""
      obj.scheduleNotifyTime = ""
      const sessionClone = new Session(obj);
      const newSession = await sessionClone.save();
      if(newSession){
        return res.status(200).send({
          status: true,
          message: "Cloning completed successfully!",
          data: newSession,
        });
      }} else {
        return res.status(200).send({
          status: true,
          message: "Error in while creating cloning event session!",
        });
      }
  } catch (error) {
    return res
      .status(200)
      .json({ status: false, message: "Internal server error!", error: error });
  }
};

/** CURD opreation of session end **/

// sync session for dynamic role from sync old data to dynaic structure
exports.syncSessionForDynamicRole = async (req, res) => {
    try {
      let allRole = await eventParticipantTypes.find({
        isDelete: false,
        isDefault: true,
      });
      let allSession = await Session.aggregate([
        {
          $match: {
            isDelete: false,
          },
        },
        {
          $lookup: {
            from: "events",
            localField: "event",
            foreignField: "_id",
            as: "events_result",
          },
        },
        {
          $unwind: {
            path: "$events_result",
            preserveNullAndEmptyArrays: false,
          },
        },
      ]);
      let totalSync = 0;
      //   for (let i = 0; i < 0; i++) {
      for (let i = 0; i < allSession.length; i++) {
        let data = allSession[i];
        let event = data.event;
        for (let j = 0; j < allRole.length; j++) {
          let typeDetail = allRole[j];
          let role = allRole[j]["role"];
          let existType = await eventWiseParticipantTypes.findOne({
            role: role,
            event: event,
            isDelete: false,
          });
          if (!existType) {
            let addObj = {
              role: typeDetail.role,
              description: typeDetail.description,
              isDefault: typeDetail.isDefault,
              event: event,
              baseRole: typeDetail._id,
            };
            await eventWiseParticipantTypes.create(addObj);
          }
        }
      }
      for (let i = 0; i < allSession.length; i++) {
        let isUpdatable = false;
        let accessRoles = [];
        let data = allSession[i];
        let allRole = await eventWiseParticipantTypes.find({
          event: data["event"],
          isDelete: false,
        });
        if (data["member"] == true) {
          let tempRole = allRole.find((x) => x.role.toLowerCase() === "member");
          if (tempRole && tempRole["_id"]) {
            accessRoles.push(ObjectId(tempRole["_id"]));
            isUpdatable = true;
          }
        }
        if (data["speaker"] == true) {
          let tempRole = allRole.find((x) => x.role.toLowerCase() === "speaker");
          if (tempRole && tempRole["_id"]) {
            accessRoles.push(ObjectId(tempRole["_id"]));
            isUpdatable = true;
          }
        }
        if (data["partner"] == true) {
          let tempRole = allRole.find((x) => x.role.toLowerCase() === "partner");
          if (tempRole && tempRole["_id"]) {
            accessRoles.push(ObjectId(tempRole["_id"]));
            isUpdatable = true;
          }
        }
        if (data["guest"] == true) {
          let tempRole = allRole.find((x) => x.role.toLowerCase() === "guest");
          if (tempRole && tempRole["_id"]) {
            accessRoles.push(ObjectId(tempRole["_id"]));
            isUpdatable = true;
          }
        }
        if (!data["accessRoles"]) {
          const updatedSession = await Session.findOneAndUpdate(
            { _id: data._id },
            { $set: { accessRoles: [] } },
            { new: true }
          );
        }
        if (isUpdatable === true) {
          const updatedSession = await Session.findOneAndUpdate(
            { _id: data._id },
            { $set: { accessRoles: accessRoles } },
            { new: true }
          );
          totalSync = totalSync + 1;
        }
      }
  
      return res.status(200).json({
        status: true,
        message: "Session data sync successfully!",
        data: { totalSync },
      });
    } catch (error) {
      return res
        .status(500)
        .json({ status: false, message: "Internal server error!", error: error });
    }
  };

  // session listing based on activity
exports.getSessionListByActivityV2 = async (req, res) => {
    try {
        const authUser = req.authUserId;
        // const role = new ObjectId( req.query.role );
        var userData;
        const activityId = new ObjectId(req.params.id);
        //this code is commented after disscuse with KD, suggested by client
        // let conditionTemp = { '$ne': ["$role", "Member"] };
        // if ( req.query && req.query.role && req.query.role === "member" ) {
        //     conditionTemp = { '$in': ["$role", [ "Member", "Staff" ]] };
        // }
        let pipeline = [
            {
              '$match': {
              _id:  activityId,
              isDelete: false
                // 'event': new ObjectId('65953a89aa8ca84bbed9735f'), 
                // '_id': new ObjectId('65953a89aa8ca84bbed973db')
              }
            }, {
              '$lookup': {
                'from': 'sessions', 
                'localField': 'session', 
                'foreignField': '_id', 
                'as': 'session', 
                'pipeline': [
                //this code is commented after disscuse with KD, suggested by client
                //   {
                //     '$lookup': {
                //         'from': 'event_wise_participant_types', 
                //         'localField': 'accessRoles', 
                //         'foreignField': '_id', 
                //         'as': 'event_wise_participant_types',
                //         'pipeline': [
                //             {
                //               '$match': {
                //                 '$expr': conditionTemp
                //               }
                //             }
                //         ]
                //     }
                //   },
                //   {
                //     '$unwind': {
                //       'path': '$event_wise_participant_types',
                //       'preserveNullAndEmptyArrays': false
                //     }
                //   },
                  {
                    '$lookup': {
                      'from': 'rooms', 
                      'localField': 'room', 
                      'foreignField': '_id', 
                      'as': 'room',
                      'pipeline': [
                        {
                          '$lookup': {
                            'from': 'eventlocations', 
                            'localField': 'location', 
                            'foreignField': '_id', 
                            'as': 'location'
                          }
                        }, {
                          '$unwind': {
                            'path': '$location', 
                            'preserveNullAndEmptyArrays': false
                          }
                        }
                      ]
                    }
                  }, {
                    '$unwind': {
                      'path': '$room', 
                      'preserveNullAndEmptyArrays': false
                    }
                  }, {
                    '$lookup': {
                      'from': 'airtable-syncs', 
                      'localField': 'speakerId', 
                      'foreignField': '_id', 
                      'as': 'speakerId', 
                      'pipeline': [
                        {
                          '$project': {
                            '_id': 1, 
                            'first_name': 1, 
                            'last_name': 1,
                            'display_name': 1, 
                            'attendeeDetail': 1,
                            'profileImg': {
                                '$cond': [
                                    {
                                        "$ifNull": [
                                            "$profileImg",
                                            false
                                        ]
                                    },
                                    "$profileImg", ""
                                ]
                            },
                          }
                        }
                      ]
                    }
                  }
                ]
              }
            }, {
                '$lookup': {
                  'from': 'events', 
                  'localField': 'event', 
                  'foreignField': '_id', 
                  'as': 'event'
                }
              }, {
                '$unwind': {
                  'path': '$event', 
                  'preserveNullAndEmptyArrays': false
                }
              }, {
                '$lookup': {
                  'from': 'eventlocations', 
                  'localField': 'location', 
                  'foreignField': '_id', 
                  'as': 'location'
                }
              }, {
                '$unwind': {
                  'path': '$location', 
                  'preserveNullAndEmptyArrays': true
                }
              }, {
              '$lookup': {
                'from': 'event_wise_participant_types', 
                'localField': 'accessRoles', 
                'foreignField': '_id', 
                'as': 'accessRoles'
              }
            }
          ]
        
        let activityData = await eventActivity.aggregate(pipeline);

        if (activityData && activityData.length && activityData[0] ) {
            activityData = activityData[0];
            let pipeline = [
            {
              '$match': {
                'user': ObjectId(authUser), 
                'event': activityData.event._id, 
                // 'role': role
              }
            }, {
              '$lookup': {
                'from': 'airtable-syncs', 
                'localField': 'user', 
                'foreignField': '_id', 
                'as': 'airtable-syncs_result', 
                'pipeline': [
                  {
                    '$project': {
                      '_id': 1, 
                      'firebaseId': 1, 
                      'first_name': 1, 
                      'last_name': 1, 
                      'display_name': 1,
                      'email': 1, 
                      'accessible_groups': 1, 
                      'purchased_plan': 1, 
                      'notificationFor': 1, 
                      'attendeeDetail': 1
                    }
                  }
                ]
              }
            }, {
              '$unwind': {
                'path': '$airtable-syncs_result', 
                'preserveNullAndEmptyArrays': false
              }
            }
          ]
        let userData = await eventParticipantAttendees.aggregate(pipeline);
        if (userData && userData.length) {
            userData = userData[0];
            let sessionList = [];
            if(activityData && activityData.session){
                sessionList = activityData.session;
            }
            return res.status(200).json({ status: true, message: "Session list retrived based on activity!", data: sessionList });
        }else {
            return res.status(200).json({ status: true, message: "User not found!", data: [] });
        }
    }
    } catch (error) {
        return res.status(500).json({ status: false, message: "Internal server error!", error: error });
    }
};