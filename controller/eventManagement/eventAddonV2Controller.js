const { ObjectId } = require("mongodb");
const eventAddonV2 = require("../../database/models/eventAddon/eventAddonV2");
const event = require("../../database/models/event");
const debugErrorLogs = require("../../middleware/debugErrorLogs");
const { validationResult } = require("express-validator");
const eventAddonGroup = require("../../database/models/eventAddon/eventAddonGroup");

const S3FileUpload = require("../../libraries/file-upload-service");
const eventTicket = require("../../database/models/eventTicketsManagement/eventTicket");
const s3fileUploadService = new S3FileUpload();
/** Admin Routes */
// create event add-on-v2
exports.createEventAddonV2 = async (req, res) => {
  try {
    const { name, eventId, addonGroupId, price, quantity, salesPeriodStartDate, salesPeriodStartTime, salesPeriodEndDate, salesPeriodEndTime, shortDescription, longDescription, status, isSubAddon, isRequired, ticketIds, subAddon = [], subAddonMultiSelect,
      sameTimeAsTicket } = req.body;
    let image = req.image ? req.image : req?.body?.image;
   
    const { relation_id } = req;
    const errors = validationResult(req);
    if (!errors.isEmpty()) {
      let result = validationResult(req).array({ onlyFirstError: false });
      result = result.map((err) => {
        return { path: err["path"], msg: err["msg"] };
      });
      return res.status(400).json({
        status: false,
        message: "Validation error! Please check the input fields.",
        errors: result[0],
      });
    } else {
      let isExist = await eventAddonV2.aggregate([
        { $addFields: { nameLower: { $toLower: "$name" } } },
        {
          $match: {
            isDelete: false,
            nameLower: name.toLowerCase(),
            eventId: new ObjectId(eventId),
          },
        },
      ]);
      if (isExist && isExist.length != 0) {
        return res.status(400).json({
          status: false,
          message:
            "This add-on name already exists for this event. Please choose a different name.",
        });
      } else {
        let getEvent = await event
          .findOne({ _id: new ObjectId(eventId), isDelete: false })
          .lean();
        if (!getEvent) {
          return res
            .status(400)
            .send({ status: false, message: `Event not found.` });
        }
        let getEventAddonsGroup = await eventAddonGroup
          .findOne({ _id: new ObjectId(addonGroupId), isDelete: false })
          .lean();
        if (!getEventAddonsGroup) {
          return res
            .status(400)
            .send({ status: false, message: `EventAddonGroup not found.` });
        }
        const ids = await eventAddonV2
          .find({
            isDelete: false,
            eventId: new ObjectId(eventId),
            relationId: ObjectId(relation_id),
          })
          .sort({ order: -1 });
        let addonOrder = ids && ids.length > 0 ? ids[0].order + 1 : 1;
        let ticketData

        if (ticketIds && Array.isArray(ticketIds)) {
          const validTicketIds = ticketIds.filter(
            (ticketId) => ticketId !== "" && ObjectId.isValid(ticketId)
          );
          let match = {
            _id: { $in: validTicketIds.map((ticketId) => new ObjectId(ticketId)) },
            relation_id: new ObjectId(req.relation_id),
            isDelete: false,
          };
           ticketData = await eventTicket.aggregate([{ $match: match }]);
        
        
          if (ticketData.length < validTicketIds.length) {
            const notFoundTickets = validTicketIds.filter(
              (ticketId) =>
                !ticketData.some((ticket) => ticket._id.toString() === ticketId)
            );
            return res.status(404).send({
              status: false,
              message: `The following ticket IDs do not exist or are deleted: ${notFoundTickets.join(
                ", "
              )}`,
            });
          }
        } 
        
        const newEventAddonv2 = new eventAddonV2({
          eventId: eventId,
          addonGroupId: addonGroupId,
          name: name,
          price: price,
          isSubAddon: isSubAddon,
          subAddon: subAddon,
          relationId: relation_id,
          order: addonOrder,
          isRequired: isRequired,
          subAddonMultiSelect: subAddonMultiSelect,
          quantity: Number(quantity),
          salesPeriodStartDate: salesPeriodStartDate,
          salesPeriodStartTime: salesPeriodStartTime,
          salesPeriodEndDate: salesPeriodEndDate,
          salesPeriodEndTime: salesPeriodEndTime,
          shortDescription: shortDescription,
          longDescription: longDescription,
          status: status,
          image: image,
          sameTimeAsTicket: sameTimeAsTicket
        });

        const eventAddonV2Data = await newEventAddonv2.save();
    
        if (ticketData && ticketData.length > 0) {
             await eventTicket.updateMany(
            { _id: { $in: ticketData.map((ticket) => ticket._id) } },
            {
              $addToSet: { addons: eventAddonV2Data._id },
            }
          );
        }
        if (!eventAddonV2Data) {
          return res.status(400).send({
            status: false,
            message: `Something went wrong while creating event addon!`,
          });
        } else {
          return res.status(200).send({
            status: true,
            message: `Event add-on created successfully.`,
            data: eventAddonV2Data,
          });
        }
      }
    }
  } catch (error) {
    console.log({ error });
    await debugErrorLogs.createErrorLogs(error, "createEventAddonV2", {});
    return res
      .status(500)
      .json({ status: false, message: "Internal server error!" });
  }
};

//Edit event Add-on-v2 (By admin)
exports.editEventAddonV2 = async (req, res) => {
  try {
    const errors = validationResult(req);
    if (!errors.isEmpty()) {
      let result = validationResult(req).array({ onlyFirstError: false });
      result = result.map((err) => {
        return { path: err["path"], msg: err["msg"] };
      });
      return res
        .status(403)
        .json({
          status: false,
          message: "Validation error! Please check the input fields.",
          errors: result[0],
        });
    } else {
      const { id } = req.params;
      const { name, eventId, addonGroupId, price, quantity, salesPeriodStartDate, salesPeriodStartTime, ticketIds, removeIds, salesPeriodEndDate, salesPeriodEndTime, shortDescription, longDescription, status, isSubAddon, isRequired, subAddon = [], subAddonMultiSelect,
      } = req.body;
      const { relation_id } = req;
      // fetch add-on from that id
      const existAddonV2 = await eventAddonV2
        .findOne({ _id: new ObjectId(id), isDelete: false })
        .lean();
      if (!existAddonV2) {
        return res
          .status(400)
          .send({ status: false, message: `Event add-on not found.` });
      } else {
        let isExist = await eventAddonV2.aggregate([
          { $addFields: { nameLower: { $toLower: "$name" } } },
          {
            $match: {
              _id: { $nin: [ObjectId(id)] },
              isDelete: false,
              nameLower: name.toLowerCase(),
              addonGroupId: ObjectId(addonGroupId),
            },
          },
        ]);
        if (isExist && isExist.length != 0) {
          return res
            .status(400)
            .json({
              status: false,
              message: "This add-on v2 name already exists.",
            });
        } else {
          let getEvent = await event
            .findOne({ _id: new ObjectId(eventId), isDelete: false })
            .lean();
          if (!getEvent) {
            return res
              .status(400)
              .send({ status: false, message: `Event not found.` });
          }
          let getEventAddonsGroup = await eventAddonGroup
            .findOne({ _id: new ObjectId(addonGroupId || existAddonV2.addonGroupId), isDelete: false })
            .lean();
          if (!getEventAddonsGroup) {
            return res
              .status(400)
              .send({ status: false, message: `EventAddonGroup not found.` });
          }
          let ticketData

          if (ticketIds && Array.isArray(ticketIds)) {
            const validTicketIds = ticketIds.filter(
              (ticketId) => ticketId !== "" && ObjectId.isValid(ticketId)
            );
          
            let match = {
              _id: { $in: validTicketIds.map((ticketId) => new ObjectId(ticketId)) },
              relation_id: new ObjectId(req.relation_id),
              isDelete: false,
            };
          
             ticketData = await eventTicket.aggregate([{ $match: match }]);
          
            if (ticketData.length < validTicketIds.length) {
              const notFoundTickets = validTicketIds.filter(
                (ticketId) =>
                  !ticketData.some((ticket) => ticket._id.toString() === ticketId)
              );
              return res.status(404).send({
                status: false,
                message: `The following ticket IDs do not exist or are deleted: ${notFoundTickets.join(
                  ", "
                )}`,
              });
            }
          } 
            
          if (removeIds && removeIds.length > 0) {
            const validIds = removeIds.filter((id) => ObjectId.isValid(id));
            if (validIds.length > 0) {;
              await eventTicket.updateMany(
                { _id: { $in: validIds.map((id) => new ObjectId(id)) } },
                {
                  $pull: { addons: existAddonV2._id },
                }
              );
            } 
          }
          const editEventAddonv2 = await eventAddonV2.findByIdAndUpdate(
            id,
            {
              name: name ?? existAddonV2.name,
              eventId: eventId ?? existAddonV2.eventId,
              addonGroupId: addonGroupId ?? existAddonV2.addonGroupId,
              price: price ?? existAddonV2.price,
              isSubAddon: isSubAddon ?? existAddonV2.isSubAddon,
              subAddon: subAddon ?? existAddonV2.subAddon,
              isRequired: isRequired ?? existAddonV2.isRequired,
              subAddonMultiSelect: subAddonMultiSelect ?? existAddonV2.subAddonMultiSelect,
              quantity: Number(quantity) ?? existAddonV2.quantity,
              salesPeriodStartDate: salesPeriodStartDate ?? existAddonV2.salesPeriodStartDate,
              salesPeriodStartTime: salesPeriodStartTime ?? existAddonV2.salesPeriodStartTime,
              salesPeriodEndDate: salesPeriodEndDate ?? existAddonV2.salesPeriodEndDate,
              salesPeriodEndTime: salesPeriodEndTime ?? existAddonV2.salesPeriodEndTime,
              shortDescription: shortDescription ?? existAddonV2.shortDescription,
              longDescription: longDescription ?? existAddonV2.longDescription,
              status: status ?? existAddonV2.status,
              image: req.image ?? existAddonV2.image,
            },
            { new: true }
          );
          if (ticketData && ticketData.length > 0) {
              await eventTicket.updateMany(
              { _id: { $in: ticketData.map((ticket) => ticket._id) } },
              {
                $addToSet:{ addons: editEventAddonv2._id },
              }
            );
          }
          if (!editEventAddonv2) {
            return res
              .status(400)
              .send({
                status: false,
                message: `Something went wrong while edit event add-on!`,
              });
          } else {
            return res
              .status(200)
              .send({
                status: true,
                message: `Event add-on edited successfully`,
                data: editEventAddonv2,
              });
          }
        }
      }
    }
  } catch (error) {
    await debugErrorLogs.createErrorLogs(error, "editEventAddonV2", {});
    return res
      .status(500)
      .json({ status: false, message: "Internal server error!", error: error });
  }
};

// delete event Add-on-v2 (By admin - soft delete)
exports.deleteEventAddonV2 = async (req, res) => {
  try {
    // Validation check
    const errors = validationResult(req);
    if (!errors.isEmpty()) {
      const result = errors.array({ onlyFirstError: false }).map(err => ({
        path: err.path,
        msg: err.msg
      }));
      return res.status(403).json({
        status: false,
        message: "Validation error!",
        errors: result
      });
    }

    // Extract event addon ID from params
    const { id } = req.params;
    const { relation_id } = req;

    // Check if the event addon exists and is not deleted
    const existAddonV2 = await eventAddonV2.findOne({
      _id: new ObjectId(id),
      isDelete: false
    }).lean();

    if (!existAddonV2) {
      return res.status(400).send({
        status: false,
        message: "Event add-on not found."
      });
    }

    // Find all related event add-ons and update their order
    const ids = await eventAddonV2.find({
      isDelete: false,
      eventId: existAddonV2.eventId,
      relationId: ObjectId(relation_id)
    }).sort({ order: 1 });

    // Update the order of other event add-ons in parallel
    const updateOrderPromises = ids.map(async (item, i) => {
      return eventAddonV2.findByIdAndUpdate(
        ObjectId(item._id),
        { order: i + 1 },
        { new: true }
      );
    });

    // Wait for all order updates to complete
    await Promise.all(updateOrderPromises);

    // Mark the event add-on as deleted
    const deleteEventAddonv2 = await eventAddonV2.findByIdAndUpdate(
      id,
      { isDelete: true },
      { new: true }
    );

    // If deletion failed, return an error message
    if (!deleteEventAddonv2) {
      return res.status(400).send({
        status: false,
        message: "Something went wrong while deleting event add-on!"
      });
    }

    // Successfully deleted the event add-on
    return res.status(200).send({
      status: true,
      message: "Event add-on deleted successfully."
    });
  } catch (error) {
    // Log the error and return an internal server error response
    await debugErrorLogs.createErrorLogs(error, "deleteEventAddonV2", {});
    return res.status(500).json({
      status: false,
      message: "Internal server error!",
      error: error.message
    });
  }
};

// get all event addonv2 by event id for indivisual event
exports.getAllEventAddonV2 = async (req, res) => {
  try {
    const errors = validationResult(req);
    if (!errors.isEmpty()) {
      let result = validationResult(req).array({ onlyFirstError: false });
      result = result.map((err) => {
        return { path: err["path"], msg: err["msg"] };
      });
      return res
        .status(403)
        .json({ status: false, message: "Validation error!", errors: result[0] });
    } else {
      const { eventId } = req.params;
      const page = parseInt(req.query.page) || 1;
      const limit = parseInt(req.query.limit) || 10;
      const skip = (page - 1) * limit;
      const sortField = req.query.sortField ? req.query.sortField : "order";
      const sortType = req.query.sortType === "Desc" ? -1 : 1;
      const search = req.query.search;
      let sortOrder = {};
      sortOrder[sortField] = sortType;
      const { relation_id } = req;
      let condition = { eventId: new ObjectId(eventId), relationId: ObjectId(relation_id), isDelete: false };
      if (search) {
        condition["$or"] = [
          {
            name: {
              $regex: ".*" + search + ".*",
              $options: "i",
            },
          },
        ];
      }

      let pipeline = [
        {
          $match: condition,
        },
        {
          $lookup: {
            from: "events",
            localField: "eventId",
            foreignField: "_id",
            as: "event",
          },
        },
        {
          $unwind: {
            path: "$event",
            preserveNullAndEmptyArrays: false,
          },
        },
        {
          $lookup: {
            from: "event_addon_groups",
            localField: "addonGroupId",
            foreignField: "_id",
            as: "eventAddonGroup",
          },
        },
        {
          $unwind: {
            path: "$eventAddonGroup",
            preserveNullAndEmptyArrays: false,
          },
        },
        {
          $lookup: {
            from: "ticket_addon_purchases",
            localField: "_id",
            foreignField: "addonId",
            as: "addon_purchase_count",
            pipeline: [
              {
                $match: {
                  addonOrderStatus: "succeeded",
                  isDelete: false,
                },
              },
              {
                $group: {
                  _id: null,
                  count: {
                    $sum: 1,
                  },
                },
              },
              {
                $project: {
                  _id: 0,
                  count: 1,
                },
              },
            ],
          },
        },
        {
          $set: {
            addon_purchase_count: {
              $cond: {
                if: { $gt: [{ $size: "$addon_purchase_count" }, 0] },
                then: { $arrayElemAt: ["$addon_purchase_count.count", 0] },
                else: 0,
              },
            },
          },
        },
      ];
      let countAddon = 0;
      const [allAddonV2Data, AddOnV2Count] = await Promise.all([
        eventAddonV2.aggregate([
          ...pipeline,
          ...[{ $sort: sortOrder }, { $skip: skip }, { $limit: limit }],
        ]),
        eventAddonV2.aggregate([...pipeline, ...[{ $count: "total" }]]),
      ]);
      if (AddOnV2Count && AddOnV2Count[0] && AddOnV2Count[0]["total"]) {
        countAddon = AddOnV2Count[0]["total"];
      }
      if (!allAddonV2Data) {
        return res
          .status(200)
          .send({ status: false, message: `Event add-on not found.` });
      } else {
        return res
          .status(200)
          .send({
            status: true,
            message: "Event add-on retrieved successfully.",
            data: allAddonV2Data,
            totalPages: Math.ceil(countAddon / limit),
            currentPage: page,
            totalAddonV2s: countAddon,
          });
      }
    }
  } catch (error) {
    await debugErrorLogs.createErrorLogs(error, "getAllEventAddonV2", {});
    return res
      .status(500)
      .json({ status: false, message: "Internal server error!", error: error });
  }
};

// get all event addonv2 by event id suggestion by event id
exports.getAllEventAddonV2Suggestion = async (req, res) => {
  try {
    const { eventId } = req.params;
    const { relation_id } = req;
    const { search } = req.query;
    let condition = { eventId: new ObjectId(eventId), relationId: ObjectId(relation_id), isDelete: false };
    if (search) {
      condition["$or"] = [
        {
          name: {
            $regex: ".*" + search + ".*",
            $options: "i",
          },
        },
      ];
    }
    const allAddonV2Data = await eventAddonV2
      .find({ ...condition }, { name: 1 }).sort({ name: 1 }).lean();

    if (allAddonV2Data.length)
      return res.status(200).json({
        status: true,
        message: "Event add-on suggestion retrieved successfully!",
        data: allAddonV2Data,
      });
    else
      return res.status(200).json({
        status: false,
        message: "Something went wrong while getting all event locations!",
      });
  } catch (e) {
    return res
      .status(500)
      .json({ status: false, message: "Something went wrong!", error: e });
  }
};
// get event packege reorder API
exports.eventAddonV2Reorder = async (req, res) => {
  try {
    const ids = req.body.ids;
    if (ids.length > 0) {
      let resOrder = ids.map(async (item, i) => {
        await eventAddonV2.findByIdAndUpdate(
          ObjectId(item),
          { order: i + 1 },
          { new: true }
        );
      });
      await Promise.all([...resOrder]);

      return res
        .status(200)
        .json({ status: true, message: "Event add-on list rearrange succesfully!" });
    } else {
      return res.status(200).json({
        status: false,
        message: "Something went wrong while rearrange package!",
      });
    }
  } catch (error) {
    return res
      .status(200)
      .json({ status: false, message: "Something went wrong!", error: error });
  }
};

// get all event add-on-v2 by addon for indivisual addon
exports.getEventAddonV2ById = async (req, res) => {
  try {
    const errors = validationResult(req);
    if (!errors.isEmpty()) {
      let result = validationResult(req).array({ onlyFirstError: false });
      result = result.map((err) => {
        return { path: err["path"], msg: err["msg"] };
      });
      return res
        .status(403)
        .json({ status: false, message: "Validation error!", errors: result[0] });
    } else {
      const { id } = req.params;
      let pipeline = [
        {
          $match: { _id: new ObjectId(id), isDelete: false },
        },
        {
          $lookup: {
            from: "events",
            localField: "eventId",
            foreignField: "_id",
            as: "event",
          },
        },
        {
          $unwind: {
            path: "$event",
            preserveNullAndEmptyArrays: false,
          },
        },
        {
          $lookup: {
            from: "event_addon_groups",
            localField: "addonGroupId",
            foreignField: "_id",
            as: "eventAddonGroup",
          },
        },
        {
          $unwind: {
            path: "$eventAddonGroup",
            preserveNullAndEmptyArrays: false,
          },
        },
        {
          $lookup: {
            from: "event_tickets",
            let: { addonId: "$_id" },
            pipeline: [
              {
                $match: {
                  $expr: {
                    $and: [
                      { $in: ["$$addonId", { $ifNull: ["$addons", []] }] },
                    ],
                  },
                },
              },
              {
                $project: {
                  _id: 1,   
                  name: 1, 
                },
              },
            ],
            as: "tickets",
          },
        },
        {
          $lookup: {
            from: "ticket_addon_purchases",
            localField: "_id",
            foreignField: "addonId",
            as: "addon_purchase_count",
            pipeline: [
              {
                $match: {
                  addonOrderStatus: "succeeded",
                  isDelete: false,
                },
              },
              {
                $group: {
                  _id: null,
                  count: {
                    $sum: 1,
                  },
                },
              },
              {
                $project: {
                  _id: 0,
                  count: 1,
                },
              },
            ],
          },
        },
        {
          $set: {
            addon_purchase_count: {
              $cond: {
                if: { $gt: [{ $size: "$addon_purchase_count" }, 0] },
                then: { $arrayElemAt: ["$addon_purchase_count.count", 0] },
                else: 0,
              },
            },
          },
        },
      ];
      let allAddonV2Data = await eventAddonV2.aggregate(pipeline);
      let addonV2Data;
      if (allAddonV2Data && allAddonV2Data.length > 0) {
        addonV2Data = allAddonV2Data[0];
      }
      if (addonV2Data.image) {
        const presignedUrl = await s3fileUploadService.generatePresignedUrl({ key: addonV2Data.image });
        addonV2Data.image = await presignedUrl;
      }
  
      if (!addonV2Data) {
        return res
          .status(200)
          .send({ status: false, message: `add-on v2 is not found!` });
      } else {
        return res
          .status(200)
          .send({
            status: true,
            message: "Event add-on retrieved successfully.",
            data: addonV2Data,
          });
      }
    }
  } catch (error) {
    await debugErrorLogs.createErrorLogs(error, "getEventAddonV2ById", {});
    return res
      .status(500)
      .json({ status: false, message: "Internal server error!", error: error });
  }
};

exports.getEventAddonGroupV2ById = async (req, res) => {
  try {
    const errors = validationResult(req);
    if (!errors.isEmpty()) {
      let result = validationResult(req).array({ onlyFirstError: false });
      result = result.map((err) => {
        return { path: err["path"], msg: err["msg"] };
      });
      return res
        .status(403)
        .json({ status: false, message: "Validation error!", errors: result[0] });
    }else{

    const { id } = req.params;
    const { status, dateFilter, reqFromDate, reqToDate, search } = req.query;

    const addonGroupFilters = [
      { $eq: ["$addonGroupId", "$$groupId"] },
      { $eq: ["$isDelete", false] },
    ];


    if (status) {
      addonGroupFilters.push({ $eq: ["$status", status] });
    }

    // Date filter for `createdAt`
    if (dateFilter) {
      let fromDate = new Date();
      let toDate = new Date();
      let addFilterCount = 0;
    
      switch (dateFilter) {
        case "todays":
          // Set fromDate to the start of the day
          fromDate.setHours(0, 0, 0, 0);
          // Set toDate to the end of the day
          toDate.setHours(23, 59, 59, 999);
          break;
    
        case "past7days":
          addFilterCount = 6;
          break;
    
        case "past30days":
          addFilterCount = 29;
          break;
    
        case "past90days":
          addFilterCount = 89;
          break;
    
        case "past365days":
          addFilterCount = 364;
          break;
    
        case "custom":
          if (reqFromDate && reqToDate) {
            fromDate = new Date(reqFromDate);
            toDate = new Date(reqToDate);
          } else {
            return res.status(400).json({
              status: false,
              message: "Custom date filter requires 'reqFromDate' and 'reqToDate'!",
            });
          }
          break;
    
        default:
          return res.status(400).json({
            status: false,
            message: "Invalid date filter type provided!",
          });
      }
    
      if (dateFilter !== "custom" && dateFilter !== "todays") {
        fromDate.setDate(fromDate.getDate() - addFilterCount);
        fromDate.setHours(0, 0, 0, 0);
        toDate.setHours(23, 59, 59, 999);
      }
    
      // Add date filter conditions for `createdAt`
      addonGroupFilters.push({
        $and: [
          { $gte: ["$createdAt", fromDate] },
          { $lte: ["$createdAt", toDate] },
        ],
      });
    }

    // Search filter
    if (search) {
      const searchRegex = new RegExp(search, "i"); 
      addonGroupFilters.push({
        $or: [
          { $regexMatch: { input: "$name", regex: searchRegex } },
          { $regexMatch: { input: "$description", regex: searchRegex } },
        ],
      });
    }

    const pipeline = [
      {
        $match: { _id: ObjectId(id) },
      },
      {
        $lookup: {
          from: "event_addon_v2",
          let: { groupId: "$_id" },
          pipeline: [
            {
              $match: {
                $expr: {
                  $and: addonGroupFilters,
                },
              },
            },
          ],
          as: "addonGroup",
        },
      },
    ];

      const result = await eventAddonGroup.aggregate(pipeline);

      if (!result || result.length === 0) {
        return res
          .status(404)
          .send({ status: false, message: "No add-ons found for the specified group!" });
      } else {
        return res
          .status(200)
          .send({
            status: true,
            message: "Event addon groups retrieved successfully.",
            data: result, // Returning the result
          });
      }
    }
  } catch (error) {
    await debugErrorLogs.createErrorLogs(error, "getEventAddonGroupV2ById", {});
    return res
      .status(500)
      .json({ status: false, message: "Internal server error!", error: error });
  }
};
exports.editStatusEventAddonV2 = async (req, res) => {
  try {
    const errors = validationResult(req);
    if (!errors.isEmpty()) {
      let result = validationResult(req).array({ onlyFirstError: false });
      result = result.map((err) => {
        return { path: err["path"], msg: err["msg"] };
      });
      return res.status(403).json({
        status: false,
        message: "Validation error! Please check the input fields.",
        errors: result[0],
      });
    } else {
      const { id } = req.params;
      const { status } = req.body;
      if (!status || !["draft", "live", "publish", "pause"].includes(status)) {
        return res
          .status(400)
          .send({
            status: false,
            message:
              "Status is required, and it must be either 'draft' or 'live'.",
            data: {},
          });
      }
      // fetch add-on from that id
      const existAddonV2 = await eventAddonV2
        .findOne({ _id: new ObjectId(id), isDelete: false })
        .lean();
      if (!existAddonV2) {
        return res
          .status(400)
          .send({
            status: false,
            message: `Event add-on not found.`,
            data: {},
          });
      } else {
        const editEventAddonv2 = await eventAddonV2.findByIdAndUpdate(
          id,
          {
            status: status ?? existAddonV2.status,
          },
          { new: true }
        );
        if (!editEventAddonv2) {
          return res.status(400).send({
            status: false,
            message: `Something went wrong while edit event add-on!`,
            data: {},
          });
        } else {
          return res.status(200).send({
            status: true,
            message: `Event add-on edited successfully`,
            data: editEventAddonv2,
          });
        }
      }
    }
  } catch (error) {
    await debugErrorLogs.createErrorLogs(error, "editEventAddonV2", {});
    return res
      .status(500)
      .json({ status: false, message: "Internal server error!", error: error });
  }
};
exports.cloneEventAddon = async (req, res) => {
  try {
    const { addonId } = req.body;

    if (!addonId) {
      return res.status(400).json({ status: false, message: "Addon id is required!" });
    }
    const objData = await eventAddonV2
      .findOne({ _id: ObjectId(addonId), isDelete: false })
      .select("-_id -__v -updatedAt -createdAt");

    if (!objData) {
      return res.status(200).json({ status: false, message: "Addon data not found!" });
    }

    const maxOrder = await eventAddonV2
      .find({ eventId: objData.eventId, addonGroupId: objData.addonGroupId, isDelete: false })
      .sort({ order: -1 })
      .limit(1);

    const newOrder = maxOrder.length > 0 ? maxOrder[0].order + 1 : 1;

    // Create a new object for the cloned data
    const obj = { ...objData.toObject(), name: "Copy - " + objData.name, order: newOrder };

    // Only retain necessary fields for the new addon
    const cloneFields = [
      "eventId", "addonGroupId", "image", "isDelete", "relationId", 
      "price", "quantity", "salesPeriodStartDate", "salesPeriodEndDate", 
      "salesPeriodStartTime", "salesPeriodEndTime", "longDescription", 
      "order", "isRequired", "subAddonMultiSelect", "isSubAddon", "subAddon", 
      "sameTimeAsTicket"
    ];

    // Create the cloned object with the updated order field
    const clonedAddonData = cloneFields.reduce((acc, field) => {
      acc[field] = objData[field];
      return acc;
    }, obj);

    // Create a new event addon instance and save it
    const addonClone = new eventAddonV2(clonedAddonData);
    const newAddon = await addonClone.save();

    if (newAddon) {
      await eventTicket.updateMany(
        { addons: { $in: ObjectId(addonId) } },
        {
          $push: { addons: newAddon._id },
        }
      );
      return res.status(200).json({
        status: true,
        message: "Cloning completed successfully!",
        data: newAddon,
      });
    } else {
      return res.status(500).json({
        status: false,
        message: "Error while creating cloned addon!",
      });
    }
  } catch (error) {
    return res.status(500).json({
      status: false,
      message: "Internal server error!",
      error: error.message || error,
    });
  }
};















