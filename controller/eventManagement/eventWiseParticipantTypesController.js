const moment = require("moment");
const ObjectId = require("mongoose").Types.ObjectId;
const { validationResult } = require("express-validator");
const EventParticipantTypes = require("../../database/models/eventWiseParticipantTypes");
const eventWiseParticipantTypes = require("../../database/models/eventWiseParticipantTypes");
const eventParticipantAttendees = require("../../database/models/eventParticipantAttendees");

// create event participant types from admin side
exports.create = async (req, res) => {
  try {
    const errors = validationResult(req);
    if (!errors.isEmpty()) {
      let result = validationResult(req).array({ onlyFirstError: false });
      result = result.map((err) => {
        return { path: err["path"], msg: err["msg"] };
      });
      return res
        .status(403)
        .json({ status: false, message: "Validation error!", errors: result });
    } else {
      let inputData = req.body;
      let role = inputData.role;
      let event = inputData.event;
      let isDefault = false;
      let isExist = await EventParticipantTypes.aggregate([
        { $addFields: { roleLower: { $toLower: "$role" } } },
        { $match: { event: ObjectId(event), isDelete: false, roleLower: role.toLowerCase() } },
      ]);
      if (isExist && isExist.length == 0) {
        const eventParticipantTypesData = new EventParticipantTypes({
          role,
          event,
          isDefault,
        });
        const saveEventParticipantTypesData =
          await eventParticipantTypesData.save();
        if (saveEventParticipantTypesData) {
          return res
            .status(200)
            .json({
              status: true,
              message: `Event participant types created successfully!`,
              data: saveEventParticipantTypesData,
            });
        } else {
          return res
            .status(200)
            .json({
              status: false,
              message: `Something went wrong while adding event participant types!`,
            });
        }
      } else {
        return res
          .status(200)
          .json({
            status: false,
            message: `Event participant types already exist!`,
          });
      }
    }
  } catch (error) {
    return res.status(400).json({ status: false, message: `${error.message}` });
  }
};

// get all event participant types list with pagnation for admin
exports.list = async (req, res) => {
  try {
    const errors = validationResult(req);
    if (!errors.isEmpty()) {
      let result = validationResult(req).array({ onlyFirstError: false });
      result = result.map((err) => {
        return { path: err["path"], msg: err["msg"] };
      });
      return res
        .status(403)
        .json({ status: false, message: "Validation error!", errors: result });
    } else {
      let inputData = req.query;
      let search = inputData.search;
      let id = ObjectId(req.params.id);
      let page = inputData.page ? +inputData.page : 1;
      let limit = inputData.limit ? +inputData.limit : 10;
      let skip = (page - 1) * limit;
      const sortField = req.query.sortField === "role" ? "role" : "createdAt";
      const sortType = req.query.sortType === "Asc" ? 1 : -1;
      let sortTemp = {};
      sortTemp[ sortField ] = sortType;

      let match = { isDelete: false, event: id };
      if (search) {
        match["$or"] = [
          {
            role: {
              $regex: ".*" + search + ".*",
              $options: "si",
            },
          },
        ];
      }
      let pipeline = [{ $match: match }, 
        {
          '$lookup': {
            'from': 'event_participant_attendees', 
            'localField': '_id', 
            'foreignField': 'role', 
            'as': 'event_participant_attendees_result', 
            'let': {
              'event_id': '$event'
            }, 
            'pipeline': [
              {
                '$match': {
                  '$expr': {
                    '$eq': [
                      '$$event_id', '$event'
                    ]
                  }
                }
              }, {
                '$match': {
                  '$expr': {
                    '$eq': [
                      '$isDelete', false
                    ]
                  }
                }
              }
            ]
          }
        }, {
            '$addFields': {
              'total_participant_attendees': {
                '$size': '$event_participant_attendees_result'
              }
            }
          }, {
            '$unset': 'event_participant_attendees_result'
          }, 
          { '$sort': sortTemp } ];

      let [all, count] = await Promise.all([
        eventWiseParticipantTypes.aggregate([
          ...pipeline,
          { $skip: skip },
          { $limit: limit },
        ]),
        eventWiseParticipantTypes.aggregate([...pipeline, { $count: "count" }]),
      ]);

      if (count && count[0] && count[0]["count"]) {
        count = count[0]["count"];
      } else {
        count = 0;
      }
      return res
        .status(200)
        .json({
          status: true,
          message: "Event participant types retrive successfully!",
          data: { list: all, total: count },
        });
    }
  } catch (error) {
    return res.status(400).json({ status: false, message: `${error.message}` });
  }
};

// get all event participant types list for suggetion for admin
exports.all = async (req, res) => {
  try {
    const errors = validationResult(req);
    if (!errors.isEmpty()) {
      let result = validationResult(req).array({ onlyFirstError: false });
      result = result.map((err) => {
        return { path: err["path"], msg: err["msg"] };
      });
      return res
        .status(403)
        .json({ status: false, message: "Validation error!", errors: result });
    } else {
      let inputData = req.query;
      let search = inputData.search;
      let id = ObjectId(req.params.id);
      let match = { isDelete: false, event: id };
      if (search) {
        match["$or"] = [
          {
            role: {
              $regex: ".*" + search + ".*",
              $options: "si",
            },
          },
        ];
      }
      let pipeline = [
        { $match: match },
        {
          '$lookup': {
            'from': 'event_participant_attendees', 
            'let': { 'sourceId': '$_id' },
            'pipeline': [
              {
                '$match': {
                  '$expr': {
                    '$eq': ['$role', '$$sourceId']
                  },
                  'event' : id,
                  'isDelete': false,
                }
              },
              // Add additional stages to the pipeline if needed
            ],
            'as': 'event_participant_attendees_result'
          }
        }, {
          '$addFields': {
            'total_attendees': {
              '$size': '$event_participant_attendees_result'
            }
          }
        }, {
          '$unset': 'event_participant_attendees_result'
        }
      ];
      let all = await EventParticipantTypes.aggregate(pipeline);
      return res
        .status(200)
        .json({
          status: true,
          message: "Event participant types retrive successfully!",
          list: all,
        });
    }
  } catch (error) {
    return res.status(400).json({ status: false, message: `${error.message}` });
  }
};

// get all event participant types suggestion
exports.participantTypeSuggestion = async (req, res) => {
  try {
    const errors = validationResult(req);
    if (!errors.isEmpty()) {
      let result = validationResult(req).array({ onlyFirstError: false });
      result = result.map((err) => {
        return { path: err["path"], msg: err["msg"] };
      });
      return res
        .status(403)
        .json({ status: false, message: "Validation error!", errors: result });
    } else {
      let id = ObjectId(req.params.id);
      let match = { isDelete: false, event: id,} ;
      let pipeline = [
        { $match: match },
        {
          $addFields: {
            roleLower: { $toLower: "$role" }
          }
        },
        {
          $sort: {
            roleLower: 1
          }
        },
        {$project:{role:1}},
      ];
      let all = await EventParticipantTypes.aggregate(pipeline);
      return res.status(200).json({status: true,message: "Event participant types suggestion retrive successfully!",list: all,});
    }
  } catch (error) {
    return res.status(400).json({ status: false, message: `${error.message}` });
  }
};

// get event participant types by id for admin
exports.getById = async (req, res) => {
  try {
    const errors = validationResult(req);
    if (!errors.isEmpty()) {
      let result = validationResult(req).array({ onlyFirstError: false });
      result = result.map((err) => {
        return { path: err["path"], msg: err["msg"] };
      });
      return res
        .status(403)
        .json({ status: false, message: "Validation error!", errors: result });
    } else {
      let id = new ObjectId(req.params.id);
      let match = { isDelete: false, _id: id };
      let data = await EventParticipantTypes.findOne(match);
      if (data) {
        return res
          .status(200)
          .json({
            status: true,
            message: "Event participant types retrive successfully!",
            data: data,
          });
      } else {
        return res
          .status(200)
          .json({
            status: false,
            message: "Event participant types not found!",
            data: {},
          });
      }
    }
  } catch (error) {
    return res.status(400).json({ status: false, message: `${error.message}` });
  }
};

// delete event participant types by id for admin
exports.deleteById = async (req, res) => {
  try {
    const errors = validationResult(req);
    if (!errors.isEmpty()) {
      let result = validationResult(req).array({ onlyFirstError: false });
      result = result.map((err) => {
        return { path: err["path"], msg: err["msg"] };
      });
      return res
        .status(403)
        .json({ status: false, message: "Validation error!", errors: result });
    } else {
      let id = new ObjectId(req.params.id);
      let match = { isDelete: false, _id: id };
      let data = await eventWiseParticipantTypes.findOne(match);
      if (data) {
        if (data.isDefault != true) {
          let attendeesList = await eventParticipantAttendees.find({
            event: new ObjectId(data["event"]),
            role: new ObjectId(data["_id"]),
            isDelete: false,
          });
          if (attendeesList && attendeesList.length != 0) {
            return res.status(200).json({
              status: false,
              message: `There are in total ${
                attendeesList.length
              } ${data.role.toLowerCase()} assignee to this event, therefore you can not delete this role!`,
            });
          } else {
            let updated = await eventWiseParticipantTypes.findByIdAndUpdate(
              id,
              { isDelete: true },
              { new: true }
            );
            if (updated) {
              return res.status(200).json({
                status: true,
                message: "Event participant types deleted successfully!",
              });
            } else {
              return res.status(200).json({
                status: false,
                message:
                  "Something went wrong while deleting event participant types!",
              });
            }
          }
        } else {
          return res.status(200).json({
            status: false,
            message: "Default event participant types cannot be debatable!",
            data: {},
          });
        }
      } else {
        return res.status(200).json({
          status: false,
          message: "Event participant types not found!",
          data: {},
        });
      }
    }
  } catch (error) {
    return res.status(400).json({ status: false, message: `${error.message}` });
  }
};

// update event participant types by id for admin
exports.update = async (req, res) => {
  try {
    const errors = validationResult(req);
    if (!errors.isEmpty()) {
      let result = validationResult(req).array({ onlyFirstError: false });
      result = result.map((err) => {
        return { path: err["path"], msg: err["msg"] };
      });
      return res
        .status(403)
        .json({ status: false, message: "Validation error!", errors: result });
    } else {
      let id = new ObjectId(req.body.id);
      let event = new ObjectId(req.body.event);
      let match = { isDelete: false, _id: id, event: event };
      let data = await EventParticipantTypes.findOne(match);
      if (data) {
        if (data.isDefault != true) {
          let obj = {};
          if (req.body.role) {
            let isExist = await EventParticipantTypes.aggregate([
              { $addFields: { roleLower: { $toLower: "$role" } } },
              {
                $match: {
                  _id: { $nin: [id] },
                  event: event,
                  isDelete: false,
                  roleLower: req.body.role.toLowerCase(),
                },
              },
            ]);
            if (isExist && isExist.length != 0) {
              return res.status(200).json({
                status: false,
                message: "Event participant types name is already exist!",
                data: {},
              });
            } else {
              obj.role = req.body.role;
            }
          }
          // obj.isDefault = req.body.isDefault
          //   ? req.body.isDefault
          //   : data.isDefault;
          obj.event = req.body.event ? req.body.event : data.event;
          let updated = await EventParticipantTypes.findOneAndUpdate(
            { _id: id },
            { $set: obj },
            { new: true }
          );
          if (updated) {
            return res.status(200).json({
              status: true,
              message: "Event participant types updated successfully!",
              data: updated,
            });
          } else {
            return res.status(200).json({
              status: false,
              message:
                "Something went wrong while updated event participant types!",
            });
          }
        } else {
          return res.status(200).json({
            status: false,
            message: "Default event participant types cannot be editable!",
            data: {},
          });
        }
      } else {
        return res.status(200).json({
          status: false,
          message: "Event participant types not found!",
          data: {},
        });
      }
    }
  } catch (error) {
    return res.status(400).json({ status: false, message: `${error.message}` });
  }
};