/*
This file created by BPA.
Code is already developed by respective developers ( BPA only done function separation in separate files ).
*/

const { ObjectId } = require("mongodb");
const eventPackage = require("../../database/models/eventPackage");
const eventLocation = require("../../database/models/eventLocation");
const eventActivity = require("../../database/models/eventActivity");
const event = require("../../database/models/event");
const eventTicket = require("../../database/models/eventTicketsManagement/eventTicket");
const eventTicketSubmission = require("../../database/models/eventTicketsManagement/ticketSubmission");
const applicationForm = require("../../microservices/user/components/application-forms/database/models/application-forms");
const eventSession = require("../../database/models/eventSession");
const eventRoom = require("../../database/models/eventRoom");
const group = require("../../database/models/group");
const membershipPlan = require("../../database/models/membershipPlanManagement/membership_plan");
const EventParticipantTypes = require("../../database/models/eventParticipantTypes");
const eventWiseParticipantTypes = require("../../database/models/eventWiseParticipantTypes");
const User = require("../../database/models/airTableSync");
const eventParticipantAttendees = require("../../database/models/eventParticipantAttendees");
const contentArchive_tag = require("../../database/models/contentArchive_tag");
const { getAllTiersfromBilling } = require("../userAccessRules/tiers");

const { v4: uuidv4 } = require("uuid");
const AWS = require("aws-sdk");
const { deleteImage } = require("../../utils/mediaUpload");
const moment = require("moment");
require("moment-timezone");
const eventFaqs = require("../../database/models/eventFaqs");
const {
  userAccessRulesCommonCondition,
} = require("../../controller/userAccessRules/restrictionAccess");
const {
  send_notification,
  notification_template,
  addTime,
  subtractTime,
} = require("../../utils/notification");

var axios = require("axios").default;
const s3 = new AWS.S3({
  accessKeyId: process.env.AWS_ID,
  secretAccessKey: process.env.AWS_SECRET,
  Bucket: process.env.AWS_BUCKET,
});

const { eventDateAndTimeCommonCondition, dateValidation } = require('../userAccessRules/restrictionAccess');

const { checkValidIds } = require("../../controller/userAccessRules/restrictionAccess");

/** Admin APIs starts **/

/** CURD opreation of event start **/
exports.createEventV4 = async (req, res) => {
  try {
    if (!req.body.title) {
      return res
        .status(200)
        .json({ status: false, message: `Title is required!` });
    }
    
    const escapedTitle = escapeRegExp(req.body.title);
    const existEvent = await event.find({
      title: new RegExp("^" + escapedTitle + "$", "i"),
      isDelete: false,
      relation_id: ObjectId(req.relation_id),
    });
    if (existEvent && existEvent.length > 0) {
      return res.status(200).send({ status: false, message: `Event name must be unique.` });
    }


    if (
      req.body.airTableEventName &&
      req.body.airTableEventName.trim().length > 0
    ) {
      const eventAirTableExist = await event.aggregate([
        {
          $match: {
            $expr: {
              $eq: [
                {
                  $trim: { input: "$airTableEventName" },
                },
                req.body.airTableEventName.trim(),
              ],
            },
            isDelete: false,
            relation_id: ObjectId(req.relation_id),
          },
        },
        {
          $project: {
            _id: 1,
          },
        },
      ]);

      if (eventAirTableExist && eventAirTableExist.length > 0) {
        return res.status(200).json({
          status: false,
          message: `Event air table name must be unique.`,
        });
      }
    }

    // check valid or not
    let checkIds =  await checkValidIds({
      restrictedAccessGroupId:req.body.restrictedAccessGroupId,
      restrictedAccessMembershipPlanId:req.body.restrictedAccessMembershipPlanId,
      restrictedAccessUserId:req.body.restrictedAccessUserId,
      restrictedAccessTagId:req.body.restrictedAccessTagId,
      restrictedAccessTierId: req.body.restrictedAccessTierId,
      relation_id: req.relation_id
    });

    if(checkIds.status === false)
    { return res.status(200).json({ status: false, message: checkIds.msg }); }

    
    let description = `<div "font-family: 'Muller';">${req.body.longDescription}</div>`;
    let shortDescription = req.body.shortDescription;
    let preDescription = `${req.body.preRegisterDescription}`;

    const year = moment(req.body.startDate, "MM-DD-YYYY").year();

    req.body.location =
      req.body.location !== undefined &&
      req.body?.location !== null &&
      req.body.location !== ""
        ? JSON.parse(req.body.location)
        : null;
    if (req?.body?.ticketType === "paid") {
      req.body.maxTicketPurchase ??= req.body.maxTicketPurchase || 1;
    }
    let obj = {
      title: req.body.title,
      thumbnail: req.thumbnail,
      shortDescription: shortDescription,
      longDescription: description,
      eventUrl: req.body.eventUrl,
      chatChannel: req.body.chatChannel,
      ticketType: req.body.ticketType,      
      maxTicketPurchase: req.body.maxTicketPurchase,      
      restrictionAccess: req.body.restrictionAccess,
      restrictedAccessGroupId: req.body.restrictedAccessGroupId,
      restrictedAccessMembershipPlanId: req.body.restrictedAccessMembershipPlanId,
      restrictedAccessUserId: req.body.restrictedAccessUserId,
      restrictedAccessTagId: req.body.restrictedAccessTagId,
      restrictedAccessTierId: req.body.restrictedAccessTierId,
      relation_id: ObjectId(req.relation_id),
      isPreRegister: req.body.isPreRegister ?? false,
      preRegisterTitle: req.body.preRegisterTitle ?? "",
      preRegisterDescription: preDescription ?? "",
      preRegisterBtnTitle: req.body.preRegisterBtnTitle ?? "",
      preRegisterBtnLink: req.body.preRegisterBtnLink ?? "",
      preRegisterStartDate: req.body.preRegisterStartDate
        ? req.body.preRegisterStartDate.substring(
            0,
            req.body.preRegisterStartDate.indexOf("+")
          ) + ".000Z" ?? null
        : null,
      preRegisterEndDate: req.body.preRegisterEndDate
        ? req.body.preRegisterEndDate.substring(
            0,
            req.body.preRegisterEndDate.indexOf("+")
          ) + ".000Z" ?? null
        : null,
      isLocation: req.body.isLocation,
      location:
        req.body.isLocation && req.body.locationType === "inPerson"
          ? req.body.location
          : null,
      // timeZone: !req.body.isTimeZoneAutomatic ? req.body.timeZone : "",
      timeZone: req.body.timeZone,
      year: year,
      airTableEventName:
        req.body.airTableEventName !== undefined &&
        req.body.airTableEventName !== null
          ? req.body.airTableEventName
          : "",

      status: req.body.status ? req.body.status : "draft",
      tag:
        req.body.tag !== undefined && req.body.tag !== null ? req.body.tag : [],
      category:
        req.body.category !== undefined && req.body.category !== null
          ? req.body.category
          : [],
      subcategory:
        req.body.subcategory !== undefined && req.body.subcategory !== null
          ? req.body.subcategory
          : [],
      locationType:
        req.body.locationType !== undefined && req.body.locationType !== null
          ? req.body.locationType
          : null,
      onlineLocationDetail:
        req.body.locationType !== undefined &&
        req.body.locationType !== null &&
        req.body.locationType === "virtual"
          ? {
              onlineEventUrl: req.body.onlineEventUrl
                ? req.body.onlineEventUrl
                : "",
            }
          : null,
      toBeAnnouceDetail:
        req.body.locationType !== undefined &&
        req.body.locationType !== null &&
        req.body.locationType === "to_be_announce"
          ? req.body.toBeAnnouceDetail
          : "",
      isTimeZoneAutomatic: req.body.isTimeZoneAutomatic
        ? req.body.isTimeZoneAutomatic
        : false,
      isPublic: req.body.isPublic ? req.body.isPublic : false,
      isCheckInAllow: req.body.isCheckInAllow == "true" ? true : false,
      ticketPlatform: req.body.ticketPlatform ? req.body.ticketPlatform : "external",
    };

    const isPublic = JSON.parse(obj.isPublic);
    if (isPublic === true) {      
      obj.restrictionAccess = "public";
      obj.restrictedAccessGroupId = [];
      obj.restrictedAccessMembershipPlanId = [];
      obj.restrictedAccessUserId = [];
      obj.restrictedAccessTagId = [];
      obj.restrictedAccessTierId = [];
    }

    if (
      req.body.isTimeZoneAutomatic &&
      req.body.locationType === "inPerson" &&
      req.body.location
    ) {
      const locationDetail = req.body.location;
      const timezoneUrl = `https://maps.googleapis.com/maps/api/timezone/json?location=${
        locationDetail.latitude
      },${locationDetail.longitude}&timestamp=${Math.floor(
        Date.now() / 1000
      )}&key=${process.env.GOOGLE_PLACE_KEY}`;

      const timezoneResponse = await axios.get(timezoneUrl);
      if (timezoneResponse) {
        const timezoneData = await timezoneResponse.data;
        if (timezoneData.status === "OK") {
          const utcOffsetMinutes = moment
            .tz(timezoneData.timeZoneId)
            .utcOffset();
          // Convert minutes to hours and minutes
          const hours = Math.floor(Math.abs(utcOffsetMinutes) / 60);
          const minutes = Math.abs(utcOffsetMinutes) % 60;
          // Create the UTC offset string
          const utcOffsetString = `${utcOffsetMinutes < 0 ? "-" : "+"}${hours
            .toString()
            .padStart(2, "0")}:${minutes.toString().padStart(2, "0")}`;
          const utcString = `(UTC${utcOffsetString}) ${timezoneData.timeZoneId} ${timezoneData.timeZoneName}`;
          // obj.timeZone = utcString;
        }
      }
    }

    if(req.body.status === "published"){
      if (!req.body.ticketType) {
        return res.status(400).json({ error: "TicketType is required when status is published" });
      }
    }

    let eventArr = [];
    const isRecurring =
      req.body.isRecurring != undefined &&
      (req.body.isRecurring === true || req.body.isRecurring === "true")
        ? true
        : false;
    if (isRecurring) {
      const recurrenceDetails = JSON.parse(req.body.recurrenceDetails);
      let startDate = moment(recurrenceDetails.eventStartDate);
      let endDate = moment();

      if (!recurrenceDetails.isNoOfRecurrence) {
        switch (recurrenceDetails.pattern) {
          case "DAILY":
            endDate = moment(
              recurrenceDetails.endDateOfRecurrence,
              "YYYY-MM-DD"
            ).add(1, "days");
            break;
          case "WEEKLY":
            endDate = moment(
              recurrenceDetails.endDateOfRecurrence,
              "YYYY-MM-DD"
            );
            break;
          case "MONTHLY":
            endDate = moment(
              recurrenceDetails.endDateOfRecurrence,
              "YYYY-MM-DD"
            );
            break;
        }
      } else {
        switch (recurrenceDetails.pattern) {
          case "DAILY":
            let totalAddDays = +recurrenceDetails.noOfRecurrence * 1 + 1;
            endDate = moment(startDate, "YYYY-MM-DD").add(totalAddDays, "days");
            break;
          case "WEEKLY":
            let totalAddWeekDays = +recurrenceDetails.noOfRecurrence * 7;
            endDate = moment(startDate, "YYYY-MM-DD").add(
              totalAddWeekDays,
              "days"
            );
            break;
          case "MONTHLY":
            let totalAddMonthDays = +recurrenceDetails.noOfRecurrence * 30;
            endDate = moment(startDate, "YYYY-MM-DD").add(
              totalAddMonthDays,
              "days"
            );
            break;
        }
      }

      let titleIndex = 0;
      for (
        let day = new Date(startDate);
        day < new Date(endDate);
        day.setDate(day.getDate() + 1)
      ) {
        let newObj = obj;
        let newWeekDate = null;
        switch (recurrenceDetails.pattern) {
          case "DAILY":
            newWeekDate = moment(day);
            break;
          case "WEEKLY":
            if (
              moment(day).format("ddd").toLowerCase() ===
              recurrenceDetails.weekDayOccurOption.toLowerCase()
            ) {
              newWeekDate = moment(day);
            }
            break;
          case "MONTHLY":
            if (moment(day).format("D") === moment(startDate).format("D")) {
              newWeekDate = moment(day);
            }
            break;
        }

        if (newWeekDate !== null) {
          newObj = {
            ...newObj,
            title: `${obj.title} ${titleIndex === 0 ? "" : titleIndex}`.trim(),
            airTableEventName: `${
              titleIndex === 0 ? obj.airTableEventName : ""
            }`.trim(),
            startDate: newWeekDate.format("MM-DD-YYYY"),
            startTime: recurrenceDetails.eventStartTime,
            endDate: newWeekDate.format("MM-DD-YYYY"),
            endTime: recurrenceDetails.eventEndTime,
            year: moment(newWeekDate).year(),
          };
          eventArr.push(newObj);
          titleIndex++;
        }
      }
    } else {
      obj = {
        ...obj,
        startDate: req.body.startDate,
        startTime: req.body.startTime,
        endDate: req.body.endDate,
        endTime: req.body.endTime,
        year: year,
      };
      eventArr.push(obj);
    }

    let finalEvnts = [];
    let finalEventArr = eventArr.map(async (evt) => {
      const newEvent = new event(evt);
      const eventData = await newEvent.save();
      await addDefaultAttendeeType(eventData["_id"]);
      finalEvnts.push(eventData);
    });
    await Promise.all([...finalEventArr]);

    if (finalEvnts && finalEvnts.length > 0) {
      return res.status(200).json({
        status: true,
        message: `Event created successfully`,
        data: finalEvnts,
      });
    } else {
      return res.status(200).json({
        status: false,
        message: `Something went wrong while creating event!`,
      });
    }
  } catch (e) {
    return res
      .status(500)
      .json({ status: false, message: `Internal server error. ${e}` });
  }
};


// edit event
exports.editEvent = async (req, res) => {
  try {
    const getEvent = await event
      .findOne({ _id: new ObjectId(req.params.id), isDelete: false })
      .lean();
      const escapedTitle = escapeRegExp(req.body.title);

      const existEvent = await event.find({
        _id: { $ne: new ObjectId(req.params.id) },
        title: new RegExp("^" + escapedTitle + "$", "i"),
        isDelete: false,
        relation_id: ObjectId(req.relation_id),
      });
      if (existEvent && existEvent.length > 0) {
        return res.status(200).send({ success: false, message: `Event name must be unique.` });
      }

    if (
      req.body.airTableEventName &&
      req.body.airTableEventName.trim().length > 0
    ) {
      const eventAirTableExist = await event.aggregate([
        {
          $match: {
            _id: { $ne: new ObjectId(req.params.id) },
            $expr: {
              $eq: [
                {
                  $trim: { input: "$airTableEventName" },
                },
                req.body.airTableEventName.trim(),
              ],
            },
            isDelete: false,
          },
        },
        {
          $project: {
            _id: 1,
          },
        },
      ]);

      if (eventAirTableExist && eventAirTableExist.length > 0) {
        return res.status(200).json({
          status: false,
          message: `Event air table name must be unique.`,
        });
      }
    }

    if (getEvent) {
     // check valid or not
    let checkIds =  await checkValidIds({
      restrictedAccessGroupId:req.body.restrictedAccessGroupId,
      restrictedAccessMembershipPlanId:req.body.restrictedAccessMembershipPlanId,
      restrictedAccessUserId:req.body.restrictedAccessUserId,
      restrictedAccessTagId:req.body.restrictedAccessTagId,
      restrictedAccessTierId: req.body.restrictedAccessTierId,
      relation_id: req.relation_id
    });

      let description = `<div "font-family: 'Muller';">${req.body.longDescription}</div>`;
      let shortDescription = req.body.shortDescription;
      let preDescription = `${req.body.preRegisterDescription}`;

      req.body.location =
        req.body.location !== undefined &&
        req.body?.location !== null &&
        req.body.location !== ""
          ? JSON.parse(req.body.location)
          : null;

      const year = moment(req.body.startDate, "MM-DD-YYYY").year();
      if (
        req.thumbnail &&
        getEvent.thumbnail &&
        getEvent.thumbnail.length > 0
      ) {
        deleteImage(getEvent.thumbnail);
      }
      let timezoneStr = "";
      let timezoneBool = req.body.isTimeZoneAutomatic
        ? JSON.parse(req.body.isTimeZoneAutomatic)
        : false;

      if (
        timezoneBool &&
        req.body.locationType !== undefined &&
        req.body.locationType !== null &&
        req.body.locationType === "inPerson"
      ) {
        if (req.body.location && req.body.location != "") {
          const locationDetail = req.body.location;
          const timezoneUrl = `https://maps.googleapis.com/maps/api/timezone/json?location=${
            locationDetail.latitude
          },${locationDetail.longitude}&timestamp=${Math.floor(
            Date.now() / 1000
          )}&key=${process.env.GOOGLE_PLACE_KEY}`;

          const timezoneResponse = await axios.get(timezoneUrl);
          if (timezoneResponse) {
            const timezoneData = await timezoneResponse.data;

            if (timezoneData.status === "OK") {
              const utcOffsetMinutes = moment
                .tz(timezoneData.timeZoneId)
                .utcOffset();
              // Convert minutes to hours and minutes
              const hours = Math.floor(Math.abs(utcOffsetMinutes) / 60);
              const minutes = Math.abs(utcOffsetMinutes) % 60;
              // Create the UTC offset string
              const utcOffsetString = `${
                utcOffsetMinutes < 0 ? "-" : "+"
              }${hours.toString().padStart(2, "0")}:${minutes
                .toString()
                .padStart(2, "0")}`;
              const utcString = `(UTC${utcOffsetString}) ${timezoneData.timeZoneId} ${timezoneData.timeZoneName}`;
              // timezoneStr = utcString;
            }
          }
        } else {
          timezoneStr =
            req.body.timeZone !== undefined && req.body.timeZone !== null
              ? req.body.timeZone
              : getEvent.timeZone;
        }
      } else {
      timezoneStr = req.body.timeZone ? req.body.timeZone : getEvent.timeZone ; 
    }
    const ticketData = await eventTicket.aggregate([
      {$match: { eventId: ObjectId(req.params.id), isDelete: false }},
    ]);

    if (req.body && req.body.isPublic) {
      const isPublic = JSON.parse(req.body.isPublic); 
      if (isPublic === true) {
        req.body.restrictionAccess = "public";
        req.body.restrictedAccessGroupId = [];
        req.body.restrictedAccessMembershipPlanId = [];
        req.body.restrictedAccessUserId = [];
        req.body.restrictedAccessTagId = [];
        req.body.restrictedAccessTierId = [];
      }
    }
    req.body.maxTicketPurchase = 
    req?.body?.ticketType === "paid" 
      ? (req.body?.maxTicketPurchase == null ? 1 : req.body.maxTicketPurchase) // For paid, default to 1 if maxTicketPurchase is not provided
      : req?.body?.ticketType === "free" 
        ? (req.body?.maxTicketPurchase == null ? 1 : req.body.maxTicketPurchase)  // For free tickets, set maxTicketPurchase to null (no limit)
        : req.body.maxTicketPurchase;
       
      let eventDataTemp = await event.findByIdAndUpdate(
        req.params.id,
        {
          title: req.body.title ?? getEvent.title,
          thumbnail: req.thumbnail ?? getEvent.thumbnail,
          shortDescription: shortDescription ?? getEvent.shortDescription,
          longDescription: description ?? getEvent.longDescription,
          eventUrl: req.body.eventUrl ?? getEvent.eventUrl,
          ticketType: req.body.ticketType ?? getEvent.ticketType,
          maxTicketPurchase: req.body.maxTicketPurchase,
          // type: typeData,
          // timeZone: timezoneStr,
          timeZone: req.body.timeZone ?? getEvent.timeZone,
          startDate: req.body.startDate ?? getEvent.startDate,
          startTime: req.body.startTime ?? getEvent.startTime,
          endDate: req.body.endDate ?? getEvent.endDate,
          endTime: req.body.endTime ?? getEvent.endTime,
          chatChannel: req.body.chatChannel ? req.body.chatChannel : null,
          restrictionAccess: req.body.restrictionAccess ?? getEvent.restrictionAccess,
          restrictedAccessGroupId: req.body.restrictedAccessGroupId ? req.body.restrictedAccessGroupId : [],
          restrictedAccessMembershipPlanId: req.body.restrictedAccessMembershipPlanId ? req.body.restrictedAccessMembershipPlanId: [],
          restrictedAccessUserId: req.body.restrictedAccessUserId ? req.body.restrictedAccessUserId: [],
          restrictedAccessTagId: req.body.restrictedAccessTagId ? req.body.restrictedAccessTagId: [],
          restrictedAccessTierId: req.body.restrictedAccessTierId ? req.body.restrictedAccessTierId: [],
          isPreRegister: req.body.isPreRegister ?? getEvent.isPreRegister,
          preRegisterTitle:
            req.body.preRegisterTitle ?? getEvent.preRegisterTitle,
          preRegisterDescription:
            preDescription ?? getEvent.preRegisterDescription,
          preRegisterBtnTitle:
            req.body.preRegisterBtnTitle ?? getEvent.preRegisterBtnTitle,
          preRegisterBtnLink:
            req.body.preRegisterBtnLink ?? getEvent.preRegisterBtnLink,
          preRegisterStartDate: req.body.preRegisterStartDate
            ? req.body.preRegisterStartDate.substring(
                0,
                req.body.preRegisterStartDate.indexOf("+")
              ) + ".000Z" ?? getEvent.preRegisterStartDate
            : getEvent.preRegisterStartDate,
          preRegisterEndDate: req.body.preRegisterEndDate
            ? req.body.preRegisterEndDate.substring(
                0,
                req.body.preRegisterEndDate.indexOf("+")
              ) + ".000Z" ?? getEvent.preRegisterEndDate
            : getEvent.preRegisterEndDate,
          isLocation: req.body.isLocation,
          location:
            req.body.locationType !== undefined &&
            req.body.locationType !== null &&
            req.body.locationType === "inPerson" &&
            req.body.location !== undefined &&
            req.body?.location !== null &&
            req.body.location !== ""
              ? req.body.location
              : null,
          year:
            year !== undefined && year !== null && year !== ""
              ? year
              : getEvent.year
              ? ""
              : getEvent.year,
          airTableEventName:
            req.body.airTableEventName !== undefined &&
            req.body.airTableEventName !== null
              ? req.body.airTableEventName
              : getEvent.airTableEventName === ""
              ? ""
              : getEvent.airTableEventName,
          status: req.body.status ?? getEvent.status,
          tag:
            req.body.tag !== undefined && req.body.tag !== null
              ? req.body.tag
              : [],
          category:
            req.body.category !== undefined && req.body.category !== null
              ? req.body.category
              : [],
          subcategory:
            req.body.subcategory !== undefined && req.body.subcategory !== null
              ? req.body.subcategory
              : [],
          locationType:
            req.body.locationType !== undefined &&
            req.body.locationType !== null
              ? req.body.locationType
              : null,
          onlineLocationDetail: {
            onlineEventUrl:
              req.body.locationType !== undefined &&
              req.body.locationType !== null &&
              req.body.locationType === "virtual" &&
              req.body.onlineEventUrl
                ? req.body.onlineEventUrl
                : "",
            // videoPreviewImage:
            //   req.body.locationType !== undefined &&
            //   req.body.locationType !== null &&
            //   req.body.locationType === "virtual"
            //     ? req.videoPreviewImage
            //       ? req.videoPreviewImage
            //       : getEvent.onlineLocationDetail.videoPreviewImage
            //       ? getEvent.onlineLocationDetail.videoPreviewImage
            //       : ""
            //     : "",
            // onlineDescription:
            //   req.body.locationType !== undefined &&
            //   req.body.locationType !== null &&
            //   req.body.locationType === "virtual" &&
            //   req.body.onlineDescription
            //     ? req.body.onlineDescription
            //     : "",
          },
          toBeAnnouceDetail:
            req.body.locationType !== undefined &&
            req.body.locationType !== null &&
            req.body.locationType === "to_be_announce"
              ? req.body.toBeAnnouceDetail
              : "",
          isTimeZoneAutomatic: req.body.isTimeZoneAutomatic
            ? req.body.isTimeZoneAutomatic
            : getEvent.isTimeZoneAutomatic
            ? getEvent.isTimeZoneAutomatic
            : false,
          isPublic: req.body.isPublic ? req.body.isPublic : getEvent.isPublic,
          isCheckInAllow: req.body.isCheckInAllow == "true" ? true : false,
          ticketPlatform: req.body.ticketPlatform ?? getEvent.ticketPlatform,
        },
        { new: true }
      );

      if(req.body.status === "published"){
        if (!req.body.ticketType) {
          return res.status(400).json({ error: "TicketType is required when status is published" });
        }
      }

      let eventData = await event.findOne({_id: ObjectId(req.params.id), isDelete: false }).lean();
      if (eventData){
        eventData.mainTicketCount = ticketData.length;
        return res.status(200).json({
          status: true,
          message: "Event updated successfully!",
          data: eventData,
        });
      } else{
        return res.status(200).json({
          status: false,
          message: "Something went wrong while updating event!",
        });
      }
    } else {
      return res
        .status(200)
        .json({ status: false, message: "Event not found!" });
    }
  } catch (error) {
    return res
      .status(500)
      .json({ status: false, message: "Internal server error!", error: error });
  }
};

// delete event
exports.deleteEvent = async (req, res) => {
  try {
    const getEvent = await event
      .findOne({ _id: new ObjectId(req.params.id), isDelete: false })
      .lean();
    if (getEvent) {
      const eventData = await event.findByIdAndUpdate(
        req.params.id,
        { isDelete: true },
        { new: true }
      );
      if (eventData)
        return res.status(200).json({
          status: true,
          message: "Event deleted successfully!",
          data: eventData,
        });
      else
        return res.status(200).json({
          status: false,
          message: "Something went wrong while deleteing event!",
        });
    } else {
      return res
        .status(200)
        .json({ status: false, message: "Event not found!" });
    }
  } catch (e) {
    return res
      .status(500)
      .json({ status: false, message: "Internal server error!", error: e });
  }
};

// add or update contact support details in event
exports.editContactSupportInEvent = async (req, res) => {
  try {
    const body = req.body;
    const getContactData = await event
      .findOne({ _id: new ObjectId(req.params.id), isDelete: false })
      .lean();
    if (getContactData) {
      let contactSupport = {
        email: body.email ?? getContactData.email,
        phone: body.phone ?? getContactData.phone,
        localPhone: body.localPhone ?? getContactData.localPhone,
      };

      const contactData = await event.findByIdAndUpdate(
        req.params.id,
        {
          contactSupport: contactSupport,
        },
        { new: true }
      );

      if (contactData)
        return res.status(200).json({
          status: true,
          message: "Contact support data updated successfully!",
          data: contactData,
        });
      else
        return res.status(200).json({
          status: false,
          message: "Something went wrong while updating contact data!",
        });
    } else {
      return res
        .status(200)
        .json({ status: false, message: "Contact not found!" });
    }
  } catch (error) {
    return res
      .status(500)
      .json({ status: false, message: "Internal server error!", error: error });
  }
};

// get all event
exports.getAllEvent = async (req, res) => {
  try {
    const page = req.query.page ? parseInt(req.query.page) : 1;
    const limit = req.query.limit ? parseInt(req.query.limit) : 1;
    const skip = (page - 1) * limit;
    const commonCondition = await eventDateAndTimeCommonCondition();
    let dateValidationNew = await dateValidation();
    var filter =
      req.query.filter ||
      req.query.filter !== "" ||
      req.query.filter !== null ||
      req.query.filter !== undefined
        ? req.query.filter
        : "All";
    const sortType = req.query.sortType === "Asc" ? 1 : -1;

    var match = {
      isDelete: false,
      relation_id: ObjectId(req.relation_id),
    };

    var search = "";
    if (req.query.search) {      
      search = req.query.search;
      search = search.replace(/[-[\]{}()*+?.,\\^$|#\s]/g, "\\$&");
      match = {
        ...match,
        title: { $regex: ".*" + search + ".*", $options: "i" },
      };
    }
    // locationTypeFilter
    if (req.query.locationTypeFilter) {
      match = {
        ...match,
        locationType: req.query.locationTypeFilter,
      };
    }

    // city Filter
    if (req.query.city) {
      match = {
        ...match,
        'location.city': req.query.city,
      };
    }


    var tag = "";
    if (req.query.tag) {
      tag = req.query.tag;
      match = {
        ...match,
        tag: { $in: [ObjectId(tag)] },
      };
    }

    // var toDayDate = new Date();
    // toDayDate.setUTCHours(0, 0, 0, 0);
    // const mm = String(toDayDate.getMonth() + 1).padStart(2, "0");
    // const dd = String(toDayDate.getDate()).padStart(2, "0");
    // const yyyy = toDayDate.getFullYear();
    // const localDate = moment(toDayDate, "YYYY-MM-DD").toDate();

    var localDate;

    if (req.query && req.query.localDate) {
        localDate = new Date(req.query.localDate);
    } else {
        localDate = new Date();
    }

    localDate = moment(localDate, "YYYY-MM-DD").toDate();

    if (filter === "Draft") {
      match = {
        ...match,
        status: { $eq: "draft" },
      };
    }
    if (filter === "Past Event") {
      match = {
        ...match,
        status: { $in: ["published", "paused"] },
        EndDate: { $lt: localDate },
      };
    }
    if (filter === "ongoing") {
      match = {
        ...match,
        status: { $in: ["published", "paused"] },
        Date: { $lte: localDate },
        EndDate: { $gte: localDate },
      };
    }
    if (filter === "Upcoming Event") {
      match = {
        ...match,
        status: { $in: ["published", "paused"] },
        Date: { $gt: localDate },
      };
    }
    if (filter !== "Draft") {
      var pipeline = [
        {
          $match: {
            isDelete: false,
            relation_id: ObjectId(req.relation_id),
          },
        },
        // {
        // $addFields: commonCondition.$addFields  // Use $addFields from commonCondition
        // },
        ...dateValidationNew,
        {
          $addFields: {
            Date: {
              $let: {
                vars: {
                  year: {
                    $substr: [
                      {
                        $cond: [
                          { $in: ["$startDate", ["", "Invalid date"]] },
                          "01-01-1970",
                          "$startDate",
                        ],
                      },
                      6,
                      4,
                    ],
                  },
                  month: {
                    $substr: [
                      {
                        $cond: [
                          { $in: ["$startDate", ["", "Invalid date"]] },
                          "01-01-1970",
                          "$startDate",
                        ],
                      },
                      0,
                      2,
                    ],
                  },
                  dayOfMonth: {
                    $substr: [
                      {
                        $cond: [
                          { $in: ["$startDate", ["", "Invalid date"]] },
                          "01-01-1970",
                          "$startDate",
                        ],
                      },
                      3,
                      2,
                    ],
                  },
                  startMinute: { $substr: ["$startTime", 3, 2] },
                  startHours: {
                    $toString: {
                      $cond: {
                        if: { $eq: [{ $substr: ["$startTime", 6, 2] }, "am"] },
                        then: {
                          $cond: {
                            if: { $eq: [{ $substr: ["$startTime", 0, 2] }, "12"] },
                            then: "00", // Midnight (12 AM) should be converted to "00"
                            else: { $substr: ["$startTime", 0, 2] }, // No change needed for AM times other than midnight
                            },
                          },
                        else: {
                          $cond: {
                            if: { $eq: [{ $substr: ["$startTime", 0, 2] }, "12"] },
                            then: "12", // Noon (12 PM) should remain "12"
                            else: {
                              $add: [
                                {
                                  $toInt: { $substr: ["$startTime", 0, 2] },
                                },
                                12, // Adding 12 to convert PM times to 24-hour format
                              ],
                            },
                          },
                        },
                      },
                    },
                  },
                  timeZoneTemp: {
                    $cond: {
                      if: {
                        $or: [
                          { $eq: ["$timeZone", ""] },
                          {
                            $eq: [
                              "$timeZone",
                              "(UTC) Dublin, Edinburgh, Lisbon, London"
                            ]
                          }
                        ]
                      },
                      then: "-00:00",
                      else: {
                        $substr: ["$timeZone", 4, 6]
                      }
                    }
                  }
                },
                in: {
                  $toDate: {
                    $concat: [
                      "$$year",
                      "-",
                      "$$month",
                      "-",
                      "$$dayOfMonth",
                      "T",
                      "$$startHours",
                      ":",
                      "$$startMinute",
                      ":",
                      "00.000",
                      "$$timeZoneTemp"
                    ],
                  },
                },
              },
            },
          },
        },
        {
          $addFields: {
            EndDate: {
              $let: {
                vars: {
                  year: {
                    $substr: [
                      {
                        $cond: [
                          { $in: ["$endDate", ["", "Invalid date"]] },
                          "01-01-1970",
                          "$endDate",
                        ],
                      },
                      6,
                      4,
                    ],
                  },
                  month: {
                    $substr: [
                      {
                        $cond: [
                          { $in: ["$endDate", ["", "Invalid date"]] },
                          "01-01-1970",
                          "$endDate",
                        ],
                      },
                      0,
                      2,
                    ],
                  },
                  dayOfMonth: {
                    $substr: [
                      {
                        $cond: [
                          { $in: ["$endDate", ["", "Invalid date"]] },
                          "01-01-1970",
                          "$endDate",
                        ],
                      },
                      3,
                      2,
                    ],
                  },
                  startMinute: { $substr: ["$endTime", 3, 2] },
                  startHours: {
                    $toString: {
                      $cond: {
                        if: { $eq: [{ $substr: ["$endTime", 6, 2] }, "am"] },
                        then: {
                          $cond: {
                            if: { $eq: [{ $substr: ["$endTime", 0, 2] }, "12"] },
                            then: "00", // Midnight (12 AM) should be converted to "00"
                            else: { $substr: ["$endTime", 0, 2] }, // No change needed for AM times other than midnight
                            },
                          },
                        else: {
                          $cond: {
                            if: { $eq: [{ $substr: ["$endTime", 0, 2] }, "12"] },
                            then: "12", // Noon (12 PM) should remain "12"
                            else: {
                              $add: [
                                {
                                  $toInt: { $substr: ["$endTime", 0, 2] },
                                },
                                12, // Adding 12 to convert PM times to 24-hour format
                              ],
                            },
                          },
                        },
                      },
                    },
                  },
                  timeZoneTemp: {
                    $cond: {
                      if: {
                        $or: [
                          { $eq: ["$timeZone", ""] },
                          {
                            $eq: [
                              "$timeZone",
                              "(UTC) Dublin, Edinburgh, Lisbon, London"
                            ]
                          }
                        ]
                      },
                      then: "-00:00",
                      else: {
                        $substr: ["$timeZone", 4, 6]
                      }
                    }
                  }
                },
                in: {
                  $toDate: {
                    $concat: [
                      "$$year",
                      "-",
                      "$$month",
                      "-",
                      "$$dayOfMonth",
                      "T",
                      "$$startHours",
                      ":",
                      "$$startMinute",
                      ":",
                      "00.000",
                      "$$timeZoneTemp",
                    ],
                  },
                },
              },
            },
          },
        },
        {
          $addFields: {
            StartDateAndTime: "$Date",
            EndDateAndTime: "$EndDate"
          }
        },
        // {
        //   $addFields: {
        //     StartDateAndTime: {
        //       $let: {
        //         vars: {
        //           year_: {
        //             $substr: [
        //               {
        //                 $cond: [
        //                   { $in: ["$startDate", ["", "Invalid date"]] },
        //                   "01-01-1970",
        //                   "$startDate",
        //                 ],
        //               },
        //               6,
        //               4,
        //             ],
        //           },
        //           month_: {
        //             $substr: [
        //               {
        //                 $cond: [
        //                   { $in: ["$startDate", ["", "Invalid date"]] },
        //                   "01-01-1970",
        //                   "$startDate",
        //                 ],
        //               },
        //               0,
        //               2,
        //             ],
        //           },
        //           dayOfMonth_: {
        //             $substr: [
        //               {
        //                 $cond: [
        //                   { $in: ["$startDate", ["", "Invalid date"]] },
        //                   "01-01-1970",
        //                   "$startDate",
        //                 ],
        //               },
        //               3,
        //               2,
        //             ],
        //           },
        //           startTime_: {
        //             $cond: [
        //               { $in: ["$startTime", ["", "Invalid date"]] },
        //               "12:00 pm",
        //               "$startTime",
        //             ],
        //           },
        //         },
        //         in: {
        //           $toDate: {
        //             $concat: [
        //               "$$year_",
        //               "-",
        //               "$$month_",
        //               "-",
        //               "$$dayOfMonth_",
        //               " ",
        //               "$$startTime_",
        //             ],
        //           },
        //         },
        //       },
        //     },
        //   },
        // },
        // {
        //   $addFields: {
        //     EndDateAndTime: {
        //       $let: {
        //         vars: {
        //           year_: {
        //             $substr: [
        //               {
        //                 $cond: [
        //                   { $in: ["$endDate", ["", "Invalid date"]] },
        //                   "01-01-1970",
        //                   "$endDate",
        //                 ],
        //               },
        //               6,
        //               4,
        //             ],
        //           },
        //           month_: {
        //             $substr: [
        //               {
        //                 $cond: [
        //                   { $in: ["$endDate", ["", "Invalid date"]] },
        //                   "01-01-1970",
        //                   "$endDate",
        //                 ],
        //               },
        //               0,
        //               2,
        //             ],
        //           },
        //           dayOfMonth_: {
        //             $substr: [
        //               {
        //                 $cond: [
        //                   { $in: ["$endDate", ["", "Invalid date"]] },
        //                   "01-01-1970",
        //                   "$endDate",
        //                 ],
        //               },
        //               3,
        //               2,
        //             ],
        //           },
        //           endTime_: {
        //             $cond: [
        //               { $in: ["$endTime", ["", "Invalid date"]] },
        //               "12:00 pm",
        //               "$endTime",
        //             ],
        //           },
        //         },
        //         in: {
        //           $toDate: {
        //             $concat: [
        //               "$$year_",
        //               "-",
        //               "$$month_",
        //               "-",
        //               "$$dayOfMonth_",
        //               " ",
        //               "$endTime_",
        //             ],
        //           },
        //         },
        //       },
        //     },
        //   },
        // },
        {
          $match: match,
        },
        {
          $lookup: {
            from: "event_tickets",
            localField: "_id",
            foreignField: "eventId",
            as: "ticketData",
            pipeline: [
              {
                $match: {
                  $expr: {
                    $eq: ["$isDelete", false],
                  },
                },
              },
            ],
          },
        },
        {
          $lookup: {
            from: "ticket_payment_intents",
            localField: "_id",
            foreignField: "eventId",
            as: "paymentIntentData",
            pipeline: [
              {
                $match: {
                  isDelete: false,
                  status: "succeeded",
                },
              },
            ],
          },
        },
        {
          $addFields: {
            available_ticket: {
              $sum: {
                $map: {
                  input: "$ticketData",
                  as: "ticket",
                  in: {
                    $ifNull: ["$$ticket.availableQuantity", 0],
                  },
                },
              },
            },
            quantity: {
              $sum: {
                $map: {
                  input: "$ticketData",
                  as: "ticket",
                  in: {
                    $ifNull: ["$$ticket.quantity", 0],
                  },
                },
              },
            },
            ticket_purchase: {
              $sum: {
                $map: {
                  input: "$paymentIntentData",
                  as: "paymentIntent",
                  in: {
                    $ifNull: ["$$paymentIntent.purchaseQuantity", 0],
                  },
                },
              },
            },
          },
        },
        {
          $lookup: {
            from: "event_views",
            localField: "_id",
            foreignField: "event",
            as: "event_view_data",
            pipeline: [
              {
                $lookup: {
                  from: "airtable-syncs",
                  localField: "userId",
                  foreignField: "_id",
                  as: "user_data",
                  pipeline: [
                    {
                      $match: {
                        $expr: {
                          $eq: ["$isDelete", false],
                        },
                      },
                    },
                  ],
                },
              },
              {
                $unwind: "$user_data",
              },
              {
                $addFields: {
                  user: {
                    $concat: [
                      "$user_data.first_name",
                      " ",
                      "$user_data.last_name",
                    ],
                  },
                },
              },
              {
                $project: {
                  viewCount: 1,
                  user: 1,
                  lastViewAt: 1,
                  display_name: "$user_data.display_name",
                },
              },
            ],
          },
        },
        {
          $addFields: {
            totalViewCount: {
              $sum: "$event_view_data.viewCount",
            },
            // mainTicketCount: {
            //   $size: "$ticket_data",
            // },
            // quantity: {
            //   $sum: {
            //     $sum: "$ticket_data.quantity",
            //   },
            // },
            // ticket_purchase: {
            //   $sum: {
            //     $sum: "$ticket_data.ticket_purchase",
            //   },
            // },
            // available_ticket: {
            //   $sum: {
            //     $sum: "$ticket_data.available_ticket",
            //   },
            // },
          },
        },
        {
          $project: {
            _id: 1,
            StartDateAndTime: 1,
            EndDateAndTime: 1,
            Date: 1,
            locationType: 1,
            EndDate: 1,
            endDate: 1,
            endTime: 1,
            maxTicketPurchase:1,
            createdAt: 1,
            eventUrl: 1,
            title: 1,
            startTime: 1,
            startDate: 1,
            restrictionAccess: 1,
            status: 1,
            city: "$location.city",
            quantity: 1,
            sold_ticket: "$ticket_purchase",
            available_ticket: 1,
            timeZone: 1,
            mainTicketCount: 1,
            totalViewCount: 1,
            event_view_data: 1,
            totalViewCount: 1,
            eventStatus: {
              $cond: {
                if: { $eq: ["$status", "draft"] }, // Check if status is "draft"
                then: "Draft",
                else: {
                  $cond: {
                    if: { $gt: ["$Date", localDate] }, // Check if startDate >= localDate
                    then: "On sale",
                    else: {
                      $cond: {
                        if: {
                          $and: [
                            { $lte: ["$Date", localDate] },
                            { $gte: ["$EndDate", localDate] },
                          ],
                        },
                        then: "Ongoing Event",
                        else: "Event ended",
                      },
                    },
                  },
                },
              },
            },
            isPublic: 1,
            ticketPlatform: 1,
          },
        },
        {
          $addFields: {
            sortFieldLower:
              req.query.sortField === "title"
                ? { $toLower: "$title" }
                : req.query.sortField === "eventUrl"
                ? { $toLower: "$eventUrl" }
                : req.query.sortField === "pageView"
                ? { $toInt: "$totalViewCount" }
                : req.query.sortField === "sold"
                ? { $toInt: "$sold_ticket" }
                : { $toLower: "$createdAt" },
          },
        },
        {
          $sort:
            req.query.sortField === "startDate"
              ? { Date: sortType }
              : req.query.sortField === "startDateAndTime"
              ? { StartDateAndTime: sortType }
              : req.query.sortField === "startTime"
              ? { StartDateAndTime: sortType }
              : req.query.sortField === "endDate"
              ? { EndDate: sortType }
              : req.query.sortField === "endTime"
              ? { EndDateAndTime: sortType }
              : req.query.sortField === "status"
              ? { status: sortType }
              : req.query.sortField === "eventAccess"
              ? { restrictionAccess: sortType }
              : { sortFieldLower: sortType },
        },
        {
          $project: {
            sortFieldLower: 0,
          },
        },
      ];
    } else {
      var pipeline = [
        {
          $match: match,
        },
        {
          $project: {
            _id: 1,
            endDate: 1,
            endTime: 1,
            maxTicketPurchase:1,
            createdAt: 1,
            locationType: 1,
            eventUrl: 1,
            title: 1,
            startTime: 1,
            startDate: 1,
            restrictionAccess: 1,
            status: 1,
            eventStatus: "Draft",
            isPublic: 1,
            ticketPlatform: 1,
          },
        },
      ];
    }
    let allEventData = await event.aggregate([
      ...pipeline,
      { $skip: skip },
      { $limit: limit },
    ]);

    const totalCount = await event.aggregate(pipeline);
    count = totalCount.length;

    const countAllData = await event.countDocuments({ relation_id: ObjectId(req.relation_id) })
    if (allEventData)
      return res.status(200).json({
        status: true,
        message: "All event retrive!",
        data: { 
          allEventData: allEventData,
          totalPages: Math.ceil(count / limit),
          currentPage: page,
          totalMessages: count,
          countAllData
        },
      });
    else
      return res.status(200).json({
        status: false,
        message: "Something went wrong while getting event!",
        data: {
          allEventData: [],
          totalPages: Math.ceil(count / limit),
          currentPage: page,
          totalMessages: count,
          countAllData
        },
      });
  } catch (e) {
    return res
      .status(500)
      .json({ status: false, message: "Internal server error!", error: e });
  }
};

// get listing of city
exports.getAllEventCityList = async (req, res) => {
  try {
    const allCitydata = await event.aggregate([
      {
        $match: {
          isDelete: false,
          relation_id: ObjectId(req.relation_id),
          $and: [
            { location: { $exists: true } },
            { location: { $ne: null } },
          ]
        }
      },
      {
        $group: {
          _id: null,
          uniqueValues: {
            $addToSet: "$location.city",
          },
        },
      },
      {
        $unwind: {
          path: "$uniqueValues",
          preserveNullAndEmptyArrays: false,
        },
      },
      {
        $match: {
          uniqueValues:{$ne:""}
        }
      },
      {
        $project: {
          _id:0,
          city: "$uniqueValues",
        },
      },
      {$sort:{city:1}}
    ]);
    
    if (allCitydata && allCitydata.length > 0){
      return res.status(200).json({
        status: true,
        message: "All event city retrieve!",
        data: allCitydata,
      });}
    else{
      return res.status(200).json({
        status: false,
        message: "Something went wrong while getting event!",
      });}
  } catch (e) {
    return res
      .status(500)
      .json({ status: false, message: "Internal server error!", error: e });
  }
};

// get all event with  limited fields
exports.getAllEventsLimitedFiedls = async (req, res) => {
  try {
    const communityId =req.currentEdge.relation_id._id
    const allEventData = await event
      .find({ isDelete: false, relation_id: ObjectId(req.relation_id) })
      .sort({ createdAt: -1 })
      .select("title thumbnail shortDescription relation_id");
    if (allEventData)
      return res.status(200).json({
        status: true,
        message: "All event retrive!",
        data: allEventData,
      });
    else
      return res.status(200).json({
        status: false,
        message: "Something went wrong while getting event!",
      });
  } catch (e) {
    return res
      .status(500)
      .json({ status: false, message: "Internal server error!", error: e });
  }
};

// get event by id
exports.getEventDetail = async (req, res) => {
  try {
    const obj = { relation_id: req.relation_id }
    let eventDetailTemp = await event
      .findOne({
        _id: new ObjectId(req.params.id),
        isDelete: false,
      })
      .populate({ path: "restrictedAccessGroupId", select: "groupTitle" })
      .populate({ path: "restrictedAccessTagId", select: "name" })
      .populate({
        path: "restrictedAccessMembershipPlanId",
        select: "plan_name -accessResources",
      })
      .populate({
        path: "restrictedAccessUserId",
        select: {first_name: 1,last_name: 1,attendeeDetail: 1,'Preferred Email': 1,display_name: 1},
      })
      .populate({ path: "chatChannel",select: "channelName -restrictedAccess -tagIds -eventId" }).lean();

      let eventData = {...eventDetailTemp}

      let ticketData = await eventTicket.aggregate([
        {$match: { eventId: ObjectId(req.params.id), isDelete: false }},
      ]);
      eventData.mainTicketCount = ticketData.length;

      const tiers = await getAllTiersfromBilling( obj, expand = true );
      const restrictedTierIds = Array.isArray(eventData.restrictedAccessTierId)
      ? eventData.restrictedAccessTierId.map(id => id.toString())
      : [];
      const filteredTiers = tiers.filter(tier => 
      restrictedTierIds.includes(tier._id.toString())
      ).map(tier => ({
      _id: tier._id,
      name: tier.name
      }));
      eventData.restrictedAccessTierId = filteredTiers;
    if (eventData)
      return res.status(200).json({
        status: true,
        message: "Event detail retrive!",
        data: eventData,
      });
    else
      return res.status(200).json({
        status: false,
        message: "Something went wrong while getting event!",
      });
  } catch (e) {
    console.log(e, "e");
    return res
      .status(500)
      .json({ status: false, message: "Internal server error!", error: e });
  }
};

// clone event API
exports.cloneEvent = async (req, res) => {
  try {
    const { eventId } = req.body;
    if (eventId !== undefined && eventId !== null && eventId !== "") {
      const objData = await event
        .findOne({
          _id: ObjectId(eventId),
          isDelete: false,
        })
        .select("-_id -__v -updatedAt -createdAt");

      if (!objData) {
        return res
          .status(200)
          .json({ status: false, message: "Event data not Found!" });
      }

      let obj = objData.toObject();

      if (objData.thumbnail) {
        const split = objData.thumbnail.replace(/\s/g, "_").split("-");
        const params1 = {
          Bucket: process.env.AWS_BUCKET,
          CopySource: encodeURI(objData.thumbnail),
          Key:
            "uploads/eventthumbnail/copy-" +
            Date.now() +
            "-" +
            split[split.length - 1],
          ACL: "public-read",
        };
        await s3.copyObject(params1).promise();
        obj.thumbnail = process.env.AWS_IMG_VID_PATH + params1.Key;
      }
      obj.title = "Copy - " + objData.title;
      obj.airTableEventName =
        objData.airTableEventName !== undefined &&
        objData.airTableEventName !== null &&
        objData.airTableEventName.length > 0
          ? "Copy - " + objData.airTableEventName
          : "";

      let destlocationImages = [];

      if (obj.isLocation) {
        if (
          objData.location &&
          objData.location.locationImages &&
          objData.location.locationImages.length > 0
        ) {
          var multi_files_promise = [];
          const orderedImages = objData.location.locationImages.sort(
            (a, b) => a.order - b.order
          );
          multi_files_promise = orderedImages.map(async (locImg, i) => {
            var random_id = uuidv4();
            const split = locImg
              .replace("event-location", "eventlocation")
              .split("-");
            const locParams = {
              Bucket: process.env.AWS_BUCKET,
              CopySource: encodeURI(locImg),
              Key:
                "uploads/event-location/copy-" +
                random_id +
                "_" +
                Date.now() +
                "-" +
                split[split.length - 1],
              ACL: "public-read",
            };

            await s3.copyObject(locParams).promise();
            destlocationImages.push(
              process.env.AWS_IMG_VID_PATH + locParams.Key
            );
          });
          await Promise.all([...multi_files_promise]);
        }
        obj.location = {
          address: objData.location.address,
          latitude: objData.location.latitude,
          longitude: objData.location.longitude,
          postalCode: objData.location.postalCode,
          city: objData.location.city,
          country: objData.location.country,
          placeId: objData.location.placeId,
          locationImages: destlocationImages,
        };
      }

      if (!obj.isPreRegister) {
        obj.preRegisterTitle = "";
        obj.preRegisterDescription = "";
        obj.preRegisterBtnTitle = "";
        obj.preRegisterBtnLink = "";
        obj.preRegisterStartDate = null;
        obj.preRegisterEndDate = null;
      }

      let destPhotos = [];
      if (objData.photos && objData.photos.length > 0) {
        var multi_photoes_promise = [];
        multi_photoes_promise = objData.photos.map(async (singlePhoto, i) => {
          var random_id = uuidv4();
          const split = singlePhoto
            .replace("event-media", "event-media")
            .split("-");
          const evntPhoto = {
            Bucket: process.env.AWS_BUCKET,
            CopySource: encodeURI(singlePhoto),
            Key:
              "uploads/event-media/copy-" +
              random_id +
              "_" +
              Date.now() +
              "-" +
              split[split.length - 1],
            ACL: "public-read",
          };

          await s3.copyObject(evntPhoto).promise();
          destPhotos.push(process.env.AWS_IMG_VID_PATH + evntPhoto.Key);
        });
        await Promise.all([...multi_photoes_promise]);
      }
      obj.photos = destPhotos;
      obj.relation_id= ObjectId(req.relation_id);

      const eventClone = new event(obj);
      const newEvent = await eventClone.save();
      if (newEvent) {
        const cloneEventId = newEvent._id;
        await addDefaultAttendeeType(cloneEventId);
        //cloning location data
        const objEventLocations = await eventLocation.find({
          event: ObjectId(eventId),
        });
        if (objEventLocations.length > 0) {
          const resultEventLocs = objEventLocations.map(async (eventLoc) => {
            let eventLocObj = eventLoc.toObject();
            eventLocObj.event = cloneEventId;
            delete eventLocObj._id;
            const newEventLoc = new eventLocation(eventLocObj);
            const updatedEventLoc = await newEventLoc.save();
          });
          await Promise.all([...resultEventLocs]);
        }

        //cloning package data
        const objEventPackages = await eventPackage.find({
          event: ObjectId(eventId),
        });
        if (objEventPackages.length > 0) {
          const resultEventPackages = objEventPackages.map(async (eventPkg) => {
            let eventPkgObj = eventPkg.toObject();
            eventPkgObj.event = cloneEventId;
            delete eventPkgObj._id;
            const newEventPkg = new eventPackage(eventPkgObj);
            const updatedEventPkg = await newEventPkg.save();
          });
          await Promise.all([...resultEventPackages]);
        }

        //cloning room data
        const objEventRooms = await eventRoom.find({
          event: ObjectId(eventId),
        });
        if (objEventRooms.length > 0) {
          const resultEventRooms = objEventRooms.map(async (evtRoom) => {
            let eventRoomObj = evtRoom.toObject();
            eventRoomObj.event = cloneEventId;
            delete eventRoomObj._id;
            const newEventRoom = new eventRoom(eventRoomObj);
            const updatedEventRoom = await newEventRoom.save();
          });
          await Promise.all([...resultEventRooms]);
        }

        //cloning session data
        const objEventSession = await eventSession.find({
          event: ObjectId(eventId),
        });
        if (objEventSession.length > 0) {
          const resultEventSession = objEventSession.map(async (evtSession) => {
            let evtSessionObj = evtSession.toObject();
            evtSessionObj.event = cloneEventId;
            delete evtSessionObj._id;
            const newEventSession = new eventSession(evtSessionObj);
            const updatedEventSession = await newEventSession.save();
          });
          await Promise.all([...resultEventSession]);
        }

        //cloning activity data
        const objEventActivity = await eventActivity.find({
          event: ObjectId(eventId),
        });
        if (objEventActivity.length > 0) {
          const resultEventActivity = objEventActivity.map(
            async (evtActivity) => {
              let evtActivityObj = evtActivity.toObject();
              evtActivityObj.event = cloneEventId;
              delete evtActivityObj._id;
              const newEventActivity = new eventActivity(evtActivityObj);
              const updatedEventActivity = await newEventActivity.save();
            }
          );
          await Promise.all([...resultEventActivity]);
        }

        //cloning event faqs
        const objEventFaq = await eventFaqs.find({ event: ObjectId(eventId) });
        if (objEventFaq.length > 0) {
          const resultEventFaq = objEventFaq.map(async (evtFaq) => {
            let evtFaqObj = evtFaq.toObject();
            evtFaqObj.event = cloneEventId;
            delete evtFaqObj._id;
            const newEvtFaq = new eventFaqs(evtFaqObj);
            const updatedEventFaq = await newEvtFaq.save();
          });
          await Promise.all([...resultEventFaq]);
        }

        return res.status(200).json({
          status: true,
          message: "Cloning completed successfully!",
          data: newEvent,
        });
      } else {
        return res.status(200).json({
          status: true,
          message: "Error in while creating cloning event!",
          data: newEvent,
        });
      }
    } else {
      return res
        .status(200)
        .json({ status: false, message: "Partner data not found!", data: [] });
    }
  } catch (error) {
    return res
      .status(200)
      .json({ status: false, message: "Internal server error!", error: error });
  }
};

/** CURD opreation of event end **/

// add default attendee dynamic type
async function addDefaultAttendeeType(eventId) {
  let list = await EventParticipantTypes.find({
    isDelete: false,
    isDefault: true,
  });
  let addObj = [];
  for (let i = 0; i < list.length; i++) {
    let typeDetail = list[i];
    addObj.push({
      role: typeDetail.role,
      description: typeDetail.description,
      isDefault: typeDetail.isDefault,
      event: eventId,
      baseRole: typeDetail._id,
    });
  }
  let createdList = await eventWiseParticipantTypes.create(addObj);
  return createdList;
}

// sync event for dynamic role from sync old data to dynaic structure
exports.syncEventForDynamicRole = async (req, res) => {
  try {
    let allRole = await EventParticipantTypes.find({
      isDelete: false,
      isDefault: true,
      relation_id: ObjectId(req.relation_id),
    });
    let allList = await event.aggregate([
      {
        $match: {
          isDelete: false,
          relation_id: ObjectId(req.relation_id),
        },
      },
    ]);
    let totalSync = 0;
    //   for (let i = 0; i < 0; i++) {
    for (let i = 0; i < allList.length; i++) {
      let event = allList[i];
      for (let j = 0; j < allRole.length; j++) {
        let typeDetail = allRole[j];
        let role = allRole[j]["role"];
        let existType = await eventWiseParticipantTypes.findOne({
          role: role,
          event: new ObjectId(event._id),
          isDelete: false,
        });
        if (!existType) {
          let addObj = {
            role: typeDetail.role,
            description: typeDetail.description,
            isDefault: true,
            event: new ObjectId(event._id),
            baseRole: typeDetail._id,
          };
          await eventWiseParticipantTypes.create(addObj);
          totalSync = totalSync + 1;
        }
        console.log({ i, j }, totalSync, "/", allList.length * allRole.length);
      }
    }

    return res.status(200).json({
      status: true,
      message: "Session data sync successfully!",
      data: { totalSync },
    });
  } catch (error) {
    return res
      .status(500)
      .json({ status: false, message: "Internal server error!", error: error });
  }
};

// get timezone from location
exports.getTimeZoneFromLocation = async (req, res) => {
  try {
    if (req.query.lat && req.query.long) {
      let timeZoneStr = "";
      const timezoneUrl = `https://maps.googleapis.com/maps/api/timezone/json?location=${
        req.query.lat
      },${req.query.long}&timestamp=${Math.floor(Date.now() / 1000)}&key=${
        process.env.GOOGLE_PLACE_KEY
      }`;
      const timezoneResponse = await axios.get(timezoneUrl);
      if (timezoneResponse) {
        const timezoneData = await timezoneResponse.data;
        if (timezoneData.status === "OK") {
          const utcOffsetMinutes = moment
            .tz(timezoneData.timeZoneId)
            .utcOffset();
          // Convert minutes to hours and minutes
          const hours = Math.floor(Math.abs(utcOffsetMinutes) / 60);
          const minutes = Math.abs(utcOffsetMinutes) % 60;
          // Create the UTC offset string
          const utcOffsetString = `${utcOffsetMinutes < 0 ? "-" : "+"}${hours
            .toString()
            .padStart(2, "0")}:${minutes.toString().padStart(2, "0")}`;
          const utcString = `(UTC${utcOffsetString}) ${timezoneData.timeZoneId} ${timezoneData.timeZoneName}`;
          timeZoneStr = utcString;
          return res.status(200).json({
            status: true,
            message: "Timezone fetched successfully!",
            data: timeZoneStr,
          });
        } else {
          return res.status(200).json({
            status: false,
            message: "Timezone not found!",
            data: timeZoneStr,
          });
        }
      }
    } else {
      return res
        .status(200)
        .json({ status: false, message: "Input parameters are missing!" });
    }
  } catch (error) {
    return res
      .status(500)
      .json({ status: false, message: "Internal server error!", error: error });
  }
};

exports.getEventCount = async (
  relation_id,
  localDate,
  authUserId,
  eventType,
  categoryId,
  subCategoryId,
  year,
  type,
  search,
  city
) => {
  try {
    var localDate = localDate;
    localDate = moment(localDate, "YYYY-MM-DD").toDate();
    const authUser = authUserId;
    let ruleCondition = await userAccessRulesCommonCondition({
      userId: authUser,
      relation_id:relation_id
    });

    let matchUpCommingEvent = {
      isDelete: false,
      status: "published",
      Date: { $gt: localDate },
      relation_id: ObjectId(relation_id),
    };
    if (search) {
      matchUpCommingEvent = {
        ...matchUpCommingEvent,
        $or: [{ title: { $regex: ".*" + search + ".*", $options: "i" } },{ 'tag.name': { $regex: ".*" + search + ".*", $options: "i" }, }],
      };
    }

    let matchPastEvent = {
      isDelete: false,
      status: "published",
      Date: { $lt: localDate },
      relation_id: ObjectId(relation_id),
    };

    if (search) {
      matchPastEvent = {
        ...matchPastEvent,
        $or: [{ title: { $regex: ".*" + search + ".*", $options: "i" } },{ 'tag.name': { $regex: ".*" + search + ".*", $options: "i" }, }],
      };
    }
    //aggregate UpComming Event
    const aggregatePipelineUpCommingEvent = [
    {
      $addFields: {
        Date: {
          $let: {
            vars: {
              year: { $substr: ["$endDate", 6, 10] },
              month: { $substr: ["$endDate", 0, 2] },
              dayOfMonth: { $substr: ["$endDate", 3, 2] },
              startMinute: { $substr: ["$endTime", 3, 2] },
              startHours: {
                $toString: {
                  $cond: {
                    if: { $eq: [{ $substr: ["$endTime", 6, 2] }, "am"] },
                    then: {
                      $cond: {
                        if: { $eq: [{ $substr: ["$endTime", 0, 2] }, "12"] },
                        then: "00", // Midnight (12 AM) should be converted to "00"
                        else: { $substr: ["$endTime", 0, 2] }, // No change needed for AM times other than midnight
                      },
                    },
                    else: {
                      $cond: {
                        if: { $eq: [{ $substr: ["$endTime", 0, 2] }, "12"] },
                        then: "12", // Noon (12 PM) should remain "12"
                        else: {
                          $add: [
                            {
                              $toInt: { $substr: ["$endTime", 0, 2] },
                            },
                            12, // Adding 12 to convert PM times to 24-hour format
                          ],
                        },
                      },
                    },
                  },
                },
              },
            },
            in: {
              $toDate: {
                $concat: [
                  "$$year",
                  "-",
                  "$$month",
                  "-",
                  "$$dayOfMonth",
                  "T",
                  "$$startHours",
                  ":",
                  "$$startMinute",
                  ":",
                  "00",
                ],
              },
            },
          },
        },
      },
    },
      {
        $match: {
          ...ruleCondition,
        },
      },
      {
        $lookup: {
          from: "contentarchive_tags",
          localField: "tag",
          foreignField: "_id",
          pipeline: [
            {
              $match: {
                isDelete: false,
              },
            },
          ],
          as: "tag",
        },
      },
      {
        $match: matchUpCommingEvent,
      },
      // match type
      ...(type && eventType === "upcomming"
        ? [
            {
              $match: {
                locationType: type,
              },
            },
          ]
        : []),
        // match city
      ...(city && eventType === "upcomming"
        ? [
            {
              $match: {
                "location.city": city,
              },
            },
          ]
        : []),
      //   // match Category Id
      ...(categoryId && eventType === "upcomming"
        ? [
            {
              $match: {
                category: ObjectId(categoryId),
              },
            },
          ]
        : []),
      //   // match sub Category Id
      ...(categoryId && subCategoryId && eventType === "upcomming"
        ? [
            {
              $match: {
                category: ObjectId(categoryId),
                subcategory: ObjectId(subCategoryId),
              },
            },
          ]
        : []),
    ];

    // aggregate PastEvent
    const aggregatePipelinePastEvent = [
      {
        $addFields: {
          Date: {
            $let: {
              vars: {
                year: { $substr: ["$endDate", 6, 10] },
                month: { $substr: ["$endDate", 0, 2] },
                dayOfMonth: { $substr: ["$endDate", 3, 2] },
                startMinute: { $substr: ["$endTime", 3, 2] },
                startHours: {
                  $toString: {
                    $cond: {
                      if: { $eq: [{ $substr: ["$endTime", 6, 2] }, "am"] },
                      then: {
                        $cond: {
                          if: { $eq: [{ $substr: ["$endTime", 0, 2] }, "12"] },
                          then: "00", // Midnight (12 AM) should be converted to "00"
                          else: { $substr: ["$endTime", 0, 2] }, // No change needed for AM times other than midnight
                        },
                      },
                      else: {
                        $cond: {
                          if: { $eq: [{ $substr: ["$endTime", 0, 2] }, "12"] },
                          then: "12", // Noon (12 PM) should remain "12"
                          else: {
                            $add: [
                              {
                                $toInt: { $substr: ["$endTime", 0, 2] },
                              },
                              12, // Adding 12 to convert PM times to 24-hour format
                            ],
                          },
                        },
                      },
                    },
                  },
                },
              },
              in: {
                $toDate: {
                  $concat: [
                    "$$year",
                    "-",
                    "$$month",
                    "-",
                    "$$dayOfMonth",
                    "T",
                    "$$startHours",
                    ":",
                    "$$startMinute",
                    ":",
                    "00",
                  ],
                },
              },
            },
          },
        },
      },
      ...(year && eventType === "past"
        ? [
            {
              $match: {
                year: year,
              },
            },
          ]
        : []),
        {
          $lookup: {
            from: "contentarchive_tags",
            localField: "tag",
            foreignField: "_id",
            pipeline: [
              {
                $match: {
                  isDelete: false,
                },
              },
            ],
            as: "tag",
          },
      },
      {
        $match: matchPastEvent,
      },
      {
        $match: {
          ...ruleCondition,
        },
      },
      ...(city && eventType === "past"
        ? [
            {
              $match: {
                "location.city": city,
              },
            },
          ]
        : []),
      //  match category Id
      ...(city && eventType === "past"
        ? [
            {
              $match: {
                "location.city": city,
              },
            },
          ]
        : []),
      //  match category Id
      ...(categoryId && eventType === "past"
        ? [
            {
              $match: {
                category: ObjectId(categoryId),
              },
            },
          ]
        : []),
      // match subCategory Id
      ...(categoryId && subCategoryId && eventType === "past"
        ? [
            {
              $match: {
                category: ObjectId(categoryId),
                subcategory: ObjectId(subCategoryId),
              },
            },
          ]
        : []),
    ];

    // my event
    const role = "member"
    const userData = await User.findById(authUser).select(
      "firebaseId email accessible_groups purchased_plan attendeeDetail"
    );
    var eventList = [];
    let finalEventListData = [];
    location = {};
    var eventListData = [];
    if (userData !== null && userData !== undefined) {
      let condition = { _id: authUser, isDelete: false };
      if( role && role == "nonMember" ){
        condition = { _id: authUser, $or: [{ isDelete: false }, { isDelete: { $exists: false } }] }
      }
      const eventAttendeesData = await User.findOne(
        condition,
        { attendeeDetail: 1 }
      );
      if (eventAttendeesData !== null) {
        let conditionTemp = { '$ne': ["$role", "Member"] };
        if ( role === "member" ) {
          conditionTemp = { '$in': ["$role", [ "Member", "Staff" ]] };
        }
        let pipeline = [
          {
            '$match': {
              // event: eventId,
              user: new ObjectId(authUser),
              isDelete: false,
              relation_id: ObjectId(relation_id),
            }
          }, {
            '$lookup': {
              'from': 'event_wise_participant_types',
              'localField': 'role',
              'foreignField': '_id',
              'as': 'event_wise_participant_types_result',
              'pipeline': [
                {
                  '$match': {
                    '$expr': conditionTemp
                  }
                }
              ]
            }
          }, {
            '$unwind': {
              'path': '$event_wise_participant_types_result',
              'preserveNullAndEmptyArrays': false
            }
          }
        ]

        let attendeesList = await eventParticipantAttendees.aggregate(pipeline);
        if (attendeesList && attendeesList.length != 0) {
          for (let i = 0; i < attendeesList.length; i++) {
            let eventId = attendeesList[i]["event"];
            let match = {
              _id: eventId,
              isDelete: false,
              status: "published",
              relation_id: ObjectId(relation_id),
            }
            
            if (search) {
            search = search;
            let tagIds = []
              const matchingTags = await contentArchive_tag.find({
                name: { $regex: ".*" + search + ".*", $options: "i" },
                isDelete: false
              }).select("_id").lean();
              
              if(matchingTags.length > 0){
                tagIds = matchingTags.map(tag => tag._id);
              }
            match = {
                    ...match,
                    $or: [
                        { title: { $regex: ".*" + search + ".*", $options: "i" }, },
                        { tag: { $in: tagIds } }
                    ]
                };
            }
            if (categoryId){
              match={
                ...match,
                category: ObjectId(categoryId)
              }
            }
            if (city){
              match={
                ...match,
                "location.city": city,
              }
            }
            if (categoryId && subCategoryId){
              match={
                ...match,
                category: ObjectId(categoryId),
                subcategory:ObjectId(subCategoryId)
              }
            }

            let mergedCondition = {
              $and: [
                match,
                ruleCondition
              ]
            };
            if (( attendeesList[i]["event_wise_participant_types_result"]["role"] === "Member" || attendeesList[i]["event_wise_participant_types_result"]["role"] === "Staff" ) && role === "member") {

              let eventData = await event
                .findOne(
                  mergedCondition,
                  {
                    _id: 1,
                    title: 1,
                    thumbnail: 1,
                    eventUrl: 1,
                    startDate: 1,
                    startTime: 1,
                    endDate: 1,
                    endTime: 1,
                    timeZone: 1,
                    location: 1,
                    tag: 1,
                  }
                ).lean();
              
              if (eventData !== null) {
                let eventActivityCount = await eventActivity.countDocuments(
                  { event: eventId, isDelete: false }
                );

                if (
                  eventData.location !== undefined &&
                  eventData.location !== "" &&
                  eventData.location !== null
                ) {
                  eventData = {
                    ...eventData,
                    city: eventData.location.address
                      ? eventData.location.city
                      : null,
                    country: eventData.location.address
                      ? eventData.location.country
                      : null,
                    activityCount: eventActivityCount,
                  };
                  delete eventData.location;
                  eventList.push(eventData);
                } else {
                  location = await eventLocation
                    .findOne({
                      event: eventId,
                      locationVisible: true,
                      isDelete: false,
                    })
                    .lean();
                  delete eventData.location;
                  if (location !== null) {
                    eventData = {
                      ...eventData,
                      city: location ? location.city : null,
                      country: location ? location.country : null,
                      activityCount: eventActivityCount,
                    };
                  } else {
                    eventData = {
                      ...eventData,
                      city: null,
                      country: null,
                      activityCount: eventActivityCount,
                    };
                  }
                  eventList.push(eventData);
                }
              }
            } else {
              let eventData = await event
                .findOne(
                  mergedCondition,
                  {
                    _id: 1,
                    title: 1,
                    thumbnail: 1,
                    eventUrl: 1,
                    startDate: 1,
                    startTime: 1,
                    endDate: 1,
                    endTime: 1,
                    timeZone: 1,
                    location: 1,
                    tag: 1,
                  }
                ).lean();

              if (eventData !== null) {
                let eventActivityCount = await eventActivity.countDocuments(
                  { event: eventId, isDelete: false }
                );

                if (
                  eventData.location !== undefined &&
                  eventData.location !== "" &&
                  eventData.location !== null
                ) {
                  eventData = {
                    ...eventData,
                    city: eventData.location.address
                      ? eventData.location.city
                      : null,
                    country: eventData.location.address
                      ? eventData.location.country
                      : null,
                    activityCount: eventActivityCount,
                  };
                  delete eventData.location;
                  eventList.push(eventData);
                } else {
                  location = await eventLocation
                    .findOne({
                      event: eventId,
                      locationVisible: true,
                      isDelete: false,
                    })
                    .lean();
                  delete eventData.location;
                  if (location !== null) {
                    eventData = {
                      ...eventData,
                      city: location ? location.city : null,
                      country: location ? location.country : null,
                      activityCount: eventActivityCount,
                    };
                  } else {
                    eventData = {
                      ...eventData,
                      city: null,
                      country: null,
                      activityCount: eventActivityCount,
                    };
                  }
                  eventList.push(eventData);
                }
              }
            }
          }
        }

        if (eventList.length > 0) {
          for (let index = 0; index < eventList.length; index++) {
            // Input date and time
            const eventDate = eventList[index].endDate;
            const eventTime = eventList[index].endTime;
            const timeZone = eventList[index].timeZone;

            // Create a new Date object using the input date and time
            const sign = timeZone.substring(4, 5);
            const utcHour = timeZone.substring(5, 7);
            const utcMinute = timeZone.substring(8, 10);
            const hour24Formate = moment(eventTime, "h:mm a").format("HH:mm");

            // saprate date and time in hours and mins
            const year = moment(eventDate, "MM-DD-YYYY").year();
            const month = moment(eventDate, "MM-DD-YYYY").month();
            const day = moment(eventDate, "MM-DD-YYYY").get("date");
            const hours = moment(hour24Formate, "h:mm a").hours();
            const minutes = moment(hour24Formate, "h:mm a").minutes();

            var endDate = new Date(year, month, day, hours, minutes);
            if (sign === "+") {
              endDate = await subtractTime(
                endDate,
                parseInt(utcHour),
                parseInt(utcMinute)
              );
            } else if (sign === "-") {
              endDate = await addTime(
                endDate,
                parseInt(utcHour),
                parseInt(utcMinute)
              );
            }

            if (endDate >= localDate) {
              eventListData.push(eventList[index]);
            }
          }
        }

        for (let i = 0; i < eventListData.length; i++) {
          const event = eventListData[i];
          let isExist = finalEventListData.find(o => o["_id"].toString() === eventListData[i]["_id"].toString());
          if(!isExist){
            finalEventListData.push(event);
          }
        }
      }
    }

    //counts
    let upCommingEventcount = await event.aggregate([
      ...aggregatePipelineUpCommingEvent,
    ]);
    let pastEventcount = await event.aggregate([...aggregatePipelinePastEvent]);
    let myEventcount = finalEventListData ? finalEventListData.length : 0;

    upCommingEventcount = upCommingEventcount ? upCommingEventcount.length : 0;
    pastEventcount = pastEventcount ? pastEventcount.length : 0;
    myEventcount = myEventcount;

    return { upCommingEventcount, pastEventcount, myEventcount };
  } catch (error) {
    console.log(error, "error");
  }
};

exports.getEventCountV2 = async (
  relation_id,
  localDate,
  authUserId,
  eventType,
  categoryId,
  subCategoryId,
  year,
  type,
  search,
  city
) => {
  try {
    var localDate = localDate;
    localDate = moment(localDate, "YYYY-MM-DD").toDate();
    const authUser = authUserId;
    let ruleCondition = await userAccessRulesCommonCondition({
      userId: authUser,
      relation_id: relation_id,
    });

    let matchUpCommingEvent = {
      isDelete: false,
      status: "published",
      Date: { $gt: localDate },
      relation_id: ObjectId(relation_id),
    };
    if (search) {
      matchUpCommingEvent = {
        ...matchUpCommingEvent,
        $or: [
          { title: { $regex: ".*" + search + ".*", $options: "i" } },
          { "tag.name": { $regex: ".*" + search + ".*", $options: "i" } },
        ],
      };
    }

    let matchPastEvent = {
      isDelete: false,
      status: "published",
      Date: { $lt: localDate },
      relation_id: ObjectId(relation_id),
    };

    if (search) {
      matchPastEvent = {
        ...matchPastEvent,
        $or: [
          { title: { $regex: ".*" + search + ".*", $options: "i" } },
          { "tag.name": { $regex: ".*" + search + ".*", $options: "i" } },
        ],
      };
    }    

    //aggregate Upcoming Event
    const aggregatePipelineUpCommingEvent = [
      {
        $addFields: {
          Date: {
            $let: {
              vars: {
                year: { $substr: ["$endDate", 6, 10] },
                month: { $substr: ["$endDate", 0, 2] },
                dayOfMonth: { $substr: ["$endDate", 3, 2] },
                startMinute: { $substr: ["$endTime", 3, 2] },
                startHours: {
                  $toString: {
                    $cond: {
                      if: { $eq: [{ $substr: ["$endTime", 6, 2] }, "am"] },
                      then: {
                        $cond: {
                          if: { $eq: [{ $substr: ["$endTime", 0, 2] }, "12"] },
                          then: "00", // Midnight (12 AM) should be converted to "00"
                          else: { $substr: ["$endTime", 0, 2] }, // No change needed for AM times other than midnight
                        },
                      },
                      else: {
                        $cond: {
                          if: { $eq: [{ $substr: ["$endTime", 0, 2] }, "12"] },
                          then: "12", // Noon (12 PM) should remain "12"
                          else: {
                            $add: [
                              {
                                $toInt: { $substr: ["$endTime", 0, 2] },
                              },
                              12, // Adding 12 to convert PM times to 24-hour format
                            ],
                          },
                        },
                      },
                    },
                  },
                },
              },
              in: {
                $toDate: {
                  $concat: [
                    "$$year",
                    "-",
                    "$$month",
                    "-",
                    "$$dayOfMonth",
                    "T",
                    "$$startHours",
                    ":",
                    "$$startMinute",
                    ":",
                    "00",
                  ],
                },
              },
            },
          },
        },
      },
      {
        $match: {
          ...ruleCondition,
        },
      },
      {
        $lookup: {
          from: "contentarchive_tags",
          localField: "tag",
          foreignField: "_id",
          pipeline: [
            {
              $match: {
                isDelete: false,
              },
            },
          ],
          as: "tag",
        },
      },
      {
        $match: matchUpCommingEvent,
      },
      // match type
      ...(type && eventType === "upcomming"
        ? [
            {
              $match: {
                locationType: type,
              },
            },
          ]
        : []),
      // match city
      ...(city && eventType === "upcomming"
        ? [
            {
              $match: {
                "location.city": city,
              },
            },
          ]
        : []),
      //   // match Category Id
      ...(categoryId && eventType === "upcomming"
        ? [
            {
              $match: {
                category: ObjectId(categoryId),
              },
            },
          ]
        : []),
      //   // match sub Category Id
      ...(categoryId && subCategoryId && eventType === "upcomming"
        ? [
            {
              $match: {
                category: ObjectId(categoryId),
                subcategory: ObjectId(subCategoryId),
              },
            },
          ]
        : []),
    ];

    // aggregate PastEvent
    const aggregatePipelinePastEvent = [
      {
        $addFields: {
          Date: {
            $let: {
              vars: {
                year: { $substr: ["$endDate", 6, 10] },
                month: { $substr: ["$endDate", 0, 2] },
                dayOfMonth: { $substr: ["$endDate", 3, 2] },
                startMinute: { $substr: ["$endTime", 3, 2] },
                startHours: {
                  $toString: {
                    $cond: {
                      if: { $eq: [{ $substr: ["$endTime", 6, 2] }, "am"] },
                      then: {
                        $cond: {
                          if: { $eq: [{ $substr: ["$endTime", 0, 2] }, "12"] },
                          then: "00", // Midnight (12 AM) should be converted to "00"
                          else: { $substr: ["$endTime", 0, 2] }, // No change needed for AM times other than midnight
                        },
                      },
                      else: {
                        $cond: {
                          if: { $eq: [{ $substr: ["$endTime", 0, 2] }, "12"] },
                          then: "12", // Noon (12 PM) should remain "12"
                          else: {
                            $add: [
                              {
                                $toInt: { $substr: ["$endTime", 0, 2] },
                              },
                              12, // Adding 12 to convert PM times to 24-hour format
                            ],
                          },
                        },
                      },
                    },
                  },
                },
              },
              in: {
                $toDate: {
                  $concat: [
                    "$$year",
                    "-",
                    "$$month",
                    "-",
                    "$$dayOfMonth",
                    "T",
                    "$$startHours",
                    ":",
                    "$$startMinute",
                    ":",
                    "00",
                  ],
                },
              },
            },
          },
        },
      },
      ...(year && eventType === "past"
        ? [
            {
              $match: {
                year: year,
              },
            },
          ]
        : []),
      {
        $lookup: {
          from: "contentarchive_tags",
          localField: "tag",
          foreignField: "_id",
          pipeline: [
            {
              $match: {
                isDelete: false,
              },
            },
          ],
          as: "tag",
        },
      },
      {
        $match: matchPastEvent,
      },
      {
        $match: {
          ...ruleCondition,
        },
      },
      ...(city && eventType === "past"
        ? [
            {
              $match: {
                "location.city": city,
              },
            },
          ]
        : []),
      //  match category Id
      ...(city && eventType === "past"
        ? [
            {
              $match: {
                "location.city": city,
              },
            },
          ]
        : []),
      //  match category Id
      ...(categoryId && eventType === "past"
        ? [
            {
              $match: {
                category: ObjectId(categoryId),
              },
            },
          ]
        : []),
      // match subCategory Id
      ...(categoryId && subCategoryId && eventType === "past"
        ? [
            {
              $match: {
                category: ObjectId(categoryId),
                subcategory: ObjectId(subCategoryId),
              },
            },
          ]
        : []),
    ];

    // my event
    const role = "member";
    const userData = await User.findById(authUser).select(
      "firebaseId email accessible_groups purchased_plan attendeeDetail"
    );
    location = {};
    let myEventcount = 0;
    if (userData !== null && userData !== undefined) {
      let condition = { _id: authUser, isDelete: false };
      if (role && role == "nonMember") {
        condition = {
          _id: authUser,
          $or: [{ isDelete: false }, { isDelete: { $exists: false } }],
        };
      }

      const eventAttendeesData = await User.findOne(condition, {
        attendeeDetail: 1,
      });
      if (eventAttendeesData !== null) {
        let conditionTemp = { $ne: ["$role", "Member"] };
        if (role === "member") {
          conditionTemp = { $in: ["$role", ["Member", "Staff"]] };
        }
        let pipeline = [
          {
            $match: {
              // event: eventId,
              user: new ObjectId(authUser),
              isDelete: false,
              relation_id: ObjectId(relation_id),
            },
          },
          {
            $lookup: {
              from: "event_wise_participant_types",
              localField: "role",
              foreignField: "_id",
              as: "event_wise_participant_types_result",
              pipeline: [
                // {
                //   $match: {
                //     $expr: conditionTemp,
                //   },
                // },
              ],
            },
          },
          {
            $unwind: {
              path: "$event_wise_participant_types_result",
              preserveNullAndEmptyArrays: false,
            },
          },
        ];

        let attendeesList = await eventParticipantAttendees.aggregate(pipeline);
        if (attendeesList && attendeesList.length != 0) {
          const eventIds = [
            ...new Set(attendeesList.map((attendee) => attendee.event)),
          ];


          // Create the match condition using $in
          let match = {
            _id: { $in: eventIds },
            isDelete: false,
            status: "published",
            relation_id: ObjectId(relation_id),
          };

          let mergedCondition = {
            ...match,
            ...ruleCondition,
          };

          const aggregatePipeline = [
            {
              $addFields: {
                Date: {
                  $let: {
                    vars: {
                      year: { $substr: ["$endDate", 6, 10] },
                      month: { $substr: ["$endDate", 0, 2] },
                      dayOfMonth: { $substr: ["$endDate", 3, 2] },
                      startMinute: { $substr: ["$endTime", 3, 2] },
                      startHours: {
                        $toString: {
                          $cond: {
                            if: { $eq: [{ $substr: ["$endTime", 6, 2] }, "am"] },
                            then: {
                              $cond: {
                                if: { $eq: [{ $substr: ["$endTime", 0, 2] }, "12"] },
                                then: "00", // Midnight (12 AM) should be converted to "00"
                                else: { $substr: ["$endTime", 0, 2] }, // No change needed for AM times other than midnight
                              },
                            },
                            else: {
                              $cond: {
                                if: { $eq: [{ $substr: ["$endTime", 0, 2] }, "12"] },
                                then: "12", // Noon (12 PM) should remain "12"
                                else: {
                                  $add: [
                                    {
                                      $toInt: { $substr: ["$endTime", 0, 2] },
                                    },
                                    12, // Adding 12 to convert PM times to 24-hour format
                                  ],
                                },
                              },
                            },
                          },
                        },
                      },
                    },
                    in: {
                      $toDate: {
                        $concat: [
                          "$$year",
                          "-",
                          "$$month",
                          "-",
                          "$$dayOfMonth",
                          "T",
                          "$$startHours",
                          ":",
                          "$$startMinute",
                          ":",
                          "00",
                        ],
                      },
                    },
                  },
                },
              },
            },
            {
              $lookup: {
                from: "contentarchive_tags",
                localField: "tag",
                foreignField: "_id",
                pipeline: [
                  {
                    $match: {
                      isDelete: false,
                    },
                  },
                ],
                as: "tag",
              },
            },
            {
              $match: {
                ...mergedCondition,
                Date: { $gte: localDate },
              },
            },
            ...(city && eventType === "myevent"
              ? [
                  {
                    $match: {
                      ...match,
                      "location.city": city,
                    },
                  },
                ]
              : []),
            ...(type && eventType === "myevent"
              ? [
                  {
                    $match: {
                      ...match,
                      locationType: type,
                    },
                  },
                ]
              : []),

            ...(categoryId && eventType === "myevent"
              ? [
                  {
                    $match: {
                      ...match,
                      category: ObjectId(categoryId),
                    },
                  },
                ]
              : []),
            ...(search && eventType === "myevent"
              ? [
                  {
                    $match: {
                      ...match,
                      $or: [
                        { title: { $regex: search, $options: "i" } },
                        {
                          "tag.name": {
                            $regex: search,
                            $options: "i",
                          },
                        },
                      ],
                    },
                  },
                ]
              : []),
            ...(categoryId && subCategoryId && eventType === "myevent"
              ? [
                  {
                    $match: {
                      ...match,
                      category: ObjectId(categoryId),
                      subcategory: ObjectId(subCategoryId),
                    },
                  },
                ]
              : []),
            { $sort: { Date: -1 } },
          ];

          const eventListData = await event.aggregate([
            ...aggregatePipeline,
            {
              $lookup: {
                from: "event_categories",
                localField: "category",
                foreignField: "_id",
                pipeline: [
                  {
                    $lookup: {
                      from: "event_subcategories",
                      localField: "subcategory",
                      foreignField: "_id",
                      as: "subcategory",
                    },
                  },
                  {
                    $match: {
                      isDelete: false,
                    },
                  },
                  {
                    $project: {
                      _id: 1,
                      name: 1,
                      subcategory: 1,
                      relation_id: 1,
                    },
                  },
                ],
                as: "event_categories",
              },
            },
            {
              $lookup: {
                from: "event_subcategories", // Assuming this is your subcategory collection
                localField: "subcategory", // This matches the category's _id
                foreignField: "_id", // Assuming subcategory has a field for categoryId
                as: "subcategory",
              },
            },
            {
              $lookup: {
                from: "event_participant_attendees",
                localField: "_id",
                foreignField: "event",
                as: "event_participant_attendees_result",
                pipeline: [
                  {
                    $match: {
                      $expr: {
                        $eq: [ObjectId(userData._id), "$user"],
                      },
                    },
                  },
                  {
                    $match: {
                      $expr: {
                        $eq: ["$isDelete", false],
                      },
                    },
                  },
                ],
              },
            },
            {
              $project: {
                _id: 1,
                title: 1,
                thumbnail: 1,
                eventUrl: 1,
                startDate: 1,
                startTime: 1,
                endDate: 1,
                endTime: 1,
                timeZone: 1,
                tag: 1,
                category: "$event_categories",
                subcategory: "$subcategory",
              },
            },
          ]);
          myEventcount = eventListData.length; // Set the length here
        }
      }
    }


    let upCommingEventcount = await event.aggregate([
      ...aggregatePipelineUpCommingEvent,
    ]);
    let pastEventcount = await event.aggregate([...aggregatePipelinePastEvent]);

    upCommingEventcount = upCommingEventcount ? upCommingEventcount.length : 0;
    pastEventcount = pastEventcount ? pastEventcount.length : 0;

    return { upCommingEventcount, pastEventcount, myEventcount };
  } catch (error) {
    console.log(error, "error");
  }
};


exports.replaceRestrictionFieldForEvent = async (req, res) => {
  try {
    const eventList = await event.find({isDelete: false });

    let updateData;
    for (const item of eventList) {

      updateData = await event.findOneAndUpdate(
        {
          _id: new ObjectId(item._id),
        },
        {
          $set: { 
              restrictionAccess: item.eventAccess,
              restrictedAccessGroupId: item.restrictedAccessGroups,
              restrictedAccessMembershipPlanId: item.restrictedAccessMemberships,
          }
        },
        { new: true }
      )
     }

    return res.status(200).json({
      status: true,
      message: "replace Restriction Field For Event successfully!",
      data: updateData ,
    });
  } catch (error) {
    return res
      .status(500)
      .json({ status: false, message: "Internal server error!", error: error });
  }
};

exports.getFormFieldDetail = async (req, res) => {
  try {
    const obj = { relation_id: req.relation_id }
    if(!req.params || !req.params.id ||  !ObjectId.isValid(req.params.id)){
      return res.status(200).json({ status: false, message: "Event id is required!", });
    }
    let eventDetailTemp = await event
      .findOne({
        _id: new ObjectId(req.params.id),
        isDelete: false,
      })
    let eventData = {
      _id: eventDetailTemp._id,
      title: eventDetailTemp.title,
      status: eventDetailTemp.status,
      ticketType: eventDetailTemp.ticketType,
      status: eventDetailTemp.status,
      isDelete: eventDetailTemp.isDelete,
    }

    let ticketData = await eventTicket.find(
      { eventId: ObjectId(req.params.id), isDelete: false }, { relation_id: 0, name: 1, type: 1, applicationForm: 1, addons: 1 }
    )
      .populate({
        path: 'applicationForm',
        match: { isDelete: false },
        populate: {
          path: 'fields',
          match: { isDelete: false }
        }
      })
      .lean();
    eventData.tickets = ticketData;
    if (eventData)
      return res.status(200).json({
        status: true,
        message: "Event detail retrive!",
        data: eventData,
      });
    else
      return res.status(200).json({
        status: true,
        message: "Event detail retrive!",
        data: {},
      });
  } catch (e) {
    console.log(e, "e");
    return res
      .status(500)
      .json({ status: false, message: "Internal server error!", error: e });
  }
};

exports.getFormFieldDetailV2 = async (req, res) => {
  try {
    const obj = { relation_id: req.relation_id };
    if (!req.params || !req.params.id || !ObjectId.isValid(req.params.id)) {
      return res
        .status(200)
        .json({ status: false, message: "Event id is required!" });
    }
    let eventDetailTemp = await event.findOne({
      _id: new ObjectId(req.params.id),
      isDelete: false,
    });
    if (eventDetailTemp) {
      let eventData = {
        _id: eventDetailTemp._id,
        title: eventDetailTemp.title,
        status: eventDetailTemp.status,
        ticketType: eventDetailTemp.ticketType,
        status: eventDetailTemp.status,
        isDelete: eventDetailTemp.isDelete,
      };

      let ticketData = await eventTicket
        .find(
          { eventId: ObjectId(req.params.id), isDelete: false },
          { relation_id: 0, name: 1, type: 1, applicationForm: 1, addons: 1 }
        )
        .populate({
          path: "applicationForm",
          match: { isDelete: false },
          populate: {
            path: "fields",
            match: { isDelete: false },
          },
        })
        .lean();
      eventData.tickets = ticketData;
      if (eventData)
        return res.status(200).json({
          status: true,
          message: "Event detail retrive!",
          data: eventData,
        });
      else
        return res.status(200).json({
          status: true,
          message: "Event detail retrive!",
          data: {},
        });
    } else {
      return res.status(400).json({
        status: false,
        message: "Event not found!",
        data: {},
      });
    }
  } catch (e) {
    console.log(e, "e");
    return res
      .status(500)
      .json({ status: false, message: "Internal server error!", error: e });
  }
};
exports.getEventApplicationResponseCount = async (req, res) => {
  try {
    // Check if event ID is provided and valid
    if (!req.params || !req.params.id || !ObjectId.isValid(req.params.id)) {
      return res
        .status(400)
        .json({ status: false, message: "Valid event ID is required!" });
    }

    let eventDetails = await event.findOne({
      _id: new ObjectId(req.params.id),
      isDeleted: false,
    });

    if (!eventDetails) {
      return res.status(404).json({
        status: false,
        message: "Event not found!",
        data: {},
      });
    }

    const aggregationPipeline = [
      {
        $match: {
          eventId: ObjectId(req.params.id),
          applicationForm: { $ne: null },
          isDelete: false, 
        },
      },
      {
        $lookup: {
          from: "application_forms",
          localField: "applicationForm",
          foreignField: "_id",
          as: "applicationFormDetails",
        },
      },
      {
        $unwind: {
          path: "$applicationFormDetails",
          preserveNullAndEmptyArrays: true,
        },
      },
      {
        $lookup: {
          from: "ticket_purchase_v2",
          localField: "_id",
          foreignField: "ticketId",
          as: "ticketPurchases",
          pipeline: [
            {
              $match: {
                isDelete: false, ticketOrderStatus: {
                  $in: [
                    "requires_payment_method",
                    "succeeded"
                  ]
                },
              },
            },
            {
              $lookup: {
                from: "ticket_submissions",
                localField: "_id",
                foreignField: "ticketPurchaseId",
                as: "ticketSubmissions",
              },
            },
          ],
        },
      },
      {
        $group: {
          _id: "$applicationForm",
          applicationFormDetails: { $first: "$applicationFormDetails" },
          tickets: { $push: "$$ROOT" },
        },
      },
      {
        $project: {
          _id: "$applicationFormDetails._id",
          name: "$applicationFormDetails.name",
          createdAt:"$applicationFormDetails.createdAt",
          updatedAt:"$applicationFormDetails.updatedAt",
          tickets: {
            $map: {
              input: "$tickets",
              as: "ticket",
              in: {
                _id: "$$ticket._id",
                name: "$$ticket.name",
                createdAt:"$$ticket.createdAt",
                updatedAt:"$$ticket.updatedAt",
                submissionCount: {
                  $cond: {
                    if: { $isArray: "$$ticket.ticketPurchases.ticketSubmissions" },
                    then: { $size: "$$ticket.ticketPurchases.ticketSubmissions" },
                    else: 0, 
                  },
                },
              },
            },
          },
        },
      },
      {
        $project: {
          _id: 1,
          name: 1,
          createdAt: 1,
          updatedAt: 1,
          tickets: 1,
          ticketCount: { $size: "$tickets" },
          totalSubmissionCount: {
            $sum: {
              $map: {
                input: "$tickets",
                as: "ticket",
                in: "$$ticket.submissionCount",
              },
            },
          },
        },
      },
      {
        $sort: {
          totalSubmissionCount: -1,
        },
      }
    ];

    const reportData = await eventTicket.aggregate(aggregationPipeline);
    

    // Return the event application report data
    return res.status(200).json({
      status: true,
      message: "Event application report generated successfully!",
      data: reportData,
    });
  } catch (error) {
    console.error(error);
    return res.status(500).json({
      status: false,
      message: "Internal server error while generating the report!",
      error: error.message,
    });
  }
};
exports.getEventApplicationResponse = async (req, res) => {
  try {

    if (!req.params || !req.params.id || !ObjectId.isValid(req.params.id)) {
      return res.status(400).json({ status: false, message: "A valid Application ID is required!" });
    }

    let { eventId, ticketId, search, searchDate, page, limit } = req.query;
    let skip = 0;
    
    if (page && limit) {
      skip = (parseInt(page) - 1) * parseInt(limit);
    }

    let applicationFormDetails = await applicationForm.findOne({
      _id: new ObjectId(req.params.id),
      isDeleted: false,
    }).populate({ path: "fields", select: { label: 1 } });
    if (!applicationFormDetails) {
      return res.status(400).json({
        status: false,
        message: "Application form not found!",
        data: {},
      });
    }
    let matchConditions = {
      applicationFormId: ObjectId(req.params.id),
      isDelete: false,
    };

    if (ticketId) {
      let ticketDetails = await eventTicket.findOne({
        _id: new ObjectId(ticketId),
        isDelete: false,
      });
      if (!ticketDetails) {
        return res.status(400).json({
          status: false,
          message: "Ticket not found!",
          data: {},
        });
      }
    }
    if (eventId) {
      let eventDetails = await event.findOne({
        _id: new ObjectId(eventId),
        isDelete: false,
      });
      if (!eventDetails) {
        return res.status(400).json({
          status: false,
          message: "Event not found!",
          data: {},
        });
      }
      matchConditions.eventId = ObjectId(eventId);
    }
    
    if (searchDate) {
      const startDate = new Date(searchDate);
      const endDate = new Date(startDate);
      endDate.setDate(endDate.getDate() + 1);
      matchConditions.createdAt = { $gte: startDate, $lt: endDate };
    }

    const pipeline = [
      { $match: matchConditions },
      {
        $lookup: {
          from: 'airtable-syncs',
          let: { userId: '$userId' },
          pipeline: [
            {
              $match: {
                $expr: { $eq: ['$_id', '$$userId'] },
                isDelete: false,
              },
            },
            {
              $project: { _id: 1, display_name: 1, "Preferred Email": 1 },
            },
          ],
          as: 'userInfo',
        },
      },
      {
        $unwind: {
          path: '$userInfo',
          preserveNullAndEmptyArrays: true,
        },
      },
      ...(search ? [
        {
          $match: {
            $or: [
              { 'userInfo.display_name': { $regex: search, $options: 'i' } },
              { 'userInfo.Preferred Email': { $regex: search, $options: 'i' } },
            ],
          },
        },
      ] : []),
      {
        $lookup: {
          from: 'application_forms',
          localField: 'applicationFormId',
          foreignField: '_id',
          as: 'formInfo',
        },
      },
      {
        $lookup: {
          from: 'ticket_purchase_v2',
          localField: 'ticketPurchaseId',
          foreignField: '_id',
          as: 'ticketPurchaseInfo',
        },
      },
      {
        $match: {
          'ticketPurchaseInfo.isDelete': false,
          'ticketPurchaseInfo.ticketOrderStatus': {
            $in: [
              'requires_payment_method',
              'succeeded'
            ]
          },
          ...(ticketId ? {
            'ticketPurchaseInfo.ticketId': ObjectId(ticketId),
          } : {}),
        },
      },
      { $sort: { createdAt: -1 } },
      ...(skip > 0 ? [
        { $skip: skip },
        { $limit: parseInt(limit) },
      ] : []),
    ];

    const submissions = await eventTicketSubmission.aggregate(pipeline);

    const total = submissions.length;

    const formattedSubmissions = submissions.map((submission) => {
      return {
        id: submission._id,
        name: submission.userInfo.display_name,
        profileImg: submission.userInfo.profileImg || "",
        email: submission.userInfo["Preferred Email"],
        date: submission.createdAt,
        questions: `${submission.fields.length} of ${applicationFormDetails.fields.length}`, 
        answers: submission.fields.map(field => ({
          label: field.label,
          value: field.response,
        })),
      };
    });

    let responseData = {
      status: true,
      message: "Event application responses fetched successfully!",
      data: {
        fields: applicationFormDetails.fields,
        submissionsData: formattedSubmissions,
      },
    };

    if (page && limit) {
      responseData = {
        ...responseData,
        totalPages: Math.ceil(total / limit),
        currentPage: parseInt(page),
        totalSubmissions: total,
      };
    }

    return res.status(200).json({...responseData});

  } catch (error) {
    console.error(error);
    return res.status(500).json({
      status: false,
      message: "An internal server error occurred while generating the report.",
      error: error.message,
    });
  }
};
exports.getEventApplicationResponseSuggestion = async (req, res) => {
  try {

    if (!req.params || !req.params.id || !ObjectId.isValid(req.params.id)) {
      return res.status(400).json({ status: false, message: "A valid Application ID is required!" });
    }

    let { eventId } = req.query;

    let applicationFormDetails = await applicationForm.findOne({
      _id: new ObjectId(req.params.id),
      isDeleted: false,
    })
    if (!applicationFormDetails) {
      return res.status(400).json({
        status: false,
        message: "Application form not found!",
        data: {},
      });
    }

    let matchConditions = {
      applicationFormId: ObjectId(req.params.id),
      isDelete: false,
    };
    
    if (eventId) {
      let eventDetails = await event.findOne({
        _id: new ObjectId(eventId),
        isDelete: false,
      });
      if (!eventDetails) {
        return res.status(400).json({
          status: false,
          message: "Event not found!",
          data: {},
        });
      }
      matchConditions.eventId = ObjectId(eventId);
    }
    const pipeline = [
      { $match: matchConditions },
      {
        $lookup: {
          from: 'airtable-syncs',
          let: { userId: '$userId' },
          pipeline: [
            {
              $match: {
                $expr: { $eq: ['$_id', '$$userId'] },
                isDelete: false,
              },
            },
            {
              $project: { _id: 1, display_name: 1, "Preferred Email": 1 },
            },
          ],
          as: 'userInfo',
        },
      },
      {
        $unwind: {
          path: '$userInfo',
          preserveNullAndEmptyArrays: true,
        },
      },
      {
        $group: {
          _id: '$userId',
          display_name: { $first: '$userInfo.display_name' },
          email: { $first: '$userInfo.Preferred Email' },
        },
      },
      {
        $project: {
          _id: 1,
          display_name: 1,
          email: 1,
        },
      }
    ];

    const submissions = await eventTicketSubmission.aggregate(pipeline);

    let responseData = {
      status: true,
      message: "Event application responses suggestion fetched successfully!",
      data: submissions,
    };
    return res.status(200).json(responseData);

  } catch (error) {
    console.error(error);
    return res.status(500).json({
      status: false,
      message: "An internal server error occurred while generating the report.",
      error: error.message,
    });
  }
};
exports.getAllFromsById = async (req, res) => {
  try {

    if (!req.params || !req.params.id || !ObjectId.isValid(req.params.id)) {
      return res.status(400).json({ status: false, message: "A valid event ID is required!" });
    }

    let eventDetails = await event.findOne({
      _id: new ObjectId(req.params.id),
      isDeleted: false,
    });

    if (!eventDetails) {
      return res.status(400).json({
        status: false,
        message: "Event not found!",
        data: {},
      });
    }
    
    const aggregationPipeline = [
      {
        $match: {
          eventId: ObjectId(req.params.id),
          applicationForm: { $ne: null },
          isDelete: false, 
        },
      },
      {
        $lookup: {
          from: "application_forms",
          localField: "applicationForm",
          foreignField: "_id",
          as: "applicationFormDetails",
        },
      },
      {
        $unwind: {
          path: "$applicationFormDetails",
          preserveNullAndEmptyArrays: true,
        },
      },
      {
        $group: {
          _id: "$applicationForm",
          applicationFormDetails: { $first: "$applicationFormDetails" },
          tickets: { $push: "$$ROOT" },
        },
      },
      {
        $project: {
          _id: "$applicationFormDetails._id",
          name: "$applicationFormDetails.name",
        },
      },
      {
        $project: {
          _id: 1,
          name: 1,
        },
      },
      {
        $sort: {
          name: 1,
        },
      }
    ];

    const result = await eventTicket.aggregate(aggregationPipeline);

    return res.status(200).json({
      status: true,
      message: "Event application forms fetched successfully!",
      data: result,
    });

  } catch (error) {
    console.error(error);
    return res.status(500).json({
      status: false,
      message: "An internal server error occurred while generating the report.",
      error: error.message,
    });
  }
};
// helper function for Escape special characters
const escapeRegExp = (string) => {
  return string.replace(/[.*+?^=!:${}()|\[\]\/\\]/g, '\\$&'); 
};