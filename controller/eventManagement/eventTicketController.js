const eventTicket = require("../../database/models/eventTicketsManagement/eventTicket");
const event = require("../../database/models/event");
const ticketPurchase = require("../../database/models/eventTicketsManagement/ticketPurchase");
const User = require("../../database/models/airTableSync");
const EventParticipantAttendees = require("../../database/models/eventParticipantAttendees");
const eventWiseParticipantTypes = require("../../database/models/eventWiseParticipantTypes");
const debugErrorLogs = require("../../middleware/debugErrorLogs");
const {newGeneratePresignedURL} = require("../mediaUploadController/mediaUploadController")
const { sendEmail, sendEmailAdmin } = require("../../config/common");
const { userAccessRulesCommonConditionForTicket } = require("../../controller/userAccessRules/eventTicket");
const { getEventTicketRegistrationEmailTemplate } = require("../../controller/eventManagement/eventTickerEmailController");
const { deleteImage, copyTicketThumbnail } = require("../../utils/mediaUpload");
const { getAllTiersfromBilling } = require("../userAccessRules/tiers");
const { ObjectId } = require("mongodb");
const AWS = require("aws-sdk");
var s3 = new AWS.S3({
  accessKeyId: process.env.AWS_ID,
  secretAccessKey: process.env.AWS_SECRET,
  Bucket: process.env.AWS_BUCKET,
  signatureVersion: 'v4',
  region: process.env.AWS_REGION
});
const TicketPayment = require("../../database/models/eventTicketsManagement/ticketPayment");
const S3FileUpload = require("../../libraries/file-upload-service");
const ticketPayment = require("../../database/models/eventTicketsManagement/ticketPayment");
const ticketPurchaseV2 = require("../../database/models/eventTicketsManagement/ticketPurchaseV2");
const s3fileUploadService = new S3FileUpload();

// create event ticket from admin side
exports.createEventTicket = async (req, res) => {
  try {
    let { name, type, quantity, basePrice, isAbsorbPlatformFee, isSpecialPrice, salesPriceType, salesValue, salesStartDate,
      salesEndDate, description, isVisibilityAccessWithCode, visibilityType, visibilityStartDate, visibilityEndDate,
      minimumTicket, maximumTicket, ticketAccess, restrictedAccessGroups, restrictedAccessMemberships, restrictedIndivisualUser,
      applicationForm, isCancellation, cancellationDaysBeforeEvent, isGuestAllow, guestPerTicket, guestTickets, isAddOnAvailable,
      selectAddons, addons, eventId, restrictedAccessTiers, ticketFor } = req.body

      // const { maxTicketPurchase } = await event.findById(eventId).lean();
      // if (maxTicketPurchase != null) {
      //   const maximumTicket = parseInt(req.body.maximumTicket, 10);
      //   // Check if maximumTicket exceeds maxTicketPurchase
      //   if (maximumTicket > maxTicketPurchase) {
      //     return res.status(400).json({
      //       message: `Cannot create ticket, maximum ticket purchase limit is ${maxTicketPurchase}.`
      //     });
      //   }
      // }
      
    const ids = await eventTicket.find({ eventId: eventId, isDelete: false }, { _id: 1, order: 1, name:1}).sort({ order: -1 });
    let ticketOrder = (ids && ids.length > 0) ? ids[0].order + 1 : 1;
    let actualPrice = ""
    salesValue = +salesValue;
    basePrice = +basePrice;
    // Use reduce to filter unique addons by ID
    addons = addons
      ? (Array.isArray(addons) ? addons : JSON.parse(addons))
        .reduce((uniqueItems, item) => {
          if (item && !uniqueItems.set.has(item)) {
            uniqueItems.set.add(item);
            uniqueItems.array.push(item);
          }
          return uniqueItems;
        }, { set: new Set(), array: [] }).array
      : [];
    if (!name) {
      return res
        .status(200)
        .json({ status: false, message: `Name is required!` });
    } else {
      const uniqueName = ids.some(ticket => ticket.name === name);
      if (uniqueName) {
        return res.status(400).json({
          status: false,
          message: "This ticket name already exists.",
        })
      } else {
        if (type == "PAID") {
          if (isSpecialPrice === true || isSpecialPrice === "true") {
            if (salesPriceType && salesValue && ((salesPriceType === "FLAT" && salesValue >= 0 && salesValue < basePrice) || (salesPriceType === "PERCENTAGE" && salesValue <= 100))) {
              if (salesPriceType === "FLAT") {
                actualPrice = basePrice - salesValue
              } else {
                actualPrice = Math.round(basePrice - (basePrice * salesValue / 100));
              }
            } else {
              return res.status(400).json({ status: false, message: `Please give valid data!` });
            }
          } else {
            salesValue = null;
            actualPrice = basePrice;
          }
        } else {
          basePrice = 0;
          isSpecialPrice = false;
          salesValue = null;
          actualPrice = 0;
        }
        // Check if quantity is within the order limits
        // if (maximumTicket && minimumTicket &&
        //   (+quantity > +maximumTicket && +minimumTicket > 0 && +quantity > +minimumTicket && +minimumTicket <= +maximumTicket))
        if (maximumTicket && minimumTicket &&
          (+quantity >= +maximumTicket && +quantity >= +minimumTicket && +minimumTicket <= +maximumTicket)) {
          const newEventTicket = new eventTicket({
            name: name,
            type: type,
            quantity: quantity,
            basePrice: basePrice,
            isAbsorbPlatformFee: isAbsorbPlatformFee,
            isSpecialPrice: isSpecialPrice,
            salesPriceType: salesPriceType,
            salesValue: salesValue,
            actualPrice: actualPrice,
            salesStartDate: salesStartDate,
            salesEndDate: salesEndDate,
            description: description,
            thumbnail: req.thumbnail,
            isVisibilityAccessWithCode: isVisibilityAccessWithCode,
            visibilityType: visibilityType,
            visibilityStartDate: visibilityStartDate,
            visibilityEndDate: visibilityEndDate,
            minimumTicket: minimumTicket,
            maximumTicket: maximumTicket,
            ticketAccess: ticketAccess,
            restrictedAccessGroups: restrictedAccessGroups ? restrictedAccessGroups : null,
            restrictedAccessMemberships: restrictedAccessMemberships ? restrictedAccessMemberships : null,
            restrictedIndivisualUser: restrictedIndivisualUser ? restrictedIndivisualUser : null,
            restrictedAccessTiers: restrictedAccessTiers ? restrictedAccessTiers : null,
            applicationForm: applicationForm || null,
            isCancellation: isCancellation,
            cancellationDaysBeforeEvent: cancellationDaysBeforeEvent,
            isGuestAllow: isGuestAllow,
            guestPerTicket: guestPerTicket,
            guestTickets: guestTickets,
            isAddOnAvailable: isAddOnAvailable,
            // selectAddons: selectAddons,
            addons: addons,
            order: ticketOrder,
            eventId: eventId,
            ticketFor: ticketFor ? ticketFor : "ALL",
            relation_id: ObjectId(req.relation_id),
          });

          const eventTicketData = await newEventTicket.save();
          if (!eventTicketData) {
            return res.status(200).send({
              status: false,
              message: `Something went wrong while creating event ticket!`,
            });
          } else {
            return res.status(200).send({
              message: `Event ticket created successfully`,
              data: eventTicketData,
            });
          }
        } else {
          return res.status(400).json({ status: false, error: `Please enter correct information about quantity.` });
        }
      }
    }
  } catch (error) {
    await debugErrorLogs.createErrorLogs(error, "createEventTicket", {});
    return res.status(500).json({ status: false, message: `${error.message}` });
  }
};

//Edit event Ticket (By admin)
exports.editEventTicket = async (req, res) => {
  try {
    const { id } = req.params;
    let { name, type, quantity, basePrice, isAbsorbPlatformFee, isSpecialPrice, salesPriceType, salesValue, salesStartDate,
      salesEndDate, description, isVisibilityAccessWithCode, visibilityType, visibilityStartDate, visibilityEndDate,
      minimumTicket, maximumTicket, ticketAccess, restrictedAccessGroups, restrictedAccessMemberships, restrictedIndivisualUser, applicationForm,
      isCancellation, cancellationDaysBeforeEvent, isGuestAllow, guestPerTicket, guestTickets, isAddOnAvailable,
      selectAddons, addons, restrictedAccessTiers,eventId, ticketFor} = req.body

      // const { maxTicketPurchase } = await event.findById(eventId).lean();
      // if (maxTicketPurchase != null) {
      //   const maximumTicket = parseInt(req.body.maximumTicket, 10);
      //   // Check if maximumTicket exceeds maxTicketPurchase
      //   if (maximumTicket > maxTicketPurchase) {
      //     return res.status(400).json({
      //       message: `Cannot create ticket, maximum ticket purchase limit is ${maxTicketPurchase}.`
      //     });
      //   }
      // }
    let actualPrice = ""
    salesValue = +salesValue;
    basePrice = +basePrice;
    minimumTicket = parseInt(minimumTicket);
    maximumTicket = parseInt(maximumTicket);

    // Use reduce to filter unique addons by ID
    addons = addons
      ? (Array.isArray(addons) ? addons : JSON.parse(addons))
        .reduce((uniqueItems, item) => {
          if (item && !uniqueItems.set.has(item)) {
            uniqueItems.set.add(item);
            uniqueItems.array.push(item);
          }
          return uniqueItems;
        }, { set: new Set(), array: [] }).array
      : [];
    // fetch ticket from that id 
    const existTicket = await eventTicket
      .findOne({ _id: new ObjectId(id), relation_id: ObjectId(req.relation_id), isDelete: false })
      .lean();
    if (!existTicket) {
      return res
        .status(200)
        .send({ status: false, message: `Ticket is not found!` });
    } else {
      let isExist = await eventTicket.aggregate([
        { $addFields: { nameLower: { $toLower: "$name" } } },
        {
          $match: {
            _id: { $nin: [ObjectId(id)] },
            isDelete: false,
            nameLower: name.toLowerCase(),
            eventId: existTicket.eventId
          },
        },
      ]);
      if (isExist && isExist.length != 0) {
        return res.status(200).json({
          status: false,
          message: "This guest ticket name already exists.",
        });
      } else {



        // need to check with quantity should not less then already sold tickets
        // console.log({quantity, })

        // let currentSoldTickets = await eventTicket.aggregate([
        //   {
        //     $match:
        //       {
        //         _id: new ObjectId(id)
        //       }
        //   },
        //   {
        //     $lookup: {
        //       from: "ticket_purchases",
        //       localField: "_id",
        //       foreignField: "ticketId",
        //       as: "sold_ticket",
        //       pipeline: [
        //         {
        //           $lookup: {
        //             from: "event_participant_attendees",
        //             localField: "userParticipantId",
        //             foreignField: "_id",
        //             as: "event_participant_attendees",
        //             pipeline: [
        //               {
        //                 $match: {
        //                   $expr: {
        //                     $and: [
        //                       {
        //                         $eq: ["$isDelete", false]
        //                       }
        //                     ]
        //                   }
        //                 }
        //               }
        //             ]
        //           }
        //         },
        //         {
        //           $unwind: {
        //             path: "$event_participant_attendees",
        //             preserveNullAndEmptyArrays: false
        //           }
        //         },
        //         {
        //           $match: {
        //             $expr: {
        //               $and: [
        //                 {
        //                   $eq: ["$isDelete", false]
        //                 },
        //                 {
        //                   $eq: [
        //                     "$ticketOrderStatus",
        //                     "success"
        //                   ]
        //                 }
        //               ]
        //             }
        //           }
        //         },
        //         {
        //           $project: {
        //             purchaseQuantity: 1,
        //             _id: 0
        //           }
        //         },
        //         {
        //           $group: {
        //             _id: null,
        //             sold_ticket: {
        //               $sum: "$purchaseQuantity"
        //             }
        //           }
        //         },
        //         {
        //           $project: {
        //             _id: 0
        //           }
        //         }
        //       ]
        //     }
        //   },
        //   {
        //     $unwind: {
        //       path: "$sold_ticket",
        //       preserveNullAndEmptyArrays: true
        //     }
        //   },
        //   {
        //     $project:
        //       /**
        //        * specifications: The fields to
        //        *   include or exclude.
        //        */
        //       {
        //         _id: 0,
        //         sold_ticket: "$sold_ticket.sold_ticket"
        //       }
        //   }
        // ]);

        currentSoldTickets = existTicket.quantity - existTicket.availableQuantity;

        if(currentSoldTickets > quantity) {
          return res.status(200).json({ status: false, message: `Quantity can't be less then sold tickets!` });
        }
        
        // if(quantity <= maximumTicket || minimumTicket < 1 || quantity <= currentSoldTickets?.sold_ticket || maximumTicket <= minimumTicket ){
        //   return res.status(200).json({ status: false, message: `Quantity invalid!` });
        // }

        if( quantity < minimumTicket || quantity < maximumTicket || quantity <= currentSoldTickets || maximumTicket < minimumTicket ){
          return res.status(200).json({ status: false, message: `Quantity invalid!` });
        }


        if (type == "PAID") {
          if (isSpecialPrice == true || isSpecialPrice == "true") {
            if (salesPriceType && salesValue && ((salesPriceType == "FLAT" && salesValue >= 0 && salesValue < basePrice) || (salesPriceType == "PERCENTAGE" && salesValue <= 100))) {
              if (salesPriceType == "FLAT") {
                actualPrice = basePrice - salesValue
              } else {
                actualPrice = Math.round(basePrice - (basePrice * salesValue / 100));
              }
            } else {
              return res.status(400).json({ status: false, message: `Please give valid data!` });
            }
          } else if ((isSpecialPrice == false || isSpecialPrice == "false") && salesValue) {
            return res.status(400).json({ status: false, message: `Please give valid data!` });
          } else {
            salesValue = null;
            actualPrice = basePrice;
          }
        } else {
          basePrice = 0;
          isSpecialPrice = false;
          salesValue = null;
          actualPrice = 0;
        }
        // Check if quantity is within the order limits
        // if (maximumTicket && minimumTicket &&
        //   (+quantity > +maximumTicket && +minimumTicket > 0 && +quantity > +minimumTicket && +minimumTicket <= +maximumTicket))
        if (maximumTicket && (minimumTicket || minimumTicket === 0) && (+quantity >= +maximumTicket && +quantity >= +minimumTicket && +minimumTicket <= +maximumTicket)) {
            // If thumbnail is available then previous was delete from bucket
            if (req.thumbnail && existTicket.thumbnail && existTicket.thumbnail.length > 0) {
              deleteImage(existTicket.thumbnail);
            }
            
          //  pushing price history if any change detected on price for event update
            const editEventTicketPriceHistory = existTicket?.actualPrice !== actualPrice ? eventTicket.findByIdAndUpdate(
              id,
              {
                $push: {
                  ticketPriceHistory: {
                    oldPrice: existTicket?.actualPrice,
                    newPrice: actualPrice,
                    updatedAt: Date.now()
                  }
                }
              },
              {
                new: true
              }
            ) : Promise.resolve("Not adding into priceHistory");

          // Edit event Ticket by Id get data from body if not then it saved already exist ticket data
          const [editEventTicket, ticketPriceHistory ]= await 
          Promise.all(
            [
              eventTicket.findByIdAndUpdate(
                  id,
                  {
                    name: name ?? existTicket.name,
                    type: type ?? existTicket.type,
                    quantity: quantity ?? existTicket.quantity,
                    availableQuantity: quantity - currentSoldTickets, 
                    basePrice: basePrice ?? existTicket.basePrice,
                    isAbsorbPlatformFee: isAbsorbPlatformFee ?? existTicket.isAbsorbPlatformFee,
                    isSpecialPrice: isSpecialPrice ?? existTicket.isSpecialPrice,
                    salesPriceType: (type == "FREE") ? null : salesPriceType ?? existTicket.salesPriceType,
                    salesValue: (salesValue === null) ? null : (salesValue !== undefined) ? salesValue : existTicket.salesValue,
                    actualPrice: actualPrice ?? existTicket.actualPrice,
                    salesStartDate: (type == "FREE") ? null : salesStartDate ?? existTicket.salesStartDate,
                    salesEndDate: (type == "FREE") ? null : salesEndDate ?? existTicket.salesEndDate,
                    description: description ?? existTicket.description,
                    thumbnail: req.thumbnail ?? existTicket.thumbnail,
                    isVisibilityAccessWithCode: isVisibilityAccessWithCode ?? existTicket.isVisibilityAccessWithCode,
                    visibilityType: visibilityType ?? existTicket.visibilityType,
                    visibilityStartDate: visibilityStartDate ?? existTicket.visibilityStartDate,
                    visibilityEndDate: visibilityEndDate ?? existTicket.visibilityEndDate,
                    minimumTicket: minimumTicket ?? existTicket.minimumTicket,
                    maximumTicket: maximumTicket ?? existTicket.maximumTicket,
                    ticketAccess: ticketAccess ?? existTicket.ticketAccess,
                    restrictedAccessGroups: restrictedAccessGroups ? restrictedAccessGroups ?? existTicket.restrictedAccessGroups : null,
                    restrictedAccessMemberships: restrictedAccessMemberships ? restrictedAccessMemberships ?? existTicket.restrictedAccessMemberships : null,
                    restrictedIndivisualUser: restrictedIndivisualUser ? restrictedIndivisualUser ?? existTicket.restrictedIndivisualUser : null,
                    restrictedAccessTiers: restrictedAccessTiers ? restrictedAccessTiers ?? existTicket.restrictedAccessTiers : null,
                    applicationForm: applicationForm ? applicationForm ?? existTicket.applicationForm : null,
                    isCancellation: isCancellation ?? existTicket.isCancellation,
                    cancellationDaysBeforeEvent: cancellationDaysBeforeEvent ?? existTicket.cancellationDaysBeforeEvent,
                    isGuestAllow: isGuestAllow ?? existTicket.isGuestAllow,
                    guestPerTicket: guestPerTicket ?? existTicket.guestPerTicket,
                    guestTickets: guestTickets ?? existTicket.guestTickets,
                    isAddOnAvailable: isAddOnAvailable ?? existTicket.isAddOnAvailable,
                    // selectAddons: selectAddons ?? existTicket.selectAddons,
                    addons: addons.length > 0 ? addons : existTicket.addons,
                    ticketFor: ticketFor ?? existTicket.ticketFor,
                  },
                  { new: true }
                ), 
                editEventTicketPriceHistory
              ]
            );
          if (!editEventTicket) {
            return res.status(200).send({
              status: false,
              message: `Something went wrong while edit event ticket!`,
            });
          } else {
            return res.status(200).send({
              message: `Event ticket edited successfully`,
              data: editEventTicket,
            });
          }
        } else {
          return res.status(400).json({ status: false, error: `Please enter correct information about quantity.` });
        };
      }
    }
  } catch (error) {
    await debugErrorLogs.createErrorLogs(error, "editEventTicket", {});
    return res.status(500).json({ status: false, message: `${error.message}` });
  }
};

// delete event Ticket (By admin - soft delete
exports.deleteEventTicket = async (req, res) => {
  try {
    const { id } = req.params;
    const existTicket = await eventTicket
      .findOne({ _id: new ObjectId(id), relation_id: ObjectId(req.relation_id), isDelete: false })
      .lean();
    if (!existTicket) {
      return res
        .status(200)
        .send({ status: false, message: `Ticket is not found!` });
    } else {
      const purchaseTicket = await ticketPurchase.findOne({ ticketId: new ObjectId(id), isDelete: false });
      if (purchaseTicket) {
        return res.status(200).json({
          status: false,
          message: "This particular ticket is not eligible for deletion as it has been acquired by a customer through purchase.",
        })
      } else {
        const deleteEventTicket = await eventTicket.findByIdAndUpdate(
          id,
          { isDelete: true },
          { new: true }
        );
        if (!deleteEventTicket)
          return res.status(200).send({
            status: false,
            message: "Something went wrong while deleteing event ticket!",
          });
        else
          return res.status(200).send({
            status: true,
            message: "Event ticket deleted successfully!",
          });
      }
    }
  } catch (error) {
    await debugErrorLogs.createErrorLogs(error, "deleteEventTicket", {});
    return res.status(500).json({ status: false, message: `${error.message}` });
  }
};

// Reorder ticket by event Id
exports.reorderTicket = async (req, res) => {
  try {
    const { eventId } = req.params;
    const { ids } = req.body;
    // Check if each id is a valid ObjectId
    for (const item of ids) {
      if (!ObjectId.isValid(item)) {
        return res.status(400).send({ status: false, message: "Invalid ID!" });
      }
    }
    // Count total number of tickets for the event where isDelete is false
    const ticketData = await eventTicket.countDocuments({ eventId: eventId, relation_id: ObjectId(req.relation_id), isDelete: false });
    if (ids.length !== ticketData) {
      return res.status(400).json({ status: false, message: "Please give valid data!" });
    } else {
      let reOrder = ids.map(async (item, i) => {
        await eventTicket.findByIdAndUpdate({ _id: ObjectId(item), eventId: eventId }, { order: i + 1 }, { new: true })
      });
      await Promise.all([...reOrder]);
      if (!reOrder) {
        return res.status(200).json({ status: false, message: "Something went wrong while getting tickets!" });
      } else {
        return res.status(200).send({
          status: true,
          message: "Ticket list rearrange succesfully!",
        });
      }
    }
  } catch (error) {
    await debugErrorLogs.createErrorLogs(error, "reorderTicket", {});
    return res.status(500).json({ status: false, message: `${error.message}` });
  }
};


exports.getPurchasedTicketsByEventidOfUser = async(req,res) => {
  try {
    const { eventId }= req.params;
    const userId = req?.userId;

    const aggregate = [
      {
        $match: {
          userId: ObjectId(
            userId
          ),
          eventId: ObjectId(
            eventId
          ),
        }
      },
      {
        $lookup: {
          from: "event_tickets",
          localField: "ticketId",
          foreignField: "_id",
          as: "ticketId"
        }
      },
      {
        $unwind: "$ticketId"
      },
      {
        $sort: {
          createdAt: -1
        }
      },
      {
        $project: {
          paymentIntent: 0,
        }
      }
    ];
    const data = await TicketPayment.aggregate(aggregate);
    
    async function processData(data) {
      const arr = await Promise.all(data.map(async (value) => {
        const thumbnailUrl = await s3fileUploadService.generatePresignedUrl({key: value?.ticketId?.thumbnail});
        
        return {
          ...value,
          ticketId: {
            ...value.ticketId,
            thumbnail: thumbnailUrl,
          }
        };
      }));
    
      return arr;
    }

    // pre-signed url generation for thumbnail
    const processedData = await processData(data);

    if(data.length !== 0){
      return res.status(200).json({
        status: true,
        message: "Fetched user's purchased tickets!",
        data: processedData,
      });
    } else {
      return res.status(404).json({
        status: false,
        message: "User don't have purchased tickets!",
        data: [],
      })
    }
  }catch (error) {
    await debugErrorLogs.createErrorLogs(error, "getAllEventTickets", {});
    return res.status(500).json({ status: false, message: `${error.message}` });
  }
}

// get all event ticket by event id for indivisual event
exports.getAllEventTickets = async (req, res) => {
  try {
    const { eventId } = req.params;
    let inputQuery = req.query;
    let search = inputQuery.search;
    let page = inputQuery.page ? +inputQuery.page : 1;
    let limit = inputQuery.limit ? +inputQuery.limit : 10;
    let skip = (page - 1) * limit;
    const addonId = inputQuery.addon;
    const guestTicketId = inputQuery.guestTicket;
    const filter = inputQuery.type ? inputQuery.type : "All";
    const dateFilter = inputQuery.filterType;
    const status = inputQuery.visibilityFilter ? inputQuery.visibilityFilter : "All";
    const sortField = req.query.sortField ? req.query.sortField : "order";
    const sortType = req.query.sortType === "Asc" ? 1 : -1;
    let sortTemp = {};
    const applicationFormId = inputQuery.applicationForm;

    // Default sorting on order of ticket in assending order
    if (sortField == "order" && (!req.query || !req.query.sortType)) {
      sortTemp[sortField] = 1;
    } else {
      sortTemp[sortField] = sortType;
    }

    // Define a list of valid sort fields   name, description, min price, type, status
    const validSortFields = ['name', 'description', 'basePrice', 'order', 'type', 'visibilityType', 'createdAt', 'updatedAt', 'available_guest_ticket'];
    if (!validSortFields.includes(sortField)) {
      return res.status(400).json({ status: false, message: "Invalid sort field" });
    }

    let match = { eventId: ObjectId(eventId), relation_id: ObjectId(req.relation_id), isDelete: false };
    // Apply filter for type
    if (filter !== "All") {
      match = {
        ...match,
        type: { $eq: filter },
      };
    }

    // Apply filter based on addon id
    if (addonId && ObjectId.isValid(addonId)) {
      match = {
        ...match,
        addons: { $eq: new ObjectId(addonId) },
      }
    };

    // Apply filter based on guest ticket
    if (guestTicketId && ObjectId.isValid(guestTicketId)) {
      match = {
        ...match,
        guestTickets: { $eq: new ObjectId(guestTicketId) },
      }
    };

    // Apply filter for ticket visibility
    if (status !== "All") {
      match = {
        ...match,
        visibilityType: { $eq: status },
      };
    }

    // Apply search
    if (search) {
      match["$or"] = [
        { name: { $regex: ".*" + search + ".*", $options: "i" } },
        // { description: { $regex: ".*" + search + ".*", $options: "i" } }
      ];
    }

    // Apply filter for saleStartDate and saleEndDate
    if (dateFilter) {
      let addFilterCount = 0;
      let toDate = new Date();
      let fromDate = new Date(toDate.toJSON().slice(0, 10));
      switch (dateFilter) {
        case "todays":
          break;
        case "past7days":
          addFilterCount = 6;
          break;
        case "past30days":
          addFilterCount = 29;
          break;
        case "past90days":
          addFilterCount = 89;
          break;
        case "past365days":
          addFilterCount = 364;
          break;
        case "custom":
          fromDate = new Date(req.query.reqFromDate);
          toDate = new Date(req.query.reqToDate);
          break;
        default:
          // Handle unsupported filter types or invalid values
          console.error("Unsupported date filter type:", dateFilter);
          break;
      }
      fromDate.setDate(fromDate.getDate() - addFilterCount);
      match = {
        ...match,
        createdAt: { $gte: fromDate, $lt: toDate  },
      };
    }

     // Apply filter based on applicationForm id
     if (applicationFormId && ObjectId.isValid(applicationFormId)) {
      match = {
        ...match,
        applicationForm: { $eq: new ObjectId(applicationFormId) },
      }
    };

    //pipeline  for aggregate function
    let pipeline = [
      { $match: match },
      {
        $lookup: {
          from: 'event_guest_tickets',
          localField: 'guestTickets',
          foreignField: '_id',
          as: 'selectGuestTicket',
          pipeline: [
            {
              $match: {
                $expr: {
                  $eq: [
                    "$isDelete", false
                  ]
                }
              }
            },
            {
              $project: {
                isDelete: 0,
                createdAt: 0,
                updatedAt: 0,
                __v: 0
              }
            }
          ]
        }
      },
      // {
      //   $lookup: {
      //     from: 'event_addons',
      //     localField: 'selectAddons',
      //     foreignField: '_id',
      //     as: 'selectAddon',
      //     pipeline: [
      //       {
      //         $match: {
      //           $expr: {
      //             $eq: [
      //               "$isDelete", false
      //             ]
      //           }
      //         }
      //       },
      //       {
      //         $project: {
      //           isDelete: 0,
      //           createdAt: 0,
      //           updatedAt: 0,
      //           __v: 0
      //         }
      //       }
      //     ]
      //   }
      // },
       {
        $lookup: {
          from: 'event_addon_v2',
           localField: 'addons',
          foreignField: '_id',
           as: 'addons',
          pipeline: [
            {
              $match: {
                $expr: {
                  $eq: [
                    "$isDelete", false
                  ]
                }
              }
            },
            {
              $sort: { order: 1 }  // Sort by 'order' field in ascending order
            },
            {
              $project: {
                isDelete: 0,
                createdAt: 0,
                updatedAt: 0,
                __v: 0
              }
            }
          ]
        }
      },
      {
        $lookup: {
          from: "ticket_purchase_v2",
          let: {
            ticket: "$_id",
            event: "$eventId"
          },
          as: "soldTicket",
          pipeline: [
            {
              $match: {
                $expr: {
                  $and: [
                    {
                      $eq: ["$ticketId", "$$ticket"]
                    },
                    {
                      $eq: ["$eventId", "$$event"]
                    },
                    {
                      $eq: ["$isDelete", false]
                    },
                    {
                      $eq: ["$ticketOrderStatus", "succeeded"]
                    }
                  ],
                  
                },
              }
            },
            {
              $group: {
                _id: null,
                soldTicket: {
                  $sum: 1
                }
              }
            }
          ]
        }
      },
      {
        $unwind: {
          path: "$soldTicket",
          preserveNullAndEmptyArrays: true
        }
      },
      {
        $unwind: {
          path: "$soldTicket",
          preserveNullAndEmptyArrays: true,
        },
      },
      {
        $lookup: {
          from: "application_forms",
          localField: "applicationForm",
          foreignField: "_id",
          as: "application_forms_data",
          pipeline: [
            {
              $project: {
                name: 1,
              },
            },
          ],
        },
      },
      {
        $unwind: {
          path: "$application_forms_data",
          preserveNullAndEmptyArrays: true,
        },
      },
      // {
      //   $lookup: {
      //     from: 'ticket_purchases',
      //     localField: '_id',
      //     foreignField: 'ticketId',
      //     as: 'cancelledTicket',
      //     pipeline: [
      //       {
      //         $match: {
      //           isDelete: false,
      //           ticketOrderStatus:"cancelled"
      //         },
      //       },
      //       {
      //         $group: {
      //           _id: null,
      //           cancelledTicket: { $sum: "$purchaseQuantity" }
      //         }
      //       },
      //     ]
      //   }
      // },
      // {
      //   $unwind: {
      //     path: "$cancelledTicket",
      //     preserveNullAndEmptyArrays: true,
      //   },
      // },
      {
        $addFields: {
          // available_addon: { $size: '$selectAddon' },
          available_guest_ticket: { $size: '$selectGuestTicket' },
          soldTicket: { '$ifNull': ['$soldTicket.soldTicket', 0] },
          // cancelledTicket: { '$ifNull': ['$cancelledTicket.cancelledTicket', 0] },
          sortFieldName: (sortField === "name" ? { $toLower: "$name" } : ""),
        },
      },
      { $sort: (sortField === "name" ? { sortFieldName: sortType } : sortTemp) },
      { $skip: skip },
      { $limit: limit },
      {
        $project: {
          sortFieldName: 0,
          restrictedAccessGroups: 0,
          restrictedAccessMemberships: 0,
          restrictedIndivisualUser: 0,
        },
      },
    ];

    let [all, count] = await Promise.all([
      eventTicket.aggregate(pipeline),
      eventTicket.countDocuments(match)
    ]);

    if (!all || all.length === 0) {
      return res.status(200).send({
        status: false,
        message: `Event ticket is not found!`,
        data: [],
        totalPages: 0,
        currentPage: 1,
        totalTicket: 0
      })
    }
    else {
      return res.status(200).send({
        status: true,
        message: "Event ticket get successfully!",
        data: all,
        totalPages: Math.ceil(count / limit),
        currentPage: page,
        totalTicket: count
      })
    }
  }
  catch (error) {
    await debugErrorLogs.createErrorLogs(error, "getAllEventTickets", {});
    return res.status(500).json({ status: false, message: `${error.message}` });
  }
};

// get all event ticket by event id for indivisual event
exports.getAllTicketList = async (req, res) => {
  try {
    const { eventId } = req.params;
    let match = { eventId: ObjectId(eventId), relation_id: ObjectId(req.relation_id), isDelete: false };

    //pipeline  for aggregate function
    let pipeline = [
      { $match: match },
      {
        $project: {
          name: 1
        },
      },
    ];

    let all= await eventTicket.aggregate(pipeline);
    if (!all || all.length === 0) {
      return res.status(200).send({
        status: false,
        message: `Ticket is not found!`,
        data: [],
      })
    }
    else {
      return res.status(200).send({
        status: true,
        message: "Getting Ticket!",
        data: all,
      })
    }
  }
  catch (error) {
    await debugErrorLogs.createErrorLogs(error, "getAllEventTickets", {});
    return res.status(500).json({ status: false, message: `${error.message}` });
  }
};

// get All ticket Suggestion List
exports.getTicketSuggestionList = async (req, res) => {
  try {
    const { eventId } = req.params;
    const ticketData = await eventTicket.find({ eventId: eventId, relation_id: ObjectId(req.relation_id), isDelete: false }).select("name -_id").sort({ name: 1 }).lean();
    if (!ticketData || ticketData.length === 0)
      return res.status(200).send({ status: false, message: "Ticket is not available for this event!" });
    else
      return res.status(200).send({ status: true, message: "All ticket retrieved!", data: ticketData });

  } catch (error) {
    await debugErrorLogs.createErrorLogs(error, "getTicketSuggestionList", {});
    return res.status(500).json({ status: false, message: `${error.message}` });
  }
};

exports.getAllBookTicketsListforAdmin = async (req, res) => {
  try {
    let page = req.query.page ? parseInt(req.query.page) : 1;
    let limit = req.query.limit ? parseInt(req.query.limit) : 10;
    let skip = (page - 1) * limit;

    let match = {
      eventId: ObjectId(req.query.eventId),
      isDelete: false,
      relation_id: ObjectId(req.relation_id),
    };

    var fromDate 
    var toDate 
    if(req.query.filterByPurchaseDate){
      fromDate = new Date(req.query.filterByPurchaseDate);
      toDate = new Date(req.query.filterByPurchaseDate);
      toDate.setDate(toDate.getDate() + 1);
    }
    let pipeline = [
      { $match: match },
      {
        $lookup: {
          from: "ticket_payments",
          localField: "_id",
          foreignField: "ticketId",
          as: "ticketPaymentsData",
          pipeline: [
            {
              $match: {
                isDelete: false,
                // statusOfPayment: {
                //   $nin: ["requires_payment_method"]
                // }
              },
            },
            {
              $lookup: {
                from: "airtable-syncs",
                localField: "userId",
                foreignField: "_id",
                pipeline: [
                  {
                    $project: {
                      _id: 1,
                      first_name: 1,
                      last_name: 1,
                      display_name: 1,
                      Preferred_Email: "$Preferred Email",
                    },
                  },
                ],
                as: "UserData",
              },
            },
            {
              $lookup: {
                from: 'ticket_purchases',
                let: {
                  ticketId: '$ticketId',
                  userId: '$userId'
                },
                pipeline: [
                  {
                    $match: {
                      $expr: {
                        $and: [
                          { $eq: ['$ticketId', '$$ticketId'] },
                          { $eq: ['$userId', '$$userId'] }, 
                          { $eq: ['$isDelete', false] },
                          //{ $in: ['$ticketOrderStatus', ['cancelled', 'success', 'card_declined']] } // Match status
                        ]
                      }
                    }
                  },
                  {
                    '$sort': {
                        'purchaseDate': -1
                      }
                  }
                ],
                as: 'ticketPurchasesData'
              }
            },
            {
              $project: {
                _id: 1,
                ticketId: 1,
                userId: 1,
                amount: 1,
                purchaseQuantity: 1,
                statusOfPayment: 1,
                first_name:{$arrayElemAt: ["$UserData.first_name", 0]},
                last_name:{$arrayElemAt: ["$UserData.last_name", 0]},
                display_name:{$arrayElemAt: ["$UserData.display_name", 0]},
                "Preferred Email":{$arrayElemAt: ["$UserData.Preferred_Email", 0]},
                ticketPurchasesData: 1,
              },
            },
          ],
        },
      },
      // Filter
      ...(req.query.filterByTicketId ? [
        {
          $match: {
            "ticketPaymentsData.ticketId": ObjectId(req.query.filterByTicketId)
          }
        }
      ] : []),
      // Filter by user
      ...(req.query.filterByUserId ? [
        {
          $match: {
            "ticketPaymentsData.userId": ObjectId(req.query.filterByUserId)
          }
        }
      ] : []),

      // Filter by status
      ...(req.query.filterByStatus ? [
        {
          $match: {
            "ticketPaymentsData.statusOfPayment": req.query.filterByStatus
          }
        }
      ] : []),
      // Filter by Date
      ...(req.query.filterByPurchaseDate ? [
        {
          $match: {
            "ticketPaymentsData.ticketPurchasesData.purchaseDate": {
              $gte: fromDate,
              $lte: toDate,
            },
          }
        }
      ] : []),

      //search
      ...(req.query.search
      	? [
      		{
      		  $match: {
              $or: [
                    { name: { $regex: ".*" + req.query.search + ".*", $options: "i" } },
                    { 'ticketPaymentsData.display_name': { $regex: ".*" + req.query.search + ".*", $options: "i" } },
                    { 'ticketPaymentsData.ticketPurchasesData.ticketOrderStatus': { $regex: ".*" + req.query.search + ".*", $options: "i" } },
                  ]
                }
      			},
      	  ]
      : []),
      { $skip: skip },
      { $limit: limit },
    ];


    let [all, count] = await Promise.all([
      eventTicket.aggregate(pipeline),
      eventTicket.countDocuments(match),
    ]);

    if (!all || all.length === 0) {
      return res.status(200).send({
        status: false,
        message: `ticket data is not found!`,
        data: [],
        totalPages: 0,
        currentPage: 1,
        totalTicket: 0,
      });
    } else {
      return res.status(200).send({
        status: true,
        message: "ticket data get successfully!",
        data: all,
        totalPages: Math.ceil(count / limit),
        currentPage: page,
        totalTicket: count,
      });
    }
  } catch (error) {
    await debugErrorLogs.createErrorLogs(error, "getTicketSuggestionList", {});
    return res.status(500).json({ status: false, message: `${error.message}` });
  }
};

exports.getAllBookTicketsListforAdminV1 = async (req, res) => {
  try {
    let page = req.query.page ? parseInt(req.query.page) : 1;
    let limit = req.query.limit ? parseInt(req.query.limit) : 10;
    let skip = (page - 1) * limit;

    let sortBy = req?.query?.sortBy;
    sortBy = 
      sortBy === "email" ? "Preferred Email" : 
      sortBy === "username" ? "display_name" : 
      sortBy === "ticket_name" ? "name" : 
      sortBy === "purchase_quantity" ? "purchaseQuantity" : 
      sortBy === "total_amount" ? "amount" : "createdAt";

    let sortOrder = req?.query?.sortOrder;
    sortOrder = sortOrder === 'asc' ? 1 : -1;

    let sort = { [`${sortBy}`] : sortOrder };

    let match = {
      eventId: ObjectId(req.query.eventId),
      isDelete: false,
      relation_id: ObjectId(req.relation_id),
    };

    var fromDate 
    var toDate 
    if(req.query.filterByPurchaseDate){
      fromDate = new Date(req.query.filterByPurchaseDate);
      toDate = new Date(req.query.filterByPurchaseDate);
      toDate.setDate(toDate.getDate() + 1);
    }
    let pipeline = [
      { $match: match },
      {
        $lookup: {
          from: "ticket_payments",
          localField: "_id",
          foreignField: "ticketId",
          as: "ticketPaymentsData",
          pipeline: [
            {
              $match: {
                isDelete: false,
                // statusOfPayment: {
                //   $nin: ["requires_payment_method"]
                // }
              },
            },
            {
              $lookup: {
                from: "airtable-syncs",
                localField: "userId",
                foreignField: "_id",
                pipeline: [
                  {
                    $project: {
                      _id: 1,
                      first_name: 1,
                      last_name: 1,
                      display_name: 1,
                      Preferred_Email: "$Preferred Email",
                    },
                  },
                ],
                as: "UserData",
              },
            },
            {
              $lookup: {
                from: 'ticket_purchases',
                let: {
                  ticketId: '$ticketId',
                  userId: '$userId'
                },
                pipeline: [
                  {
                    $match: {
                      $expr: {
                        $and: [
                          { $eq: ['$ticketId', '$$ticketId'] },
                          { $eq: ['$userId', '$$userId'] }, 
                          { $eq: ['$isDelete', false] },
                          //{ $in: ['$ticketOrderStatus', ['cancelled', 'success', 'card_declined']] } // Match status
                        ]
                      }
                    }
                  },
                  {
                    '$sort': {
                        'purchaseDate': -1
                      }
                  }
                ],
                as: 'ticketPurchasesData'
              }
            },
            {
              $project: {
                _id: 1,
                ticketId: 1,
                userId: 1,
                amount: 1,
                purchaseQuantity: 1,
                statusOfPayment: 1,
                first_name:{$arrayElemAt: ["$UserData.first_name", 0]},
                last_name:{$arrayElemAt: ["$UserData.last_name", 0]},
                display_name:{$arrayElemAt: ["$UserData.display_name", 0]},
                "Preferred Email":{$arrayElemAt: ["$UserData.Preferred_Email", 0]},
                ticketPurchasesData: 1,
              },
            },
          ],
        },
      },
      // Filter
      ...(req.query.filterByTicketId ? [
        {
          $match: {
            "ticketPaymentsData.ticketId": ObjectId(req.query.filterByTicketId)
          }
        }
      ] : []),
      // Filter by user
      ...(req.query.filterByUserId ? [
        {
          $match: {
            "ticketPaymentsData.userId": ObjectId(req.query.filterByUserId)
          }
        }
      ] : []),

      // Filter by status
      ...(req.query.filterByStatus ? [
        {
          $match: {
            "ticketPaymentsData.statusOfPayment": req.query.filterByStatus
          }
        }
      ] : []),
      // Filter by Date
      ...(req.query.filterByPurchaseDate ? [
        {
          $match: {
            "ticketPaymentsData.ticketPurchasesData.purchaseDate": {
              $gte: fromDate,
              $lte: toDate,
            },
          }
        }
      ] : []),

      //search
      ...(req.query.search
      	? [
      		{
      		  $match: {
              $or: [
                    { name: { $regex: ".*" + req.query.search + ".*", $options: "i" } },
                    { 'ticketPaymentsData.display_name': { $regex: ".*" + req.query.search + ".*", $options: "i" } },
                    { 'ticketPaymentsData.ticketPurchasesData.ticketOrderStatus': { $regex: ".*" + req.query.search + ".*", $options: "i" } },
                  ]
                }
      			},
      	  ]
      : []),
      {
        '$unwind': {
          'path': '$ticketPaymentsData', 
          'preserveNullAndEmptyArrays': false
        }
      }, 
      {
        '$project': {
          'name': '$name', 
          'type': '$type', 
          'actualPrice':"$actualPrice",
          'ticketAccess':"$ticketAccess",
          'first_name': '$ticketPaymentsData.first_name', 
          'last_name': '$ticketPaymentsData.last_name', 
          'display_name': '$ticketPaymentsData.display_name', 
          'Preferred Email': '$ticketPaymentsData.Preferred Email', 
          'ticketId': '$ticketPaymentsData.ticketId', 
          'userId': '$ticketPaymentsData.userId', 
          'amount': '$ticketPaymentsData.amount', 
          'purchaseQuantity': '$ticketPaymentsData.purchaseQuantity', 
          'refundedQuantity': '$ticketPaymentsData.refundedQuantity', 
          'statusOfPayment': '$ticketPaymentsData.statusOfPayment', 
          'paymentIntentId': '$ticketPaymentsData.paymentIntentId', 
          'ticketPaymentsData': '$ticketPaymentsData.ticketPurchasesData'
        }
      },
      // sorting
      {
        "$sort": sort
      }, 
      { $skip: skip },
      { $limit: limit },
    ];

    let [all, count] = await Promise.all([
      eventTicket.aggregate(pipeline),
      eventTicket.countDocuments(match),
    ]);

    if (!all || all.length === 0) {
      return res.status(200).send({
        status: false,
        message: `ticket data is not found`,
        data: [],
        totalPages: 0,
        currentPage: 1,
        totalTicket: 0,
      });
    } else {
      return res.status(200).send({
        status: true,
        message: "ticket data get successfully!",
        data: all,
        totalPages: Math.ceil(count / limit),
        currentPage: page,
        totalTicket: count,
      });
    }
  } catch (error) {
    await debugErrorLogs.createErrorLogs(error, "getTicketSuggestionList", {});
    return res.status(500).json({ status: false, message: `${error.message}` });
  }
};

exports.getAllBookTicketsListforAdminSuggestion = async (req, res) => {
  try {

    let match = {
      eventId: ObjectId(req.query.eventId),
      isDelete: false,
      relation_id: ObjectId(req.relation_id),
    };

    let pipeline = [
      { $match: match },
      {
        $project: {
          name: 1,
        },
      },
    ];

    let [all, count] = await Promise.all([
      eventTicket.aggregate(pipeline),
      eventTicket.countDocuments(match),
    ]);

    if (!all || all.length === 0) {
      return res.status(200).send({
        status: false,
        message: `ticket suggestion is not found!`,
        data: [],
      });
    } else {
      return res.status(200).send({
        status: true,
        message: "ticket suggestion get successfully!",
        data: all,
      });
    }
  } catch (error) {
    await debugErrorLogs.createErrorLogs(error, "getTicketSuggestionList", {});
    return res.status(500).json({ status: false, message: `${error.message}` });
  }
};

// get all event ticket by ticket for indivisual ticket
exports.getEventTicketsById = async (req, res) => {
  try {
    const obj = { relation_id: req.relation_id };
    const { ticketId } = req.params;
    let match = { _id: ObjectId(ticketId), relation_id: ObjectId(req.relation_id), isDelete: false };
    let pipeline = [
      { $match: match },
      {
        $lookup: {
          from: 'event_guest_tickets',
          localField: 'guestTickets',
          foreignField: '_id',
          as: 'selectGuestTicket',
          pipeline: [
            {
              $match: {
                $expr: {
                  $eq: [
                    "$isDelete", false
                  ]
                }
              }
            },
            {
              $project: {
                isDelete: 0,
                createdAt: 0,
                updatedAt: 0,
                __v: 0
              }
            }
          ]
        }
      },
      // {
      //   $lookup: {
      //     from: 'event_addons',
      //     localField: 'selectAddons',
      //     foreignField: '_id',
      //     as: 'selectAddon',
      //     pipeline: [
      //       {
      //         $match: {
      //           $expr: {
      //             $eq: [
      //               "$isDelete", false
      //             ]
      //           }
      //         }
      //       },
      //       {
      //         $project: {
      //           isDelete: 0,
      //           createdAt: 0,
      //           updatedAt: 0,
      //           __v: 0
      //         }
      //       }
      //     ]
      //   }
      // },
      {
        $lookup: {
          from: 'event_addon_v2',
          localField: 'addons',
          foreignField: '_id',
          as: 'addons',
          pipeline: [
            {
              $match: {
                $expr: {
                  $eq: [
                    "$isDelete", false
                  ]
                }
              }
            },
            {
              $sort: { order: 1 }  // Sort by 'order' field in ascending order
            },
            {
              $project: {
                isDelete: 0,
                createdAt: 0,
                updatedAt: 0,
                __v: 0
              }
            }
          ]
        }
      },
      {
        $addFields: {
          available_guest_ticket: { $size: '$selectGuestTicket' },
          // available_addon: { $size: '$selectAddon' },
        }
      },
      {
        $lookup: {
          from: 'groups',
          localField: 'restrictedAccessGroups',
          foreignField: '_id',
          as: 'restrictedAccessGroups',
          pipeline: [
            {
              $match: {
                $expr: {
                  $eq: [
                    "$isDelete", false
                  ]
                }
              }
            },
            {
              $project: {
                groupTitle: 1,
                groupInfo: 1
              }
            }
          ]
        }
      },
      {
        $lookup: {
          from: 'membership_plans',
          localField: 'restrictedAccessMemberships',
          foreignField: '_id',
          as: 'restrictedAccessMemberships',
          pipeline: [
            {
              $match: {
                $expr: {
                  $eq: [
                    "$isDelete", false
                  ]
                }
              }
            },
            {
              $project: {
                plan_name: 1,
              }
            }
          ]
        }
      },
      {
        $lookup: {
          from: 'airtable-syncs',
          localField: 'restrictedIndivisualUser',
          foreignField: '_id',
          as: 'restrictedIndivisualUser',
          pipeline: [
            {
              $match: {
                $expr: {
                  $eq: [
                    "$isDelete", false
                  ]
                }
              }
            },
            {
              $project: {
                first_name: 1,
                last_name: 1,
                display_name: 1,
                attendeeDetail: 1,
                "Preferred Email": 1,
              }
            }
          ]
        }
      },];
    let ticketData = await eventTicket.aggregate(pipeline);

    const tiers = await getAllTiersfromBilling(obj, (expand = true));  // Assuming you get tiers via the request body or other method
    const restrictedTierIds = Array.isArray(ticketData[0]?.restrictedAccessTiers)
      ? ticketData[0].restrictedAccessTiers.map((id) => id.toString())
      : [];
    
    // Filter the tiers based on restrictedTierIds
    const filteredTiers = tiers
      .filter((tier) => restrictedTierIds.includes(tier._id.toString()))
      .map((tier) => ({
        _id: tier._id,
        name: tier.name,
      }));

    if (ticketData.length > 0) {
      ticketData[0].restrictedAccessTiers = filteredTiers;
    }

    let arr = [];
    for (let i = 0; i < ticketData.length; i++) {
      if (ticketData[i].thumbnail) {
        let url = s3.getSignedUrl("getObject", {
          Bucket: "arn:aws:s3:us-east-2:496737174815:accesspoint/accessforapp",
          Key: ticketData[i].thumbnail,
          Expires: 100000,
        });
        arr.push({ ...ticketData[i], thumbnail: url });
        ticketData = arr;
      }
    }

    if (!ticketData || ticketData.length == 0) {
      return res.status(200).send({ status: false, message: `Event ticket is not found!` })
    }
    else {
      return res.status(200).send({ status: true, message: "Event ticket get successfully!", data: ticketData[0], })
    }
  }
  catch (error) {
    await debugErrorLogs.createErrorLogs(error, "getEventTicketsById", {});
    return res.status(500).json({ status: false, message: `${error.message}` });
  }
};

// clone of event ticket
exports.cloneEventTicket = async (req, res) => {
  try {
    const { ticketId } = req.body;
    if (ticketId && ObjectId.isValid(ticketId)) {
      const ticketData = await eventTicket.findOne({ _id: ObjectId(ticketId), relation_id: ObjectId(req.relation_id), isDelete: false, }).select("-_id -__v -updatedAt -createdAt");
      if (!ticketData) {
        return res.status(200).json({ status: false, message: "Ticket data not found!" });
      } else {
        const ids = await eventTicket.find({ eventId: ticketData.eventId, isDelete: false }, { _id: 1, order: 1 }).sort({ order: -1 });
        let orderData = (ids && ids.length > 0) ? ids[0].order + 1 : 1;
        let ticketObject = ticketData.toObject();
        if (ticketData.thumbnail) {
          ticketObject.thumbnail = await copyTicketThumbnail(ticketData.thumbnail)
        }
        ticketObject.name = "Copy - " + ticketObject.name;
        ticketObject.salesStartDate = ""
        ticketObject.salesEndDate = ""
        ticketObject.visibilityStartDate = ""
        ticketObject.visibilityEndDate = ""
        ticketObject.order = orderData
        const ticketClone = new eventTicket(ticketObject);
        const newTicket = await ticketClone.save();

        return res.status(200).send({ message: "Cloning completed successfully!", data: newTicket, });
      }
    } else {
      return res.status(200).json({ status: false, message: "Ticket id not found!", data: {}, });
    }
  } catch (error) {
    await debugErrorLogs.createErrorLogs(error, "cloneEventTicket", {});
    return res.status(500).json({ status: false, message: "Internal server error!", error: error });
  }
}

// get all ticket list
exports.allTicketList = async (req, res) => {
  try {
    const ticketData = await eventTicket.find({ relation_id: ObjectId(req.relation_id), isDelete: false });

    if (!ticketData) {
      return res
        .status(200)
        .send({ status: false, message: `ticket is not found!` })
    }
    else {
      return res.status(200).send({
        status: true,
        message: "ticket get successfully!",
        data: ticketData,
      })
    }
  } catch (error) {
    console.log(error, "error");
  }
}

// Reorder guest ticket by ticket Id
exports.reorderGuestTicket = async (req, res) => {
  try {
    const { ticketId } = req.params;
    const { ids } = req.body;
    // Check if each id is a valid ObjectId
    for (const item of ids) {
      if (!ObjectId.isValid(item)) {
        return res.status(400).send({ status: false, message: "Invalid ID!" });
      }
    }
    // Count total number of guest tickets for the ticket where isDelete is false
    const ticketData = await eventTicket.findById({ _id: ObjectId(ticketId), relation_id: ObjectId(req.relation_id), isDelete: false });
    if (ids.length !== ticketData["guestTickets"].length) {
      return res.status(400).json({ status: false, message: "Please give valid data!" });
    } else {
      let reOrder = await eventTicket
        .findByIdAndUpdate({ _id: ObjectId(ticketId), isDelete: false }, { guestTickets: ids }, { new: true });
      if (!reOrder) {
        return res.status(200).json({ status: false, message: "Something went wrong while getting tickets!" });
      } else {
        return res.status(200).send({
          status: true,
          message: "Guest ticket list rearrange succesfully!",
        });
      }
    }
  } catch (error) {
    await debugErrorLogs.createErrorLogs(error, "reorderGuestTicket", {});
    return res.status(500).json({ status: false, message: `${error.message}` });
  }
};

// get purchased ticket by event attendee
exports.fetchTicketByAttendee = async (req, res) => {
  try {
    const { userId, eventId, participantId, } = req.body;
    // let condition = { userId: ObjectId(userId), userParticipantId: ObjectId(participantId), eventId: ObjectId(eventId), relation_id: ObjectId(req.relation_id), isDelete: false }
    let condition = { userId: ObjectId(userId), eventId: ObjectId(eventId), relation_id: ObjectId(req.relation_id), isDelete: false, purchaseQuantity: { $ne: 0 } }

    const purchaseTicket = await ticketPurchase
      .find(condition)
      .lean();
    if (purchaseTicket.length === 0) {
      return res.status(200).send({ status: false, message: `Ticket is not found!` });
    } else {
      const ticketData = await ticketPayment.aggregate([
        { $match: condition },
        {
          $lookup: {
            from: 'event_tickets',
            localField: 'ticketId',
            foreignField: '_id',
            as: 'ticket',
            pipeline: [
              {
                $match: {
                  $expr: {
                    $eq: [
                      "$isDelete", false
                    ]
                  }
                }
              },
              {
                $project: {
                  name: 1,
                  actualPrice: 1,
                  description: 1,
                  thumbnail: 1,
                  ticketAccess: 1,
                }
              }
            ]
          }
        },
        {
          $unwind: {
            path: "$ticket",
            preserveNullAndEmptyArrays: true,
          },
        },
        {
          $addFields: {
            ticket: "$ticket.name",
            description: "$ticket.description",
            thumbnail: "$ticket.thumbnail",
            ticketAccess: "$ticket.ticketAccess",
          },
        },
        {
          $project: {
            isDelete: 0,
            createdAt: 0,
            updatedAt: 0,
            __v: 0,
          },
        },
      ])
      if (ticketData.length === 0) {
        return res.status(200).send({ status: false, message: "Something went wrong while get ticket!", });
      } else {
        return res.status(200).send({ status: true, message: "Event ticket get successfully!", status: true, data: ticketData, });
      }
    }
  }
  catch (error) {
    await debugErrorLogs.createErrorLogs(error, "fetchTicketByAttendee", {});
    return res.status(500).json({ status: false, message: "Internal server error!", error: error });
  }
}

// get user Info by Ticket Id
exports.fetchUserByTicket = async (req, res) => {
  try {
    const { ticketId } = req.params;
    let page = req.query.page ? +req.query.page : 1;
    let limit = req.query.limit ? +req.query.limit : 10;
    let skip = (page - 1) * limit;
    const sortType = req.query.sortType === "Asc" ? 1 : -1;
    let condition = { 
      ticketId: ObjectId(ticketId),
      relation_id: ObjectId(req.relation_id), 
      isDelete: false ,
      ticketOrderStatus: "succeeded"
    };

    let pipeline = [
      { $match: condition },
      {
        $lookup: {
          from: "event_participant_attendees",
          let: {
            user: "$userId",
            event: "$eventId"
          },
          as: "event_participant_attendees",
          pipeline: [
            {
              $match: {
                $expr: {
                  $and: [
                    { $eq: ["$user", "$$user"] },
                    { $eq: ["$event", "$$event"] },
                    { $eq: ["$isDelete", false] }
                  ]
                }
              }
            },
          ],
        }
      },
      {
        $unwind: {
          path: "$event_participant_attendees",
          preserveNullAndEmptyArrays: false
        }
      },
      {
        $group: {
          _id: "$userId",
          purchaseQuantity: {
            $sum: 1
          },
          purchaseDate: {
            $first: "$purchaseDate"
          },
          ticketOrderStatus: {
            $first: "$ticketOrderStatus"
          }
        }
      },
      {
        $lookup: {
          from: "airtable-syncs",
          localField: "_id",
          foreignField: "_id",
          as: "userData",
          pipeline: [
            {
              $match: {
                $expr: {
                  $eq: ["$isDelete", false]
                }
              }
            },
            {
              $project: {
                first_name: {
                  $ifNull: ["$first_name", ""]
                },
                last_name: {
                  $ifNull: ["$last_name", ""]
                },
                display_name: {
                  $ifNull: ["$display_name", ""]
                },
                Preferred_Email: "$Preferred Email"
              }
            }
          ]
        }
      },
      {
        $unwind: {
          path: "$userData",
          preserveNullAndEmptyArrays: true
        }
      },
      {
        $project: {
          purchaseQuantity: 1,
          purchaseDate: 1,
          ticketOrderStatus: 1,
          first_name: "$userData.first_name",
          last_name: "$userData.last_name",
          display_name: "$userData.display_name",
          Preferred_Email: "$userData.Preferred_Email"
        }
      },
      {
        $addFields: {
          sortFieldLower:
            req.query.sortField === "first_name"
              ? { $toLower: "$first_name" }
              : req.query.sortField === "last_name"
                ? { $toLower: "$last_name" }
              : req.query.sortField === "display_name"
                ? { $toLower: "$display_name" }
              : req.query.sortField === "Preferred_Email"
                ? { $toLower: "$Preferred_Email" }
              : req.query.sortField === "purchaseQuantity"
                ? { $toInt: "$purchaseQuantity" }
                : "$purchaseDate",
        },
      },
      { $sort: { sortFieldLower: sortType } },
    ]

    const ticketData = await eventTicket.findById({ _id: ObjectId(ticketId), isDelete: false }).lean();
    if (!ticketData) {
      return res.status(200).send({ status: false, message: `Ticket is not found!` });
    } else {

      console.log({piepline: JSON.stringify(pipeline) })
      let [all, count] = await Promise.all([
        ticketPurchaseV2.aggregate([...pipeline, { $skip: skip }, { $limit: limit },]),
        ticketPurchaseV2.countDocuments(condition)
      ]);

      if (!all || all.length === 0) {
        return res.status(200).send({
          status: false,
          message: `This ticket is not purchase by any user!`,
          data: [],
          totalPages: 0,
          currentPage: 1,
          totalTicket: 0
        })
      }
      else {
        return res.status(200).json({
          status: true,
          message: "Ticket purchased user data get successfully !",
          data: all,
          totalPages: Math.ceil(count / limit),
          currentPage: page,
          totalTicket: count
        })
      }
    }
  }
  catch (error) {
    await debugErrorLogs.createErrorLogs(error, "fetchUserByTicket", {});
    return res.status(500).json({ status: false, message: "Internal server error!", error: error });
  }
}


// get ticket Info, sold ticket and no.of user which is purchased ticket(By event Id)
exports.fetchTicketAndUserByEvent = async (req, res) => {
  try {
    const { eventId } = req.params;
    let condition = {
      eventId: ObjectId(eventId),
      relation_id: ObjectId(req.relation_id),
      isDelete: false,
    };

    let pipeline = [
      {
        $match: condition,
      },
      {
        $lookup: {
          from: "ticket_purchase_v2",
          localField: "_id",
          foreignField: "ticketId",
          as: "purchase_ticket",
          pipeline: [
            {
              $match: {
                $expr: {
                  $and: [
                    { $eq: ["$isDelete", false] },
                    { $eq: ["$ticketOrderStatus", "succeeded"] },
                  ],
                },
              },
            },
            {
              $lookup: {
                from: "event_participant_attendees",
                localField: "userParticipantId",
                foreignField: "_id",
                as: "event_participant_attendees",
                pipeline: [
                  {
                    $match: {
                      $expr: {
                        $and: [
                          {
                            $eq: ["$isDelete", false],
                          },
                        ],
                      },
                    },
                  },
                ],
              },
            },
            {
              $unwind: {
                path: "$event_participant_attendees",
                preserveNullAndEmptyArrays: false,
              },
            },
            {
              $group: {
                _id: null,
                userId: {
                  $addToSet: "$userId",
                },
                sold_ticket: {
                  $sum: 1,
                },
              },
            },
          ],
        },
      },
      {
        $unwind: {
          path: "$purchase_ticket",
          preserveNullAndEmptyArrays: false,
        },
      },
      {
        $lookup: {
          from: "ticket_purchase_v2",
          localField: "_id",
          foreignField: "ticketId",
          as: "purchase_ticket_data",
          pipeline: [
            {
              $lookup: {
                from: "ticket_addon_purchases",
                localField: "_id",
                foreignField: "ticketPurchaseId",
                as: "ticket_addon_purchases_data",
                pipeline: [
                  {
                    $match: {
                      addonOrderStatus: "succeeded",
                      isDelete: false,
                    },
                  },
                  {
                    $group: {
                      _id: null,
                      count: {
                        $sum: 1,
                      },
                    },
                  },
                  {
                    $project: {
                      _id: 0,
                      count: {
                        $ifNull: ["$count", 0],
                      },
                    },
                  },
                ],
              },
            },
            {
              $lookup: {
                from: "ticket_submissions",
                localField: "_id",
                foreignField: "ticketPurchaseId",
                as: "ticket_submissions_data",
                pipeline: [
                  {
                    $match: {
                      isDelete: false,
                    },
                  },
                  {
                    $group: {
                      _id: null,
                      count: {
                        $sum: 1,
                      },
                    },
                  },
                  {
                    $project: {
                      _id: 0,
                      count: {
                        $ifNull: ["$count", 0],
                      },
                    },
                  },
                ],
              },
            },
            {
              $unwind: {
                path: "$ticket_addon_purchases_data",
                preserveNullAndEmptyArrays: true,
              },
            },
            {
              $unwind: {
                path: "$ticket_submissions_data",
                preserveNullAndEmptyArrays: true,
              },
            },
            {
              $project: {
                addon_purchase_count: "$ticket_addon_purchases_data.count",
                submission_form_count: "$ticket_submissions_data.count",
              },
            },
          ],
        },
      },
      {
        $addFields: {
          addon_purchase_count: {
            $arrayElemAt: ["$ticket_addon_purchases_data.count", 0],
          },
          submission_form_count: {
            $arrayElemAt: ["$ticket_submissions_data.count", 0],
          },
        },
      },
      {
        $addFields: {
          TotalUser: {
            $size: "$purchase_ticket.userId",
          },
          sold_ticket: "$purchase_ticket.sold_ticket",
        },
      },
      {
        $project: {
          _id: 0,
          name: 1,
          TotalUser: 1,
          sold_ticket: 1,
          addon_purchase_count: {
            $sum: {
              $map: {
                input: {
                  $ifNull: ["$purchase_ticket_data", []],
                },
                as: "ticket",
                in: { $ifNull: ["$$ticket.addon_purchase_count", 0] },
              },
            },
          },
          submission_form_count: {
            $sum: {
              $map: {
                input: {
                  $ifNull: ["$purchase_ticket_data", []],
                },
                as: "ticket",
                in: { $ifNull: ["$$ticket.submission_form_count", 0] },
              },
            },
          },
        },
      },
    ];

    const ticketData = await eventTicket.aggregate(pipeline);
    if (!ticketData || ticketData.length == 0) {
      return res.status(200).send({
        status: false,
        message: `There is no information of ticket for this event!`,
      });
    } else {
      return res.status(200).send({
        status: true,
        message: `Fetch event ticket purchased qty and no of user purchased`,
        data: ticketData,
      });
    }
  } catch (error) {
    await debugErrorLogs.createErrorLogs(
      error,
      "fetchTicketAndUserByEvent",
      {}
    );
    return res
      .status(500)
      .json({ status: false, message: "Internal server error!", error: error });
  }
};

/** user Routes */

// get event ticket for user
exports.getTicketList = async (req, res) => {
  try {
    const { eventId } = req.params;
    const userId = req.authUserId
    const relation_id = req.relation_id;    

    let ruleCondition = await userAccessRulesCommonConditionForTicket({ userId: userId, relation_id: relation_id });
    let match = { eventId: ObjectId(eventId), relation_id: ObjectId(relation_id) ,...ruleCondition, isDelete: false };
    let today = new Date();

    //visibility  conditions
    let condition = {
      $and: [match, {
        $or: [
          { visibilityType: { $eq: "VISIBLE" } },
          { visibilityType: { $eq: "CUSTOM" }, 
          visibilityStartDate: { $lte: today }, 
          visibilityEndDate: { $gte: today } },]
      },
      {
        $or: [
          { ticketFor: { $eq: "ALL" } },
          { ticketFor: { $eq: "MEMBER" } }
        ]
      }
    ]
    };

    //pipeline  for aggregate function
    let pipeline = [
      { $match: condition },
      {
        $addFields: {
          ticketAddons: {
            $map: {
              input: { $ifNull: ["$addons", []] },
              as: "addon", 
              in: { $toObjectId: "$$addon" }
            }
          },
          ticketId: "$_id"
        }
      },
      {
        $lookup: {
          from: "event_addon_groups",
          let: { eventId: "$eventId" ,ticketAddons: "$ticketAddons", ticketId: "$ticketId" }, 
          pipeline: [
            {
              $match: {
                $expr: {
                  $and: [
                    { $eq: ["$eventId", "$$eventId"] },
                    { $eq: ["$isDelete", false] },
                    { $eq: ["$status", "publish"] },
                  ]
                }
              }
            },
            {
              $lookup: {
                from: "event_addon_v2",
                let: { groupId: "$_id",ticketAddons: "$$ticketAddons"  }, 
                let: {
                  groupId: "$_id",
                },
                pipeline: [
                  {
                    $match: {
                      $expr: {
                        $and: [
                          { $eq: ["$addonGroupId", "$$groupId"] },
                          { 
                            $in: [
                              "$_id", 
                               "$$ticketAddons" ,
                            ]
                          },
                          { $eq: ["$isDelete", false] },
                          { $eq: ["$status", "live"] }
                        ]
                      }
                    }
                  },
                  {
                    $sort: {
                      order: 1 
                    }
                  }
                ],
                as: "addons"
              }
            },
            {
              $addFields: {
                "addons": {
                  $map: {
                    input: "$addons",
                    as: "addon",
                    in: {
                      $mergeObjects: [
                        "$$addon",
                        { type: "$type" },  // Add the addonGroupStatus field
                        { ticketId: "$$ticketId" }
                      ]
                    }
                  }
                }
              }
            }
          ],
          as: "addonGroups"
        }
      },
      {
        $lookup: {
          from: 'event_guest_tickets',
          localField: 'guestTickets',
          foreignField: '_id',
          as: 'selectGuestTicket',
          pipeline: [
            {
              $match: {
                $expr: {
                  $eq: [
                    "$isDelete", false
                  ]
                }
              }
            },
            {
              $project: {
                isDelete: 0,
                createdAt: 0,
                updatedAt: 0,
                __v: 0
              }
            }
          ]
        }
      },
      // {
      //   $lookup: {
      //     from: "event_addon_v2",
      //     localField: "addons",
      //     foreignField: "_id",
      //     pipeline: [
      //       {
      //         $match: {
      //           isDelete: false,
      //         },
      //       },
      //       {
      //         $lookup: {
      //           from: "event_addon_groups",
      //           localField: "addonGroupId",
      //           foreignField: "_id",
      //           as: "event_addon_groups_data",
      //           pipeline: [
      //             {
      //               $match: {
      //                 isDelete: false,
      //               },
      //             },
      //           ],
      //         },
      //       },
      //       {
      //         $unwind: {
      //           path: "$event_addon_groups_data",
      //           preserveNullAndEmptyArrays: true,
      //         },
      //       },
      //       {
      //         $project: {
      //           _id: 1,
      //           name: 1,
      //           price: 1,
      //           subAddon: 1,
      //           addonGroupId: 1,
      //           event_addon_groups_data: 1,
      //         },
      //       },
      //     ],
      //     as: "addons",
      //   },
      // },
      {
        $addFields: {
          available_guest_ticket: { $size: '$selectGuestTicket' }
        }
      },
      {
        $lookup: {
          from: "ticket_purchase_v2",
          localField: "_id",
          foreignField: "ticketId",
          as: "sold_ticket",
          pipeline: [
            {
              $match: {
                userId: ObjectId(userId),
                ticketOrderStatus: "succeeded",
                isDelete: false
              }
            },
            {
              $group: {
                _id: "$ticketId",
                sold_ticket: {
                  $sum: 1
                }
              }
            },
            {
              $project: {
                _id: 0
              }
            }
          ]
        },
      },
      {
        $unwind: {
          path: "$sold_ticket",
          preserveNullAndEmptyArrays: true,
        },
      },
      {
        $addFields:
        {
          sold_ticket: {
            $ifNull: [
              "$sold_ticket.sold_ticket",
              0,
            ],
          },
          available_ticket: {
            $subtract: [
              "$quantity",
              {
                $ifNull: [
                  "$sold_ticket.sold_ticket",
                  0,
                ],
              },
            ],
          },
        },
      },
      {
        $sort: {
          order: 1
        }
      },
    ];

    let [all, count] = await Promise.all([
      eventTicket.aggregate([...pipeline]),
      eventTicket.countDocuments(condition)
    ]);

    let arr = [];
    for (let i = 0; i < all.length; i++) {
      if (all[i].thumbnail) {
        let url = s3.getSignedUrl("getObject", {
          Bucket: "arn:aws:s3:us-east-2:496737174815:accesspoint/accessforapp",
          Key: all[i].thumbnail,
          Expires: 100000,
        });
        arr.push({ ...all[i], thumbnail: url });
      } else {
        arr.push({ ...all[i] });
      }
      if (all[i].addonGroups && all[i].addonGroups.length > 0) {
        for (let j = 0; j < all[i].addonGroups.length; j++) {
          // Check if there are addons in the group
          if (all[i].addonGroups[j].addons && all[i].addonGroups[j].addons.length > 0) {
            // Loop through each addon and access the image
            for (let k = 0; k < all[i].addonGroups[j].addons.length; k++) {
              let addon = all[i].addonGroups[j].addons[k];
              const addonImage = await s3fileUploadService.generatePresignedUrl({key: addon?.image});
              addon.image = addonImage;
            }
          }
        }
      }
    }

    const eventData = await eventTicket.findOne({ eventId: eventId, isDelete: false })
      .select({eventId:1, _id:1})
      // .select({relation_id:1,event:1,addons:0})
      .populate([{ path: "eventId", select: "title thumbnail eventUrl timeZone startDate startTime endDate endTime maxTicketPurchase" }]);
    // const eventData = await eventTicket.aggregate([
    //   {
    //     $match: {
    //       eventId: ObjectId(eventId), 
    //       isDelete: false,
    //     },
    //   },
    //   {
    //     $lookup: {
    //       from: "event_addon_groups",
    //       let: { eventId: "$eventId" },
    //       pipeline: [
    //         {
    //           $match: {
    //             $expr: {
    //               $eq: ["$eventId", "$$eventId"], 
    //             },
    //           },
    //         },
    //         {
    //           $lookup: {
    //             from: "event_addon_v2",
    //             let: { groupId: "$addonGroupId" },
    //             pipeline: [
    //               {
    //                 $match: {
    //                   $expr: {
    //                     $eq: ["$groupId", "$$groupId"],
    //                   },
    //                 },
    //               },
    //             ],
    //             as: "addons", 
    //           },
    //         },
    //       ],
    //       as: "addonGroups", 
    //     },
    //   },
    //   {
    //     $lookup: {
    //       from: "events",
    //       localField: "eventId",
    //       foreignField: "_id", 
    //       as: "eventId", 
    //     },
    //   },
    //   {
    //     $unwind: {
    //       path: "$eventDetails",
    //       preserveNullAndEmptyArrays: true,
    //     },
    //   },
    //   {
    //     $lookup: {
    //       from: 'communities',
    //       localField: 'relation_id',  
    //       foreignField: '_id', 
    //       as: 'relation_id',
    //     }
    //   },
    //   {
    //     $project: {
    //       relation_id: 1,
    //       eventId: 1,
    //       "eventDetails.title": 1,
    //       "eventDetails.thumbnail": 1,
    //       "eventDetails.eventUrl": 1,
    //       "eventDetails.timeZone": 1,
    //       "eventDetails.startDate": 1,
    //       "eventDetails.startTime": 1,
    //       "eventDetails.endDate": 1,
    //       "eventDetails.endTime": 1,
    //       "eventDetails.maxTicketPurchase": 1,
    //       addonGroups: 1, 
    //     },
    //   },
    // ]);
    
  
    
    
    if (!all || all.length === 0) {
      return res.status(200).send({
        status: false,
        message: `Event ticket is not found!`,
        data: [],
      })
    }
    else {
      return res.status(200).send({
        status: true,
        message: "Event ticket get successfully!",
        data: { eventData: eventData, ticketData: arr },
      })
    }
  }
  catch (error) {
    await debugErrorLogs.createErrorLogs(error, "getTicketList", {});
    return res.status(500).json({ status: false, message: "Internal server error!", error: error });
  }
}

exports.getTicketListv2 = async (req, res) => {
  try {
    const { eventId } = req.params;
    const relation_id = req.query.relation_id;    

    let match = { eventId: ObjectId(eventId), relation_id: ObjectId(relation_id), isDelete: false };
    let today = new Date();

    //visibility  conditions
    let condition = {
      $and: [match, {
        $or: [
          { visibilityType: { $eq: "VISIBLE" } },
          { visibilityType: { $eq: "CUSTOM" }, 
          visibilityStartDate: { $lte: today }, 
          visibilityEndDate: { $gte: today } },]
      },
      {
        $or: [
          { ticketFor: { $eq: "ALL" } },
          { ticketFor: { $eq: "PUBLIC" } }
        ]
      }
    ]
    };

    //pipeline  for aggregate function
    let pipeline = [
      { $match: condition },
      {
        $addFields: {
          ticketAddons: {
            $map: {
              input: { $ifNull: ["$addons", []] },
              as: "addon", 
              in: { $toObjectId: "$$addon" }
            }
          },
          ticketId: "$_id"
        }
      },
      {
        $lookup: {
          from: "event_addon_groups",
          let: { eventId: "$eventId" ,ticketAddons: "$ticketAddons", ticketId: "$ticketId" }, 
          pipeline: [
            { 
              $match: { 
                $expr: { 
                  $and: [
                    { $eq: ["$eventId", "$$eventId"] },
                    { $eq: ["$isDelete", false] },
                  ]
                }
              } 
            },
            {
              $lookup: {
                from: "event_addon_v2",
                let: { groupId: "$_id",ticketAddons: "$$ticketAddons"  }, 
                let: { 
                  groupId: "$_id", 
                },
                pipeline: [
                  { 
                    $match: { 
                      $expr: { 
                        $and: [
                          { $eq: ["$addonGroupId", "$$groupId"] }, 
                          { 
                            $in: [
                              "$_id", 
                               "$$ticketAddons" ,
                            ]
                          },
                          { $eq: ["$isDelete", false] },
                        ]
                      }
                    }
                  }
                ],
                as: "addons"
              }
            },
            {
              $addFields: {
                "addons": {
                  $map: {
                    input: "$addons",
                    as: "addon",
                    in: {
                      $mergeObjects: [
                        "$$addon",
                        { type: "$type" },  // Add the addonGroupStatus field
                        { ticketId: "$$ticketId" }
                      ]
                    }
                  }
                }
              }
            }
          ],
          as: "addonGroups"
        }
      },   
      {
        $lookup: {
          from: 'event_guest_tickets',
          localField: 'guestTickets',
          foreignField: '_id',
          as: 'selectGuestTicket',
          pipeline: [
            {
              $match: {
                $expr: {
                  $eq: [
                    "$isDelete", false
                  ]
                }
              }
            },
            {
              $project: {
                isDelete: 0,
                createdAt: 0,
                updatedAt: 0,
                __v: 0
              }
            }
          ]
        }
      },
      {
        $addFields: {
          available_guest_ticket: { $size: '$selectGuestTicket' }
        }
      },
      // {
      //   $lookup: {
      //     from: "event_addon_v2",
      //     localField: "addons",
      //     foreignField: "_id",
      //     pipeline: [
      //       {
      //         $match: {
      //           isDelete: false,
      //         },
      //       },
      //       {
      //         $lookup: {
      //           from: "event_addon_groups",
      //           localField: "addonGroupId",
      //           foreignField: "_id",
      //           as: "event_addon_groups_data",
      //           pipeline: [
      //             {
      //               $match: {
      //                 isDelete: false,
      //               },
      //             },
      //           ],
      //         },
      //       },
      //       {
      //         $unwind: {
      //           path: "$event_addon_groups_data",
      //           preserveNullAndEmptyArrays: true,
      //         },
      //       },
      //       {
      //         $project: {
      //           _id: 1,
      //           name: 1,
      //           price: 1,
      //           subAddon: 1,
      //           addonGroupId: 1,
      //           event_addon_groups_data: 1,
      //         },
      //       },
      //     ],
      //     as: "addons",
      //   },
      // },
      {
        $lookup: {
          from: "ticket_purchase_v2",
          localField: "_id",
          foreignField: "ticketId",
          as: "sold_ticket",
          pipeline: [
            {
              $match: {
                ticketOrderStatus: "succeeded",
                isDelete: false
              }
            },
            {
              $group: {
                _id: "$ticketId",
                sold_ticket: {
                  $sum: 1
                }
              }
            },
            {
              $project: {
                _id: 0
              }
            }
          ]
        },
      },
      {
        $unwind: {
          path: "$sold_ticket",
          preserveNullAndEmptyArrays: true,
        },
      },
      {
        $lookup: {
          from: "application_forms",
          localField: "applicationForm",
          foreignField: "_id",
          as: "application_forms",
          pipeline: [
            {
              $lookup: {
                from: "application_form_fields",
                localField: "fields",
                foreignField: "_id",
                as: "application_form_fields"
              }
            }
          ]
        }
      },
      {
        $unwind: {
          path: "$application_forms",
          preserveNullAndEmptyArrays: true
        }
      },
      {
        $addFields:
        {
          sold_ticket: {
            $ifNull: [
              "$sold_ticket.sold_ticket",
              0,
            ],
          },
          available_ticket: {
            $subtract: [
              "$quantity",
              {
                $ifNull: [
                  "$sold_ticket.sold_ticket",
                  0,
                ],
              },
            ],
          },
        },
      },
      {
        $sort: {
          order: 1
        }
      },
    ];

    let [all, count] = await Promise.all([
      eventTicket.aggregate([...pipeline]),
      eventTicket.countDocuments(condition)
    ]);

    let arr = [];
    for (let i = 0; i < all.length; i++) {
      if (all[i].thumbnail) {
        let url = s3.getSignedUrl("getObject", {
          Bucket: "arn:aws:s3:us-east-2:496737174815:accesspoint/accessforapp",
          Key: all[i].thumbnail,
          Expires: 100000,
        });
        arr.push({ ...all[i], thumbnail: url });
      } else {
        arr.push({ ...all[i] });
      }
      if (all[i].addonGroups && all[i].addonGroups.length > 0) {
        for (let j = 0; j < all[i].addonGroups.length; j++) {
          // Check if there are addons in the group
          if (all[i].addonGroups[j].addons && all[i].addonGroups[j].addons.length > 0) {
            // Loop through each addon and access the image
            for (let k = 0; k < all[i].addonGroups[j].addons.length; k++) {
              let addon = all[i].addonGroups[j].addons[k];
              const addonImage = await s3fileUploadService.generatePresignedUrl({key: addon?.image});
              addon.image = addonImage;
            }
          }
        }
      }
    }

    const eventData = await eventTicket.findOne({ eventId: eventId, isDelete: false, isPublic: true })
      .select("eventId -_id")
      .populate([{ path: "eventId", select: "title thumbnail eventUrl timeZone startDate startTime endDate endTime maxTicketPurchase" }]);
    if (!all || all.length === 0) {
      return res.status(200).send({
        status: false,
        message: `Event ticket is not found!`,
        data: [],
      })
    }
    else {
      return res.status(200).send({
        status: true,
        message: "Event ticket get successfully!",
        data: { eventData: eventData, ticketData: arr },
      })
    }
  }
  catch (error) {
    console.log(error);
    
    await debugErrorLogs.createErrorLogs(error, "getTicketList", {});
    return res.status(500).json({ status: false, message: "Internal server error!", error: error });
  }
}

// get particular ticket for user
exports.getTicket = async (req, res) => {
  try {
    const { id } = req.params;
    const userId = req.authUserId;
    const relation_id = req.relation_id;

    let ruleCondition = await userAccessRulesCommonConditionForTicket({ userId: userId, relation_id: relation_id });
    let match = { _id: ObjectId(id), relation_id: ObjectId(relation_id), ...ruleCondition };
    let today = new Date();

    //visibility  conditions
    let condition = {
      $and: [match, {
        $or: [
          { visibilityType: { $eq: "VISIBLE" } },
          { visibilityType: { $eq: "CUSTOM" }, visibilityStartDate: { $lte: today }, visibilityEndDate: { $gte: today } },]
      }]
    };

    //pipeline  for aggregate function
    let pipeline = [
      { $match: condition },
      {
        $lookup: {
          from: 'event_guest_tickets',
          localField: 'guestTickets',
          foreignField: '_id',
          as: 'selectGuestTicket',
          pipeline: [
            {
              $match: {
                $expr: {
                  $eq: [
                    "$isDelete", false
                  ]
                }
              }
            },
            {
              $project: {
                isDelete: 0,
                createdAt: 0,
                updatedAt: 0,
                __v: 0
              }
            }
          ]
        }
      },
      {
        $addFields: {
          available_guest_ticket: { $size: '$selectGuestTicket' }
        }
      },
      {
        $lookup: {
          from: 'events',
          localField: 'eventId',
          foreignField: '_id',
          as: 'eventId',
          pipeline: [
            {
              $match: {
                $expr: {
                  $eq: [
                    "$isDelete", false
                  ]
                }
              }
            },
            {
              $project: {
                title: 1,
                thumbnail: 1,
                eventUrl: 1,
                timeZone: 1,
                startDate: 1,
                startTime: 1,
                endDate: 1,
                endTime: 1,

              }
            }
          ]
        }
      }
    ];

    let all = await eventTicket.aggregate([...pipeline]);
    let arr = [];
    for (let i = 0; i < all.length; i++) {
      if (all[i].thumbnail) {
        let url = s3.getSignedUrl("getObject", {
          Bucket: "arn:aws:s3:us-east-2:496737174815:accesspoint/accessforapp",
          Key: all[i].thumbnail,
          Expires: 100000,
        });
        arr.push({ ...all[i], thumbnail: url });
        all = arr;
      }
    }

    if (!all || all.length === 0) {
      return res.status(200).send({
        status: false,
        message: `Event ticket is not found!`,
        data: {},
      })
    }
    else {
      return res.status(200).send({
        status: true,
        message: "Event ticket get successfully!",
        data: all[0],
      })
    }
  }
  catch (error) {
    await debugErrorLogs.createErrorLogs(error, "getTicket", {});
    return res.status(500).json({ status: false, message: "Internal server error!", error: error });
  }
}

// event ticket purchase for user
exports.ticketPurchaseByUser = async (req, res) => {
  try {
    const { eventId, ticketId, purchaseQuantity, } = req.body;
    const userId = req.authUserId;
    let relation_id = req.relation_id
    let typeIcon = "";
    let updateUserData
    let isGuest = req.currentEdge.type == "GU" ? true : false;

    if (req && req.type_icon) {
      typeIcon = req.type_icon;
    }
    const eventData = await event.findById(ObjectId(eventId), { isDelete: false }).lean();
    if (!eventData) {
      return res.status(200).json({ status: false, message: "For this ticket event is not define." });
    }

    const eventTickets = await eventTicket.findOne({ _id: ObjectId(ticketId), eventId: ObjectId(eventId), isDelete: false, });
    if (!eventTickets) {
      return res.status(200).json({ status: false, message: "Please enter valid ticket id." });
    }

    let roleDetail = await eventWiseParticipantTypes.findOne({ event: ObjectId(eventId), isDelete: false, role: "Member" });
    if (roleDetail) {
      const userData = await User.findOne({ _id: userId, isDelete: false }).lean();
      // attendee Object create or update  
      if (userData) { //user table update and If  attendees not exist then insert new one
        let attendeeObj = {
          title: userData.attendeeDetail?.title ?? "",
          name: userData.attendeeDetail?.name ?? userData.first_name + " " + userData.last_name,
          firstName: userData.attendeeDetail?.firstName ?? userData.first_name,
          lastName: userData.attendeeDetail?.lastName ?? userData.last_name,
          email: userData.attendeeDetail?.email ?? userData["Preferred Email"],
          company: userData.attendeeDetail?.company ?? "",
          phone: userData.attendeeDetail?.phone ?? "",
          facebook: userData.attendeeDetail?.facebook ?? "",
          linkedin: userData.attendeeDetail?.linkedin ?? "",
          firebaseId: userData.attendeeDetail?.firebaseId ?? userData.firebaseId,
          description: userData.attendeeDetail?.description ?? "",
          profession: userData.attendeeDetail?.profession ?? "",
          offer: userData.attendeeDetail?.offer ?? "",
          contactPartnerName: userData.attendeeDetail?.contactPartnerName ?? "",
        }
        updateUserData = await User.findByIdAndUpdate(
          userData._id,
          {
            attendeeDetail: attendeeObj
          }, { new: true });
      };

      //verify query for all purchase ticket by user
      let ticketQuantity = 0;
      const ticketDataTemp = await ticketPurchase.aggregate([
        {
          $match: {
            ticketId: ObjectId(ticketId),
            relation_id: ObjectId(req.relation_id),
            isDelete: false,
          }
        },
        {
          $group: {
            _id: null,
            totalQty: { $sum: "$purchaseQuantity" }
          }
        },
        {
          $project: {
            _id: 0,                    // Exclude the default _id field
            totalQty: 1
          }
        }
      ]);

      if (ticketDataTemp.length) {
        ticketQuantity = ticketDataTemp[0]["totalQty"];
      }
      
      if (!(+eventTickets.maximumTicket >= +purchaseQuantity && +eventTickets.minimumTicket <= +purchaseQuantity)) {
        return res.status(200).send({ status: false, message: `Ticket purchase quantity should be between ${eventTickets.minimumTicket} and ${eventTickets.maximumTicket}` });
      }

      if (ticketQuantity + +purchaseQuantity > eventTickets.quantity) {
        return res.status(200).send({ status: false, message: `For this event ticket is not available` });
      } else {
        // create EventParticipantAttendees if is not exist
        let participantObj = { user: userId, event: ObjectId(eventId), role: roleDetail._id, isDelete: false, relation_id: ObjectId(req.relation_id), };
        const participantData = await EventParticipantAttendees.findOne(participantObj).lean();
        let newParticipant;
        if (!participantData) {
          participantObj["type_icon"] = typeIcon ?? "";
          newParticipant = await EventParticipantAttendees.create(participantObj);
        }
        const userTicket = new ticketPurchase({
          ticketId: ticketId,
          userId: userId,
          userParticipantId: participantData ? participantData._id : newParticipant._id,
          eventId: eventId,
          purchaseQuantity: purchaseQuantity,
          ticketValue: eventTickets.actualPrice,
          purchaseDate: new Date(),
          relation_id: ObjectId(req.relation_id),
        })

        const ticketData = await userTicket.save();
        if (!ticketData) {
          return res.status(200).json({ status: false, message: "Something went wrong while ticket purchase.", data: {} });
        } else {
        
          const subdomain = req.subdomains[0]?req.subdomains[0]:"";
          const updatePaymentData = ""
          const regMailData = await getEventTicketRegistrationEmailTemplate(updateUserData, eventData, eventTickets, purchaseQuantity,subdomain,updatePaymentData, relation_id, isGuest)
          sendEmail(regMailData);
          return res.status(200).json({ status: true, message: "Ticket purchase successfully", data: ticketData });
        }
      }
    } else {
      return res.status(200).json({ status: false, message: "Role detail not found!" });
    }
  }
  catch (error) {
    await debugErrorLogs.createErrorLogs(error, "ticketPurchaseByUser", {});
    return res.status(500).json({ status: false, message: "Internal server error!", error: error });
  }
}

// event ticket cancel for user
exports.ticketCancelByUser = async (req, res) => {
  try {
    const { userTicketId } = req.params;
    const userId = req.authUserId;

    const userTicket = await TicketPayment
      .findOne({ ticketId: ObjectId(userTicketId), userId: ObjectId(userId), relation_id: ObjectId(req?.relation_id), isDelete: false })
      .lean();
    if (!userTicket) {
      return res.status(200).send({ status: false, message: `Ticket is not found!` });
    } else {
      const eventData = await event.findById(userTicket.eventId, { isDelete: false }).lean();
      const userData = await User.findById(userTicket.userId,{ isDelete: false }).lean();
      const deleteTicket = await TicketPayment.updateOne(
        {
          ticketId: ObjectId(userTicketId), userId: ObjectId(userId), relation_id: ObjectId(req?.relation_id), isDelete: false
        },
        {
          $set: {
            isDelete: true
          }
        },
        { new: true }
      );
      const ticketPurchasetHistory = await ticketPurchase.create({
            ticketId: userTicket?.ticketId,
            userId: userTicket?.userId?._id,
            eventId: userTicket.eventId?._id,
            purchaseQuantity: userTicket?.purchaseQuantity,
            ticketValue: userTicket?.amount,
            purchaseDate: new Date(),
            ticketOrderStatus: "cancelled",
            paymentId: userTicket?._id,
            relation_id: userTicket?.relation_id,
      });
      await ticketPurchasetHistory.save();
      if (!deleteTicket) {
        return res.status(200).send({ status: false, message: "Something went wrong while cancel ticket!", });
      } else {
        const communityLogo = req.currentEdge.relation_id.logo ? req.currentEdge.relation_id.logo : ""
        let communityId =  req.currentEdge.relation_id?._id ? ObjectId(req.currentEdge.relation_id?._id) : "";
        let customSMTP =  req.currentEdge.relation_id?.customSMTP ? req.currentEdge.relation_id?.customSMTP : false;
        const mailData = {
          email: `${userData["Preferred Email"]}`,
          subject: `Ticket Cancellation Confirmation`,
          html:
            `<div style="max-width: 600px; margin: 0 auto; font-family: Arial, sans-serif; background-color: #f5f5f5; padding: 20px; border-radius: 10px;">
            <div style="text-align: center; margin-bottom: 20px;">
              <img src=${communityLogo} alt="Your Ticketing Platform Logo" style="max-width: 200px;">
            </div>
            <div style="background-color: #ffffff; padding: 30px; border-radius: 10px; box-shadow: 0px 3px 6px rgba(0, 0, 0, 0.1);">
              <h2 style="text-align: center; margin-bottom: 20px; color: #333;">Ticket Cancellation Confirmation</h2>
              <p style="font-size: 16px; line-height: 1.6; margin-bottom: 20px;">
                Dear ${userData.first_name + " " + userData.last_name},<br />
              </p>
              <p style="font-size: 16px; line-height: 1.6; margin-bottom: 20px;">
              We have received your request to cancel your ticket for the ${eventData.title} scheduled on ${eventData.startDate + " " + eventData.startTime}. Your ticket cancellation has been successfully processed.
              </p>
              <p style="font-size: 16px; line-height: 1.6; margin-bottom: 20px;">
                If you have any questions or need further assistance, feel free to reply to this email.
              </p>
              <p style="font-size: 16px; line-height: 1.6; margin-bottom: 0;">
                See you soon!<br>
                Your Ticketing Platform Team
              </p>
            </div>
          </div>`,
          relationId:communityId,
          customsmtp:customSMTP
        };
        await sendEmail(mailData);
        await EventParticipantAttendees.findByIdAndUpdate(
          deleteTicket.userParticipantId,
          { isDelete: true },
          { new: true }).lean();
        return res.status(200).send({ status: true, message: "Event ticket cancel successfully!", });
      }
    }
  }
  catch (error) {
    await debugErrorLogs.createErrorLogs(error, "ticketCancelByUser", {});
    return res.status(500).json({ status: false, message: "Internal server error!", error: error });
  }
}

// event ticket book by user listing for particular event
exports.ticketBookByUser = async (req, res) => {
  try {
    const userId = req.authUserId;
    const { eventId } = req.params;
    const userTicket = await ticketPayment
      .find({ userId: ObjectId(userId), eventId: ObjectId(eventId), relation_id: ObjectId(req.relation_id), isDelete: false })
      .sort({ createdAt: -1 })
      .populate("ticketId", "-_id name description thumbnail type")
      .select({ amount: 1, purchaseQuantity: 1, purchaseDate: 1, ticketOrderStatus: "$statusOfPayment", stripe_processing_fee: 1 })
      .sort({ createdAt: -1 })
      .lean();

    if (userTicket.length === 0) {
      return res.status(200).send({ status: false, message: `Ticket data is not found!` });
    } else {
      // calculate total amount of all tickets
      userTicket.forEach((ticket) => {
        ticket.totalPrice = parseFloat(ticket?.amount || 0) + parseFloat(ticket?.stripe_processing_fee || 0),
        ticket.thumbnailURL = ticket.ticketId && ticket.ticketId.thumbnail ? process.env.AWS_IMG_VID_PATH + ticket.ticketId.thumbnail :"";
      });
      return res.status(200).send({ status: true, message: "Tickets listing retrieved successfully", data: userTicket });
    }
  }
  catch (error) {
    await debugErrorLogs.createErrorLogs(error, "ticketBookByUser", {});
    return res.status(500).json({ status: false, message: "Internal server error!", error: error });
  }
}

exports.linkedTicketsInAddons = async (req, res) => {
  const { addOnId } = req.params;
  const { ticketsId } = req.body;

  let match = {
    _id: { $in: ticketsId.map((ticketId) => ObjectId(ticketId)) },
    relation_id: ObjectId(req.relation_id),
    isDelete: false,
  };

  try {
    const ticketData = await eventTicket.aggregate([{ $match: match }]);

    if (ticketData.length < ticketsId.length) {
      const notFoundTickets = ticketsId.filter(
        (ticketId) =>
          !ticketData.some((ticket) => ticket._id.toString() === ticketId)
      );
      return res.status(404).send({
        status: false,
        message: `The following ticket IDs do not exist or are deleted: ${notFoundTickets.join(
          ", "
        )}`,
      });
    }

    await eventTicket.updateMany(
      { _id: { $in: ticketData.map((ticket) => ticket._id) } },
      {
        $addToSet: { addons: addOnId },
      }
    );

    return res
      .status(200)
      .send({ status: true, message: "Tickets Linked successfully" });
  } catch (error) {
    console.error("Error during update operation:", error);
    return res
      .status(500)
      .send({ status: false, message: "An error occurred during the update." });
  }
};

exports.unlinkedTicketsInAddons = async (req, res) => {
  const { addOnId } = req.params;
  const { ticketsId } = req.body;

  let match = {
    _id: { $in: ticketsId.map((ticketId) => ObjectId(ticketId)) },
    relation_id: ObjectId(req.relation_id),
    isDelete: false,
  };

  try {
    const ticketData = await eventTicket.aggregate([{ $match: match }]);

    if (ticketData.length < ticketsId.length) {
      const notFoundTickets = ticketsId.filter(
        (ticketId) =>
          !ticketData.some((ticket) => ticket._id.toString() === ticketId)
      );
      return res.status(404).send({
        status: false,
        message: `The following ticket IDs do not exist or are deleted: ${notFoundTickets.join(
          ", "
        )}`,
      });
    }

      await eventTicket.updateMany(
        { _id: { $in: ticketData.map((ticket) => ticket._id) } },
        {
          $pull: { addons: addOnId },
        }
      );    

    return res
      .status(200)
      .send({ status: true, message: "Tickets Unlinked successfully" });
  } catch (error) {
    console.error("Error during update operation:", error);
    return res
      .status(500)
      .send({ status: false, message: "An error occurred during the update." });
  }
};


// Re-send ticket mail by attendee window(event-adminside)
exports.reSendTicketPurchaseEmail = async (req, res) => {
  try {
    let eventId = ObjectId(req.query.eventId);
    const subdomain = req.subdomains[0] ? req.subdomains[0] : "";
    const relation_id = req.relation_id;
    let { attendeeData } = req.body;
    let attendee = [];

    attendeeData.map(async (item) => { attendee.push(ObjectId(item.attendeeId)); });
    const eventData = await event.findOne({ _id: eventId, isDelete: false });
    if (!eventData) {
      return res.status(200).send({ status: false, message: `Event is not found` })
    };

    let pipeline = [
      {
        $match: {
          _id: { $in: attendee },
          event: eventId,
          isDelete: false,
        },
      },
      {
        $lookup: {
          from: "events",
          localField: "event",
          foreignField: "_id",
          as: "event_data",
          pipeline: [
            {
              $match: {
                $expr: {
                  $eq: ["$isDelete", false],
                },
              },
            },
          ],
        },
      },
      {
        $unwind: {
          path: "$event_data",
          preserveNullAndEmptyArrays: false,
        },
      },
      {
        $lookup: {
          from: "airtable-syncs",
          localField: "user",
          foreignField: "_id",
          as: "user_data",
          pipeline: [
            {
              $match: {
                $expr: {
                  $eq: ["$isDelete", false],
                },
              },
            },
          ],
        },
      },
      {
        $unwind: {
          path: "$user_data",
          preserveNullAndEmptyArrays: false,
        },
      },
      {
        $lookup: {
          from: "ticket_purchase_v2",
          localField: "_id",
          foreignField: "userParticipantId",
          as: "purchase_data",
          pipeline: [
            {
              $match: {
                ticketOrderStatus: "succeeded",
                eventId: eventId,
                isDelete: false,
              },
            },
            {
              $group: {
                _id: "$ticketId",
                qty: {
                  $sum: 1,
                },
                amount: {
                  $sum: "$price",
                },
              },
            },
            {
              $lookup: {
                from: "event_tickets",
                localField: "_id",
                foreignField: "_id",
                as: "ticketData",
                pipeline: [
                  {
                    $project: {
                      name: 1,
                      type: 1,
                    },
                  },
                ],
              },
            },
            {
              $unwind: {
                path: "$ticketData",
                preserveNullAndEmptyArrays: false,
              },
            },
            {
              $project: {
                qty: 1,
                amount: 1,
                ticketName: "$ticketData.name",
                type: "$ticketData.type",
              },
            },
            {
              $sort: {
                ticketName: 1,
              },
            },
          ],
        },
      },
    ];
    const ticketData = await EventParticipantAttendees.aggregate(pipeline);
    if(!ticketData.length){
      return res.status(200).send({ status: true, message: 'For this event no attendee found!' });
    };

    // Resend email for every attendee with qr and without qr
    const emailPromises = ticketData.map(async (item) => {
      let paymentStatus = item.purchase_data.length > 0 ? "succeeded" : "";
      const regMailData = await getEventTicketRegistrationEmailTemplate(
        item.user_data,
        item.event_data,
        item.purchase_data,
        subdomain,
        paymentStatus,
        relation_id
      );
      sendEmail(regMailData);
    });
    // Wait for all email promises to resolve
    await Promise.all(emailPromises);
    res.status(200).send({ status: true, message: 'Emails processed successfully for resend ticket for attendee!', });
  }
  catch (error) {
    console.log(error, "error");
    await debugErrorLogs.createErrorLogs(error, "reSendTicketPurchaseEmail", {});
    return res.status(500).json({ status: false, message: "Internal server error!", error: error });
  }
}
