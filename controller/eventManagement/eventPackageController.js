/*
This file created by BPA.
Code is already developed by respective developers ( BPA only done function separation in separate files ).
*/

const { ObjectId } = require("mongodb");
const eventPackage = require("../../database/models/eventPackage");
const { escapeRegExp } = require("../../utils");

/** Admin APIs starts **/
/** start Create, edit, delete and get all event packages **/
// create event packege
exports.createEventPackage = async (req, res) => {
  try {
    if (req.body.name && req.body.price) {
      const package = await eventPackage.find({
        name: req.body.name,
        event: ObjectId(req.body.event),
        isDelete: false,
        relation_id: ObjectId(req.relation_id),
      });

      if (package && package.length > 0) {
        return res
          .status(200)
          .json({ status: false, message: `Package name must be unique.` });
      }

      let description = `<div "font-family: 'Muller';">${req.body.description}</div>`;
      const ids = await eventPackage
        .find({ isDelete: false, event: req.body.event,relation_id: ObjectId(req.relation_id), })
        .sort({ order: -1 });
      let packageOrder = ids && ids.length > 0 ? ids[0].order + 1 : 1;

      const newPackage = new eventPackage({
        name: req.body.name,
        description: description,
        price: req.body.price,
        event: req.body.event,
        url: req.body.url,
        eventUrlFlag: req.body.eventUrlFlag,
        order: packageOrder,
        relation_id: ObjectId(req.relation_id),
      });
      const packageData = await newPackage.save();
      if (packageData)
        return res.status(200).json({
          status: true,
          message: "Package added successfully!",
          data: packageData,
        });
      else
        return res.status(200).json({
          status: false,
          message: "Something went wrong while adding package!",
        });
    } else {
      return res.status(200).json({
        status: false,
        message: "Name and price are required fields!",
      });
    }
  } catch (e) {
    if (e.name === "MongoServerError" && e.code === 11000) {
      return res
        .status(200)
        .json({ status: false, message: `Package name must be unique.` });
    } else {
      return res
        .status(500)
        .json({ status: false, message: `Internal server error. ${e}` });
    }
  }
};

// edit event packege
exports.editEventPackage = async (req, res) => {
  try {
    const getPackage = await eventPackage
      .findOne({ _id: new ObjectId(req.params.id), isDelete: false })
      .lean();
    if (getPackage) {
      if (req.body.name !== getPackage.name) {
        const package = await eventPackage.find({
          name: req.body.name,
          isDelete: false,
        });
      }
      let description = `<div "font-family: 'Muller';">${req.body.description}</div>`;
      const packageData = await eventPackage.findByIdAndUpdate(
        req.params.id,
        {
          name: req.body.name ?? getPackage.name,
          description: description ?? getPackage.description,
          price: req.body.price ?? getPackage.price,
          event: req.body.event ?? getPackage.event,
          eventUrlFlag: req.body.eventUrlFlag ?? getPackage.eventUrlFlag,
          url: req.body.url ?? getPackage.url,
        },
        { new: true }
      );
      if (packageData)
        return res.status(200).json({
          status: true,
          message: "Package updated successfully!",
          data: packageData,
        });
      else
        return res.status(200).json({
          status: false,
          message: "Something went wrong while updating package!",
        });
    } else {
      return res
        .status(200)
        .json({ status: false, message: "Package not found!" });
    }
  } catch (e) {
    if (e.name === "MongoServerError" && e.code === 11000) {
      return res
        .status(200)
        .json({ status: false, message: `Package name must be unique.` });
    } else {
      return res
        .status(500)
        .json({ status: false, message: `Internal server error! ${e}` });
    }
  }
};

// delete event packege
exports.deleteEventPackage = async (req, res) => {
  try {
    const getPackage = await eventPackage
      .findOne({ _id: new ObjectId(req.params.id), isDelete: false })
      .lean();
    if (getPackage) {
      const packageData = await eventPackage.findByIdAndUpdate(
        req.params.id,
        { isDelete: true },
        { new: true }
      );
      if (packageData) {
        const ids = await eventPackage
          .find({ isDelete: false, event: getPackage.event, relation_id: ObjectId(req.relation_id)})
          .sort({ order: 1 });
        let resOrder = ids.map(async (item, i) => {
          await eventPackage.findByIdAndUpdate(
            ObjectId(item),
            { order: i + 1 },
            { new: true }
          );
        });
        await Promise.all([...resOrder]);
        return res.status(200).json({
          status: true,
          message: "Package deleted successfully!",
          data: packageData,
        });
      } else {
        return res.status(200).json({
          status: false,
          message: "Something went wrong while deleteing package!",
        });
      }
    } else {
      return res
        .status(200)
        .json({ status: false, message: "Package not found!" });
    }
  } catch (e) {
    return res
      .status(500)
      .json({ status: false, message: "Something went wrong!", error: e });
  }
};

// get all event packege
exports.getAllEventPackages = async (req, res) => {
  try {
    const allPackageData = await eventPackage
      .find({ isDelete: false, relation_id: ObjectId(req.relation_id),})
      .sort({ order: 1 });
    if (allPackageData)
      return res
        .status(200)
        .json({ status: true, message: "All packages!", data: allPackageData });
    else
      return res.status(200).json({
        status: false,
        message: "Something went wrong while getting all package!",
      });
  } catch (e) {
    return res
      .status(500)
      .json({ status: false, message: "Something went wrong!", error: e });
  }
};

// get all event packege by event id
exports.getAllEventPackagesByEventId = async (req, res) => {
  try {
    const sortField =
      req.query.sortField === "name"
        ? "name"
        : req.query.sortField === "price"
        ? "price"
        : "order";

    const sortType = req.query.sortType === "Asc" ? 1 : -1;
    const escapedName = req.query.search? escapeRegExp(req.query?.search): undefined;
    const allPackageData = await eventPackage
      .find({
        isDelete: false,
        event: req.params.eventId,
        relation_id: ObjectId(req.relation_id),
        ...(req.query.search && {
          name: { $regex: escapedName, $options: "i" },
        }),
      })
      // .collation({ locale: "en" })
      .sort({ [`${sortField}`]: sortType });
    if (allPackageData)
      return res
        .status(200)
        .json({ status: true, message: "All packages!", data: allPackageData });
    else
      return res.status(200).json({
        status: false,
        message: "Something went wrong while getting all package!",
      });
  } catch (e) {
    return res
      .status(500)
      .json({ status: false, message: "Something went wrong!", error: e });
  }
};

// get all event packege suggestion by event id
exports.getAllEventPackagesSuggestion = async (req, res) => {
  try {
    const allPackageData = await eventPackage
      .find({ isDelete: false, event: req.params.eventId, relation_id: ObjectId(req.relation_id) },{name:1}).sort({ name: 1 }).lean()

    if (allPackageData.length){
      return res
        .status(200)
        .json({ status: true, message: "All packages suggestion!", data: allPackageData });
    }else{
      return res.status(200).json({
        status: false,
        message: "Something went wrong while getting all package!",
      });
    }
  } catch (e) {
    return res
      .status(500)
      .json({ status: false, message: "Something went wrong!", error: e });
  }
};

// get event packege by ID
exports.getPackageDetail = async (req, res) => {
  try {
    const packageData = await eventPackage.findOne({
      _id: new ObjectId(req.params.id),
      isDelete: false,
    });
    if (packageData)
      return res
        .status(200)
        .json({ status: true, message: "Package detail!", data: packageData });
    else
      return res.status(200).json({
        status: false,
        message: "Something went wrong while getting package!",
      });
  } catch (e) {
    return res
      .status(200)
      .json({ status: false, message: "Something went wrong!", error: e });
  }
};

// get event packege reorder API
exports.packageReorder = async (req, res) => {
  try {
    const ids = req.body.ids;
    if (ids.length > 0) {
      let resOrder = ids.map(async (item, i) => {
        await eventPackage.findByIdAndUpdate(
          ObjectId(item),
          { order: i + 1 },
          { new: true }
        );
      });
      await Promise.all([...resOrder]);

      return res
        .status(200)
        .json({ status: true, message: "Package list rearrange succesfully!" });
    } else {
      return res.status(200).json({
        status: false,
        message: "Something went wrong while rearrange package!",
      });
    }
  } catch (error) {
    return res
      .status(200)
      .json({ status: false, message: "Something went wrong!", error: error });
  }
};

/** end Create, edit, delete and get all event packages **/
