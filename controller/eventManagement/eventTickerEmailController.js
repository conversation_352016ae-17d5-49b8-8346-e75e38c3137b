const moment = require("moment");
const momentTimeZone = require("moment-timezone");
const jwt = require("jsonwebtoken");
const CommunitiesService = require("../../microservices/user/components/communities/services/communities-service");
const { AWS_BUCKET, BASE_URL } = require("../../config/config");

const communitiesService = new CommunitiesService();

exports.getEventTicketRegistrationEmailTemplate = async (
  updateUserData,
  eventData,
  ticketData,
  subdomain,
  updatePaymentData,
  relation_id = null,
  isGuest = false,
  addonData = []
) => {
  const timeZoneStr = eventData.timeZone;
  const timeZoneStr1 = eventData.timeZone;
  const match = timeZoneStr.match(/UTC([+-]\d{2}:\d{2})/);
  const utcOffset = match ? match[1] : null;
  const [hoursStr, minutesStr] = utcOffset.split(":");
  const hours = parseInt(hoursStr);
  const minutes = parseInt(minutesStr);
  const sign = utcOffset[0];

  let community = null;
  if (relation_id)
    community = await communitiesService.repository.findCommunity({
      filter: {
        _id: relation_id,
        isDelete: false,
      },
    });

  let host = process.env.GATEWAY_DOMAIN;
  if (
    community &&
    community?.customDomain.domain &&
    community?.customDomain.status === "active"
  ) {
    host = community?.customDomain?.isMainDomain
      ? `https://www.${community?.customDomain.domain}`
      : `https://${community?.customDomain.domain}`;
  } else {
    host = `https://${community.nickname}.${BASE_URL}`;
  }

  const communitiyLogo = community?.logo
    ? `https://${AWS_BUCKET}.s3.us-east-2.amazonaws.com/groupos/${community._id}/${community.logo}`
    : "https://mds-community.s3.us-east-2.amazonaws.com/uploads/groupos-util/groupos_logo.png";

  // Calculating the total offset in minutes
  const totalOffsetMinutes =
    (Math.abs(hours) * 60 + minutes) * (sign === "-" ? -1 : 1);

  // Get timezone using moment-timezone
  const timezone = momentTimeZone.tz
    .names()
    .find((zone) => momentTimeZone.tz(zone).utcOffset() === totalOffsetMinutes);

  const location =
    eventData.locationType === "venue" || eventData.locationType === "inPerson"
      ? `${eventData.location.address},${eventData.location.city},${eventData.location.country},${eventData.location.postalCode}`
      : "";

  let gatway = process.env.GATEWAY_DOMAIN;
  let urlTemp = gatway.split("://");
  let urlMain = urlTemp[0] + "://" + subdomain + "." + urlTemp[1];

  const eventUrl = urlMain + "/events/u/" + eventData._id;
  const communityEventUrl = host + "/events/u/" + eventData._id;
  const guestUrl = host + "/guest-login";
  const orderUrl = urlMain + "/events/u/t/" + eventData._id;
  const communityOrderUrl = host + "/events/u/" + eventData._id;
  const contactTheOrganizerUrl = urlMain + "/events/u/t/" + eventData._id;
  const termsOfService = urlMain + "/events/u/t/" + eventData._id;
  const privacyPolicy = urlMain + "/events/u/t/" + eventData._id;

  const secretKey = "vOVH6sdmpNWjRRIqCc7rdxs01lwHzfr3";

  const startDateTime = moment(
    eventData.startDate + " " + eventData.startTime
  ).format("YYYYMMDDTHHmmss");
  const endDateTime = moment(
    eventData.endDate + " " + eventData.endTime
  ).format("YYYYMMDDTHHmmss");
  const location_type =
    eventData.locationType === "venue" || eventData.locationType === "inPerson"
      ? "In Person"
      : eventData.locationType === "online" ||
        eventData.locationType === "virtual"
      ? "Virtual"
      : "Will be announced later";

  let description = "";
  if (eventData.locationType === "virtual") {
    description =
      "For details, go here: <br/><br/>" +
      encodeURIComponent(eventData.onlineLocationDetail.onlineEventUrl) +
      "<br/><br/>" +
      encodeURIComponent(eventUrl);
  } else {
    description =
      "For details, go here: <br/><br/>" + encodeURIComponent(eventUrl);
  }

  let descriptionIcal = "";
  if (eventData.locationType === "virtual") {
    descriptionIcal =
      "For details, go here: \n\n" +
      eventData.onlineLocationDetail.onlineEventUrl +
      "\n\n" +
      eventUrl;
  } else {
    descriptionIcal = "For details, go here: \n\n" + eventUrl;
  }

  const startIcsDateTime = momentTimeZone
    .tz(
      `${eventData.startDate} ${eventData.startTime}`,
      "MM/DD/YYYY hh:mm a",
      timezone
    )
    .format();
  const endIcsDateTime = momentTimeZone
    .tz(
      `${eventData.endDate} ${eventData.endTime}`,
      "MM/DD/YYYY hh:mm a",
      timezone
    )
    .format();

  const eventICSData = {
    start: startIcsDateTime,
    end: endIcsDateTime,
    summary: eventData.title,
    description: descriptionIcal,
    location: location,
    timezone: timezone,
  };

  const token = jwt.sign(eventICSData, process.env.JWT_SECRET);

  const downloadUrl = `${host}/events/calender-reminder?token=${token}`;

  const ticketRaw = ticketData
    .map(
      (data) =>
        `<p
      style="margin:0;font-size:16px;line-height:24px;font-family:'Helvetica Neue',Helvetica,Arial,sans-serif;color:#060D14;font-weight: 600;letter-spacing: -0.1px;">
      ${data.qty} X ${data.ticketName} - ${data.type}</p>`
    )
    .join("");
  const groupedAddons = addonData.reduce((acc, addon) => {
    const key = `${addon.name}-${addon.price}`;
    if (!acc[key]) {
      acc[key] = { ...addon, count: 1 };
    } else {
      acc[key].count += 1;
    }
    return acc;
  }, {});

  // Generating HTML for grouped add-ons
  const addonRow = Object.values(groupedAddons)
    .map((addon) => {
      const prefix = `${addon.count} X `;
      return `<p style="margin:0;font-size:16px;line-height:24px;font-family:'Helvetica Neue',Helvetica,Arial,sans-serif;color:#060D14;font-weight:600;letter-spacing:-0.1px;">
      ${prefix}${addon.name} - (${addon.price}$)</p>`;
    })
    .join("");
  const totalAmountAddon = Object.values(groupedAddons)
    .map((addon) => {
      const count = addon.count;
      const prefix = `${addon.count} X `;
      const totalPrice = addon.price * count;
      return `<p  style="margin:0 0 4px;font-size:15px;line-height:24px;font-family:'Helvetica Neue',Helvetica,Arial,sans-serif;color:#394046;font-weight:normal;letter-spacing: -0.1px;">
      ${prefix}${addon.name} - (${totalPrice}$)</p>`;
    })
    .join("");

  const totalAmountTicket = ticketData
    .map(
      (data) =>
        `<p 
      style="margin:0 0 4px;font-size:15px;line-height:24px;font-family:'Helvetica Neue',Helvetica,Arial,sans-serif;color:#394046;font-weight:normal;letter-spacing: -0.1px;"> ${data.qty} X ${data.ticketName} - (${data.amount}$)
     </p>`
    )
    .join("");

  const mailData = {
    // email: `<EMAIL>`,
    email: `${updateUserData["Preferred Email"]}`,
    subject: `Order notification for ${eventData.title}!`,
    html: `<html xmlns:v="urn:schemas-microsoft-com:vml" xmlns:o="urn:schemas-microsoft-com:office:office" lang="en">
  
      <head>
        <title></title>
        <meta http-equiv="Content-Type" content="text/html; charset=utf-8">
        <meta name="viewport" content="width=device-width, initial-scale=1.0">
        <!--[if mso]><xml><o:OfficeDocumentSettings><o:PixelsPerInch>96</o:PixelsPerInch><o:AllowPNG/></o:OfficeDocumentSettings></xml><![endif]-->
      
      <body>
        <table width="100%" bgcolor="#F7F9FB"
          style="font-family:'Helvetica Neue',Helvetica,Arial,sans-serif;font-size:12px;font-style:normal;font-variant-caps:normal;font-weight:400;letter-spacing:normal;text-align:start;text-transform:none;white-space:normal;word-spacing:0px;text-decoration:none;min-width:100%">
          <tbody>
            <tr>
              <td style="padding: 0;">
                <table style="max-width: 680px;width: 100%;margin: 64px auto;background-color: #F7F9FB;">
                  <tbody>
                    <tr>
                      <td style="margin-left:auto;margin-right:auto">
                        <div style="margin: 40px 0;">
                          <img src=${communitiyLogo} style="display: block; height: auto; border: 0; width: 136px; max-width: 100%;" 
                            alt="Your Ticketing Platform Logo" width="160" height="47" width="136" alt="Your Ticketing Platform Logo" title="Logo">
                        </div>
                      </td>
                    </tr>
                    <tr>
                      <td style="padding: 0;">
                        <table style="width: 100%;padding: 48px 38px;background-color: #FFFFFF;">
                          <tbody>
                            <tr>
                              <td style="padding: 0;">
                                <p style="font-family: 'Helvetica Neue', Helvetica, Arial, sans-serif;
                                font-size: 36px;line-height: 40px;font-weight: 700;margin: 0;color: #060D14;">
                                ${updateUserData.first_name} ${
      updateUserData.last_name
    } you’ve got tickets!
                                </p>
                              </td>
                            </tr>
                            <tr>
                            <td style="padding: 0;">
                              <hr
                                style="width: 100%;border: 0;height: 1px;background-color: #0D0D0D1A; margin: 24px 0;">
                              </td>
                            </tr>
                            <tr>
                              <td style="padding: 0;">
                                <table>
                                  <tbody>
                                    <tr>
                                      <td style="padding: 0;">
                                        <p style="padding-bottom:18px;margin:0px;font-size:20px;line-height:28px;font-weight:700;font-family:'Helvetica Neue',Helvetica,Arial,sans-serif;color:#060D14">${
                                          eventData.title
                                        }</p>
                                      </td>
                                    </tr>
                                    <tr>
                                      <td style="padding: 0;">
                                        <table>
                                          <tr>
                                            <td width="26"
                                              style="border-collapse:collapse;vertical-align:top;padding: 0 0 18px;">
                                              <img src="${
                                                process.env.AWS_IMG_VID_PATH
                                              }uploads/mail-images/ticket_new.png"
                                                title="" alt="" border="0" width="18" height="18"
                                                style="border:0px;outline:currentcolor;text-decoration:none;height:auto;line-height:12px;display:inline-block;padding-top:4px;margin-right: 12px;width: 24px;"
                                                class="CToWUd" data-bit="iit">
                                            </td>
                                           <td style="border-collapse:collapse;vertical-align:top;padding: 0 0 18px;">
                                            ${ticketRaw}
                                            ${
                                              addonRow && addonRow.trim() !== ""
                                                ? `
                                                  <div style="margin-top: 12px;">
                                                    <p style="margin: 0 0 8px; font-size: 16px; line-height: 24px; font-family: 'Helvetica Neue', Helvetica, Arial, sans-serif; color: #060D14; font-weight: 700;">
                                                      Add-ons:
                                                    </p>
                                                    ${addonRow}
                                                  </div>
                                                `
                                                : ""
                                            }
                                          </td>
                                          </tr>
                                          <tr>
                                            <td width="26"
                                              style="margin-left:auto;margin-right:auto;vertical-align: top;padding: 0 0 18px;">
                                              <img src="${
                                                process.env.AWS_IMG_VID_PATH
                                              }uploads/mail-images/date_new.png" title=""
                                                alt="" border="0" width="18" height="18"
                                                style="border:0px;outline:currentcolor;text-decoration:none;height:auto;line-height:12px;display:inline-block;padding-top:4px;margin-right: 12px;width: 24px;"
                                                class="CToWUd" data-bit="iit">
                                            </td>
                                            <td style="margin-left:auto;margin-right:auto;padding: 0 0 18px;">
                                              <p
                                                style="margin:0;font-size:16px;line-height:24px;font-family:'Helvetica Neue',Helvetica,Arial,sans-serif;color:#000;font-weight: 600;letter-spacing: -0.1px;">
                                                ${moment(
                                                  eventData.startDate,
                                                  "MM/DD/YYYY"
                                                ).format("DD MMMM, YYYY")}
                                                ${moment(
                                                  eventData.startDate +
                                                    " " +
                                                    eventData.startTime,
                                                  "MM/DD/YYYY hh:mm a"
                                                ).format(
                                                  "hh:mm A"
                                                )} to ${moment(
      eventData.endDate,
      "MM/DD/YYYY"
    ).format("DD MMMM, YYYY")}
                                                ${moment(
                                                  eventData.endDate +
                                                    " " +
                                                    eventData.endTime,
                                                  "MM/DD/YYYY hh:mm a"
                                                ).format("hh:mm A")}</p>
                                              <p
                                                style="margin:0 0 5px;font-size:15px;line-height:24px;font-family:'Helvetica Neue',Helvetica,Arial,sans-serif;color:#394046;font-weight:normal;letter-spacing: -0.1px;">
                                                Timezone: ${timeZoneStr1}
                                              </p>
                                              <span
                                                style="display: inline-block;margin: 0 0px;font-size:15px;line-height: 24px;font-family:'Helvetica Neue',Helvetica,Arial,sans-serif;color:#394046;font-weight:normal;width: 100%;letter-spacing: -0.1px;">Add
                                                to <a
                                                  href="https://calendar.google.com/calendar/u/0/r/eventedit?text=${
                                                    eventData.title
                                                  }&dates=${moment(
      eventData.startDate + " " + eventData.startTime
    ).format("YYYYMMDDTHHmmssZ")}/${moment(
      eventData.endDate + " " + eventData.endTime
    ).format(
      "YYYYMMDDTHHmmssZ"
    )}&details=${description}&ctz=${timezone}&location${
      location !== "" ? "=" + location : ""
    }&pli=1"
                                                  style="text-decoration:none;color:rgb(63,96,231);font-family:'Helvetica Neue',Helvetica,Arial,sans-serif;font-weight:normal"
                                                  target="_blank">Google</a>
                                                <span
                                                  style="font-weight:bold;font-size: 18px;display: inline-block;line-height: 0;vertical-align: middle;padding-bottom: 12px;">.</span>
                                                <a href="${downloadUrl}"
                                                  style="text-decoration:none;color:rgb(63,96,231);font-family:'Helvetica Neue',Helvetica,Arial,sans-serif;font-weight:normal"
                                                  target="_blank">Outlook</a>
                                                <span
                                                  style="font-weight:bold;font-size: 18px;display: inline-block;line-height: 0;vertical-align: middle;padding-bottom: 12px;">.</span><a
                                                  href="${downloadUrl}"
                                                  style="text-decoration:none;color:rgb(63,96,231);font-family:'Helvetica Neue',Helvetica,Arial,sans-serif;font-weight:normal"
                                                  target="_blank"> iCal</a>
                                                <span
                                                  style="font-weight:bold;font-size: 18px;display: inline-block;line-height: 0;vertical-align: middle;padding-bottom: 12px;">.</span>
                                                <a href="https://calendar.yahoo.com/?v=60&view=d&type=20&title=${encodeURIComponent(
                                                  eventData.title
                                                )}&st=${startDateTime}&et=${endDateTime}${
      location !== "" ? "&in_loc=" + location : ""
    }&tz=${timezone}&desc=${description}"
                                                  style="text-decoration:none;color:rgb(63,96,231);font-family:'Helvetica Neue',Helvetica,Arial,sans-serif;font-weight:normal"
                                                  target="_blank">Y<wbr>ahoo</a></span>
                                            </td>
                                          </tr>
                                          <tr>
                                            <td width="26"
                                              style="border-collapse:collapse;vertical-align:top;padding: 0;">
                                              <img src="${
                                                process.env.AWS_IMG_VID_PATH
                                              }uploads/mail-images/place_new.png" title=""
                                                alt="" border="0" width="18" height="18"
                                                style="border:0px;outline:currentcolor;text-decoration:none;height:auto;line-height:12px;display:inline-block;padding-top:4px;margin-right: 12px;width: 24px;"
                                                class="CToWUd" data-bit="iit">
                                            </td>
                                            <td style="margin-left:auto;margin-right:auto;padding: 0;">
                                              <p
                                                style="margin:4px 0px 0;font-size:16px;line-height:24px;font-family:'Helvetica Neue',Helvetica,Arial,sans-serif;color:#060D14;font-weight:600;letter-spacing: -0.1px;">
                                                Location</p>
                                              <p
                                                style="margin:0 0 4px;font-size:15px;line-height:24px;font-family:'Helvetica Neue',Helvetica,Arial,sans-serif;color:#394046;font-weight:normal;letter-spacing: -0.1px;">
                                                ${location_type}</p>
                                            </td>
                                          </tr>
                                        </table>
                                      </td>
                                    </tr>
                                    <tr>
                                      <td style="padding: 24px 0;">
                                        <a href="${communityEventUrl}" target="_blank"
                                          style="background-color: #0B42CF;border-radius: 50px;color: #FFF;font-size: 15px;line-height: 24px;font-family: 'Helvetica Neue', Helvetica, Arial, sans-serif;display: inline-block;padding: 12px 16px;text-decoration: none;font-weight: 600;letter-spacing: -0.1px;">Go
                                          to event page</a>
                                         <!--  ${ isGuest ?`
                                           <a href="${guestUrl}" target="_blank"
                                          style="background-color: #0B42CF;border-radius: 50px;color: #FFF;font-size: 15px;line-height: 24px;font-family: 'Helvetica Neue', Helvetica, Arial, sans-serif;display: inline-block;padding: 12px 16px;text-decoration: none;font-weight: 600;letter-spacing: -0.1px; margin-left: 8px;">Login
                                          as guest</a>`:``} -->
                                      </td>
                                    </tr>
                                    ${
                                      eventData.locationType === "virtual"
                                        ? `<tr>
                                      <td style="padding: 0;">
                                        <p
                                          style="font-size: 15px;line-height: 24px;font-family: 'Helvetica Neue', Helvetica, Arial, sans-serif;color: #060D14;margin: 0;">
                                          or access event:
                                          <a href="${eventData.onlineLocationDetail.onlineEventUrl}" target="_blank"
                                            style="color: #0B42CF;text-decoration: none;letter-spacing: -0.1px;">${eventData.onlineLocationDetail.onlineEventUrl}</a>
                                        </p>
                                      </td>
                                    </tr>`
                                        : ``
                                    }
                                    <tr>
                                      <td style="padding: 0;">
                                        <hr
                                          style="width: 100%;border: 0;height: 1px;background-color: #0D0D0D1A; margin: 24px 0;">
                                      </td>
                                    </tr>
  
                                    ${
                                      updatePaymentData !== ""
                                        ? `<tr>
                                        <td style="margin-left:auto;margin-right:auto;padding: 0;">
                                          <p
                                            style="margin:4px 0px 0;font-size:16px;line-height:24px;font-family:'Helvetica Neue',Helvetica,Arial,sans-serif;color:#060D14;font-weight:600;letter-spacing: -0.1px;">
                                            Order Summary</p>
                                          <p
                                            style="margin:0 0 4px;font-size:15px;line-height:24px;font-family:'Helvetica Neue',Helvetica,Arial,sans-serif;color:#394046;font-weight:normal;letter-spacing: -0.1px;">
                                            ${moment().format(
                                              "DD MMMM, YYYY h:mm A"
                                            )}</p>
                                        </td>
                                      </tr>
                                      <tr>
                                          <td style="margin-left:auto;margin-right:auto;padding: 0;">
                                            <p
                                              style="margin:4px 0px 0;font-size:16px;line-height:24px;font-family:'Helvetica Neue',Helvetica,Arial,sans-serif;color:#060D14;font-weight:600;letter-spacing: -0.1px;">
                                              PAID </p>
                                            <p 
                                              style="margin:0 0 4px;font-size:15px;line-height:24px;font-family:'Helvetica Neue',Helvetica,Arial,sans-serif;color:#394046;font-weight:normal;letter-spacing: -0.1px;"> ${
                                                updateUserData.first_name
                                              } ${updateUserData.last_name}
                                            </p>
                                              ${totalAmountTicket}
                                              ${totalAmountAddon}
                                          </td>
                                        </tr>
                                        <tr>
                                            <td style="margin-left:auto;margin-right:auto;padding: 0;">
                                              <p
                                                style="font-size: 15px;line-height: 24px;font-family: 'Helvetica Neue', Helvetica, Arial, sans-serif;color: #060D14;margin: 0;">
                                                <a href="${communityOrderUrl}" target="_blank"
                                                  style="color: #0B42CF;text-decoration: none;letter-spacing: -0.1px;">View and
                                                  manage </a> your order.
                                              </p>
                                              <p style="font-size: 15px; line-height: 24px; font-family: 'Helvetica Neue', Helvetica, Arial, sans-serif; color: #060D14; font-weight: bold; margin: 0;">
                                                Please log in to update your order.
                                              </p>
                                            </td>
                                         </tr>`
                                        : ``
                                    }
  
                                    <tr>
                                      <td style="padding: 0;">
                                        <hr
                                          style="width: 100%;border: 0;height: 1px;background-color: #0D0D0D1A; margin: 24px 0;">
                                      </td>
                                    </tr>
                                    <!--
                                    <tr>
                                      <td style="padding: 0;">
                                        <h6
                                          style="padding-bottom:12px;margin:0px;font-size:20px;line-height:28px;font-weight:700;font-family:'Helvetica Neue',Helvetica,Arial,sans-serif;color:#060D14">
                                          Want to change something?</h6>
                                        <p
                                          style="font-size: 15px;line-height: 24px;font-family: 'Helvetica Neue', Helvetica, Arial, sans-serif;color: #060D14;margin: 0;">
                                          <a href="${communityOrderUrl}" target="_blank"
                                            style="color: #0B42CF;text-decoration: none;letter-spacing: -0.1px;">View and manage </a> your order.
                                        </p>
                                      </td>
                                    </tr>
                                    -->
                                  </tbody>
                                </table>
                              </td>
                            </tr>
                          </tbody>
                        </table>
                      </td>
                    </tr>
                    <tr>
                      <!-- Social media icons -->
                      <td style="padding: 0;">
                        <!-- <div style="max-width: 184px;margin: auto;margin-bottom: 16px">
                          <a href="https://www.youtube.com/@milliondollarsellers" target="_blank" style="text-decoration:none;display:inline-block;margin-right: 11px;width: 24px;height: 24px;object-fit: contain;object-position: center;">
                            <img src="${ process.env.AWS_IMG_VID_PATH }uploads/mail-images/youtube-color.png" title="" alt="" width="24px" height="24px" >
                          </a>
                          <a href="https://www.instagram.com/mdsonly/" target="_blank" style="text-decoration:none;display:inline-block;margin-right: 11px;width: 24px;height: 24px;object-fit: contain;object-position: center;">
                            <img src="${  process.env.AWS_IMG_VID_PAT }uploads/mail-images/instagram-color.png" title="" alt="" width="24px" height="24px" >
                          </a>
                          <a href="https://x.com/mdsonly?lang=en" target="_blank" style="text-decoration:none;display:inline-block;margin-right: 11px;width: 24px;height: 24px;object-fit: contain;object-position: center;">
                            <img src="${ process.env.AWS_IMG_VID_PATH }uploads/mail-images/twitter-color.png" title="" alt="" width="24px" height="24px" >
                          </a>
                          <a href="https://www.facebook.com/milliondollarsellers" target="_blank" style="text-decoration:none;display:inline-block;margin-right: 11px;width: 24px;height: 24px;object-fit: contain;object-position: center;">
                            <img src="${ process.env.AWS_IMG_VID_PATH }uploads/mail-images/facebook-color.png" title="" alt="" width="24px" height="24px" >
                          </a>
                          <a href="https://www.linkedin.com/company/million-dollar-sellers" target="_blank" style="text-decoration:none;display:inline-block;margin-right: 0;width: 24px;height: 24px;object-fit: contain;object-position: center;">
                            <img src="${ process.env.AWS_IMG_VID_PATH }uploads/mail-images/linkedin-color.png" title="" alt="" width="24px" height="24px" >
                          </a>
                        </div> -->
                        <!-- // <a href="javascript:void(0)" target="_blank" style="display: block;text-align: center; font-size: 15px;line-height: 24px;font-family: 'Helvetica Neue', Helvetica, Arial, sans-serif;color: #0B42CF;text-decoration: none;letter-spacing: -0.1px;">Unsubscribe from our emails</a> -->
                      </td>
                    </tr>
                  </tbody>
                </table>
              </td>
            </tr>
            <tr>
           
            </tr>
          </tbody>
        </table>
      </body>
      
      </html>`,
    relationId: community?._id ? community?._id : "",
    customsmtp: community?.customSMTP ? community?.customSMTP : false,
  };
  return mailData;
};
