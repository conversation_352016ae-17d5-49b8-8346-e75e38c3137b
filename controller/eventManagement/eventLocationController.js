/*
This file created by BPA.
Code is already developed by respective developers ( BPA only done function separation in separate files ).
*/

const { ObjectId } = require("mongodb");
const eventLocation = require("../../database/models/eventLocation");
const eventActivity = require("../../database/models/eventActivity");
const eventRoom = require("../../database/models/eventRoom");

/** Admin APIs starts **/

/**  starts Create, edit, delete and get all event locations **/
// create event location
exports.createEventLocation = async (req, res) => {
  try {
    let locationImages = [];
    if (req.body.address && req.body.country && req.body.city) {
      if (req?.locationImages.length > 0) {
        req?.locationImages.sort((a, b) => a.order - b.order);
        let resOrder = req?.locationImages.map(async (item, i) => {
          locationImages.push(item.img);
        });
        await Promise.all([...resOrder]);
        req.locationImages = locationImages;
      }
      const newLocation = new eventLocation({
        name: req.body.name,
        address: req.body.address,
        country: req.body.country,
        city: req.body.city,
        placeId: req.body.placeId,
        event: req.body.event,
        postalCode: req.body.postalCode,
        latitude: req.body.latitude,
        longitude: req.body.longitude,
        locationImages: req?.locationImages ?? [],
        locationVisible: req.body.locationVisible,
        relation_id: ObjectId(req.relation_id),
      });
      const locationData = await newLocation.save();
      if (locationData)
        return res.status(200).json({
          status: true,
          message: "Location added successfully!",
          data: locationData,
        });
      else
        return res.status(200).json({
          status: false,
          message: "Something went wrong while adding location!",
        });
    } else {
      return res.status(200).json({
        status: false,
        message: "Address, country and city are required fields!",
      });
    }
  } catch (e) {
    return res
      .status(500)
      .json({ status: false, message: "Something went wrong!", error: e });
  }
};

// edit event location
exports.editEventLocation = async (req, res) => {
  try {
    const getLocation = await eventLocation
      .findOne({ _id: new ObjectId(req.params.id), isDelete: false })
      .lean();
    if (getLocation) {
      var locationImg = [],
        orderImages = [],
        oldImages = [];
      if (
        req.body.locationOldData !== undefined &&
        req.body.locationOldData !== null &&
        req.body.locationOldData !== "null"
      ) {
        if (typeof req.body.locationOldData !== "string") {
          if (req?.locationImages.length > 0) {
            req?.locationImages.sort((a, b) => a.order - b.order);
          }
          if (
            req.body.locationOldData.length > 0 &&
            req?.locationImages.length > 0
          ) {
            [
              ...req.body?.locationOldData,
              ...req?.locationImages.map((image) => {
                return image.img;
              }),
            ].forEach((locationData, i) => {
              locationImg.push({ img: locationData, order: i + 1 });
            });
          } else if (req.body.locationOldData.length > 0) {
            req.body?.locationOldData.forEach((locationData, i) => {
              locationImg.push({ img: locationData, order: i + 1 });
            });
          } else if (req?.locationImages.length > 0) {
            req?.locationImages.forEach((locationDetails) => {
              locationImg.push({
                img: locationDetails.img,
                order: locationDetails.order,
              });
            });
          }
        } else {
          oldImages.push(req.body.locationOldData);
          oldImages.forEach((locationData, i) => {
            locationImg.push({ img: locationData, order: i + 1 });
          });
          if (req?.locationImages.length > 0) {
            req?.locationImages.forEach((locationDetails) => {
              locationImg.push({
                img: locationDetails.img,
                order: locationDetails.order,
              });
            });
          }
        }
      } else {
        if (req?.locationImages.length > 0) {
          req?.locationImages.sort((a, b) => a.order - b.order);
          req?.locationImages.forEach((locationDetails) => {
            locationImg.push({
              img: locationDetails.img,
              order: locationDetails.order,
            });
          });
        }
      }

      if (locationImg.length > 0) {
        let resOrder = locationImg.map(async (item, i) => {
          orderImages.push(item.img);
        });
        await Promise.all([...resOrder]);
        locationImg = orderImages;
      }

      const locationData = await eventLocation.findByIdAndUpdate(
        req.params.id,
        {
          name: req.body.name ?? getLocation.name,
          address: req.body.address ?? getLocation.address,
          country: req.body.country ?? getLocation.country,
          city: req.body.city ?? getLocation.city,
          placeId: req.body.placeId ?? getLocation.placeId,
          event: req.body.event ?? getLocation.event,
          postalCode: req.body.postalCode ?? getLocation.postalCode,
          latitude: req.body.latitude ?? getLocation.latitude,
          longitude: req.body.longitude ?? getLocation.longitude,
          locationImages: locationImg
            ? locationImg
            : getLocation.locationImages.length > 0
            ? []
            : getLocation?.locationImages,
          locationVisible: req.body.locationVisible
            ? req.body.locationVisible
            : getLocation.locationVisible === undefined
            ? false
            : getLocation.locationVisible,
        },
        { new: true }
      );

      if (locationData)
        return res.status(200).json({
          status: true,
          message: "Location updated successfully!",
          data: locationData,
        });
      else
        return res.status(200).json({
          status: false,
          message: "Something went wrong while updating location!",
        });
    } else {
      return res
        .status(200)
        .json({ status: false, message: "Location not found!" });
    }
  } catch (e) {
    return res
      .status(500)
      .json({ status: false, message: "Something went wrong!", error: e });
  }
};

// delete event location
exports.deleteEventLocation = async (req, res) => {
  try {
      const { eventId } = req.body;
    const alreadyAssignRoom = await eventRoom
      .find({ event:eventId,location: new ObjectId(req.params.id), isDelete: false, status: "published", relation_id: ObjectId(req.relation_id) })
      .lean();
    const alreadyAssignActivity = await eventActivity
      .find({ event:eventId , location: new ObjectId(req.params.id), isDelete: false,relation_id: ObjectId(req.relation_id) })
      .lean();
    if (
      (alreadyAssignRoom && alreadyAssignRoom.length > 0) ||
      (alreadyAssignActivity && alreadyAssignActivity.length > 0)
    ) {
      var roomList = [];
      if (alreadyAssignRoom.length > 0) {
        alreadyAssignRoom.map((itemRoom, i) => {
          roomList.push(itemRoom.name);
        });
      }

      var activityList = [];
      if (alreadyAssignActivity.length > 0) {
        alreadyAssignActivity.map((itemActivity, i) => {
          activityList.push(itemActivity.name);
        });
      }

      return res.status(200).json({
        status: false,
        message:
          "You cannot delete this location because it is assigned to following rooms and activities: ",
        data: { roomList, activityList },
      });
    } else {
      const getLocation = await eventLocation
        .findOne({ _id: new ObjectId(req.params.id), isDelete: false,relation_id: ObjectId(req.relation_id) })
        .lean();
      if (getLocation) {
        const locationData = await eventLocation.findByIdAndUpdate(
          req.params.id,
          { isDelete: true },
          { new: true }
        );
        if (locationData)
          return res.status(200).json({
            status: true,
            message: "Location deleted successfully!",
            data: locationData,
          });
        else
          return res.status(200).json({
            status: false,
            message: "Something went wrong while deleteing location!",
          });
      } else {
        return res
          .status(200)
          .json({ status: false, message: "Location not found!" });
      }
    }
  } catch (e) {
    return res
      .status(500)
      .json({ status: false, message: "Something went wrong!", error: e });
  }
};

// get all event location
exports.getAllEventLocations = async (req, res) => {
  try {
    const allLocationData = await eventLocation
      .find({ isDelete: false, relation_id: ObjectId(req.relation_id) })
      .sort({ createdAt: -1 });
    if (allLocationData)
      return res.status(200).json({
        status: true,
        message: "All locations!",
        data: allLocationData,
      });
    else
      return res.status(200).json({
        status: false,
        message: "Something went wrong while getting all event locations!",
      });
  } catch (e) {
    return res
      .status(500)
      .json({ status: false, message: "Something went wrong!", error: e });
  }
};

// get all event location by event id
exports.getAllEventLocationsByEventId = async (req, res) => {
  try {
    const sortField =
      req.query.sortField === "name"
        ? "name"
        : req.query.sortField === "address"
        ? "address"
        : req.query.sortField === "city"
        ? "city"
        : req.query.sortField === "country"
        ? "country"
        : "createdAt";
    const sortType = req.query.sortType === "Asc" ? 1 : -1;

    const allLocationData = await eventLocation
      .find({ isDelete: false, event: req.params.eventId, relation_id: ObjectId(req.relation_id) })
      // .collation({ locale: "en" })
      .sort({ [`${sortField}`]: sortType });
    if (allLocationData)
      return res.status(200).json({
        status: true,
        message: "All locations!",
        data: allLocationData,
      });
    else
      return res.status(200).json({
        status: false,
        message: "Something went wrong while getting all event locations!",
      });
  } catch (e) {
    return res
      .status(500)
      .json({ status: false, message: "Something went wrong!", error: e });
  }
};

// get all event location suggestion by event id
exports.getAllEventLocationsSuggestion = async (req, res) => {
  try {
 
    const allLocationData = await eventLocation
      .find({ isDelete: false, event: req.params.eventId, relation_id: ObjectId(req.relation_id) },{name:1}).sort({ name: 1 }).lean();

    if (allLocationData.length)
      return res.status(200).json({
        status: true,
        message: "All locations suggestion!",
        data: allLocationData,
      });
    else
      return res.status(200).json({
        status: false,
        message: "Something went wrong while getting all event locations!",
      });
  } catch (e) {
    return res
      .status(500)
      .json({ status: false, message: "Something went wrong!", error: e });
  }
};

// get event location by ID
exports.getLocationDetail = async (req, res) => {
  try {
    const locationData = await eventLocation.findOne({
      _id: new ObjectId(req.params.id),
      isDelete: false,
    });
    if (locationData)
      return res.status(200).json({
        status: true,
        message: "Location detail!",
        data: locationData,
      });
    else
      return res.status(200).json({
        status: false,
        message: "Something went wrong while getting location!",
      });
  } catch (e) {
    return res
      .status(500)
      .json({ status: false, message: "Something went wrong!", error: e });
  }
};
/** end Create, edit, delete and get all event locations **/
