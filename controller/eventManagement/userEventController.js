/*
This file created by BPA.
Code is already developed by respective developers ( BPA only done function separation in separate files ).
*/

const { ObjectId } = require("mongodb");
const moment = require("moment");
const { validationResult } = require('express-validator');
const eventLocation = require("../../database/models/eventLocation");
const eventActivity = require("../../database/models/eventActivity");
const event = require("../../database/models/event");
const eventView = require("../../database/models/eventView");
const eventAttendees = require("../../database/models/eventAttendees");
const eventSession = require("../../database/models/eventSession");
const eventParticipantAttendees = require("../../database/models/eventParticipantAttendees");
const eventWiseParticipantTypes = require("../../database/models/eventWiseParticipantTypes");
const contentArchiveTag = require("../../database/models/contentArchive_tag");
const { userAccessRulesCommonCondition } = require("../../controller/userAccessRules/restrictionAccess");
const { getEventCount, getEventCountV2 } = require("../../controller/eventManagement/eventController");
const User = require("../../database/models/airTableSync");
const {
  send_notification,
  notification_template,
  addTime,
  subtractTime,
} = require("../../utils/notification");
const debugErrorLogs = require("../../middleware/debugErrorLogs");
const chatChannel = require("../../database/models/chatChannel/chatChannel");

/** User APIs starts **/

// get event list for user
exports.getEventList = async (req, res) => {
  try {
    const sortField = (req.query.sortField === "startDate" ?"startDate" : req.query.sortField === "title" ? "title" : "startDate")
    const sortType = req.query.sortType === "Dec" ? -1 : 1;

    const authUser = req.authUserId;
    const role = req.query.role;
    const localDate = new Date(req.query.localDate);
    const userData = await User.findById(authUser).select(
      "firebaseId email accessible_groups purchased_plan attendeeDetail"
    );
    let ruleCondition =  await userAccessRulesCommonCondition({userId:authUser,relation_id:req.relation_id});
    var eventList = [],
      location = {};
    var eventListData = [];
    let search = "";

    // get upcomming ,past ,myevent count
    let categoryId = req.query.categoryId ? req.query.categoryId :""
    let subCategoryId = req.query.subCategoryId ? req.query.subCategoryId :""
    let city = req.query.city ? req.query.city :""

    let allEventCount = await getEventCount(req.relation_id,localDate,authUser,"myEvent",categoryId,subCategoryId,"","",search,city);
    
    if (userData !== null && userData !== undefined) {
      let condition = { _id: authUser, isDelete: false };
      if( role && role == "nonMember" ){
        condition = { _id: authUser, $or: [{ isDelete: false }, { isDelete: { $exists: false } }] }
      }
      const eventAttendeesData = await User.findOne(
        condition,
        { attendeeDetail: 1 }
      );
      if (eventAttendeesData !== null) {
        let conditionTemp = { '$ne': ["$role", "Member"] };
        if ( role === "member" ) {
          conditionTemp = { '$in': ["$role", [ "Member", "Staff" ]] };
        }
        let pipeline = [
          {
            '$match': {
              // event: eventId,
              user: new ObjectId(authUser),
              isDelete: false,
            }
          }, 
          {
            '$lookup': {
              'from': 'event_wise_participant_types',
              'localField': 'role',
              'foreignField': '_id',
              'as': 'event_wise_participant_types_result',
              'pipeline': [
                {
                  '$match': {
                    '$expr': conditionTemp
                  }
                }
              ]
            }
          }, 
          {
            '$unwind': {
              'path': '$event_wise_participant_types_result',
              'preserveNullAndEmptyArrays': false
            }
          }
        ]

        let attendeesList = await eventParticipantAttendees.aggregate(pipeline);
        if (attendeesList && attendeesList.length != 0) {
          for (let i = 0; i < attendeesList.length; i++) {
            let eventId = attendeesList[i]["event"];
            let match = {
              _id: eventId,
              isDelete: false,
              status: "published",
              relation_id: ObjectId(req.relation_id),
            }
            
            if (req.query.search) {
            search = req.query.search;
            let tagIds = []
              const matchingTags = await contentArchiveTag.find({
                name: { $regex: ".*" + search + ".*", $options: "i" },
                isDelete: false
              }).select("_id").lean();
              
              if(matchingTags.length > 0){
                tagIds = matchingTags.map(tag => tag._id);
              }
            match = {
                    ...match,
                    $or: [
                        { title: { $regex: ".*" + search + ".*", $options: "i" }, },
                        { tag: { $in: tagIds } }
                    ]
                };
            }
            if (req.query.categoryId){
              match={
                ...match,
                category: ObjectId(req.query.categoryId)
              }
            }
            if (req.query.categoryId && req.query.subCategoryId){
              match={
                ...match,
                category: ObjectId(req.query.categoryId),
                subcategory:ObjectId(req.query.subCategoryId)
              }
            }
            if (req.query.city) {
              match = {
                ...match,
                "location.city": req.query.city
              };
            }
            // if (( attendeesList[i]["event_wise_participant_types_result"]["role"] === "Member" || attendeesList[i]["event_wise_participant_types_result"]["role"] === "Staff" ) && role === "member") {

            let mergedCondition = {
              $and: [
                match,
                ruleCondition
              ]
            };
            
            if (( attendeesList[i]["event_wise_participant_types_result"]["role"] === "Member" || attendeesList[i]["event_wise_participant_types_result"]["role"] === "Staff" ) && role === "member") {
              let eventData = await event
                .findOne(
                  mergedCondition,
                  {
                    _id: 1,
                    title: 1,
                    thumbnail: 1,
                    eventUrl: 1,
                    startDate: 1,
                    startTime: 1,
                    endDate: 1,
                    endTime: 1,
                    timeZone: 1,
                    location: 1,
                    eventLocation : "$location",
                    locationType: 1,
                    tag: 1,
                  }
                ).sort({ [`${sortField}`]: sortType })
                .lean();
              
              if (eventData !== null) {
                const aggregate = [
                  { $match : { _id: new ObjectId(eventData._id) } },
                  {
                    $lookup:{
                        from: "event_tickets",
                        let: {
                          event_id: "$_id"
                        },
                      pipeline: [
                        {
                          $match: {
                            $expr: {
                              $eq: ["$eventId", "$$event_id"]
                            },
                            isDelete: false
                          }
                        },
                        {
                          $project: {
                            actualPrice: 1,
                            _id: 0
                          }
                        }
                      ],
                      as: "priceData"
                    },
                  },
                  {
                      $lookup: {
                          from: "event_participant_attendees",
                          localField: "_id",
                          foreignField: "event",
                          as: "event_participant_attendees_result",
                          pipeline: [
                            {
                              $match: {
                                $expr: {
                                  $eq: [
                                    ObjectId(userData._id),
                                    "$user",
                                  ],
                                },
                              },
                            },
                            {
                              $match: {
                                $expr: {
                                  $eq: [
                                    "$isDelete",
                                    false
                                  ],
                                },
                              },
                            },
                          ],
                      },
                  },
                  {
                      $project: {
                          _id: 1, title: 1, 
                          // type: ["$type.name"],
                          price: {
                              $ifNull: [
                                  {
                                      $min: {
                                          $filter: {
                                              input: "$priceData.actualPrice",
                                              cond: { $gt: ["$$this", 0] }
                                          }
                                      }
                                  },
                                  0
                              ],
                          },
                          registationFlag: {
                              $let: {
                                  vars: { }, 
                                  in: {
                                      $gt: [{ $size: "$event_participant_attendees_result" }, 0]
                                  }
                              }
                          },
                      },
                  },
                ];

                const getOtherData = await event.aggregate(aggregate);
                // eventData.type = getOtherData[0].type
                eventData.price = getOtherData[0].price
                eventData.registationFlag = getOtherData[0].registationFlag

                let eventActivityCount = await eventActivity.countDocuments(
                  { event: eventId, isDelete: false }
                );

                if (
                  eventData.location !== undefined &&
                  eventData.location !== "" &&
                  eventData.location !== null
                ) {
                  eventData = {
                    ...eventData,
                    city: eventData.location.address
                      ? eventData.location.city
                      : null,
                    country: eventData.location.address
                      ? eventData.location.country
                      : null,
                    activityCount: eventActivityCount,
                  };
                  delete eventData.location;
                  eventList.push(eventData);
                } else {
                  location = await eventLocation
                    .findOne({
                      event: eventId,
                      locationVisible: true,
                      isDelete: false,
                    })
                    .lean();
                  delete eventData.location;
                  if (location !== null) {
                    eventData = {
                      ...eventData,
                      city: location ? location.city : null,
                      country: location ? location.country : null,
                      activityCount: eventActivityCount,
                    };
                  } else {
                    eventData = {
                      ...eventData,
                      city: null,
                      country: null,
                      activityCount: eventActivityCount,
                    };
                  }
                  eventList.push(eventData);
                }
              }
            } else {
              let eventData = await event
                .findOne(
                  mergedCondition,
                  {
                    _id: 1,
                    title: 1,
                    thumbnail: 1,
                    eventUrl: 1,
                    startDate: 1,
                    startTime: 1,
                    endDate: 1,
                    endTime: 1,
                    timeZone: 1,
                    location: 1,
                    eventLocation : "$location",
                    locationType: 1,
                    tag: 1,
                  }
                ).sort({ [`${sortField}`]: sortType })
                .lean();

              if (eventData !== null) {
                const getOtherData = await event.aggregate([
                  { $match : { _id: eventData._id } },
                  {
                      $lookup: {
                          from: "eventpackages",
                          let: { event_id: "$_id" },
                          pipeline: [
                              {
                                  $match: {
                                      $expr: {
                                          $eq: ["$event", "$$event_id"],
                                      },
                                      isDelete: false,
                                  },
                              },
                              { $project: { price: 1, _id: 0 } },
                          ],
                          as: "priceData"
                      }
                  },
                  {
                      $lookup: {
                          from: "event_participant_attendees",
                          localField: "_id",
                          foreignField: "event",
                          as: "event_participant_attendees_result",
                          pipeline: [
                            {
                              $match: {
                                $expr: {
                                  $eq: [
                                    ObjectId(userData._id),
                                    "$user",
                                  ],
                                },
                              },
                            },
                            {
                              $match: {
                                $expr: {
                                  $eq: [
                                    "$isDelete",
                                    false
                                  ],
                                },
                              },
                            },
                          ],
                      },
                  },
                  {
                      $project: {
                          _id: 1, title: 1, 
                          // type: ["$type.name"],
                          price: {
                              $ifNull: [
                                  {
                                      $min: {
                                          $filter: {
                                              input: "$priceData.price",
                                              cond: { $gt: ["$$this", 0] }
                                          }
                                      }
                                  },
                                  0
                              ],
                          },
                          registationFlag: {
                              $let: {
                                  vars: { }, 
                                  in: {
                                      $gt: [{ $size: "$event_participant_attendees_result" }, 0]
                                  }
                              }
                          },
                      },
                  },
                ]);
                // eventData.type = getOtherData[0].type
                eventData.price = getOtherData[0].price
                eventData.registationFlag = getOtherData[0].registationFlag
                
                let eventActivityCount = await eventActivity.countDocuments(
                  { event: eventId, isDelete: false }
                );

                if (
                  eventData.location !== undefined &&
                  eventData.location !== "" &&
                  eventData.location !== null
                ) {
                  eventData = {
                    ...eventData,
                    city: eventData.location.address
                      ? eventData.location.city
                      : null,
                    country: eventData.location.address
                      ? eventData.location.country
                      : null,
                    activityCount: eventActivityCount,
                  };
                  delete eventData.location;
                  eventList.push(eventData);
                } else {
                  location = await eventLocation
                    .findOne({
                      event: eventId,
                      locationVisible: true,
                      isDelete: false,
                    })
                    .lean();
                  delete eventData.location;
                  if (location !== null) {
                    eventData = {
                      ...eventData,
                      city: location ? location.city : null,
                      country: location ? location.country : null,
                      activityCount: eventActivityCount,
                    };
                  } else {
                    eventData = {
                      ...eventData,
                      city: null,
                      country: null,
                      activityCount: eventActivityCount,
                    };
                  }
                  eventList.push(eventData);
                }
              }
            }
          //}
        }

        if (eventList.length > 0) {
          for (let index = 0; index < eventList.length; index++) {
            // Input date and time
            const eventDate = eventList[index].endDate;
            const eventTime = eventList[index].endTime;
            const timeZone = eventList[index].timeZone;

            // Create a new Date object using the input date and time
            const sign = timeZone.substring(4, 5);
            const utcHour = timeZone.substring(5, 7);
            const utcMinute = timeZone.substring(8, 10);
            const hour24Formate = moment(eventTime, "h:mm a").format("HH:mm");

            // saprate date and time in hours and mins
            const year = moment(eventDate, "MM-DD-YYYY").year();
            const month = moment(eventDate, "MM-DD-YYYY").month();
            const day = moment(eventDate, "MM-DD-YYYY").get("date");
            const hours = moment(hour24Formate, "h:mm a").hours();
            const minutes = moment(hour24Formate, "h:mm a").minutes();

            var endDate = new Date(year, month, day, hours, minutes);
            if (sign === "+") {
              endDate = await subtractTime(
                endDate,
                parseInt(utcHour),
                parseInt(utcMinute)
              );
            } else if (sign === "-") {
              endDate = await addTime(
                endDate,
                parseInt(utcHour),
                parseInt(utcMinute)
              );
            }

            if (endDate >= localDate) {
              eventListData.push(eventList[index]);
            }
          }
        }

        let finalEventListData = [];
        for (let i = 0; i < eventListData.length; i++) {
          const event = eventListData[i];
          let isExist = finalEventListData.find(o => o["_id"].toString() === eventListData[i]["_id"].toString());
          if(!isExist){
            finalEventListData.push(event);
          }
        }

        // Sort finalEventListData
        finalEventListData.sort((a, b) => {
          if (sortField === 'startDate') {
            const startDateA = new Date(
              parseInt(a.startDate.substring(6)), 
              parseInt(a.startDate.substring(0, 2)) - 1,
              parseInt(a.startDate.substring(3, 5))
          );
          const startDateB = new Date(
              parseInt(b.startDate.substring(6)),
              parseInt(b.startDate.substring(0, 2)) - 1,
              parseInt(b.startDate.substring(3, 5))
          );
          return (startDateA.getTime() - startDateB.getTime()) * sortType;
          } else {
            const fieldValueA = a[sortField].toString().toLowerCase();
            const fieldValueB = b[sortField].toString().toLowerCase();
            if (fieldValueA < fieldValueB) return -sortType;
            if (fieldValueA > fieldValueB) return sortType;
            return 0;
          }
        });

        const page = req.query.page ? parseInt(req.query.page) : 1;
        const limit = req.query.limit ? parseInt(req.query.limit) : 10;
        const startIndex = (page - 1) * limit;
        const endIndex = page * limit;
        const paginatedData = finalEventListData.slice(startIndex, endIndex);


        if (paginatedData.length > 0) {
          return res.status(200).json({
            status: true,
            message: "Event list retrive.",
            data: paginatedData,
            totalPages: Math.ceil(finalEventListData.length / limit),
            currentPage: page,
            allCount:allEventCount
          });
        } else {
          return res.status(200).json({
            status: false,
            message: "Event list not found for this user!",
            data: [],
            allCount:allEventCount
          });
        }
      } else {
        return res.status(200).json({
          status: false,
          message: "Event list not found for this user!",
          data: [],
          allCount:allEventCount
        });
      }
    }
  }
 } catch (error) {
    await debugErrorLogs.createErrorLogs(error, "getEventList", {});
    return res
      .status(500)
      .json({ status: false, message: "Internal server error!", error: error });
  }
};

exports.getEventListv2 = async (req,res) => {
  try {
    let sortType = req.query && req.query.sortType && req.query.sortType == "Dec" ? -1 : 1;
    var localDate = new Date(req.query.localDate);
    localDate = moment(localDate, "YYYY-MM-DD").toDate();
    const page = parseInt(req.query.page);
    const limit = parseInt(req.query.limit);
    const skip = (page - 1) * limit;
    const authUser = req.authUserId;
    const userData = await User.findById(authUser).select("firebaseId accessible_groups purchased_plan attendeeDetail.evntData");
    let ruleCondition =  await userAccessRulesCommonCondition({ userId: authUser, relation_id: req.relation_id});
    const role = req.query.role;
    let search = "";

    let categoryId = req.query.categoryId ? req.query.categoryId :""
    let subCategoryId = req.query.subCategoryId ? req.query.subCategoryId :""
    let city = req.query.city ? req.query.city :""


    if (userData !== null && userData !== undefined) {      
      let condition = { _id: authUser, isDelete: false }; 
      if( role && role == "nonMember" ){        
        condition = { _id: authUser, $or: [{ isDelete: false }, { isDelete: { $exists: false } }] }
      }

      const eventAttendeesData = await User.findOne(
        condition,
        { attendeeDetail: 1 }
      );
      if (eventAttendeesData !== null) {
      let conditionTemp = { '$ne': ["$role", "Member"] };
      if ( role === "member" ) {
        conditionTemp = { '$in': ["$role", [ "Member", "Staff" ]] };
      }
      let pipeline = [
        {
          '$match': {
            // event: eventId,
            user: new ObjectId(authUser),
            isDelete: false,
          }
        }, {
          '$lookup': {
            'from': 'event_wise_participant_types',
            'localField': 'role',
            'foreignField': '_id',
            'as': 'event_wise_participant_types_result',
            'pipeline': [
              // {
              //   '$match': {
              //     '$expr': conditionTemp
              //   }
              // }
            ]
          }
        }, {
          '$unwind': {
            'path': '$event_wise_participant_types_result',
            'preserveNullAndEmptyArrays': false
          }
        }
      ]
      let attendeesList = await eventParticipantAttendees.aggregate(pipeline);

      if (attendeesList && attendeesList.length != 0) {
        const eventIds = [...new Set(attendeesList.map(attendee => attendee.event))];

        // Create the match condition using $in
        let match = {
          _id: { $in: eventIds},
          isDelete: false,
          status: "published",
          relation_id: ObjectId(req.relation_id),
        };
        
        let mergedCondition = {
          ...match,
          ...ruleCondition
        };

        const aggregatePipeline = [
          {
              $addFields: {
                Date: {
                  $let: {
                    vars: {
                      year: { $substr: ["$endDate", 6, 10] },
                      month: { $substr: ["$endDate", 0, 2] },
                      dayOfMonth: { $substr: ["$endDate", 3, 2] },
                      startMinute: { $substr: ["$endTime", 3, 2] },
                      startHours: {
                        $toString: {
                          $cond: {
                            if: { $eq: [{ $substr: ["$endTime", 6, 2] }, "am"] },
                            then: {
                              $cond: {
                                if: { $eq: [{ $substr: ["$endTime", 0, 2] }, "12"] },
                                then: "00", // Midnight (12 AM) should be converted to "00"
                                else: { $substr: ["$endTime", 0, 2] }, // No change needed for AM times other than midnight
                              },
                            },
                            else: {
                              $cond: {
                                if: { $eq: [{ $substr: ["$endTime", 0, 2] }, "12"] },
                                then: "12", // Noon (12 PM) should remain "12"
                                else: {
                                  $add: [
                                    {
                                      $toInt: { $substr: ["$endTime", 0, 2] },
                                    },
                                    12, // Adding 12 to convert PM times to 24-hour format
                                  ],
                                },
                              },
                            },
                          },
                        },
                      },
                      timeZoneTemp: {
                        $cond: {
                          if: {
                            $or: [
                              { $eq: ["$timeZone", ""] },
                              {
                                $eq: [
                                  "$timeZone",
                                  "(UTC) Dublin, Edinburgh, Lisbon, London",
                                ],
                              },
                            ],
                          },
                          then: "-00:00",
                          else: { $substr: ["$timeZone", 4, 6] },
                        },
                      },
                    },
                    in: {
                      $toDate: {
                        $concat: [
                          "$$year",
                          "-",
                          "$$month",
                          "-",
                          "$$dayOfMonth",
                          "T",
                          "$$startHours",
                          ":",
                          "$$startMinute",
                          ":",
                          "00.000",
                          "$$timeZoneTemp",
                        ],
                      },
                    },
                  },
                },
              },
          },
          {
              $lookup: {
                from: "contentarchive_tags",
                localField: "tag",
                foreignField: "_id",
                pipeline: [
                  {
                    $match: {
                      isDelete: false,
                    },
                  },
                ],
                as: "tag",
              },
          },
          {
            $match: {
              ...mergedCondition,
              Date: { $gte: localDate }, 
            },
          },
          ... (req.query.city ? [
            {
              $match: {
                ...match,
                "location.city": req.query.city
              }
            }
          ] : []),
          ...(req.query.type ? [
              {
                $match: { 
                  ...match, 
                  locationType: req.query.type, }
              },
            ] : []),
            
            ...(req.query.categoryId ? [
              {
                $match: {
                  ...match,
                  category: ObjectId(req.query.categoryId),
                },
              },
            ] : []),
            ...(req.query.search ? [
              {
                $match: {
                  ...match,
                  $or: [
                    { title: { $regex: req.query.search, $options: "i" } },
                    { 'tag.name': { $regex: req.query.search, $options: "i" } },
                  ],
                },
              },
            ] : []),
            ...(req.query.categoryId && req.query.subCategoryId? [
              {
                $match: {
                  ...match,
                  category: ObjectId(req.query.categoryId),
                  subcategory:ObjectId(req.query.subCategoryId)
                },
              },
            ] : []),
          { $sort: { Date: -1 } },
        ];

        const eventListData = await event.aggregate([
          ...aggregatePipeline,
          {
            $lookup: {
                from: "event_tickets",
                let: { event_id: "$_id" },
                pipeline: [
                    {
                        $match: {
                            $expr: {
                                $eq: ["$eventId", "$$event_id"],
                            },
                            isDelete: false,
                        },
                    },
                    { $project: { actualPrice: 1, _id: 0 } },
                ],
                as: "priceData"
            }
          },
          {
            $lookup: {
              from: "eventpackages",
              let: { event_id: "$_id" },
              pipeline: [
                {
                  $match: {
                    $expr: {
                      $eq: ["$event", "$$event_id"],
                    },
                    isDelete: false,
                  },
                },
                { $project: { price: 1, _id: 0 } },
              ],
              as: "priceData1"
            }
          },
          { $lookup: {
            from: "eventactivities",
            localField: "_id",
            foreignField: "event",
            pipeline: [
              {
                $match: {
                  isDelete: false
                }
              }
            ],
            as: "activities"
            }
          },
          {
            $addFields: {
            activityCount:  { $size: '$activities' }
            }
          },
          {
            $lookup: {
              from: "event_categories",
              localField: "category",
              foreignField: "_id",
              pipeline: [
                {
                      $lookup: {
                        from: "event_subcategories", 
                        localField: "subcategory",         
                        foreignField: "_id",   
                        as: "subcategory"
                      }
                    },
                    {
                      $match: {
                        isDelete: false,
                      },
                    },
                    {
                      $project: {
                        _id: 1,     
                        name: 1,
                        subcategory: 1,
                        relation_id: 1
                      }
                    }
              ],
              as: "event_categories"
            }
          },
          {
            $lookup: {
                from: "event_subcategories", // Assuming this is your subcategory collection
                localField: "subcategory", // This matches the category's _id
                foreignField: "_id", // Assuming subcategory has a field for categoryId
                as: "subcategory"
              }
          },
          {
            $lookup: {
                from: "communities",
                localField: "relation_id",
                foreignField: "_id",
                as: "relation_id"
              }
          },
          {
            $unwind: {
              path: "$relation_id",
              preserveNullAndEmptyArrays: true 
            }
          },
          {
              $lookup: {
                  from: "event_participant_attendees",
                  localField: "_id",
                  foreignField: "event",
                  as: "event_participant_attendees_result",
                  pipeline: [
                    {
                      $match: {
                        $expr: {
                          $eq: [
                            ObjectId(userData._id),
                            "$user",
                          ],
                        },
                      },
                    },
                    {
                      $match: {
                        $expr: {
                          $eq: [
                            "$isDelete",
                            false
                          ],
                        },
                      },
                    },
                  ],
              },
          },
          {
            $addFields: {
              totalTicketCount: {
                $cond: {
                  if: { $eq: ["$ticketPlatform", "internal"] },
                  then: { 
                    $size: "$priceData"
                  },
                  else: { 
                    $size: "$priceData1"
                  }
                }
              }
            }
          },
          {
              $project: {
                  _id: 1, title: 1, thumbnail: 1, eventUrl: 1, startDate: 1, startTime: 1, endDate: 1, endTime: 1, timeZone: 1, tag : 1, city: "$location.city", country: "$location.country", 
                  totalTicketCount: 1,
                  formatedStartDate: {
                      $dateFromString: {
                          dateString: {
                              $concat: [
                                  { $substr: ["$startDate", 6, 4] },
                                  "-",
                                  { $substr: ["$startDate", 0, 2] },
                                  "-",
                                  { $substr: ["$startDate", 3, 2] }
                              ]
                          }
                      }
                  },
                  locationType: 1,
                  eventLocation : "$location",
                  Date:1,
                  activityCount:"$activityCount",
                  category: "$event_categories",
                  subcategory: "$subcategory",
                  relation_id: 1,
                  price: {
                    $ifNull: [
                      {
                        $cond: {
                          if: { $eq: ["$ticketPlatform", "internal"] }, // Check if ticketPlatform is "internal"
                          then: { // If true, get price from priceData
                            $min: {
                              $filter: {
                                input: "$priceData.actualPrice",
                                cond: { $gt: ["$$this", 0] }
                              }
                            }
                          },
                          else: { // If false, get price from priceData1
                            $min: {
                              $filter: {
                                input: "$priceData1.price",
                                cond: { $gt: ["$$this", 0] }
                              }
                            }
                          }
                        }
                      },
                      0
                    ]
                  },
                  ticketPlatform:1,
              isCheckInAllow: 1,
                  registationFlag: {
                      $let: {
                          vars: { }, 
                          in: {
                              $gt: [{ $size: "$event_participant_attendees_result" }, 0]
                          }
                      }
                  },
              }, 
          },
          {
          $addFields: {
              sortFieldLower:
              req.query.sortField === "startDate" ? { $toDate: "$formatedStartDate" } : req.query.sortField === "title" ? { $toLower:"$title"} : req.query.sortField === "price" ? { $toInt:"$price"} :  { $toDate: "$formatedStartDate" }
              },
          },
          { $sort: { sortFieldLower: sortType } },
          { $skip: skip },
          { $limit: limit },
        ]);


        let allEventCount = await getEventCountV2(req.relation_id,localDate,authUser,"myEvent",categoryId,subCategoryId,"","",search,city);

        if (eventListData.length > 0) {
            return res.status(200).json({
                status: true, message: "Event list retrive!",
                data: eventListData,
                totalPages: Math.ceil(allEventCount.myEventcount / limit),
                currentPage: page,
                allCount: allEventCount
            
            });
        } else {
            return res.status(200).json({
                status: true, message: "There is no my events for this user!",
                data: [],
                totalPages: Math.ceil(allEventCount.myEventcount / limit),
                currentPage: page,
                allCount: allEventCount
              
            });
        }
      
      } else {
      return res.status(200).json({
        status: false,
        message: "User not found!",
        data: [],
        allCount: 0,
      });
      }
      }
    }
    }catch(error){
    return res.status(500).json({ status: false, message: "Internal server error!", error: error });
  }
}

// get event activity list with session count for user
exports.getEventActivityByEventId = async (req, res) => {
  try {
    const authUser = req.authUserId;
    const role = req.query.role;
    const eventId = new ObjectId(req.params.id);
    const userData = await User.findOne(
      {
        _id: authUser,
        "attendeeDetail.evntData": { $elemMatch: { event: eventId } },
      },
      {
        firebaseId: 1,
        email: 1,
        accessible_groups: 1,
        purchased_plan: 1,
        notificationFor: 1,
        "attendeeDetail.evntData.$": 1,
      }
    );

    let activityList = [];
    var match = {
      event: eventId,
      isDelete: false,
    };

    if (userData !== null && userData !== undefined) {
      if (
        userData.attendeeDetail.evntData[0].member === true &&
        role === "member"
      ) {
        match = { ...match, member: true };
        let attendeeData = await User.findOne(
          {
            _id: authUser,
            "attendeeDetail.evntData": {
              $elemMatch: { event: eventId, [`member`]: true },
            },
          },
          { _id: 1, email: 1, firebaseId: 1, "attendeeDetail.evntData.$": 1 }
        ).lean();

        if (attendeeData !== null) {
          activityList = await eventActivity.aggregate([
            {
              $match: match,
            },
            {
              $lookup: {
                from: "sessions",
                let: { activity_session_id: "$session" },
                pipeline: [
                  {
                    $match: {
                      $expr: {
                        $in: ["$_id", "$$activity_session_id"],
                      },
                      member: true,
                    },
                  },
                  {
                    $lookup: {
                      from: "rooms",
                      let: { activity_rooms_id: "$room" },
                      pipeline: [
                        {
                          $match: {
                            $expr: {
                              $eq: ["$_id", "$$activity_rooms_id"],
                            },
                          },
                        },
                        {
                          $lookup: {
                            from: "eventlocations",
                            let: { location_id: "$location" },
                            pipeline: [
                              {
                                $match: {
                                  $expr: {
                                    $eq: ["$_id", "$$location_id"],
                                  },
                                },
                              },
                              {
                                $project: {
                                  name: 1,
                                  address: 1,
                                  country: 1,
                                  city: 1,
                                  latitude: 1,
                                  longitude: 1,
                                  locationVisible: 1,
                                  locationImages: 1,
                                },
                              },
                            ],
                            as: "location",
                          },
                        },
                        {
                          $unwind: "$location",
                        },
                        { $project: { location: 1 } },
                      ],
                      as: "room",
                    },
                  },
                  {
                    $unwind: "$room",
                  },
                  { $project: { room: 1 } },
                ],
                as: "sessions",
              },
            },
            {
              $lookup: {
                from: "eventlocations",
                let: { activity_location_id: "$location" },
                pipeline: [
                  {
                    $match: {
                      $expr: {
                        $eq: ["$_id", "$$activity_location_id"],
                      },
                    },
                  },
                  {
                    $project: {
                      name: 1,
                      address: 1,
                      country: 1,
                      city: 1,
                      latitude: 1,
                      longitude: 1,
                      locationVisible: 1,
                      locationImages: 1,
                    },
                  },
                ],
                as: "location",
              },
            },
            {
              $addFields: {
                sessionCount: {
                  $cond: {
                    if: { $isArray: "$sessions" },
                    then: { $size: "$sessions" },
                    else: 0,
                  },
                },
              },
            },
            {
              $project: {
                _id: 1,
                name: 1,
                icon: 1,
                description: "$shortDescription",
                shortDescription: 1,
                longDescription: 1,
                date: 1,
                startTime: 1,
                endDate: 1,
                endTime: 1,
                reserved: 1,
                reserved_URL: 1,
                reservedLabelForDetail: 1,
                reservedLabelForListing: 1,
                location: 1,
                sessions: 1,
                sessionCount: 1,
                notifyChanges: 1,
                notifyChangeText: 1,
                notificationFlag: {
                  $let: {
                    vars: {
                      test: {
                        $filter: {
                          input: userData.notificationFor,
                          cond: {
                            $and: [
                              { $eq: ["$_id", "$$this.id"] },
                              { $eq: ["activity", "$$this.type"] },
                              { $eq: ["user", "$$this.setBy"] },
                            ],
                          },
                        },
                      },
                    },
                    in: {
                      $gt: [{ $size: "$$test" }, 0],
                    },
                  },
                },
              },
            },
          ]);
        }

        if (activityList.length > 0) {
          return res.status(200).json({
            status: true,
            message: "Event activity list retrive.",
            data: activityList,
          });
        } else {
          return res.status(200).json({
            status: false,
            message: "There is no activity for this member in this event!",
            data: [],
          });
        }
      } else if (
        userData.attendeeDetail.evntData[0].member === false &&
        role === "nonMember"
      ) {
        let attendeeData = await User.findOne(
          {
            _id: authUser,
            "attendeeDetail.evntData": { $elemMatch: { event: eventId } },
          },
          { _id: 1, email: 1, firebaseId: 1, "attendeeDetail.evntData.$": 1 }
        ).lean();

        if (attendeeData !== null) {
          if (
            userData.attendeeDetail.evntData[0].speaker === true &&
            userData.attendeeDetail.evntData[0].member === true
          ) {
            match = { ...match, member: true };
            activityList = await eventActivity.aggregate([
              {
                $match: match,
              },
              {
                $lookup: {
                  from: "sessions",
                  let: { activity_session_id: "$session" },
                  pipeline: [
                    {
                      $match: {
                        $expr: {
                          $in: ["$_id", "$$activity_session_id"],
                        },
                        member: true,
                      },
                    },
                    {
                      $lookup: {
                        from: "rooms",
                        let: { activity_rooms_id: "$room" },
                        pipeline: [
                          {
                            $match: {
                              $expr: {
                                $eq: ["$_id", "$$activity_rooms_id"],
                              },
                            },
                          },
                          {
                            $lookup: {
                              from: "eventlocations",
                              let: { location_id: "$location" },
                              pipeline: [
                                {
                                  $match: {
                                    $expr: {
                                      $eq: ["$_id", "$$location_id"],
                                    },
                                  },
                                },
                                {
                                  $project: {
                                    name: 1,
                                    address: 1,
                                    country: 1,
                                    city: 1,
                                    latitude: 1,
                                    longitude: 1,
                                    locationVisible: 1,
                                    locationImages: 1,
                                  },
                                },
                              ],
                              as: "location",
                            },
                          },
                          {
                            $unwind: "$location",
                          },
                          { $project: { location: 1 } },
                        ],
                        as: "room",
                      },
                    },
                    {
                      $unwind: "$room",
                    },
                    { $project: { room: 1 } },
                  ],
                  as: "sessions",
                },
              },
              {
                $lookup: {
                  from: "eventlocations",
                  let: { activity_location_id: "$location" },
                  pipeline: [
                    {
                      $match: {
                        $expr: {
                          $eq: ["$_id", "$$activity_location_id"],
                        },
                      },
                    },
                    {
                      $project: {
                        name: 1,
                        address: 1,
                        country: 1,
                        city: 1,
                        latitude: 1,
                        longitude: 1,
                        locationVisible: 1,
                        locationImages: 1,
                      },
                    },
                  ],
                  as: "location",
                },
              },
              {
                $addFields: {
                  sessionCount: {
                    $cond: {
                      if: { $isArray: "$sessions" },
                      then: { $size: "$sessions" },
                      else: 0,
                    },
                  },
                },
              },
              {
                $project: {
                  _id: 1,
                  name: 1,
                  icon: 1,
                  description: "$shortDescription",
                  shortDescription: 1,
                  longDescription: 1,
                  date: 1,
                  startTime: 1,
                  endDate: 1,
                  endTime: 1,
                  reserved: 1,
                  reserved_URL: 1,
                  reservedLabelForDetail: 1,
                  reservedLabelForListing: 1,
                  location: 1,
                  sessions: 1,
                  sessionCount: 1,
                  notifyChanges: 1,
                  notifyChangeText: 1,
                  notificationFlag: {
                    $let: {
                      vars: {
                        test: {
                          $filter: {
                            input: userData.notificationFor,
                            cond: {
                              $and: [
                                { $eq: ["$_id", "$$this.id"] },
                                { $eq: ["activity", "$$this.type"] },
                                { $eq: ["user", "$$this.setBy"] },
                              ],
                            },
                          },
                        },
                      },
                      in: {
                        $gt: [{ $size: "$$test" }, 0],
                      },
                    },
                  },
                },
              },
            ]);
          } else if (
            userData.attendeeDetail.evntData[0].speaker === true &&
            userData.attendeeDetail.evntData[0].partner === true
          ) {
            match = { ...match, partner: true };
            activityList = await eventActivity.aggregate([
              {
                $match: match,
              },
              {
                $lookup: {
                  from: "sessions",
                  let: { activity_session_id: "$session" },
                  pipeline: [
                    {
                      $match: {
                        $expr: {
                          $in: ["$_id", "$$activity_session_id"],
                        },
                        partner: true,
                      },
                    },
                    {
                      $lookup: {
                        from: "rooms",
                        let: { activity_rooms_id: "$room" },
                        pipeline: [
                          {
                            $match: {
                              $expr: {
                                $eq: ["$_id", "$$activity_rooms_id"],
                              },
                            },
                          },
                          {
                            $lookup: {
                              from: "eventlocations",
                              let: { location_id: "$location" },
                              pipeline: [
                                {
                                  $match: {
                                    $expr: {
                                      $eq: ["$_id", "$$location_id"],
                                    },
                                  },
                                },
                                {
                                  $project: {
                                    name: 1,
                                    address: 1,
                                    country: 1,
                                    city: 1,
                                    latitude: 1,
                                    longitude: 1,
                                    locationVisible: 1,
                                    locationImages: 1,
                                  },
                                },
                              ],
                              as: "location",
                            },
                          },
                          {
                            $unwind: "$location",
                          },
                          { $project: { location: 1 } },
                        ],
                        as: "room",
                      },
                    },
                    {
                      $unwind: "$room",
                    },
                    { $project: { room: 1 } },
                  ],
                  as: "sessions",
                },
              },
              {
                $lookup: {
                  from: "eventlocations",
                  let: { activity_location_id: "$location" },
                  pipeline: [
                    {
                      $match: {
                        $expr: {
                          $eq: ["$_id", "$$activity_location_id"],
                        },
                      },
                    },
                    {
                      $project: {
                        name: 1,
                        address: 1,
                        country: 1,
                        city: 1,
                        latitude: 1,
                        longitude: 1,
                        locationVisible: 1,
                        locationImages: 1,
                      },
                    },
                  ],
                  as: "location",
                },
              },
              {
                $addFields: {
                  sessionCount: {
                    $cond: {
                      if: { $isArray: "$sessions" },
                      then: { $size: "$sessions" },
                      else: 0,
                    },
                  },
                },
              },
              {
                $project: {
                  _id: 1,
                  name: 1,
                  icon: 1,
                  description: "$shortDescription",
                  shortDescription: 1,
                  longDescription: 1,
                  date: 1,
                  startTime: 1,
                  endDate: 1,
                  endTime: 1,
                  reserved: 1,
                  reserved_URL: 1,
                  reservedLabelForDetail: 1,
                  reservedLabelForListing: 1,
                  location: 1,
                  sessions: 1,
                  sessionCount: 1,
                  notifyChanges: 1,
                  notifyChangeText: 1,
                  notificationFlag: {
                    $let: {
                      vars: {
                        test: {
                          $filter: {
                            input: userData.notificationFor,
                            cond: {
                              $and: [
                                { $eq: ["$_id", "$$this.id"] },
                                { $eq: ["activity", "$$this.type"] },
                                { $eq: ["user", "$$this.setBy"] },
                              ],
                            },
                          },
                        },
                      },
                      in: {
                        $gt: [{ $size: "$$test" }, 0],
                      },
                    },
                  },
                },
              },
            ]);
          } else if (
            userData.attendeeDetail.evntData[0].member === true &&
            userData.attendeeDetail.evntData[0].guest === true
          ) {
            match = { ...match, guest: true };
            activityList = await eventActivity.aggregate([
              {
                $match: match,
              },
              {
                $lookup: {
                  from: "sessions",
                  let: { activity_session_id: "$session" },
                  pipeline: [
                    {
                      $match: {
                        $expr: {
                          $in: ["$_id", "$$activity_session_id"],
                        },
                        guest: true,
                      },
                    },
                    {
                      $lookup: {
                        from: "rooms",
                        let: { activity_rooms_id: "$room" },
                        pipeline: [
                          {
                            $match: {
                              $expr: {
                                $eq: ["$_id", "$$activity_rooms_id"],
                              },
                            },
                          },
                          {
                            $lookup: {
                              from: "eventlocations",
                              let: { location_id: "$location" },
                              pipeline: [
                                {
                                  $match: {
                                    $expr: {
                                      $eq: ["$_id", "$$location_id"],
                                    },
                                  },
                                },
                                {
                                  $project: {
                                    name: 1,
                                    address: 1,
                                    country: 1,
                                    city: 1,
                                    latitude: 1,
                                    longitude: 1,
                                    locationVisible: 1,
                                    locationImages: 1,
                                  },
                                },
                              ],
                              as: "location",
                            },
                          },
                          {
                            $unwind: "$location",
                          },
                          { $project: { location: 1 } },
                        ],
                        as: "room",
                      },
                    },
                    {
                      $unwind: "$room",
                    },
                    { $project: { room: 1 } },
                  ],
                  as: "sessions",
                },
              },
              {
                $lookup: {
                  from: "eventlocations",
                  let: { activity_location_id: "$location" },
                  pipeline: [
                    {
                      $match: {
                        $expr: {
                          $eq: ["$_id", "$$activity_location_id"],
                        },
                      },
                    },
                    {
                      $project: {
                        name: 1,
                        address: 1,
                        country: 1,
                        city: 1,
                        latitude: 1,
                        longitude: 1,
                        locationVisible: 1,
                        locationImages: 1,
                      },
                    },
                  ],
                  as: "location",
                },
              },
              {
                $addFields: {
                  sessionCount: {
                    $cond: {
                      if: { $isArray: "$sessions" },
                      then: { $size: "$sessions" },
                      else: 0,
                    },
                  },
                },
              },
              {
                $project: {
                  _id: 1,
                  name: 1,
                  icon: 1,
                  description: "$shortDescription",
                  shortDescription: 1,
                  longDescription: 1,
                  date: 1,
                  startTime: 1,
                  endDate: 1,
                  endTime: 1,
                  reserved: 1,
                  reserved_URL: 1,
                  reservedLabelForDetail: 1,
                  reservedLabelForListing: 1,
                  location: 1,
                  sessions: 1,
                  sessionCount: 1,
                  notifyChanges: 1,
                  notifyChangeText: 1,
                  notificationFlag: {
                    $let: {
                      vars: {
                        test: {
                          $filter: {
                            input: userData.notificationFor,
                            cond: {
                              $and: [
                                { $eq: ["$_id", "$$this.id"] },
                                { $eq: ["activity", "$$this.type"] },
                                { $eq: ["user", "$$this.setBy"] },
                              ],
                            },
                          },
                        },
                      },
                      in: {
                        $gt: [{ $size: "$$test" }, 0],
                      },
                    },
                  },
                },
              },
            ]);
          } else if (userData.attendeeDetail.evntData[0].speaker === true) {
            match = { ...match, speaker: true };
            activityList = await eventActivity.aggregate([
              {
                $match: match,
              },
              {
                $lookup: {
                  from: "sessions",
                  let: { activity_session_id: "$session" },
                  pipeline: [
                    {
                      $match: {
                        $expr: {
                          $in: ["$_id", "$$activity_session_id"],
                        },
                        speaker: true,
                      },
                    },
                    {
                      $lookup: {
                        from: "rooms",
                        let: { activity_rooms_id: "$room" },
                        pipeline: [
                          {
                            $match: {
                              $expr: {
                                $eq: ["$_id", "$$activity_rooms_id"],
                              },
                            },
                          },
                          {
                            $lookup: {
                              from: "eventlocations",
                              let: { location_id: "$location" },
                              pipeline: [
                                {
                                  $match: {
                                    $expr: {
                                      $eq: ["$_id", "$$location_id"],
                                    },
                                  },
                                },
                                {
                                  $project: {
                                    name: 1,
                                    address: 1,
                                    country: 1,
                                    city: 1,
                                    latitude: 1,
                                    longitude: 1,
                                    locationVisible: 1,
                                    locationImages: 1,
                                  },
                                },
                              ],
                              as: "location",
                            },
                          },
                          {
                            $unwind: "$location",
                          },
                          { $project: { location: 1 } },
                        ],
                        as: "room",
                      },
                    },
                    {
                      $unwind: "$room",
                    },
                    { $project: { room: 1 } },
                  ],
                  as: "sessions",
                },
              },
              {
                $lookup: {
                  from: "eventlocations",
                  let: { activity_location_id: "$location" },
                  pipeline: [
                    {
                      $match: {
                        $expr: {
                          $eq: ["$_id", "$$activity_location_id"],
                        },
                      },
                    },
                    {
                      $project: {
                        name: 1,
                        address: 1,
                        country: 1,
                        city: 1,
                        latitude: 1,
                        longitude: 1,
                        locationVisible: 1,
                        locationImages: 1,
                      },
                    },
                  ],
                  as: "location",
                },
              },
              {
                $addFields: {
                  sessionCount: {
                    $cond: {
                      if: { $isArray: "$sessions" },
                      then: { $size: "$sessions" },
                      else: 0,
                    },
                  },
                },
              },
              {
                $project: {
                  _id: 1,
                  name: 1,
                  icon: 1,
                  description: "$shortDescription",
                  shortDescription: 1,
                  longDescription: 1,
                  date: 1,
                  startTime: 1,
                  endDate: 1,
                  endTime: 1,
                  reserved: 1,
                  reserved_URL: 1,
                  reservedLabelForDetail: 1,
                  reservedLabelForListing: 1,
                  location: 1,
                  sessions: 1,
                  sessionCount: 1,
                  notifyChanges: 1,
                  notifyChangeText: 1,
                  notificationFlag: {
                    $let: {
                      vars: {
                        test: {
                          $filter: {
                            input: userData.notificationFor,
                            cond: {
                              $and: [
                                { $eq: ["$_id", "$$this.id"] },
                                { $eq: ["activity", "$$this.type"] },
                                { $eq: ["user", "$$this.setBy"] },
                              ],
                            },
                          },
                        },
                      },
                      in: {
                        $gt: [{ $size: "$$test" }, 0],
                      },
                    },
                  },
                },
              },
            ]);
          } else if (userData.attendeeDetail.evntData[0].partner === true) {
            match = { ...match, partner: true };
            activityList = await eventActivity.aggregate([
              {
                $match: match,
              },
              {
                $lookup: {
                  from: "sessions",
                  let: { activity_session_id: "$session" },
                  pipeline: [
                    {
                      $match: {
                        $expr: {
                          $in: ["$_id", "$$activity_session_id"],
                        },
                        partner: true,
                      },
                    },
                    {
                      $lookup: {
                        from: "rooms",
                        let: { activity_rooms_id: "$room" },
                        pipeline: [
                          {
                            $match: {
                              $expr: {
                                $eq: ["$_id", "$$activity_rooms_id"],
                              },
                            },
                          },
                          {
                            $lookup: {
                              from: "eventlocations",
                              let: { location_id: "$location" },
                              pipeline: [
                                {
                                  $match: {
                                    $expr: {
                                      $eq: ["$_id", "$$location_id"],
                                    },
                                  },
                                },
                                {
                                  $project: {
                                    name: 1,
                                    address: 1,
                                    country: 1,
                                    city: 1,
                                    latitude: 1,
                                    longitude: 1,
                                    locationVisible: 1,
                                    locationImages: 1,
                                  },
                                },
                              ],
                              as: "location",
                            },
                          },
                          {
                            $unwind: "$location",
                          },
                          { $project: { location: 1 } },
                        ],
                        as: "room",
                      },
                    },
                    {
                      $unwind: "$room",
                    },
                    { $project: { room: 1 } },
                  ],
                  as: "sessions",
                },
              },
              {
                $lookup: {
                  from: "eventlocations",
                  let: { activity_location_id: "$location" },
                  pipeline: [
                    {
                      $match: {
                        $expr: {
                          $eq: ["$_id", "$$activity_location_id"],
                        },
                      },
                    },
                    {
                      $project: {
                        name: 1,
                        address: 1,
                        country: 1,
                        city: 1,
                        latitude: 1,
                        longitude: 1,
                        locationVisible: 1,
                        locationImages: 1,
                      },
                    },
                  ],
                  as: "location",
                },
              },
              {
                $addFields: {
                  sessionCount: {
                    $cond: {
                      if: { $isArray: "$sessions" },
                      then: { $size: "$sessions" },
                      else: 0,
                    },
                  },
                },
              },
              {
                $project: {
                  _id: 1,
                  name: 1,
                  icon: 1,
                  description: "$shortDescription",
                  shortDescription: 1,
                  longDescription: 1,
                  date: 1,
                  startTime: 1,
                  endDate: 1,
                  endTime: 1,
                  reserved: 1,
                  reserved_URL: 1,
                  reservedLabelForDetail: 1,
                  reservedLabelForListing: 1,
                  location: 1,
                  sessions: 1,
                  sessionCount: 1,
                  notifyChanges: 1,
                  notifyChangeText: 1,
                  notificationFlag: {
                    $let: {
                      vars: {
                        test: {
                          $filter: {
                            input: userData.notificationFor,
                            cond: {
                              $and: [
                                { $eq: ["$_id", "$$this.id"] },
                                { $eq: ["activity", "$$this.type"] },
                                { $eq: ["user", "$$this.setBy"] },
                              ],
                            },
                          },
                        },
                      },
                      in: {
                        $gt: [{ $size: "$$test" }, 0],
                      },
                    },
                  },
                },
              },
            ]);
          } else if (userData.attendeeDetail.evntData[0].guest === true) {
            match = { ...match, guest: true };
            activityList = await eventActivity.aggregate([
              {
                $match: match,
              },
              {
                $lookup: {
                  from: "sessions",
                  let: { activity_session_id: "$session" },
                  pipeline: [
                    {
                      $match: {
                        $expr: {
                          $in: ["$_id", "$$activity_session_id"],
                        },
                        guest: true,
                      },
                    },
                    {
                      $lookup: {
                        from: "rooms",
                        let: { activity_rooms_id: "$room" },
                        pipeline: [
                          {
                            $match: {
                              $expr: {
                                $eq: ["$_id", "$$activity_rooms_id"],
                              },
                            },
                          },
                          {
                            $lookup: {
                              from: "eventlocations",
                              let: { location_id: "$location" },
                              pipeline: [
                                {
                                  $match: {
                                    $expr: {
                                      $eq: ["$_id", "$$location_id"],
                                    },
                                  },
                                },
                                {
                                  $project: {
                                    name: 1,
                                    address: 1,
                                    country: 1,
                                    city: 1,
                                    latitude: 1,
                                    longitude: 1,
                                    locationVisible: 1,
                                    locationImages: 1,
                                  },
                                },
                              ],
                              as: "location",
                            },
                          },
                          {
                            $unwind: "$location",
                          },
                          { $project: { location: 1 } },
                        ],
                        as: "room",
                      },
                    },
                    {
                      $unwind: "$room",
                    },
                    { $project: { room: 1 } },
                  ],
                  as: "sessions",
                },
              },
              {
                $lookup: {
                  from: "eventlocations",
                  let: { activity_location_id: "$location" },
                  pipeline: [
                    {
                      $match: {
                        $expr: {
                          $eq: ["$_id", "$$activity_location_id"],
                        },
                      },
                    },
                    {
                      $project: {
                        name: 1,
                        address: 1,
                        country: 1,
                        city: 1,
                        latitude: 1,
                        longitude: 1,
                        locationVisible: 1,
                        locationImages: 1,
                      },
                    },
                  ],
                  as: "location",
                },
              },
              {
                $addFields: {
                  sessionCount: {
                    $cond: {
                      if: { $isArray: "$sessions" },
                      then: { $size: "$sessions" },
                      else: 0,
                    },
                  },
                },
              },
              {
                $project: {
                  _id: 1,
                  name: 1,
                  icon: 1,
                  description: "$shortDescription",
                  shortDescription: 1,
                  longDescription: 1,
                  date: 1,
                  startTime: 1,
                  endDate: 1,
                  endTime: 1,
                  reserved: 1,
                  reserved_URL: 1,
                  reservedLabelForDetail: 1,
                  reservedLabelForListing: 1,
                  location: 1,
                  sessions: 1,
                  sessionCount: 1,
                  notifyChanges: 1,
                  notifyChangeText: 1,
                  notificationFlag: {
                    $let: {
                      vars: {
                        test: {
                          $filter: {
                            input: userData.notificationFor,
                            cond: {
                              $and: [
                                { $eq: ["$_id", "$$this.id"] },
                                { $eq: ["activity", "$$this.type"] },
                                { $eq: ["user", "$$this.setBy"] },
                              ],
                            },
                          },
                        },
                      },
                      in: {
                        $gt: [{ $size: "$$test" }, 0],
                      },
                    },
                  },
                },
              },
            ]);
          }
        }

        if (activityList.length > 0) {
          return res.status(200).json({
            status: true,
            message: "Event activity list retrive.",
            data: activityList,
          });
        } else {
          return res.status(200).json({
            status: false,
            message: "There is no activity for this member in this event!",
            data: [],
          });
        }
      }
    }
  } catch (error) {
    await debugErrorLogs.createErrorLogs(error, "getEventActivityByEventId", {});
    return res
      .status(500)
      .json({ status: false, message: "Internal server error!", error: error });
  }
};

// get event by id
exports.getEventActivityById = async (req, res) => {
  let debugObj = {};
  try {
    const authUser = req.authUserId;
    const role = req.query.role;
    const activityId = new ObjectId(req.params.id);
    
    debugObj = {
      authUser : authUser ? authUser : "",
      role: role ? role : "",
      activityId: activityId ? activityId : "",
    }

    const activityData = await eventActivity.findOne(
      { _id: activityId, isDelete: false,status:"published" },
      { _id: 1, name: 1, event: 1 }
    );
    if (activityData !== null) {
      let eventId = activityData.event._id;
      let pipeline = [
        {
          '$match': {
            event: eventId,
            user: new ObjectId(authUser),
            isDelete: false,
          }
        }, {
          '$lookup': {
            'from': 'event_wise_participant_types',
            'localField': 'role',
            'foreignField': '_id',
            'as': 'event_wise_participant_types_result'
          }
        }, {
          '$unwind': {
            'path': '$event_wise_participant_types_result',
            'preserveNullAndEmptyArrays': false
          }
        }, {
          '$lookup': {
            'from': 'airtable-syncs',
            'localField': 'user',
            'foreignField': '_id',
            'as': 'airtable-syncs_result'
          }
        }, {
          '$unwind': {
            'path': '$airtable-syncs_result',
            'preserveNullAndEmptyArrays': false
          }
        }, {
          '$project': {
            '_id': {
              '$ifNull': [
                '$airtable-syncs_result._id', ''
              ]
            },
            'email': {
              '$ifNull': [
                '$airtable-syncs_result.Preferred Email', ''
              ]
            },
            'firebaseId': {
              '$ifNull': [
                '$airtable-syncs_result.firebaseId', ''
              ]
            },
            'accessible_groups': {
              '$ifNull': [
                '$airtable-syncs_result.accessible_groups', ''
              ]
            },
            'notificationFor': {
              '$ifNull': [
                '$airtable-syncs_result.notificationFor', []
              ]
            },
            'purchased_plan': {
              '$ifNull': [
                '$airtable-syncs_result.purchased_plan', ''
              ]
            },
            'attendeeDetail': {
              '$ifNull': [
                '$airtable-syncs_result.attendeeDetail', {}
              ]
            },
            'contact_name': 1,
            'partner_order': 1,
            'description': 1,
            'offer': 1,
            'type_icon': 1,
            'contactPartnerName': '$contact_name',
            'profileImg': '$type_icon',
            'partnerIcon': '$type_icon',
            'event': '$event',
            'event': '$event',
            'role': '$event_wise_participant_types_result.role',
            'role_type': '$event_wise_participant_types_result._id'
          }
        }
      ]
      let attendeesList = await eventParticipantAttendees.aggregate(pipeline);
      let userData = attendeesList[0];
      let isHaveMemberAccess = attendeesList.find(o => o.role === "Member" || o.role === "Staff" );
      let isHaveNonMemberAccess = attendeesList.find(o => o.role != "Member");
      let accessibleGroups = [];
      let purchasedPlan = [];
      let userIdAsArray = [ ObjectId(userData._id) ];
      if (userData["accessible_groups"] && userData["accessible_groups"].length ) {
        accessibleGroups = userData["accessible_groups"].map((id) => { return ObjectId(id); });
      }
      if (userData["purchased_plan"] && userData["purchased_plan"].length ) {
        purchasedPlan = userData["purchased_plan"].map((id) => { return ObjectId(id); });
      }

      let activityList = [];
      var match = {
        _id: activityId,
        isDelete: false,
        status: "published"
      };
      if (userData !== null && userData !== undefined) {
        if (isHaveMemberAccess && role === "member") {
          if (isHaveMemberAccess) {
            let roleTemp = [ "Member", "Staff" ];
            activityList = await eventActivity.aggregate([
              {
                $match: match,
              },
              {
                $lookup: {
                  from: "event_wise_participant_types",
                  localField: "accessRoles",
                  foreignField: "_id",
                  as: "activity_event_wise_participant_types",
                  pipeline: [
                    {
                      $match: {
                        $expr: {
                          $in: ["$role", roleTemp]
                        }
                      }
                    }
                  ]
                }
              },
              // {
              //   $unwind: {
              //     path: "$activity_event_wise_participant_types",
              //     preserveNullAndEmptyArrays: false,
              //   },
              // },
              {
                $match: {
                  $or: [
                    {
                      $expr: {
                        $gt: [
                          {
                            $size:
                              "$activity_event_wise_participant_types"
                          },
                          0
                        ]
                      }
                    },
                    {
                      userId: {
                        $in: userIdAsArray
                      }
                    },
                    {
                      membershipPlanId: {
                        $in: purchasedPlan
                      }
                    },
                    {
                      groupId: {
                        $in: accessibleGroups
                      }
                    }
                  ]
                }
              },
              {
                $lookup: {
                  from: "sessions",
                  let: { activity_session_id: "$session" },
                  pipeline: [
                    {
                      $match: {
                        $expr: {
                          $in: ["$_id", "$$activity_session_id"],
                        },
                        // member: true,
                      },
                    },
                    {
                      $lookup: {
                        from: "event_wise_participant_types",
                        localField: "accessRoles",
                        foreignField: "_id",
                        as: "event_wise_participant_types",
                        //this code is commented after disscuse with KD, suggested by client
                        // pipeline: [
                        //   {
                        //     $match: {
                        //       $expr: {
                        //         $in: ["$role", roleTemp],
                        //       },
                        //     },
                        //   },
                        // ],
                      },
                    },
                    // {
                    //   $unwind: {
                    //     path: "$event_wise_participant_types",
                    //     preserveNullAndEmptyArrays: false,
                    //   },
                    // },
                    {
                      $lookup: {
                        from: "rooms",
                        let: { activity_rooms_id: "$room" },
                        pipeline: [
                          {
                            $match: {
                              $expr: {
                                $eq: ["$_id", "$$activity_rooms_id"],
                              },
                            },
                          },
                          {
                            $lookup: {
                              from: "eventlocations",
                              let: { location_id: "$location" },
                              pipeline: [
                                {
                                  $match: {
                                    $expr: {
                                      $eq: ["$_id", "$$location_id"],
                                    },
                                  },
                                },
                                {
                                  $project: {
                                    name: 1,
                                    address: 1,
                                    country: 1,
                                    city: 1,
                                    latitude: 1,
                                    longitude: 1,
                                    locationVisible: 1,
                                    locationImages: 1,
                                  },
                                },
                              ],
                              as: "location",
                            },
                          },
                          {
                            $unwind: "$location",
                          },
                          { $project: { name: 1, location: 1 } },
                        ],
                        as: "room",
                      },
                    },
                    {
                      $unwind: "$room",
                    },
                    {
                      $lookup: {
                        from: "airtable-syncs",
                        let: { speakerId: "$speakerId" },
                        pipeline: [
                          {
                            $match: {
                              $expr: {
                                $in: ["$_id", "$$speakerId"],
                              },
                            },
                          },
                          {
                            $project: {
                              _id: "$_id",
                              profileImg: "$profileImg",
                              attendeeDetail: {
                                _id: "$_id",
                                title: "$attendeeDetail.title",
                                name: "$attendeeDetail.name",
                                firstName: "$attendeeDetail.firstName"
                                  ? "$attendeeDetail.firstName"
                                  : "",
                                lastName: "$attendeeDetail.lastName"
                                  ? "$attendeeDetail.lastName"
                                  : "",
                                email: "$Preferred Email",
                                company: "$attendeeDetail.company",
                                phone: "$attendeeDetail.phone",
                                linkedin: "$attendeeDetail.linkedin",
                              },
                            },
                          },
                        ],
                        as: "speakerId",
                      },
                    },
                    {
                      $project: {
                        _id: 1,
                        title: 1,
                        shortDescription: 1,
                        longDescriptioniv: 1,
                        date: 1,
                        startTime: 1,
                        endTime: 1,
                        room: 1,
                        speakerId: 1,
                        event: 1,
                        reserved: 1,
                        reserved_URL: 1,
                        reservedLabelForListing: 1,
                        reservedLabelForDetail: 1,
                        member: 1,
                        speaker: 1,
                        partner: 1,
                        guest: 1,
                        notifyChanges: 1,
                        notifyChangeText: 1,
                        isEndOrNextDate: 1,
                        endDate: 1,
                        isDelete: 1,
                      },
                    },
                  ],
                  as: "session",
                },
              },
              {
                $lookup: {
                  from: "eventlocations",
                  let: { activity_location_id: "$location" },
                  pipeline: [
                    {
                      $match: {
                        $expr: {
                          $eq: ["$_id", "$$activity_location_id"],
                        },
                      },
                    },
                    {
                      $project: {
                        name: 1,
                        address: 1,
                        country: 1,
                        city: 1,
                        latitude: 1,
                        longitude: 1,
                        locationVisible: 1,
                        locationImages: 1,
                      },
                    },
                  ],
                  as: "location",
                },
              },
              {
                $unwind: {
                  path: "$location",
                  preserveNullAndEmptyArrays: true,
                },
              },
              {
                $addFields: {
                  sessionCount: {
                    $cond: {
                      if: { $isArray: "$session" },
                      then: { $size: "$session" },
                      else: 0,
                    },
                  },
                },
              },
              {
                $project: {
                  _id: 1,
                  name: 1,
                  icon: 1,
                  description: "$shortDescription",
                  shortDescription: 1,
                  longDescription: 1,
                  date: 1,
                  startTime: 1,
                  endDate: 1,
                  endTime: 1,
                  reserved: 1,
                  reserved_URL: 1,
                  reservedLabelForDetail: 1,
                  reservedLabelForListing: 1,
                  location: {
                    $cond: [
                      {
                        $ifNull: ["$location", false],
                      },
                      "$location",
                      null,
                    ],
                  },
                  session: 1,
                  sessionCount: 1,
                  notifyChanges: 1,
                  notifyChangeText: 1,
                  notificationFlag: {
                    $let: {
                      vars: {
                        test: {
                          $filter: {
                            input: userData.notificationFor,
                            cond: {
                              $and: [
                                { $eq: ["$_id", "$$this.id"] },
                                { $eq: ["activity", "$$this.type"] },
                                { $eq: ["user", "$$this.setBy"] },
                              ],
                            },
                          },
                        },
                      },
                      in: {
                        $gt: [{ $size: "$$test" }, 0],
                      },
                    },
                  },
                },
              },
            ]);
          }

          if (activityList.length > 0) {
            return res.status(200).json({
              status: true,
              message: "Event activity details retrive.",
              data: activityList[0],
            });
          } else {
            return res.status(200).json({
              status: false,
              message: "There is no activity for this member in this event!",
              data: [],
            });
          }
        } else if (isHaveNonMemberAccess && role === "nonMember") {
          if (isHaveNonMemberAccess) {
            for (let i = 0; i < attendeesList.length; i++) {
              let roleTemp = attendeesList[i]["role"];
              let roleType = attendeesList[i]["role_type"];
              let notificationForTemp = attendeesList[i]["notificationFor"];
              if (roleTemp != "Member" && activityList.length == 0) {
                roleTemp = [ roleTemp ];
                match = { ...match };
                activityList = await eventActivity.aggregate([
                  {
                    $match: match,
                  },
                  {
                    $lookup: {
                      from: "event_wise_participant_types",
                      localField: "accessRoles",
                      foreignField: "_id",
                      as: "activity_event_wise_participant_types",
                      pipeline: [
                        {
                          $match: {
                            $expr: {
                              $in: ["$role", roleTemp]
                            }
                          }
                        }
                      ]
                    }
                  },
                  // {
                  //   $unwind: {
                  //     path: "$activity_event_wise_participant_types",
                  //     preserveNullAndEmptyArrays: false,
                  //   },
                  // },
                  {
                    $match: {
                      $or: [
                        {
                          $expr: {
                            $gt: [
                              {
                                $size:
                                  "$activity_event_wise_participant_types"
                              },
                              0
                            ]
                          }
                        },
                        {
                          userId: {
                            $in: userIdAsArray
                          }
                        },
                        {
                          membershipPlanId: {
                            $in: purchasedPlan
                          }
                        },
                        {
                          groupId: {
                            $in: accessibleGroups
                          }
                        }
                      ]
                    }
                  },
                  {
                    $lookup: {
                      from: "sessions",
                      let: { activity_session_id: "$session" },
                      pipeline: [
                        {
                          $match: {
                            $expr: {
                              $in: ["$_id", "$$activity_session_id"],
                            },
                            // member: true,
                          },
                        },
                        {
                          $lookup: {
                            from: "event_wise_participant_types",
                            localField: "accessRoles",
                            foreignField: "_id",
                            as: "event_wise_participant_types",
                            //this code is commented after disscuse with KD, suggested by client
                            // pipeline: [
                            //   {
                            //     $match: {
                            //       $expr: {
                            //         $in: ["$role", roleTemp],
                            //       },
                            //     },
                            //   },
                            // ],
                          },
                        },
                        // {
                        //   $unwind: {
                        //     path: "$event_wise_participant_types",
                        //     preserveNullAndEmptyArrays: false,
                        //   },
                        // },
                        {
                          $lookup: {
                            from: "rooms",
                            let: { activity_rooms_id: "$room" },
                            pipeline: [
                              {
                                $match: {
                                  $expr: {
                                    $eq: ["$_id", "$$activity_rooms_id"],
                                  },
                                },
                              },
                              {
                                $lookup: {
                                  from: "eventlocations",
                                  let: { location_id: "$location" },
                                  pipeline: [
                                    {
                                      $match: {
                                        $expr: {
                                          $eq: ["$_id", "$$location_id"],
                                        },
                                      },
                                    },
                                    {
                                      $project: {
                                        name: 1,
                                        address: 1,
                                        country: 1,
                                        city: 1,
                                        latitude: 1,
                                        longitude: 1,
                                        locationVisible: 1,
                                        locationImages: 1,
                                      },
                                    },
                                  ],
                                  as: "location",
                                },
                              },
                              {
                                $unwind: "$location",
                              },
                              { $project: { name: 1, location: 1 } },
                            ],
                            as: "room",
                          },
                        },
                        {
                          $unwind: "$room",
                        },
                        {
                          $lookup: {
                            from: "airtable-syncs",
                            let: { speakerId: "$speakerId" },
                            pipeline: [
                              {
                                $match: {
                                  $expr: {
                                    $in: ["$_id", "$$speakerId"],
                                  },
                                },
                              },
                              {
                                $project: {
                                  _id: "$_id",
                                  profileImg: "$profileImg",
                                  attendeeDetail: {
                                    _id: "$_id",
                                    title: "$attendeeDetail.title",
                                    name: "$attendeeDetail.name",
                                    firstName: "$attendeeDetail.firstName"
                                      ? "$attendeeDetail.firstName"
                                      : "",
                                    lastName: "$attendeeDetail.lastName"
                                      ? "$attendeeDetail.lastName"
                                      : "",
                                    email: "$Preferred Email",
                                    company: "$attendeeDetail.company",
                                    phone: "$attendeeDetail.phone",
                                    linkedin: "$attendeeDetail.linkedin",
                                  },
                                },
                              },
                            ],
                            as: "speakerId",
                          },
                        },
                        {
                          $project: {
                            _id: 1,
                            title: 1,
                            shortDescription: 1,
                            longDescriptioniv: 1,
                            date: 1,
                            startTime: 1,
                            endTime: 1,
                            room: 1,
                            speakerId: 1,
                            event: 1,
                            reserved: 1,
                            reserved_URL: 1,
                            reservedLabelForListing: 1,
                            reservedLabelForDetail: 1,
                            member: 1,
                            speaker: 1,
                            partner: 1,
                            guest: 1,
                            notifyChanges: 1,
                            notifyChangeText: 1,
                            isEndOrNextDate: 1,
                            endDate: 1,
                            isDelete: 1,
                          },
                        },
                      ],
                      as: "session",
                    },
                  },
                  {
                    $lookup: {
                      from: "eventlocations",
                      let: { activity_location_id: "$location" },
                      pipeline: [
                        {
                          $match: {
                            $expr: {
                              $eq: ["$_id", "$$activity_location_id"],
                            },
                          },
                        },
                        {
                          $project: {
                            name: 1,
                            address: 1,
                            country: 1,
                            city: 1,
                            latitude: 1,
                            longitude: 1,
                            locationVisible: 1,
                            locationImages: 1,
                          },
                        },
                      ],
                      as: "location",
                    },
                  },
                  {
                    $unwind: {
                      path: "$location",
                      preserveNullAndEmptyArrays: true,
                    },
                  },
                  {
                    $addFields: {
                      sessionCount: {
                        $cond: {
                          if: { $isArray: "$session" },
                          then: { $size: "$session" },
                          else: 0,
                        },
                      },
                    },
                  },
                  {
                    $project: {
                      _id: 1,
                      name: 1,
                      icon: 1,
                      description: "$shortDescription",
                      shortDescription: 1,
                      longDescription: 1,
                      date: 1,
                      startTime: 1,
                      endDate: 1,
                      endTime: 1,
                      reserved: 1,
                      reserved_URL: 1,
                      reservedLabelForDetail: 1,
                      reservedLabelForListing: 1,
                      location: {
                        $cond: [
                          {
                            $ifNull: ["$location", false],
                          },
                          "$location",
                          null,
                        ],
                      },
                      session: 1,
                      sessionCount: 1,
                      notifyChanges: 1,
                      notifyChangeText: 1,
                      notificationFlag: {
                        $let: {
                          vars: {
                            test: {
                              $filter: {
                                input: notificationForTemp,
                                cond: {
                                  $and: [
                                    { $eq: ["$_id", "$$this.id"] },
                                    { $eq: ["activity", "$$this.type"] },
                                    { $eq: ["user", "$$this.setBy"] },
                                  ],
                                },
                              },
                            },
                          },
                          in: {
                            $gt: [{ $size: "$$test" }, 0],
                          },
                        },
                      },
                    },
                  },
                ]);
              }
            }
          }

          if (activityList.length > 0) {
            return res.status(200).json({
              status: true,
              message: "Event activity details retrive.",
              data: activityList[0],
            });
          } else {
            return res.status(200).json({
              status: false,
              message: "There is no activity for this member in this event!",
              data: [],
            });
          }
        } else {
          return res .status(404).json({ status: false, message: "Something went wrong!", data: {} });
        }
      }
    } else {
      return res
        .status(404)
        .json({ status: false, message: "activity data not found!", data: {} });
    }
  } catch (error) {
    await debugErrorLogs.createErrorLogs(error, "getEventActivityById", debugObj);
    return res
      .status(500)
      .json({ status: false, message: "Internal server error!", error: error });
  }
};


// get event by for user
exports.getEventById = async (req, res) => {
  try {
    const authUser = req.authUserId;
    const eventId = new ObjectId(req.params.id);
    let roles = [];
    let pipeline = [
      {
        '$match': {
          event: eventId,
          user: new ObjectId(authUser),
          isDelete: false,
        }
      }, {
        '$lookup': {
          'from': 'event_wise_participant_types',
          'localField': 'role',
          'foreignField': '_id',
          'as': 'event_wise_participant_types_result'
        }
      }, {
        '$unwind': {
          'path': '$event_wise_participant_types_result',
          'preserveNullAndEmptyArrays': false
        }
      }
    ]

    let attendeesList = await eventParticipantAttendees.aggregate(pipeline);
    const participantTypeIds = attendeesList?.map(
      (attendee) => attendee.event_wise_participant_types_result._id
    );

    let getEvent = await event.findOne({_id: eventId,isDelete: false})

    let channel = []
    if(getEvent.chatChannel){
    
    let userdata = await User.findById({ _id: authUser }).select({ tagId: 1 }).lean();
      
    let getChannel = await chatChannel.findById({ _id: getEvent.chatChannel })
    let channelAttendeesList = [];
    if( getChannel && getChannel.eventId && getChannel.eventId._id) {
    let channelEventId = getChannel.eventId._id
    let channelRestrictedAccess = getChannel.restrictedAccess

    let matchTemp = {
      event: channelEventId,
      user: new ObjectId(authUser),
      isDelete: false,
      // role : { $in : channelRestrictedAccess }
    }
    let roleTemp = [];
    for (let k = 0; k < channelRestrictedAccess.length; k++) {
      roleTemp.push(new ObjectId(channelRestrictedAccess[k]["_id"]))      
    }
    if(roleTemp && roleTemp.length){
      matchTemp.role = { $in : roleTemp };
    }
    
    let pipeline = [
      {
        '$match': matchTemp
      }, {
        '$lookup': {
          'from': 'event_wise_participant_types',
          'localField': 'role',
          'foreignField': '_id',
          'as': 'event_wise_participant_types_result'
        }
      }, {
        '$unwind': {
          'path': '$event_wise_participant_types_result',
          'preserveNullAndEmptyArrays': false
        }
      }
    ]
   channelAttendeesList = await eventParticipantAttendees.aggregate(pipeline);
  }
    const channelParticipantTypeIds = channelAttendeesList?.map(
      (attendee) => attendee.event_wise_participant_types_result._id
    );

      let ChannelPipline = [
        {
          $match: {
            // event: channelEventId,
            isDelete: false,
            // withEvent: true,
            _id: getEvent.chatChannel
          },
        },
        {
          $lookup: {
            from: "chatchannelmembers",
            localField: "_id",
            foreignField: "channelId",
            as: "channelMembers",
            pipeline: [
              {
                $match: {
                  userId: new ObjectId(authUser),
                },
              },
              {
                $project: {
                  _id: 1,
                  userId: 1,
                },
              },
            ],
          },
        },
        {
          $match: {
            $or: [
              {
                tagIds: { $in: userdata?.tagId ?? [] },
              },
              {
                $expr: { $gt: [{ $size: "$channelMembers" }, 0] },
              },
            ],
          },
        },
      ];
    let channelTemp = await chatChannel.aggregate(ChannelPipline);
  
    if((channelAttendeesList && channelAttendeesList.length != 0) || (channelTemp && channelTemp.length != 0) ) {
      let ChannelPiplineTemp = [
        {
          $match: {
            // event: channelEventId,
            isDelete: false,
            // withEvent: true,
            _id: getEvent.chatChannel
          },
        },
        {
          $lookup: {
            from: "chatchannelmembers",
            localField: "_id",
            foreignField: "channelId",
            as: "channelMembers",
            pipeline: [
              {
                $match: {
                  userId: new ObjectId(authUser),
                },
              },
              {
                $project: {
                  _id: 1,
                  userId: 1,
                },
              },
            ],
          },
        },        
      ];
      channel = await chatChannel.aggregate(ChannelPiplineTemp);
    }
  }
  if ((attendeesList && attendeesList.length != 0) || req.owner) {
      let pipeline2 = [
        {
          $match: {
            _id: eventId,
            isDelete: false,
          },
        },
        {
          $lookup: {
            from: "faqs",
            let: { event_id: "$_id" },
            pipeline: [
              {
                $match: {
                  $expr: {
                    $eq: ["$event", "$$event_id"],
                  },
                  isDelete: false,
                },
              },
              { $project: { question: 1, answer: 1, order: 1 } },
              { $sort: { order: 1 } },
            ],
            as: "faqs",
          },
        },
        {
          $lookup: {
              from: "event_tickets",
              let: { event_id: "$_id" },
              pipeline: [
                  {
                      $match: {
                          $expr: {
                              $eq: ["$eventId", "$$event_id"],
                          },
                          isDelete: false,
                      },
                  },
                  { $project: { actualPrice: 1, _id: 0 } },
              ],
              as: "priceData"
          }
      },
        {
          $lookup: {
              from: "eventpackages",
              let: { event_id: "$_id" },
              pipeline: [
                  {
                      $match: {
                          $expr: {
                              $eq: ["$event", "$$event_id"],
                          },
                          isDelete: false,
                      },
                  },
                  { $project: { price: 1, _id: 0 } },
              ],
              as: "priceData1"
          }
        },
        {
          $lookup: {
            from: "contentarchive_tags",
            localField: "tag",
            foreignField: "_id",
            pipeline: [
              {
                $match: {
                  isDelete: false,
                },
              },
            ],
            as: "tag",
          }
        },
        {
          $lookup: {
            from: "event_tickets",
            localField: "_id",
            foreignField: "eventId",
            as: "result"
          }
        },
        {
          $lookup: {
              from: "event_participant_attendees",
              localField: "_id",
              foreignField: "event",
              as: "event_participant_attendees_result",
              pipeline: [
                {
                  $match: {
                    $expr: {
                      $eq: [
                        new ObjectId(authUser),
                        "$user",
                      ],
                    },
                  },
                },
                {
                  $match: {
                    $expr: {
                      $eq: [
                        "$isDelete",
                        false
                      ],
                    },
                  },
                },
              ],
          },
        },
        {
          $addFields: {
            totalTicketCount: {
              $cond: {
                if: { $eq: ["$ticketPlatform", "internal"] },
                then: { 
                  $size: "$priceData"
                },
                else: { 
                  $size: "$priceData1"
                }
              }
            }
          }
        },
        {
          $project: {
            title: 1,
            tag: 1,
            startDate: 1,
            startTime: 1,
            maxTicketPurchase: 1,
            endDate: 1,
            endTime: 1,
            shortDescription: 1,
            longDescription: 1,
            timeZone: 1,
            contactSupport: 1,
            faqs: 1,
            isPreRegister: 1,
            preRegisterBtnLink: 1,
            preRegisterBtnTitle: 1,
            preRegisterDescription: 1,
            preRegisterEndDate: 1,
            preRegisterStartDate: 1,
            preRegisterTitle: 1,
            locationType: 1,
            location:1,
            onlineLocationDetail:1,
            toBeAnnouceDetail:1,
            thumbnail: 1,
            eventUrl: 1,
            ticketPlatform: 1,
            price: {
              $ifNull: [
                {
                  $cond: {
                    if: { $eq: ["$ticketPlatform", "internal"] }, // Check if ticketPlatform is "internal"
                    then: { // If true, get price from priceData
                      $min: {
                        $filter: {
                          input: "$priceData.actualPrice",
                          cond: { $gt: ["$$this", 0] }
                        }
                      }
                    },
                    else: { // If false, get price from priceData1
                      $min: {
                        $filter: {
                          input: "$priceData1.price",
                          cond: { $gt: ["$$this", 0] }
                        }
                      }
                    }
                  }
                },
                0
              ]
            },
            totalTicketCount:1,
            isCheckInAllow: 1,
            registationFlag: {
              $let: {
                vars: {},
                in: {
                  $gt: [
                    {
                      $size:
                        "$event_participant_attendees_result"
                    },
                    0
                  ]
                }
              }
            }
          },
        },
      ]
      let eventData = await event.aggregate(pipeline2);
      if (eventData && eventData[0]) {
        eventData[0]["privateProfile"] = false;
        for (let i = 0; i < attendeesList.length; i++) {
          let role = attendeesList[i]["event_wise_participant_types_result"]["role"];
          if (!roles.includes(role)) {
            roles.push(role);
          }
          if (i == 0) {
            eventData[0]["privateProfile"] = attendeesList[0]["private_profile"];
          }
        }
        if (eventData.length > 0) {
          // let eventViewData = await eventView.findOne({userId: authUser, event: eventId});
          // if (!eventViewData) {
          //     let newEventViewData = new eventView({
          //         userId: authUser,
          //         event: eventId,
          //         viewCount: 1,
          //         lastViewAt: new Date()
          //     });
          //     await newEventViewData.save();
          // } else {
          //     await eventView.findOneAndUpdate({ userId: authUser, event: eventId },
          //         { lastViewAt: new Date(), $inc: { viewCount: 1 }, },
          //         { new: true });
          // };
          
          var eventDetails = eventData[0];
          eventDetails = {
            ...eventDetails,
            role: roles,
            channelData: {channel},
          };
          return res.status(200).json({
            status: true,
            message: "Event detail retrive!",
            data: eventDetails,
          });
        } else {
          return res.status(200).json({
            status: false,
            message: "Something went wrong while getting event!",
          });
        }
      } else {
        return res.status(200).json({
          status: false,
          message: "Event details not found for this user!",
          data: {},
        });
      }
    } else {
      return res.status(200).json({
        status: false,
        message: "User details not found for this user!",
        data: {},
      });
    }
  } catch (error) {
    await debugErrorLogs.createErrorLogs(error, "getEventById", {});
    return res
      .status(500)
      .json({ status: false, message: "Internal server error!", error: error });
  }
};

// get Users we want to add rules
exports.getUserCount = async (req, res) => {
  try {
    let body = req.body;
    let match;

    match = { isDelete: false ,relation_id: ObjectId(req.relation_id),firebaseId: { $nin: ["", null] },$or: [{ blocked: false }, { blocked: { $exists: false } }],};
    if (body.eventAccess === "restricted") {
      match = {
        ...match,
        $or: [
          { _id: { $in: body.restrictedAccessUserId } },
          { purchased_plan: { $in: body.restrictedAccessMemberships } },
          { accessible_groups: { $in: body.restrictedAccessGroups } },
        ],
      };
    } else {
      match = {
        ...match,
        isCollaborator: false
      };
    }

    const userData = await User.find(match, {
      _id: 1,
      first_name: { '$ifNull': ['$first_name', ''] },
      last_name: { '$ifNull': ['$last_name', ''] },
      'Preferred Email': 1,
    })
      .populate({ path: "accessible_groups", select: "groupTitle groupInfo " })
      .populate({
        path: "purchased_plan",
        select: "plan_name -accessResources",
      })
      .populate({
        path: "attendeeDetail",
        populate: {
          path: "evntData",
          populate: {
            path: "event",
            select: { _id: 1, title: 1 },
          },
        },
      });

    if (userData.length !== 0) {
      return res.status(200).json({
        status: true,
        message: "Get notification list successfully!",
        data: userData,
      });
    } else {
      return res.status(200).json({
        status: false,
        message: "Data not found!",
      });
    }
  } catch (error) {
    return res.status(500).json({ status: false, message: error.message });
  }
};

// get event attendees
exports.getEventAttendeesByEventId = async (req, res) => {
  try {
    const eventId = new ObjectId(req.params.id);
    const eventData = await eventAttendees.aggregate([
      {
        $match: {
          event: eventId,
          isDelete: false,
        },
      },
    ]);

    if (eventData)
      return res.status(200).json({
        status: true,
        message: "Event detail retrive!",
        data: eventData,
      });
    else
      return res.status(200).json({
        status: false,
        message: "Something went wrong while getting event!",
      });
  } catch (e) {
    await debugErrorLogs.createErrorLogs(e, "getEventAttendeesByEventId", {});
    return res
      .status(500)
      .json({ status: false, message: "Internal server error!", error: e });
  }
};

// get event attendees list
exports.getEventAttendeeList = async (req, res) => {
  try {
    const eventId = ObjectId(req.params.id);
    let pipeline = [
      {
        '$match': {
          'event': eventId, 
          'isDelete': false
        }
      }, {
        '$lookup': {
          'from': 'event_participant_attendees', 
          'localField': '_id', 
          'foreignField': 'role', 
          'as': 'event_participant_attendees_result', 
          'let': {
            'event_id': '$event',
            'roleTemp' : "$role"
          }, 
          'pipeline': [
            {
              '$match': {
                '$expr': {
                  '$eq': [
                    '$event', '$$event_id'
                  ]
                },
                '$expr': {
                  '$eq': [
                    '$isDelete', false
                  ]
                }
              }
            }, {
              '$lookup': {
                'from': 'airtable-syncs', 
                'localField': 'user', 
                'foreignField': '_id', 
                'as': 'airtable-syncs_result', 
                'pipeline': [
                  {
                    '$match': {
                      '$or': [
                        {
                          'isDelete': false
                        }, {
                          'isDelete': {
                            '$exists': false
                          }
                        }
                      ]
                    }
                  }
                ]
              }
            }, {
              '$unwind': {
                'path': '$airtable-syncs_result', 
                'preserveNullAndEmptyArrays': false
              }
            }, {
              '$project': {
                '_id': '$airtable-syncs_result._id', 
                'firebaseId': {
                  '$ifNull': [
                    '$airtable-syncs_result.firebaseId', ''
                  ]
                }, 
                'email': {
                  '$ifNull': [
                    '$airtable-syncs_result.Preferred Email', ''
                  ]
                }, 
                'title': {
                  '$ifNull': [
                    '$airtable-syncs_result.attendeeDetail.title', ''
                  ]
                }, 
                'name': {
                  '$concat': [
                    '$airtable-syncs_result.first_name',
                    ' ',
                    '$airtable-syncs_result.last_name'
                  ]
                },
                'firstName': {
                  '$ifNull': [
                    '$airtable-syncs_result.first_name', ''
                  ]
                }, 
                'lastName': {
                  '$ifNull': [
                    '$airtable-syncs_result.last_name', ''
                  ]
                }, 
                'display_name': {
                  '$ifNull': [
                    '$airtable-syncs_result.display_name', ''
                  ]
                }, 
                'company': {
                  '$ifNull': [
                    '$airtable-syncs_result.attendeeDetail.company', ''
                  ]
                }, 
                'profession': {
                  '$ifNull': [
                    '$airtable-syncs_result.attendeeDetail.profession', ''
                  ]
                }, 
                'phone': {
                  '$ifNull': [
                    '$airtable-syncs_result.attendeeDetail.phone', ''
                  ]
                }, 
                'facebook': {
                  '$ifNull': [
                    '$airtable-syncs_result.attendeeDetail.facebook', ''
                  ]
                }, 
                'linkedin': {
                  '$ifNull': [
                    '$airtable-syncs_result.attendeeDetail.linkedin', ''
                  ]
                }, 
                'description': {
                  '$ifNull': [
                    '$airtable-syncs_result.attendeeDetail.description', ''
                  ]
                }, 
                'offer': {
                  '$ifNull': [
                    '$airtable-syncs_result.attendeeDetail.offer', ''
                  ]
                }, 
                'profileImg': {
                  '$cond': {
                    'if': { '$eq': ['$$roleTemp', "Partner"] }, //partner Role
                    'then': { '$ifNull': ['$airtable-syncs_result.partnerIcon', ''] },
                    'else': { '$ifNull': ['$airtable-syncs_result.profileImg', ''] }
                  }
                },
                'partnerIcon': { '$ifNull': ['$airtable-syncs_result.partnerIcon', ''] },
                'type_icon': {
                  '$cond': {
                    'if': { '$eq': ['$$roleTemp', "Partner"] }, //partner Role
                    'then': '$airtable-syncs_result.partnerIcon',
                    'else': '$airtable-syncs_result.profileImg'
                  }
                }, 
                // 'type_icon': '$type_icon', 
                'contact_name': '$contact_name', 
                'partner_order': '$partner_order', 
                'description': '$description', 
                'offer': '$offer', 
                'private_profile': '$private_profile'
              }
            }, {
              '$sort': {
                'partner_order': 1,
                'name': 1,
              },
            }
          ]
        }
      }, {
        '$addFields': {
            'role_display': {
                '$concat': [
                    '$role', 's'
                ]
            }
        }
    }
    ]

    const list = await eventWiseParticipantTypes.aggregate(pipeline);

    return res.status(200).json({
      status: true,
      list: list,
    });
  } catch (error) {
    await debugErrorLogs.createErrorLogs(error, "getEventAttendeeList", {});
    return res
      .status(500)
      .json({ status: false, message: "Internal server error!", error: error });
  }
};

// get past event yesr listing for user
exports.getPastEventYearList = async (req, res) => {
  try {
    var localDate = new Date(req.query.localDate);
    localDate = moment(localDate, "YYYY-MM-DD").toDate();
    const authUser = req.authUserId;
    let ruleCondition =  await userAccessRulesCommonCondition({userId:authUser,relation_id:req.relation_id});
    var year = [];

    const aggregatePipeline = [
      {
        $addFields: {
          Date: {
            $let: {
              vars: {
                year: { $substr: ["$endDate", 6, 10] },
                month: { $substr: ["$endDate", 0, 2] },
                dayOfMonth: { $substr: ["$endDate", 3, 2] },
                startMinute: { $substr: ["$endTime", 3, 2] },
                startHours: {
                  $toString: {
                    $cond: {
                      if: { $eq: [{ $substr: ["$endTime", 6, 2] }, "am"] },
                      then: {
                        $cond: {
                          if: { $eq: [{ $substr: ["$endTime", 0, 2] }, "12"] },
                          then: "00", // Midnight (12 AM) should be converted to "00"
                          else: { $substr: ["$endTime", 0, 2] }, // No change needed for AM times other than midnight
                        },
                      },
                      else: {
                        $cond: {
                          if: { $eq: [{ $substr: ["$endTime", 0, 2] }, "12"] },
                          then: "12", // Noon (12 PM) should remain "12"
                          else: {
                            $add: [
                              {
                                $toInt: { $substr: ["$endTime", 0, 2] },
                              },
                              12, // Adding 12 to convert PM times to 24-hour format
                            ],
                          },
                        },
                      },
                    },
                  },
                },
              },
              in: {
                $toDate: {
                  $concat: [
                    "$$year",
                    "-",
                    "$$month",
                    "-",
                    "$$dayOfMonth",
                    "T",
                    "$$startHours",
                    ":",
                    "$$startMinute",
                    ":",
                    "00",
                  ],
                },
              },
            },
          },
        },
      },
      {
        $addFields: {
          year: { $substr: ["$startDate", 6, 10] },
        },
      },
      {
        $match: {
          isDelete: false,
          status: "published",
          relation_id: ObjectId(req.relation_id),
          Date: { $lt: localDate },
        },
      },
      {
        $match: {
            ...ruleCondition
        },
      },
      { $sort: { Date: -1 } },
    ];

    const eventListData = await event.aggregate([
      ...aggregatePipeline,
      {
        $project: {
          _id: 0,
          year: 1,
        },
      },
    ]);

    eventListData.map((eventData) => {
      year.push(eventData.year);
    });
    function onlyUnique(value, index, array) {
      return array.indexOf(value) === index;
    }
    year = year.filter(onlyUnique);

    if (eventListData.length > 0) {
      return res
        .status(200)
        .json({ status: true, message: "Event list retrive!", data: year });
    } else {
      return res.status(200).json({
        status: false,
        message: "There is no past events for this user!",
        data: [],
      });
    }
  } catch (error) {
    console.log(error, "error");
    await debugErrorLogs.createErrorLogs(error, "getPastEventYearList", {});
    return res
      .status(500)
      .json({ status: false, message: "Internal server error!", error: error });
  }
};

// get past event yesr listing for user
exports.getPastEventYearFilterList = async (req, res) => {
  try {
    var localDate = new Date(req.query.localDate);
    localDate = moment(localDate, "YYYY-MM-DD").toDate();
    const page = parseInt(req.query.page);
    const limit = parseInt(req.query.limit);
    const skip = (page - 1) * limit;
    const authUser = req.authUserId;
    let ruleCondition =  await userAccessRulesCommonCondition({userId:authUser,relation_id:req.relation_id});

    var match = {
      isDelete: false,
      status: "published",
      relation_id: ObjectId(req.relation_id),
    };

    var year = "";
    if (req.query.year) {
      year = req.query.year;
      match = { ...match, year: year };
    }

    const aggregatePipeline = [
      {
        $addFields: {
          Date: {
            $let: {
              vars: {
                year: { $substr: ["$endDate", 6, 10] },
                month: { $substr: ["$endDate", 0, 2] },
                dayOfMonth: { $substr: ["$endDate", 3, 5] },
              },
              in: {
                $toDate: {
                  $concat: ["$$year", "-", "$$month", "-", "$$dayOfMonth"],
                },
              },
            },
          },
        },
      },
      {
        $match: {
          ...match,
          Date: { $lt: localDate },
        },
      },
      {
        $match: {
            ...ruleCondition
        },
      },
      { $sort: { Date: -1 } },
    ];

    const eventListData = await event.aggregate([
      ...aggregatePipeline,
      { $skip: skip },
      { $limit: limit },
      {
        $project: {
          _id: 1,
          title: 1,
          thumbnail: 1,
          eventUrl: 1,
          startDate: 1,
          startTime: 1,
          endDate: 1,
          endTime: 1,
          timeZone: 1,
          city: "$location.city",
          country: "$location.country",
          Date: 1,
          year: 1,
        },
      },
    ]);

    const count = await event.aggregate([...aggregatePipeline]);

    if (eventListData.length > 0) {
      return res.status(200).json({
        status: true,
        message: "Event list retrive!",
        data: {
          pastEvents: eventListData,
          totalPages: Math.ceil(count.length / limit),
          currentPage: page,
          totalEvents: count.length,
        },
      });
    } else {
      return res.status(200).json({
        status: false,
        message: "There is no past events for this user!",
        data: {
          pastEvents: [],
          totalPages: Math.ceil(count.length / limit),
          currentPage: page,
          totalEvents: count.length,
        },
      });
    }
  } catch (error) {
    console.log(error, "error");
    await debugErrorLogs.createErrorLogs(error, "getPastEventYearFilterList", {});
    return res
      .status(500)
      .json({ status: false, message: "Internal server error!", error: error });
  }
};

// get past event yesr listing for user
exports.getPastEventYearFilterListV2 = async (req, res) => {
  try {
    var localDate = new Date(req.query.localDate);
    localDate = moment(localDate, "YYYY-MM-DD").toDate();
    const page = parseInt(req.query.page);
    const limit = parseInt(req.query.limit);
    const skip = (page - 1) * limit;
    const authUser = req.authUserId;
    let ruleCondition =  await userAccessRulesCommonCondition({userId:authUser,relation_id:req.relation_id});

    var match = {
      isDelete: false,
      status: "published",
      relation_id: ObjectId(req.relation_id),
    };

    var year = "";
    if (req.query.year) {
      year = req.query.year;
      match = { ...match, year: year };
    }

    if (req.query.city) {
      match = {
        ...match,
        "location.city": req.query.city
      };
    };
    
    const aggregatePipeline = [
      {
        $addFields: {
          Date: {
            $let: {
              vars: {
                year: { $substr: ["$endDate", 6, 10] },
                month: { $substr: ["$endDate", 0, 2] },
                dayOfMonth: { $substr: ["$endDate", 3, 2] },
                startMinute: { $substr: ["$endTime", 3, 2] },
                startHours: {
                  $toString: {
                    $cond: {
                      if: { $eq: [{ $substr: ["$endTime", 6, 2] }, "am"] },
                      then: {
                        $cond: {
                          if: { $eq: [{ $substr: ["$endTime", 0, 2] }, "12"] },
                          then: "00", // Midnight (12 AM) should be converted to "00"
                          else: { $substr: ["$endTime", 0, 2] }, // No change needed for AM times other than midnight
                        },
                      },
                      else: {
                        $cond: {
                          if: { $eq: [{ $substr: ["$endTime", 0, 2] }, "12"] },
                          then: "12", // Noon (12 PM) should remain "12"
                          else: {
                            $add: [
                              {
                                $toInt: { $substr: ["$endTime", 0, 2] },
                              },
                              12, // Adding 12 to convert PM times to 24-hour format
                            ],
                          },
                        },
                      },
                    },
                  },
                },
                timeZoneTemp: {
                  $cond: {
                    if: {
                      $or: [
                        { $eq: ["$timeZone", ""] },
                        {
                          $eq: [
                            "$timeZone",
                            "(UTC) Dublin, Edinburgh, Lisbon, London",
                          ],
                        },
                      ],
                    },
                    then: "-00:00",
                    else: { $substr: ["$timeZone", 4, 6] },
                  },
                },
              },
              in: {
                $toDate: {
                  $concat: [
                    "$$year",
                    "-",
                    "$$month",
                    "-",
                    "$$dayOfMonth",
                    "T",
                    "$$startHours",
                    ":",
                    "$$startMinute",
                    ":",
                    "00.000",
                    "$$timeZoneTemp",
                  ],
                },
              },
            },
          },
        },
      },
      {
        $match: {
          ...match,
          Date: { $lt: localDate },
        },
      },
      {
        $match: {
            ...ruleCondition
        },
      },
      { $sort: { Date: -1 } },
    ];

    const eventListData = await event.aggregate([
      ...aggregatePipeline,
      { $skip: skip },
      { $limit: limit },
      {
        $project: {
          _id: 1,
          title: 1,
          thumbnail: 1,
          eventUrl: 1,
          startDate: 1,
          startTime: 1,
          endDate: 1,
          endTime: 1,
          timeZone: 1,
          city: "$location.city",
          country: "$location.country",
          Date: 1,
          locationType: 1,
          year: 1,
        },
      },
    ]);

    const count = await event.aggregate([...aggregatePipeline]);

    if (eventListData.length > 0) {
      return res.status(200).json({
        status: true,
        message: "Event list retrive!",
        data: {
          pastEvents: eventListData,
          totalPages: Math.ceil(count.length / limit),
          currentPage: page,
          totalEvents: count.length,
        },
      });
    } else {
      return res.status(200).json({
        status: false,
        message: "There is no past events for this user!",
        data: {
          pastEvents: [],
          totalPages: Math.ceil(count.length / limit),
          currentPage: page,
          totalEvents: count.length,
        },
      });
    }
  } catch (error) {
    console.log(error, "error");
    await debugErrorLogs.createErrorLogs(error, "getPastEventYearFilterList", {});
    return res
      .status(500)
      .json({ status: false, message: "Internal server error!", error: error });
  }
};

// get past event list for user
exports.getPastEventListV2 = async (req, res) => {
  try {
    let sortType = req.query && req.query.sortType && req.query.sortType == "Asc" ? 1 : -1;
    var localDate = new Date(req.query.localDate);
    localDate = moment(localDate, "YYYY-MM-DD").toDate();
    const page = parseInt(req.query.page);
    const limit = parseInt(req.query.limit);
    const skip = (page - 1) * limit;
    const authUser = req.authUserId;
    let ruleCondition =  await userAccessRulesCommonCondition({userId:authUser,relation_id:req.relation_id});
    let year = req.query.year ? req.query.year : ""
    let categoryId = req.query.categoryId ? req.query.categoryId : ""
    let subCategoryId = req.query.subCategoryId ? req.query.subCategoryId : ""
    let city = req.query.city ? req.query.city : ""

    var match = {
      isDelete: false,
      status: "published",
      Date: { $lt: localDate },
      relation_id: ObjectId(req.relation_id),
    }
    
    if (req.query.city) {
      match = {
        ...match,
        "location.city": req.query.city
      };
    };

    let search = "";
    if (req.query.search) {
    search = req.query.search;
    match = {
            ...match,
            $or: [
                { title: { $regex: ".*" + search + ".*", $options: "i" }, },
                { 'tag.name': { $regex: ".*" + search + ".*", $options: "i" }, },
            ]
        };
    }
    const aggregatePipeline = [
      {
        $match: {
          isDelete: false,
          status: "published",
        }
      }, 
      {
        $addFields: {
          Date: {
            $let: {
              vars: {
                year: { $substr: ["$endDate", 6, 10] },
                month: { $substr: ["$endDate", 0, 2] },
                dayOfMonth: { $substr: ["$endDate", 3, 2] },
                startMinute: { $substr: ["$endTime", 3, 2] },
                startHours: {
                  $toString: {
                    $cond: {
                      if: { $eq: [{ $substr: ["$endTime", 6, 2] }, "am"] },
                      then: {
                        $cond: {
                          if: { $eq: [{ $substr: ["$endTime", 0, 2] }, "12"] },
                          then: "00", // Midnight (12 AM) should be converted to "00"
                          else: { $substr: ["$endTime", 0, 2] }, // No change needed for AM times other than midnight
                        },
                      },
                      else: {
                        $cond: {
                          if: { $eq: [{ $substr: ["$endTime", 0, 2] }, "12"] },
                          then: "12", // Noon (12 PM) should remain "12"
                          else: {
                            $add: [
                              {
                                $toInt: { $substr: ["$endTime", 0, 2] },
                              },
                              12, // Adding 12 to convert PM times to 24-hour format
                            ],
                          },
                        },
                      },
                    },
                  },
                },
                timeZoneTemp: {
                  $cond: {
                    if: {
                      $or: [
                        { $eq: ["$timeZone", ""] },
                        {
                          $eq: [
                            "$timeZone",
                            "(UTC) Dublin, Edinburgh, Lisbon, London"
                          ]
                        }
                      ]
                    }, // Handle both empty string and "(UTC) Dublin, Edinburgh, Lisbon, London"
              
                    then: "-00:00",
                    else: {
                      $substr: ["$timeZone", 4, 6]
                    }
                  }
                }
              },
              in: {
                $toDate: {
                  $concat: [
                    "$$year",
                    "-",
                    "$$month",
                    "-",
                    "$$dayOfMonth",
                    "T",
                    "$$startHours",
                    ":",
                    "$$startMinute",
                    ":",
                    "00.000",
                    "$$timeZoneTemp"
                  ],
                },
              },
            },
          },
        },
      },
      {
        $lookup: {
          from: "contentarchive_tags",
          localField: "tag",
          foreignField: "_id",
          pipeline: [
            {
              $match: {
                isDelete: false,
              },
            },
          ],
          as: "tag",
        },
      },
      ...(req.query.year ? [
        {
          $match: {
            year:req.query.year
          },
        },
      ] : []),
      {
        $match: match
      },
      {
        $match: {
            ...ruleCondition
        },
      },
      ...(req.query.categoryId ? [
        {
          $match: {
            category: ObjectId(req.query.categoryId),
          },
        },
      ] : []),
    ...(req.query.categoryId && req.query.subCategoryId? [
        {
          $match: {
            category: ObjectId(req.query.categoryId),
            subcategory:ObjectId(req.query.subCategoryId)
          },
        },
      ] : []),
    ];

    const eventListData = await event.aggregate([
      ...aggregatePipeline,
      {
        $lookup:{
            from: "event_tickets",
            let: {
              event_id: "$_id"
            },
            pipeline: [
            {
              $match: {
                $expr: {
                  $eq: ["$eventId", "$$event_id"]
                },
                isDelete: false
              }
            },
            {
              $project: {
                actualPrice: 1,
                _id: 0
              }
            }
          ],
          as: "priceData"
        },
      },
      {
        $lookup: {
          from: "eventpackages",
          let: { event_id: "$_id" },
          pipeline: [
            {
              $match: {
                $expr: {
                  $eq: ["$event", "$$event_id"],
                },
                isDelete: false,
              },
            },
            { $project: { price: 1, _id: 0 } },
          ],
          as: "priceData1"
        }
      },
      {
        $addFields: {
          totalTicketCount: {
            $cond: {
              if: { $eq: ["$ticketPlatform", "internal"] },
              then: { 
                $size: "$priceData"
              },
              else: { 
                $size: "$priceData1"
              }
            }
          }
        }
      },
      {
        $project: {
          _id: 1,
          totalTicketCount: 1,
          title: 1,
          thumbnail: 1,
          eventUrl: 1,
          startDate: 1,
          startTime: 1,
          endDate: 1,
          endTime: 1,
          timeZone: 1,
          city: "$location.city",
          country: "$location.country",
          locationType:1,
          eventLocation : "$location",
          Date: 1,
          formatedStartDate: {
                $dateFromString: {
                    dateString: {
                        $concat: [
                            { $substr: ["$startDate", 6, 4] },
                            "-",
                            { $substr: ["$startDate", 0, 2] },
                            "-",
                            { $substr: ["$startDate", 3, 2] }
                        ]
                    }
                }
            },
          tag: 1,
          year:1,
          ticketPlatform:1,
          isCheckInAllow: 1,
          price: {
                    $ifNull: [
                      {
                        $cond: {
                          if: { $eq: ["$ticketPlatform", "internal"] }, // Check if ticketPlatform is "internal"
                          then: { // If true, get price from priceData
                            $min: {
                              $filter: {
                                input: "$priceData.actualPrice",
                                cond: { $gt: ["$$this", 0] }
                              }
                            }
                          },
                          else: { // If false, get price from priceData1
                            $min: {
                              $filter: {
                                input: "$priceData1.price",
                                cond: { $gt: ["$$this", 0] }
                              }
                            }
                          }
                        }
                      },
                      0
                    ]
          },
        },
      },
      {
        $addFields: {
          sortFieldLower:
          req.query.sortField === "startDate" ? { $toDate: "$formatedStartDate" } : req.query.sortField === "title" ? { $toLower:"$title"} : req.query.sortField === "price" ? { $toInt:"$price"} : "$Date"  
          },
        },
        { $sort: { sortFieldLower: sortType } },
        { $skip: skip },
        { $limit: limit },
    ]);

    // const count = await event.aggregate([...aggregatePipeline]);
    
    // get upcomming ,past ,myevent count
    let allEventCount = await getEventCountV2(req.relation_id,localDate,authUser,"past",categoryId,subCategoryId,year,"",search,city);

    if (eventListData.length > 0) {
      return res.status(200).json({
        status: true,
        message: "Event list retrive!",
        data: {
          pastEvents: eventListData,
          totalPages: Math.ceil(allEventCount.pastEventcount / limit),
          currentPage: page,
          totalEvents: allEventCount.pastEventcount,
          allCount:allEventCount
        },
      });
    } else {
      return res.status(200).json({
        status: false,
        message: "There is no past events for this user!",
        data: {
          pastEvents: [],
          totalPages: Math.ceil(allEventCount.pastEventcount / limit),
          currentPage: page,
          totalEvents: allEventCount.pastEventcount,
          allCount:allEventCount
        },
      });
    }
  } catch (error) {
    await debugErrorLogs.createErrorLogs(error, "getPastEventList", {});
    return res
      .status(500)
      .json({ status: false, message: "Internal server error!", error: error });
  }
};

// get past event details by Id for user
exports.getPastEventById = async (req, res) => {
  try {
    const authUser = req.authUserId;
    const eventId = new ObjectId(req.params.id);

    let pipeline = [
      {
        '$match': {
          event: eventId,
          user: new ObjectId(authUser),
          isDelete: false,
        }
      }, {
        '$lookup': {
          'from': 'event_wise_participant_types',
          'localField': 'role',
          'foreignField': '_id',
          'as': 'event_wise_participant_types_result',
          'pipeline': [
            {
              '$match': {
                'isDelete': false,
              },
            },
          ],
        }
      }, {
        '$unwind': {
          'path': '$event_wise_participant_types_result',
          'preserveNullAndEmptyArrays': false
        }
      },
      {
        '$lookup': {
          'from': 'eventactivities', 
          'localField': 'event', 
          'foreignField': 'event', 
          'as': 'eventactivities',
          'pipeline': [
            {
              '$match': {
                'isDelete': false,
                'status':"published"
              },
            },
          ],
        }
      }
    ]


    let attendeesList = await eventParticipantAttendees.aggregate(pipeline);
    const eventData = await event.aggregate([
      {
        $match: {
          _id: eventId,
          isDelete: false,
          status: "published",
        },
      },
      {
        $lookup: {
          from: "contentarchive_tags",
          localField: "tag",
          foreignField: "_id",
          pipeline: [
            {
              $match: {
                isDelete: false,
              },
            },
          ],
          as: "tag",
        },
      },
      {
        $project: {
          title: 1,
          thumbnail: 1,
          eventUrl: 1,
          type: ["$type.name"],
          startDate: 1,
          startTime: 1,
          endDate: 1,
          endTime: 1,
          shortDescription: 1,
          longDescription: 1,
          timeZone: 1,
          location: {
            $cond: [
              {
                $ifNull: ["$isLocation", false],
              },
              ["$location"],
              [],
            ],
          },
          contactSupport: 1,
          isPreRegister: 1,
          preRegisterBtnLink: 1,
          preRegisterBtnTitle: 1,
          preRegisterDescription: 1,
          preRegisterEndDate: 1,
          preRegisterStartDate: 1,
          preRegisterTitle: 1,
          tag: 1,
        },
      },
    ]);

    if (eventData.length > 0) {
      var eventDetails = eventData[0];
      if ( attendeesList && attendeesList[0] && attendeesList[0]["eventactivities"] && attendeesList[0]["eventactivities"].length > 0 
      ) {
        eventDetails = { ...eventDetails, showScheduleDetail: true };
      } else {
        eventDetails = { ...eventDetails, showScheduleDetail: false };
      }
      return res.status(200).json({
        status: true,
        message: "Event detail retrive!",
        data: eventDetails,
      });
    } else {
      return res.status(200).json({
        status: false,
        message: "User don't have access of this event!",
      });
    }
  } catch (error) {
    await debugErrorLogs.createErrorLogs(error, "getPastEventById", {});
    return res
      .status(500)
      .json({ status: false, message: "Internal server error!", error: error });
  }
};
// get past event details by Id for user
exports.getPastEventByIdV2 = async (req, res) => {
  try {
    const authUser = req.authUserId;
    const eventId = new ObjectId(req.params.id);
    let ruleCondition =  await userAccessRulesCommonCondition({userId:authUser,relation_id:req.relation_id});

    let pipeline = [
      {
        '$match': {
          event: eventId,
          user: new ObjectId(authUser),
          isDelete: false,
          relation_id: ObjectId(req.relation_id),
        }
      }, {
        '$lookup': {
          'from': 'event_wise_participant_types',
          'localField': 'role',
          'foreignField': '_id',
          'as': 'event_wise_participant_types_result',
          'pipeline': [
            {
              '$match': {
                'isDelete': false,
              },
            },
          ],
        }
      }, {
        '$unwind': {
          'path': '$event_wise_participant_types_result',
          'preserveNullAndEmptyArrays': false
        }
      },
      {
        '$lookup': {
          'from': 'eventactivities', 
          'localField': 'event', 
          'foreignField': 'event', 
          'as': 'eventactivities',
          'pipeline': [
            {
              '$match': {
                'isDelete': false,
                'status':'published'
              },
            },
          ],
        }
      }
    ]


    let attendeesList = await eventParticipantAttendees.aggregate(pipeline);

    const eventData = await event.aggregate([
      {
        $match: {
          _id: eventId,
          isDelete: false,
          $or: [
            { status: "paused" },
            { status: "published" }
        ]
        },
      },
      {
        $match: {
          ...(req.owner ? {}: {...ruleCondition})
            // ...ruleCondition
        },
      },
      {
        $lookup: {
            from: "event_tickets",
            let: { event_id: "$_id" },
            pipeline: [
                {
                    $match: {
                        $expr: {
                            $eq: ["$eventId", "$$event_id"],
                        },
                        isDelete: false,
                    },
                },
                { $project: { actualPrice: 1, _id: 0 } },
            ],
            as: "priceData"
        }
    },
      {
        $lookup: {
          from: "eventpackages",
          let: { event_id: "$_id" },
          pipeline: [
            {
              $match: {
                $expr: {
                  $eq: ["$event", "$$event_id"],
                },
                isDelete: false,
              },
            },
            { $project: { price: 1, _id: 0 } },
          ],
          as: "priceData1"
        }
      },
      {
        $lookup: {
          from: "contentarchive_tags",
          localField: "tag",
          foreignField: "_id",
          pipeline: [
            {
              $match: {
                isDelete: false,
              },
            },
          ],
          as: "tag",
        },
      },
      {
        $lookup: {
          from: "event_tickets",
          localField: "_id",
          foreignField: "eventId",
          as: "result"
        }
      },
      {
        $addFields: {
          totalTicketCount: {
            $cond: {
              if: { $eq: ["$ticketPlatform", "internal"] },
              then: { 
                $size: "$priceData"
              },
              else: { 
                $size: "$priceData1"
              }
            }
          }
        }
      },
      {
        $project: {
          title: 1,
          thumbnail: 1,
          eventUrl: 1,
          type: ["$type.name"],
          locationType: 1,
          startDate: 1,
          startTime: 1,
          endDate: 1,
          endTime: 1,
          shortDescription: 1,
          longDescription: 1,
          timeZone: 1,
          location: {
            $cond: [
              { $and: [ { $ifNull: ["$isLocation", false] }, { $ne: ["$location", null] } ] },
              ["$location"], 
              [] 
            ]
          },
          contactSupport: 1,
          isPreRegister: 1,
          preRegisterBtnLink: 1,
          preRegisterBtnTitle: 1,
          preRegisterDescription: 1,
          preRegisterEndDate: 1,
          preRegisterStartDate: 1,
          preRegisterTitle: 1,
          tag: 1,
          ticketPlatform: 1,
          price: {
            $ifNull: [
              {
                $cond: {
                  if: { $eq: ["$ticketPlatform", "internal"] }, // Check if ticketPlatform is "internal"
                  then: { // If true, get price from priceData
                    $min: {
                      $filter: {
                        input: "$priceData.actualPrice",
                        cond: { $gt: ["$$this", 0] }
                      }
                    }
                  },
                  else: { // If false, get price from priceData1
                    $min: {
                      $filter: {
                        input: "$priceData1.price",
                        cond: { $gt: ["$$this", 0] }
                      }
                    }
                  }
                }
              },
              0
            ]
          },
          totalTicketCount:1,
          isCheckInAllow:1,
        },
      },
    ]);

    if (eventData.length > 0) {
      let eventViewData = await eventView.findOne({userId: authUser, event: eventId});
      if (!eventViewData) {
          let newEventViewData = new eventView({
              userId: authUser,
              event: eventId,
              viewCount: 1,
              lastViewAt: new Date()
          });
          await newEventViewData.save();
      } else {
          await eventView.findOneAndUpdate({ userId: authUser, event: eventId },
              { lastViewAt: new Date(), $inc: { viewCount: 1 }, },
              { new: true });
      };

      var eventDetails = eventData[0];
      if ( attendeesList && attendeesList[0] && attendeesList[0]["eventactivities"] && attendeesList[0]["eventactivities"].length > 0 
      ) {
        eventDetails = { ...eventDetails, showScheduleDetail: true };
      } else {
        eventDetails = { ...eventDetails, showScheduleDetail: false };
      }
      return res.status(200).json({
        status: true,
        message: "Event detail retrive!",
        data: eventDetails,
      });
    } else {
      return res.status(200).json({
        status: false,
        message: "User don't have access of this event!",
      });
    }
  } catch (error) {
    await debugErrorLogs.createErrorLogs(error, "getPastEventById", {});
    return res
      .status(500)
      .json({ status: false, message: "Internal server error!", error: error });
  }
};

// get attendees profile details
exports.getEventAttendeeProfile = async (req, res) => {
  try {
    const attendeeId = ObjectId(req.params.id);
    const eventId = ObjectId(req.query.eventId);
    const role = req.query.role;
    let attendeeProfile = [];

    switch (role) {
      case "member":
        attendeeProfile = await User.aggregate([
          {
            $match: {
              _id: attendeeId,
              "attendeeDetail.evntData": {
                $elemMatch: { event: eventId, [`${role}`]: true },
              },
            },
          },
          {
            $project: {
              _id: 1,
              firebaseId: 1,
              email: "$Preferred Email",
              type: "Member",
              title: "$attendeeDetail.title",
              name: "$attendeeDetail.name",
              firstName: "$attendeeDetail.firstName"
                ? "$attendeeDetail.firstName"
                : "",
              lastName: "$attendeeDetail.lastName"
                ? "$attendeeDetail.lastName"
                : "",
              company: "$attendeeDetail.company",
              profession: "$attendeeDetail.profession",
              phone: "$attendeeDetail.phone",
              facebook: "$attendeeDetail.facebook",
              linkedin: "$attendeeDetail.linkedin",
              description: "$attendeeDetail.description" ?? "",
              offer: "$attendeeDetail.offer" ?? "",
              contactPartnerName: "$attendeeDetail.contactPartnerName" ?? "",
              event: eventId,
              profileImg: "$profileImg",
            },
          },
        ]);
        if (attendeeProfile.length > 0) {
          return res.status(200).json({
            status: true,
            message: "Attendees details retrive.",
            data: attendeeProfile[0],
          });
        } else {
          return res.status(200).json({
            status: false,
            message: "Member attendees details not found!",
          });
        }
        break;
      case "speaker":
        attendeeProfile = await User.aggregate([
          {
            $match: {
              _id: attendeeId,
              "attendeeDetail.evntData": {
                $elemMatch: { event: eventId, [`${role}`]: true },
              },
            },
          },
          {
            $project: {
              _id: 1,
              firebaseId: 1,
              email: "$Preferred Email",
              type: "Speaker",
              title: "$attendeeDetail.title",
              name: "$attendeeDetail.name",
              firstName: "$attendeeDetail.firstName"
                ? "$attendeeDetail.firstName"
                : "",
              lastName: "$attendeeDetail.lastName"
                ? "$attendeeDetail.lastName"
                : "",
              company: "$attendeeDetail.company",
              profession: "$attendeeDetail.profession",
              phone: "$attendeeDetail.phone",
              facebook: "$attendeeDetail.facebook",
              linkedin: "$attendeeDetail.linkedin",
              description: "$attendeeDetail.description" ?? "",
              offer: "$attendeeDetail.offer" ?? "",
              contactPartnerName: "$attendeeDetail.contactPartnerName" ?? "",
              event: eventId,
              profileImg: "$profileImg",
            },
          },
        ]);

        if (attendeeProfile.length > 0) {
          attendeeProfile = attendeeProfile[0];
          const sessionList = await eventSession.aggregate([
            {
              $match: {
                event: attendeeProfile.event,
                "speakerId.0": { $exists: true },
                $expr: { $in: [attendeeId, "$speakerId"] },
                isDelete: false,
              },
            },
            {
              $lookup: {
                from: "rooms",
                let: { session_rooms_id: "$room" },
                pipeline: [
                  {
                    $match: {
                      $expr: {
                        $eq: ["$_id", "$$session_rooms_id"],
                      },
                    },
                  },
                  {
                    $lookup: {
                      from: "eventlocations",
                      let: { location_id: "$location" },
                      pipeline: [
                        {
                          $match: {
                            $expr: {
                              $eq: ["$_id", "$$location_id"],
                            },
                          },
                        },
                        {
                          $project: {
                            name: 1,
                            address: 1,
                            country: 1,
                            city: 1,
                            latitude: 1,
                            longitude: 1,
                            locationVisible: 1,
                            locationImages: 1,
                          },
                        },
                      ],
                      as: "location",
                    },
                  },
                  {
                    $unwind: "$location",
                  },
                  { $project: { name: 1, location: 1 } },
                ],
                as: "room",
              },
            },
            {
              $unwind: "$room",
            },
            {
              $lookup: {
                from: "events",
                let: { event_id: "$event" },
                pipeline: [
                  {
                    $match: {
                      $expr: {
                        $eq: ["$_id", "$$event_id"],
                      },
                    },
                  },
                  {
                    $lookup: {
                      from: "eventtypes",
                      let: { type_id: "$type" },
                      pipeline: [
                        {
                          $match: {
                            $expr: {
                              $eq: ["$_id", "$$type_id"],
                            },
                            isDelete: false,
                          },
                        },
                        { $project: { name: 1, _id: 0 } },
                      ],
                      as: "type",
                    },
                  },
                  { $unwind: "$type" },
                  {
                    $project: {
                      title: 1,
                      thumbnail: 1,
                      shortDescription: 1,
                      longDescription: 1,
                      eventUrl: 1,
                      type: "$type.name",
                      timeZone: 1,
                      startDate: 1,
                      startTime: 1,
                      endDate: 1,
                      endTime: 1,
                      restrictionAccess: 1,
                      photos: 1,
                    },
                  },
                ],
                as: "events",
              },
            },
            {
              $lookup: {
                from: "airtable-syncs",
                let: { speakerId: "$speakerId" },
                pipeline: [
                  {
                    $match: {
                      $expr: {
                        $in: ["$_id", "$$speakerId"],
                      },
                    },
                  },
                  {
                    $project: {
                      _id: "$_id",
                      profileImg: "$profileImg" ? "$profileImg" : "",
                      attendeeDetail: {
                        title: "$attendeeDetail.title",
                        name: "$attendeeDetail.name",
                        firstName: "$attendeeDetail.firstName"
                          ? "$attendeeDetail.firstName"
                          : "",
                        lastName: "$attendeeDetail.lastName"
                          ? "$attendeeDetail.lastName"
                          : "",
                        company: "$attendeeDetail.company",
                        profession: "$attendeeDetail.profession",
                        phone: "$attendeeDetail.phone",
                        facebook: "$attendeeDetail.facebook",
                        linkedin: "$attendeeDetail.linkedin",
                        firebaseId: "$attendeeDetail.firebaseId",
                        description: "$attendeeDetail.description" ?? "",
                        offer: "$attendeeDetail.offer" ?? "",
                        contactPartnerName:
                          "$attendeeDetail.contactPartnerName" ?? "",
                      },
                    },
                  },
                ],
                as: "speakerId",
              },
            },
            {
              $project: {
                _id: 1,
                title: 1,
                description: 1,
                date: 1,
                startTime: 1,
                endTime: 1,
                room: 1,
                speakerId: 1,
                event: 1,
                reserved: 1,
                reserved_URL: 1,
                reservedLabelForDetail: 1,
                reservedLabelForListing: 1,
                member: 1,
                speaker: 1,
                partner: 1,
                guest: 1,
                isDelete: 1,
                createdAt: 1,
                updatedAt: 1,
              },
            },
          ]);
          if (sessionList.length > 0) {
            attendeeProfile = { ...attendeeProfile, sessionList: sessionList };
          }

          return res.status(200).json({
            status: true,
            message: "Attendees details retrive.",
            data: attendeeProfile,
          });
        } else {
          return res.status(200).json({
            status: false,
            message: "Speaker attendees details not found!",
          });
        }
        break;
      case "partner":
        attendeeProfile = await User.aggregate([
          {
            $match: {
              _id: attendeeId,
              "attendeeDetail.evntData": {
                $elemMatch: { event: eventId, [`${role}`]: true },
              },
            },
          },
          {
            $project: {
              _id: 1,
              firebaseId: 1,
              email: "$Preferred Email",
              type: "Partner",
              title: "$attendeeDetail.title",
              name: "$attendeeDetail.name",
              firstName: "$attendeeDetail.firstName"
                ? "$attendeeDetail.firstName"
                : "",
              lastName: "$attendeeDetail.lastName"
                ? "$attendeeDetail.lastName"
                : "",
              company: "$attendeeDetail.company",
              profession: "$attendeeDetail.profession",
              phone: "$attendeeDetail.phone",
              facebook: "$attendeeDetail.facebook",
              linkedin: "$attendeeDetail.linkedin",
              description: "$attendeeDetail.description" ?? "",
              offer: "$attendeeDetail.offer" ?? "",
              contactPartnerName: "$attendeeDetail.contactPartnerName" ?? "",
              event: eventId,
              profileImg: "$partnerIcon",
            },
          },
        ]);
        if (attendeeProfile.length > 0) {
          return res.status(200).json({
            status: true,
            message: "Attendees details retrive.",
            data: attendeeProfile[0],
          });
        } else {
          return res.status(200).json({
            status: false,
            message: "Partner attendees details not found!",
          });
        }
        break;
      case "guest":
        attendeeProfile = await User.aggregate([
          {
            $match: {
              _id: attendeeId,
              "attendeeDetail.evntData": {
                $elemMatch: { event: eventId, [`${role}`]: true },
              },
            },
          },
          {
            $project: {
              _id: 1,
              firebaseId: 1,
              email: "$Preferred Email",
              type: "Guest",
              title: "$attendeeDetail.title",
              name: "$attendeeDetail.name",
              firstName: "$attendeeDetail.firstName"
                ? "$attendeeDetail.firstName"
                : "",
              lastName: "$attendeeDetail.lastName"
                ? "$attendeeDetail.lastName"
                : "",
              company: "$attendeeDetail.company",
              profession: "$attendeeDetail.profession",
              phone: "$attendeeDetail.phone",
              facebook: "$attendeeDetail.facebook",
              linkedin: "$attendeeDetail.linkedin",
              description: "$attendeeDetail.description" ?? "",
              offer: "$attendeeDetail.offer" ?? "",
              event: eventId,
              profileImg: "$profileImg",
            },
          },
        ]);
        if (attendeeProfile.length > 0) {
          return res.status(200).json({
            status: true,
            message: "Attendees details retrive.",
            data: attendeeProfile[0],
          });
        } else {
          return res.status(200).json({
            status: false,
            message: "Guest attendee details not found!",
          });
        }
        break;
      default:
        break;
    }
  } catch (error) {
    await debugErrorLogs.createErrorLogs(error, "getEventAttendeeProfile", {});
    return res
      .status(500)
      .json({ status: false, message: "Internal server error!", error: error });
  }
};

// get attendees profile details for chat
exports.getAttendeeWithoutEventById = async (req, res) => {
  try {
    const attendeeId = ObjectId(req.params.id);

    const attendeeProfile = await User.aggregate([
      {
        $match: {
          _id: attendeeId,
        },
      },
      {
        $project: {
          _id: 1,
          firebaseId: 1,
          email: "$Preferred Email",
          type: "",
          title: "$attendeeDetail.title",
          name: "$attendeeDetail.name",
          firstName: "$attendeeDetail.firstName"
            ? "$attendeeDetail.firstName"
            : "",
          lastName: "$attendeeDetail.lastName"
            ? "$attendeeDetail.lastName"
            : "",
          company: "$attendeeDetail.company",
          profession: "$attendeeDetail.profession",
          phone: "$attendeeDetail.phone",
          facebook: "$attendeeDetail.facebook",
          linkedin: "$attendeeDetail.linkedin",
          description: "$attendeeDetail.description" ?? "",
          offer: "$attendeeDetail.offer" ?? "",
          contactPartnerName: "$attendeeDetail.contactPartnerName" ?? "",
          event: "",
          profileImg: "$profileImg",
        },
      },
    ]);

    if (attendeeProfile.length > 0) {
      return res.status(200).json({
        status: true,
        message: "Attendees details retrive.",
        data: attendeeProfile[0],
      });
    } else {
      return res
        .status(200)
        .json({ status: false, message: "Attendee details not found!" });
    }
  } catch (error) {
    await debugErrorLogs.createErrorLogs(error, "getAttendeeWithoutEventById", {});
    return res
      .status(500)
      .json({ status: false, message: "Internal server error!", error: error });
  }
};

/** User APIs ends **/

// get attendees profile details
exports.getEventAttendeeProfileV2 = async (req, res) => {
  try {
    const attendeeId = ObjectId(req.params.id);
    const eventId = ObjectId(req.query.eventId);
    const role = req.query.role;
    let attendeeProfile = [];

    let pipeline = [
      {
        '$match': {
          'user': attendeeId,
          'event': eventId,
          'isDelete': false,
          // 'role': role
        }
      }, 
      {
        '$lookup': {
            'from': 'event_wise_participant_types', 
            'localField': 'role', 
            'foreignField': '_id', 
            'as': 'event_wise_participant_types', 
            'pipeline': [
                {
                    '$addFields': {
                        'roleType': {
                            '$toLower': '$role'
                        }
                    }
                }, {
                    '$match': {
                        '$expr': {
                            '$eq': [
                                '$roleType', role
                            ]
                        }
                    }
                }
            ]
        }
    },
    {
      '$match': {
          '$expr': {
              '$gt': [
                  {
                      '$size': '$event_wise_participant_types'
                  }, 0
              ]
          }
      }
    },
    {
      '$unset': 'event_wise_participant_types'
    },
    {
        '$lookup': {
          'from': 'airtable-syncs',
          'localField': 'user',
          'foreignField': '_id',
          'as': 'airtable-syncs_result',
        }
      }, {
        '$unwind': {
          'path': '$airtable-syncs_result',
          'preserveNullAndEmptyArrays': false
        }
      }, {
        '$lookup': {
          'from': 'event_wise_participant_types',
          'localField': 'role',
          'foreignField': '_id',
          'as': 'event_wise_participant_types_result'
        }
      }, {
        '$unwind': {
          'path': '$event_wise_participant_types_result',
          'preserveNullAndEmptyArrays': false
        }
      }, {
        '$lookup': {
          'from': 'events',
          'localField': 'event',
          'foreignField': '_id',
          'as': 'events_result'
        }
      }, {
        '$unwind': {
          'path': '$events_result',
          'preserveNullAndEmptyArrays': false
        }
      }, {
        '$project': {
          '_id': {
            '$ifNull': [
              '$airtable-syncs_result._id', ''
            ]
          },
          'firebaseId': {
            '$ifNull': [
              '$airtable-syncs_result.firebaseId', ''
            ]
          },
          'email': {
            '$ifNull': [
              '$airtable-syncs_result.Preferred Email', ''
            ]
          },
          'title': {
            '$ifNull': [
              '$airtable-syncs_result.attendeeDetail.title', ''
            ]
          },
          'name': {
            '$ifNull': [
              '$airtable-syncs_result.attendeeDetail.name', ''
            ]
          },
          'firstName': {
            '$ifNull': [
              '$airtable-syncs_result.attendeeDetail.firstName', ''
            ]
          },
          'lastName': {
            '$ifNull': [
              '$airtable-syncs_result.attendeeDetail.lastName', ''
            ]
          },
          'display_name': {
            '$ifNull': [
              '$airtable-syncs_result.display_name', ''
            ]
          },
          'company': {
            '$ifNull': [
              '$airtable-syncs_result.attendeeDetail.company', ''
            ]
          },
          'profession': {
            '$ifNull': [
              '$airtable-syncs_result.attendeeDetail.profession', ''
            ]
          },
          'phone': {
            '$ifNull': [
              '$airtable-syncs_result.attendeeDetail.phone', ''
            ]
          },
          'facebook': {
            '$ifNull': [
              '$airtable-syncs_result.attendeeDetail.facebook', ''
            ]
          },
          'linkedin': {
            '$ifNull': [
              '$airtable-syncs_result.attendeeDetail.linkedin', ''
            ]
          },
          'description': {
            '$ifNull': [
              '$airtable-syncs_result.attendeeDetail.description', ''
            ]
          },
          'offer': {
            '$ifNull': [
              '$airtable-syncs_result.attendeeDetail.offer', ''
            ]
          },
          'contactPartnerName': {
            '$ifNull': [
              '$airtable-syncs_result.attendeeDetail.contactPartnerName', ''
            ]
          },
          'profileImg': {
            '$cond': {
              'if': { '$eq': ['$event_wise_participant_types_result.role', 'Partner'] },
              'then':  { '$ifNull': ['$airtable-syncs_result.partnerIcon', ''] },
              'else': { '$ifNull': ['$airtable-syncs_result.profileImg', ''] },
            }
          },
          'partnerIcon': { '$ifNull': ['$airtable-syncs_result.partnerIcon', ''] },
          'type': {
            '$ifNull': [
              '$event_wise_participant_types_result.role', ''
            ]
          },
          'event': '$event',
          'type_icon': {
            '$cond': {
              'if': { '$eq': ['$event_wise_participant_types_result.role', 'Partner'] },
              'then': '$airtable-syncs_result.partnerIcon',
              'else': '$airtable-syncs_result.profileImg'
            }
          }, 
          'contact_name': '$contact_name',
          'partner_order': '$partner_order',
          'description': '$description',
          'offer': '$offer',
          'private_profile': '$private_profile'
        }
      },
    ]
    attendeeProfile = await eventParticipantAttendees.aggregate(pipeline);
    if (attendeeProfile.length > 0 && attendeeProfile[0] && attendeeProfile[0]["type"] && attendeeProfile[0]["type"] == "Speaker") {
      let pipeline2 =  [
        {
          $match: {
            event: attendeeProfile[0].event,
            "speakerId.0": { $exists: true },
            $expr: { $in: [attendeeId, "$speakerId"] },
            isDelete: false,
          },
        },
        {
          $lookup: {
            from: "rooms",
            let: { session_rooms_id: "$room" },
            pipeline: [
              {
                $match: {
                  $expr: {
                    $eq: ["$_id", "$$session_rooms_id"],
                  },
                },
              },
              {
                $lookup: {
                  from: "eventlocations",
                  let: { location_id: "$location" },
                  pipeline: [
                    {
                      $match: {
                        $expr: {
                          $eq: ["$_id", "$$location_id"],
                        },
                      },
                    },
                    {
                      $project: {
                        name: 1,
                        address: 1,
                        country: 1,
                        city: 1,
                        latitude: 1,
                        longitude: 1,
                        locationVisible: 1,
                        locationImages: 1,
                      },
                    },
                  ],
                  as: "location",
                },
              },
              {
                $unwind: "$location",
              },
              { $project: { name: 1, location: 1 } },
            ],
            as: "room",
          },
        },
        {
          $unwind: "$room",
        },
        {
          $lookup: {
            from: "events",
            let: { event_id: "$event" },
            pipeline: [
              {
                $match: {
                  $expr: {
                    $eq: ["$_id", "$$event_id"],
                  },
                },
              },
              {
                $lookup: {
                  from: "eventtypes",
                  let: { type_id: "$type" },
                  pipeline: [
                    {
                      $match: {
                        $expr: {
                          $eq: ["$_id", "$$type_id"],
                        },
                        isDelete: false,
                      },
                    },
                    { $project: { name: 1, _id: 0 } },
                  ],
                  as: "type",
                },
              },
              { $unwind: "$type" },
              {
                $project: {
                  title: 1,
                  thumbnail: 1,
                  shortDescription: 1,
                  longDescription: 1,
                  eventUrl: 1,
                  type: "$type.name",
                  timeZone: 1,
                  startDate: 1,
                  startTime: 1,
                  endDate: 1,
                  endTime: 1,
                  eventAccess: 1,
                  restrictedAccessGroups: 1,
                  restrictedAccessMemberships: 1,
                  photos: 1,
                },
              },
            ],
            as: "events",
          },
        },
        {
          $lookup: {
            from: "airtable-syncs",
            let: { speakerId: "$speakerId" },
            pipeline: [
              {
                $match: {
                  $expr: {
                    $in: ["$_id", "$$speakerId"],
                  },
                },
              },
              {
                $project: {
                  _id: "$_id",
                  profileImg: "$profileImg" ? "$profileImg" : "",
                  attendeeDetail: {
                    title: "$attendeeDetail.title",
                    name: "$attendeeDetail.name",
                    firstName: "$attendeeDetail.firstName"
                      ? "$attendeeDetail.firstName"
                      : "",
                    lastName: "$attendeeDetail.lastName"
                      ? "$attendeeDetail.lastName"
                      : "",
                    company: "$attendeeDetail.company",
                    profession: "$attendeeDetail.profession",
                    phone: "$attendeeDetail.phone",
                    facebook: "$attendeeDetail.facebook",
                    linkedin: "$attendeeDetail.linkedin",
                    firebaseId: "$attendeeDetail.firebaseId",
                    description: "$attendeeDetail.description" ?? "",
                    offer: "$attendeeDetail.offer" ?? "",
                    contactPartnerName:
                      "$attendeeDetail.contactPartnerName" ?? "",
                  },
                },
              },
            ],
            as: "speakerId",
          },
        },
        {
          $project: {
            _id: 1,
            title: 1,
            description: 1,
            date: 1,
            startTime: 1,
            endTime: 1,
            room: 1,
            speakerId: 1,
            event: 1,
            reserved: 1,
            reserved_URL: 1,
            reservedLabelForDetail: 1,
            reservedLabelForListing: 1,
            member: 1,
            speaker: 1,
            partner: 1,
            guest: 1,
            isDelete: 1,
            createdAt: 1,
            updatedAt: 1,
          },
        },
      ];
      const sessionList = await eventSession.aggregate(pipeline2);
      if (sessionList.length > 0) {
        attendeeProfile[0] = { ...attendeeProfile[0], sessionList: sessionList };
      }
    }
    if (attendeeProfile.length > 0) {
      return res.status(200).json({
        status: true,
        message: "Attendees details retrive.",
        data: attendeeProfile[0],
      });
    } else {
      return res.status(200).json({
        status: false,
        message: "Attendees details not found!",
      });
    }
  } catch (error) {
    await debugErrorLogs.createErrorLogs(error, "getEventAttendeeProfileV2", {});
    return res
      .status(500)
      .json({ status: false, message: "Internal server error!", error: error });
  }
};


// get event activity list with session count for user
exports.getEventActivityByEventIdV2 = async (req, res) => {
  let debugObj = {}
  try {
    const authUser = req.authUserId;
    const role = req.query.role;
    const eventId = new ObjectId(req.params.id);
    debugObj = {
      authUser : authUser?authUser : "", 
      role: role?role : "", 
      eventId: eventId? eventId : "" 
    }
    let activityList = [];

    let pipeline = [
      {
        '$match': {
          event: eventId,
          user: new ObjectId(authUser),
          isDelete: false,
        }
      }, {
        '$lookup': {
          'from': 'event_wise_participant_types',
          'localField': 'role',
          'foreignField': '_id',
          'as': 'event_wise_participant_types_result'
        }
      }, {
        '$unwind': {
          'path': '$event_wise_participant_types_result',
          'preserveNullAndEmptyArrays': false
        }
      }, {
        '$lookup': {
          'from': 'airtable-syncs',
          'localField': 'user',
          'foreignField': '_id',
          'as': 'airtable-syncs_result'
        }
      }, {
        '$unwind': {
          'path': '$airtable-syncs_result',
          'preserveNullAndEmptyArrays': false
        }
      }, {
        '$project': {
          '_id': {
            '$ifNull': [
              '$airtable-syncs_result._id', ''
            ]
          },
          'email': {
            '$ifNull': [
              '$airtable-syncs_result.Preferred Email', ''
            ]
          },
          'firebaseId': {
            '$ifNull': [
              '$airtable-syncs_result.firebaseId', ''
            ]
          },
          'accessible_groups': {
            '$ifNull': [
              '$airtable-syncs_result.accessible_groups', ''
            ]
          },
          'notificationFor': {
            '$ifNull': [
              '$airtable-syncs_result.notificationFor', []
            ]
          },
          'purchased_plan': {
            '$ifNull': [
              '$airtable-syncs_result.purchased_plan', ''
            ]
          },
          'attendeeDetail': {
            '$ifNull': [
              '$airtable-syncs_result.attendeeDetail', {}
            ]
          },
          'contact_name': 1,
          'partner_order': 1,
          'description': 1,
          'offer': 1,
          'type_icon': {
            '$cond': {
              'if': { '$eq': ['$event_wise_participant_types_result.role', 'Partner'] },
              'then': '$airtable-syncs_result.partnerIcon',
              'else': '$airtable-syncs_result.profileImg'
            }
          }, 
          'contactPartnerName': '$contact_name',
          'profileImg': {
            '$cond': {
              'if': { '$eq': ['$event_wise_participant_types_result.role', 'Partner'] },
              'then':  { '$ifNull': ['$airtable-syncs_result.partnerIcon', ''] },
              'else': { '$ifNull': ['$airtable-syncs_result.profileImg', ''] },
            }
          },
          'partnerIcon': { '$ifNull': ['$airtable-syncs_result.profileImg', ''] },
          'event': '$event',
          'event': '$event',
          'role': '$event_wise_participant_types_result.role',
          'role_type': '$event_wise_participant_types_result._id'
        }
      }
    ]
    let attendeesList = await eventParticipantAttendees.aggregate(pipeline);
    if (attendeesList && attendeesList.length != 0) {
      for (let i = 0; i < attendeesList.length; i++) {
        let roleTemp = [ attendeesList[i]["role"] ];
        if(attendeesList[i]["role"] == "Staff"){
          roleTemp.push("Member");
        }
        let userIdTemp = ObjectId(attendeesList[i]["_id"]);
        let accessibleGroups = [];
        let purchasedPlan = [];
        if (attendeesList[i]["accessible_groups"] && attendeesList[i]["accessible_groups"].length ) {
          accessibleGroups = attendeesList[i]["accessible_groups"].map((id) => { return ObjectId(id); });
        }
        if (attendeesList[i]["purchased_plan"] && attendeesList[i]["purchased_plan"].length ) {
          purchasedPlan = attendeesList[i]["purchased_plan"].map((id) => { return ObjectId(id); });
        }
        let notificationForTemp = attendeesList[i] && attendeesList[i]["notificationFor"] ? attendeesList[i]["notificationFor"] : [] ;
        let pipeline = [
          {
            $match: {
              event: eventId,
              isDelete: false,
              status:"published"
            },
          },
          {
            $lookup: {
              from: "event_wise_participant_types",
              localField: "accessRoles",
              foreignField: "_id",
              as: "activity_event_wise_participant_types",
              pipeline: [
                {
                  $match: {
                    $expr: {
                      $in: ["$role", roleTemp],
                    },
                  },
                },
              ],
            }
          },
          {
            $match: {
              $or: [
                {
                  $expr: {
                    $gt: [
                      {
                        $size:
                          "$activity_event_wise_participant_types",
                      },
                      0,
                    ],
                  },
                },
                {
                  userId: {
                    $in: [userIdTemp]
                  }
                },
                {
                  membershipPlanId: {
                    $in: purchasedPlan
                  }
                },
                {
                  groupId: {
                    $in: accessibleGroups
                  }
                }
              ]
            }
          },
          {
            $lookup: {
              from: "sessions",
              let: { activity_session_id: "$session" },
              pipeline: [
                {
                  $match: {
                    $expr: {
                      $in: ["$_id", "$$activity_session_id"],
                    },
                  },
                },
                {
                  $lookup: {
                    from: "event_wise_participant_types",
                    localField: "accessRoles",
                    foreignField: "_id",
                    as: "event_wise_participant_types",
                    //this code is commented after disscuse with KD, suggested by client
                    // pipeline: [
                    //   {
                    //     $match: {
                    //       $expr: {
                    //         $in: ["$role", roleTemp],
                    //       },
                    //     },
                    //   },
                    // ],
                  },
                },
                {
                  $lookup: {
                    from: "rooms",
                    let: { activity_rooms_id: "$room" },
                    pipeline: [
                      {
                        $match: {
                          $expr: {
                            $eq: ["$_id", "$$activity_rooms_id"],
                          },
                        },
                      },
                      {
                        $lookup: {
                          from: "eventlocations",
                          let: { location_id: "$location" },
                          pipeline: [
                            {
                              $match: {
                                $expr: {
                                  $eq: ["$_id", "$$location_id"],
                                },
                              },
                            },
                            {
                              $project: {
                                name: 1,
                                address: 1,
                                country: 1,
                                city: 1,
                                latitude: 1,
                                longitude: 1,
                                locationVisible: 1,
                                locationImages: 1,
                              },
                            },
                          ],
                          as: "location",
                        },
                      },
                      {
                        $unwind: "$location",
                      },
                      { $project: { location: 1 } },
                    ],
                    as: "room",
                  },
                },
                {
                  $unwind: "$room",
                },
                { $project: { room: 1 } },
              ],
              as: "sessions",
            },
          },
          {
            $lookup: {
              from: "eventlocations",
              let: { activity_location_id: "$location" },
              pipeline: [
                {
                  $match: {
                    $expr: {
                      $eq: ["$_id", "$$activity_location_id"],
                    },
                  },
                },
                {
                  $project: {
                    name: 1,
                    address: 1,
                    country: 1,
                    city: 1,
                    latitude: 1,
                    longitude: 1,
                    locationVisible: 1,
                    locationImages: 1,
                  },
                },
              ],
              as: "location",
            },
          },
          {
            $addFields: {
              sessionCount: {
                $cond: {
                  if: { $isArray: "$sessions" },
                  then: { $size: "$sessions" },
                  else: 0,
                },
              },
            },
          },
          {
            $project: {
              _id: 1,
              name: 1,
              icon: 1,
              description: "$shortDescription",
              shortDescription: 1,
              longDescription: 1,
              date: 1,
              startTime: 1,
              endDate: 1,
              endTime: 1,
              reserved: 1,
              reserved_URL: 1,
              reservedLabelForDetail: 1,
              reservedLabelForListing: 1,
              location: 1,
              sessions: 1,
              sessionCount: 1,
              notifyChanges: 1,
              notifyChangeText: 1,
              notificationFlag: {
                $let: {
                  vars: {
                    test: {
                      $filter: {
                        input: notificationForTemp,
                        cond: {
                          $and: [
                            { $eq: ["$_id", "$$this.id"] },
                            { $eq: ["activity", "$$this.type"] },
                            { $eq: ["user", "$$this.setBy"] },
                          ],
                        },
                      },
                    },
                  },
                  in: {
                    $gt: [{ $size: "$$test" }, 0],
                  },
                },
              },
            },
          },
        ]
        let activityListTemp = await eventActivity.aggregate(pipeline);
        if (role === "member" && ( roleTemp.includes("Member") || roleTemp.includes("Staff") ) ) {
          activityList = activityListTemp;
        } else {
          if (activityListTemp && activityListTemp.length > 0) {
            let obj = activityList.find(o => o._id.toString() === activityListTemp[0]._id.toString());
            if (!obj) {
                activityList = [...activityList, ...activityListTemp];
            }
          } else {
            console.warn('ActivityList is empty or undefined');
          }
        }
      }
      let finalActivityList = [];
      for (let i = 0; i < activityList.length; i++) {
        const activity = activityList[i];
        let isExist = finalActivityList.find(o => o["_id"].toString() === activity["_id"].toString());
        if(!isExist){
          finalActivityList.push(activity);
        }
      }
      if (activityList.length > 0) {
        return res.status(200).json({
          status: true,
          message: "Event activity list retrive.",
          data: finalActivityList,
        });
      } else {
        return res.status(200).json({
          status: false,
          message: "There is no activity for this member in this event!",
          data: [],
        });
      }

    } else {
      return res.status(200).json({
        status: false,
        message: "User not found!",
        data: [],
      });
    }
  } catch (error) {
    console.log(error, "error");
    await debugErrorLogs.createErrorLogs(error, "getEventActivityByEventIdV2", debugObj);
    return res
      .status(500)
      .json({ status: false, message: "Internal server error!", error: error });
  }
};

// get attendees role details
exports.getAttendeesRolesDetail = async (req, res) => {
  try {
    const eventId = ObjectId(req.params.id);
    const authUser = ObjectId(req.authUserId);
    let pipeline = [
      {
        '$match': {
          'event': eventId,
          'user': authUser,
          'isDelete': false
        }
      }, {
        '$lookup': {
          'from': 'event_wise_participant_types',
          'localField': 'role',
          'foreignField': '_id',
          'as': 'event_wise_participant_types'
        }
      }, {
        '$unwind': {
          'path': '$event_wise_participant_types',
          'preserveNullAndEmptyArrays': false
        }
      }, {
        '$addFields': {
          'type': '$event_wise_participant_types.role'
        }
      }, {
        '$lookup': {
          'from': 'airtable-syncs',
          'localField': 'user',
          'foreignField': '_id',
          'as': 'airtable-syncs_result'
        }
      }, {
        '$unwind': {
          'path': '$airtable-syncs_result',
          'preserveNullAndEmptyArrays': false
        }
      }, {
        '$project': {
          '_id': {
            '$ifNull': [
              '$airtable-syncs_result._id', ''
            ]
          },
          'firebaseId': {
            '$ifNull': [
              '$airtable-syncs_result.firebaseId', ''
            ]
          },
          'email': {
            '$ifNull': [
              '$airtable-syncs_result.Preferred Email', ''
            ]
          },
          'title': {
            '$ifNull': [
              '$airtable-syncs_result.attendeeDetail.title', ''
            ]
          },
          'name': {
            '$ifNull': [
              '$airtable-syncs_result.attendeeDetail.name', ''
            ]
          },
          'firstName': {
            '$ifNull': [
              '$airtable-syncs_result.attendeeDetail.firstName', ''
            ]
          },
          'lastName': {
            '$ifNull': [
              '$airtable-syncs_result.attendeeDetail.lastName', ''
            ]
          },
          'company': {
            '$ifNull': [
              '$airtable-syncs_result.attendeeDetail.company', ''
            ]
          },
          'profession': {
            '$ifNull': [
              '$airtable-syncs_result.attendeeDetail.profession', ''
            ]
          },
          'phone': {
            '$ifNull': [
              '$airtable-syncs_result.attendeeDetail.phone', ''
            ]
          },
          'facebook': {
            '$ifNull': [
              '$airtable-syncs_result.attendeeDetail.facebook', ''
            ]
          },
          'linkedin': {
            '$ifNull': [
              '$airtable-syncs_result.attendeeDetail.linkedin', ''
            ]
          },
          'description': '$description',
          'offer': '$offer',
          'contactPartnerName': {
            '$ifNull': [
              '$airtable-syncs_result.attendeeDetail.contactPartnerName', ''
            ]
          },
          'profileImg': {
            '$ifNull': [
              '$type_icon', ''
            ]
          },
          'partnerIcon': {
            '$ifNull': [
              '$type_icon', ''
            ]
          },
          'type': {
            '$ifNull': [
              '$type', ''
            ]
          },
          'event': '$event',
          'type_icon': '$type_icon',
          'contact_name': '$contact_name',
          'partner_order': '$partner_order',
          'private_profile': '$private_profile'
        }
      }
    ];
    let list = await eventParticipantAttendees.aggregate(pipeline);
    if (list.length > 0) {
      return res.status(200).json({
        status: true,
        message: "Attendee roles details retrive.",
        data: list,
      });
    } else {
      return res.status(200).json({
        status: true,
        message: "Attendee roles details retrive.",
        data: [],
      });
    }
  } catch (error) {
    await debugErrorLogs.createErrorLogs(error, "getAttendeesRolesDetail", {});
    return res
      .status(500)
      .json({ status: false, message: "Internal server error!", error: error });
  }
};

// get listing of city for user
exports.getAllEventCityListForUser = async (req, res) => {
  try {
    const authUser = req.authUserId;
    let ruleCondition =  await userAccessRulesCommonCondition({userId:authUser,relation_id:req.relation_id});
    const allCitydata = await event.aggregate([
      {
        $match: {
          isDelete: false,
          ...ruleCondition,
          status:"published",
          relation_id: ObjectId(req.relation_id),
          $and: [
            { location: { $exists: true } },
            { location: { $ne: null } },
          ]
        }
      },
      {
        $group: {
          _id: null,
          uniqueValues: {
            $addToSet: "$location.city",
          },
        },
      },
      {
        $unwind: {
          path: "$uniqueValues",
          preserveNullAndEmptyArrays: false,
        },
      },
      {
        $match: {
          uniqueValues:{$ne:""}
        }
      },
      {
        $project: {
          _id:0,
          city: "$uniqueValues",
        },
      },
      {$sort:{city:1}}
    ]);
    
    if (allCitydata && allCitydata.length > 0){
      return res.status(200).json({
        status: true,
        message: "All event city retrieve!",
        data: allCitydata,
      });}
    else{
      return res.status(200).json({
        status: false,
        message: "Something went wrong while getting event!",
      });}
  } catch (e) {
    return res
      .status(500)
      .json({ status: false, message: "Internal server error!", error: e });
  }
};
exports.getEventActivityByIdV2 = async (req, res) => {
  let debugObj = {};
  try {
    const authUser = req.authUserId;
    const role = req.query.role;
    const activityId = new ObjectId(req.params.id);
    
    debugObj = {
      authUser : authUser ? authUser : "",
      role: role ? role : "",
      activityId: activityId ? activityId : "",
    }

    const activityData = await eventActivity.findOne(
      { _id: activityId, isDelete: false,status:"published" },
      { _id: 1, name: 1, event: 1 }
    );
    if (activityData !== null) {
      let eventId = activityData.event._id;
      let pipeline = [
        {
          '$match': {
            event: eventId,
            user: new ObjectId(authUser),
            isDelete: false,
          }
        }, {
          '$lookup': {
            'from': 'event_wise_participant_types',
            'localField': 'role',
            'foreignField': '_id',
            'as': 'event_wise_participant_types_result'
          }
        }, {
          '$unwind': {
            'path': '$event_wise_participant_types_result',
            'preserveNullAndEmptyArrays': false
          }
        }, {
          '$lookup': {
            'from': 'airtable-syncs',
            'localField': 'user',
            'foreignField': '_id',
            'as': 'airtable-syncs_result'
          }
        }, {
          '$unwind': {
            'path': '$airtable-syncs_result',
            'preserveNullAndEmptyArrays': false
          }
        }, {
          '$project': {
            '_id': {
              '$ifNull': [
                '$airtable-syncs_result._id', ''
              ]
            },
            'email': {
              '$ifNull': [
                '$airtable-syncs_result.Preferred Email', ''
              ]
            },
            'firebaseId': {
              '$ifNull': [
                '$airtable-syncs_result.firebaseId', ''
              ]
            },
            'accessible_groups': {
              '$ifNull': [
                '$airtable-syncs_result.accessible_groups', ''
              ]
            },
            'notificationFor': {
              '$ifNull': [
                '$airtable-syncs_result.notificationFor', []
              ]
            },
            'purchased_plan': {
              '$ifNull': [
                '$airtable-syncs_result.purchased_plan', ''
              ]
            },
            'attendeeDetail': {
              '$ifNull': [
                '$airtable-syncs_result.attendeeDetail', {}
              ]
            },
            'contact_name': 1,
            'partner_order': 1,
            'description': 1,
            'offer': 1,
            'type_icon': 1,
            'contactPartnerName': '$contact_name',
            'profileImg': '$type_icon',
            'partnerIcon': '$type_icon',
            'event': '$event',
            'event': '$event',
            'role': '$event_wise_participant_types_result.role',
            'role_type': '$event_wise_participant_types_result._id'
          }
        }
      ]
      let attendeesList = await eventParticipantAttendees.aggregate(pipeline);
      let userData = attendeesList[0];
      let accessibleGroups = [];
      let purchasedPlan = [];
      let userIdAsArray = [ ObjectId(userData._id) ];
      if (userData["accessible_groups"] && userData["accessible_groups"].length ) {
        accessibleGroups = userData["accessible_groups"].map((id) => { return ObjectId(id); });
      }
      if (userData["purchased_plan"] && userData["purchased_plan"].length ) {
        purchasedPlan = userData["purchased_plan"].map((id) => { return ObjectId(id); });
      }

      let activityList = [];
      var match = {
        _id: activityId,
        isDelete: false,
        status: "published"
      };
      if (userData !== null && userData !== undefined) {
        activityList = await eventActivity.aggregate([
          {
            $match: match,
          },
          {
            $lookup: {
              from: "event_wise_participant_types",
              localField: "accessRoles",
              foreignField: "_id",
              as: "activity_event_wise_participant_types",
            },
          },

          {
            $match: {
              $or: [
                {
                  $expr: {
                    $gt: [
                      {
                        $size: "$activity_event_wise_participant_types",
                      },
                      0,
                    ],
                  },
                },
                {
                  userId: {
                    $in: userIdAsArray,
                  },
                },
                {
                  membershipPlanId: {
                    $in: purchasedPlan,
                  },
                },
                {
                  groupId: {
                    $in: accessibleGroups,
                  },
                },
              ],
            },
          },
          {
            $lookup: {
              from: "sessions",
              let: { activity_session_id: "$session" },
              pipeline: [
                {
                  $match: {
                    $expr: {
                      $in: ["$_id", "$$activity_session_id"],
                    },
                  },
                },
                {
                  $lookup: {
                    from: "event_wise_participant_types",
                    localField: "accessRoles",
                    foreignField: "_id",
                    as: "event_wise_participant_types",
                  },
                },
                {
                  $lookup: {
                    from: "rooms",
                    let: { activity_rooms_id: "$room" },
                    pipeline: [
                      {
                        $match: {
                          $expr: {
                            $eq: ["$_id", "$$activity_rooms_id"],
                          },
                        },
                      },
                      {
                        $lookup: {
                          from: "eventlocations",
                          let: { location_id: "$location" },
                          pipeline: [
                            {
                              $match: {
                                $expr: {
                                  $eq: ["$_id", "$$location_id"],
                                },
                              },
                            },
                            {
                              $project: {
                                name: 1,
                                address: 1,
                                country: 1,
                                city: 1,
                                latitude: 1,
                                longitude: 1,
                                locationVisible: 1,
                                locationImages: 1,
                              },
                            },
                          ],
                          as: "location",
                        },
                      },
                      {
                        $unwind: "$location",
                      },
                      { $project: { name: 1, location: 1 } },
                    ],
                    as: "room",
                  },
                },
                {
                  $unwind: "$room",
                },
                {
                  $lookup: {
                    from: "airtable-syncs",
                    let: { speakerId: "$speakerId" },
                    pipeline: [
                      {
                        $match: {
                          $expr: {
                            $in: ["$_id", "$$speakerId"],
                          },
                        },
                      },
                      {
                        $project: {
                          _id: "$_id",
                          profileImg: "$profileImg",
                          attendeeDetail: {
                            _id: "$_id",
                            title: "$attendeeDetail.title",
                            name: "$attendeeDetail.name",
                            firstName: "$attendeeDetail.firstName"
                              ? "$attendeeDetail.firstName"
                              : "",
                            lastName: "$attendeeDetail.lastName"
                              ? "$attendeeDetail.lastName"
                              : "",
                            email: "$Preferred Email",
                            company: "$attendeeDetail.company",
                            phone: "$attendeeDetail.phone",
                            linkedin: "$attendeeDetail.linkedin",
                          },
                        },
                      },
                    ],
                    as: "speakerId",
                  },
                },
                {
                  $project: {
                    _id: 1,
                    title: 1,
                    shortDescription: 1,
                    longDescriptioniv: 1,
                    date: 1,
                    startTime: 1,
                    endTime: 1,
                    room: 1,
                    speakerId: 1,
                    event: 1,
                    reserved: 1,
                    reserved_URL: 1,
                    reservedLabelForListing: 1,
                    reservedLabelForDetail: 1,
                    member: 1,
                    speaker: 1,
                    partner: 1,
                    guest: 1,
                    notifyChanges: 1,
                    notifyChangeText: 1,
                    isEndOrNextDate: 1,
                    endDate: 1,
                    isDelete: 1,
                  },
                },
              ],
              as: "session",
            },
          },
          {
            $lookup: {
              from: "eventlocations",
              let: { activity_location_id: "$location" },
              pipeline: [
                {
                  $match: {
                    $expr: {
                      $eq: ["$_id", "$$activity_location_id"],
                    },
                  },
                },
                {
                  $project: {
                    name: 1,
                    address: 1,
                    country: 1,
                    city: 1,
                    latitude: 1,
                    longitude: 1,
                    locationVisible: 1,
                    locationImages: 1,
                  },
                },
              ],
              as: "location",
            },
          },
          {
            $unwind: {
              path: "$location",
              preserveNullAndEmptyArrays: true,
            },
          },
          {
            $addFields: {
              sessionCount: {
                $cond: {
                  if: { $isArray: "$session" },
                  then: { $size: "$session" },
                  else: 0,
                },
              },
            },
          },
          {
            $project: {
              _id: 1,
              name: 1,
              icon: 1,
              description: "$shortDescription",
              shortDescription: 1,
              longDescription: 1,
              date: 1,
              startTime: 1,
              endDate: 1,
              endTime: 1,
              reserved: 1,
              reserved_URL: 1,
              reservedLabelForDetail: 1,
              reservedLabelForListing: 1,
              location: {
                $cond: [
                  {
                    $ifNull: ["$location", false],
                  },
                  "$location",
                  null,
                ],
              },
              session: 1,
              sessionCount: 1,
              notifyChanges: 1,
              notifyChangeText: 1,
              notificationFlag: {
                $let: {
                  vars: {
                    test: {
                      $filter: {
                        input: userData.notificationFor,
                        cond: {
                          $and: [
                            { $eq: ["$_id", "$$this.id"] },
                            { $eq: ["activity", "$$this.type"] },
                            { $eq: ["user", "$$this.setBy"] },
                          ],
                        },
                      },
                    },
                  },
                  in: {
                    $gt: [{ $size: "$$test" }, 0],
                  },
                },
              },
            },
          },
        ]);

        if (activityList.length > 0) {
          return res.status(200).json({
            status: true,
            message: "Event activity details retrive.",
            data: activityList[0],
          });
        } else {
          return res.status(200).json({
            status: false,
            message: "There is no activity for this member in this event!",
            data: [],
          });
        }
      }
    } else {
      return res
        .status(404)
        .json({ status: false, message: "activity data not found!", data: {} });
    }
  } catch (error) {
    await debugErrorLogs.createErrorLogs(error, "getEventActivityById", debugObj);
    return res
      .status(500)
      .json({ status: false, message: "Internal server error!", error: error });
  }
};