/*
This file created by BPA.
Code is already developed by respective developers ( BPA only done function separation in separate files ).
*/

const { ObjectId } = require("mongodb");
const moment = require("moment");
const eventActivity = require("../../database/models/eventActivity");
const event = require("../../database/models/event");
const eventSession = require("../../database/models/eventSession");
const User = require("../../database/models/airTableSync");
const eventAttendees = require("../../database/models/eventAttendees");
const eventRoom = require("../../database/models/eventRoom");
const Notification = require("../../database/models/notification");
const EventParticipantTypes = require("../../database/models/eventParticipantTypes");
const EventParticipantAttendees = require("../../database/models/eventParticipantAttendees");
const eventWiseParticipantTypes = require("../../database/models/eventWiseParticipantTypes");
const chat = require("../../database/models/chat");
const chatChannel = require("../../database/models/chatChannel/chatChannel");
const chatChannelMembers = require("../../database/models/chatChannel/chatChannelMembers");
const group = require("../../database/models/group");
const membershipPlan = require("../../database/models/membershipPlanManagement/membership_plan");
// const { ObjectId } = require("mongodb");
const eventPackage = require("../../database/models/eventPackage");
const eventLocation = require("../../database/models/eventLocation");
const { v4: uuidv4 } = require("uuid");
const AWS = require("aws-sdk");
const { validationResult } = require('express-validator');
const { deleteImage } = require("../../utils/mediaUpload");
const {
  send_notification,
  notification_template,
  addTime,
  subtractTime,
} = require("../../utils/notification");
const ScheduledNotification = require("../../database/models/scheduledNotification");
const {
  schedule,
  reScheduleNotificationForActivitySession,
  rearrangeAttendee,
} = require("./eventAttendeeManageController");
const {
  user_edges,
} = require("../../microservices/user/components/user-edges/database/models");
const eventQuestion = require("../../database/models/event_question");
const scheduleNotification = require("../../database/models/notification/scheduleNotification");
const { createNotification } = require("../../microservices/user/utils/comman");

// get past event name list API code
exports.getPastEventNameList = async (req, res) => {
  try {
    const aggregatePipeline = [
      {
        $match: {
          isDelete: false,
          relation_id: ObjectId(req.relation_id),
          $or: [
            { restrictionAccess: "public" },
            { restrictionAccess: "admin/staff" },
            { restrictionAccess: "restricted" },
          ],
        },
      },
    ];

    const eventListData = await event.aggregate([
      ...aggregatePipeline,
      {
        $project: {
          _id: 1,
          title: 1,
        },
      },
    ]);

    if (eventListData.length > 0) {
      return res.status(200).json({
        status: true,
        message: "Event list retrive!",
        data: eventListData,
      });
    } else {
      return res.status(200).json({
        status: false,
        message: "There is no past events list!",
        data: [],
      });
    }
  } catch (error) {
    return res
      .status(500)
      .json({ status: false, message: "Internal server error!", error: error });
  }
};
/** Admin APIs ends **/

// setup and send notification in case of schedule event or session or activity notification
exports.scheduleNotificationForEventActivitySession = async (req, res) => {
  try {
    const authUserId = req.authUserId;
    const relationId = req.relation_id;
    const body = req.body;
    let chatData = {},
      data = {},
      notificationData = {},
      notification_for = {};
    const scheduleFor = body.scheduleFor;
    const userData = await User.findOne({ _id: ObjectId(authUserId) }).lean();
    let first_name = userData.first_name ? userData.first_name : "";
    let last_name = userData.last_name ? userData.last_name : "";
    const userName = first_name + " " + last_name;
    const eventID = body.eventId ? ObjectId(body.eventId) : null;
    const activityID = body.activityId ? ObjectId(body.activityId) : null;
    const sessionID = body.sessionId ? ObjectId(body.sessionId) : null;
    const eventData = await event.findOne({ _id: eventID });
    if (!eventData) {
      return res.status(200).json({
        status: false,
        message: "Event not found!",
      });
    }
    let activityDetail = await eventActivity.aggregate([
      {
        $match: { _id: activityID, isDelete: false, status: "published" },
      },
      {
        $lookup: {
          from: "sessions",
          let: { activity_session_id: "$session" },
          pipeline: [
            {
              $match: {
                $expr: {
                  $in: ["$_id", "$$activity_session_id"],
                },
                // member: true,
              },
            },
            {
              $lookup: {
                from: "event_wise_participant_types",
                localField: "accessRoles",
                foreignField: "_id",
                as: "event_wise_participant_types",
                pipeline: [
                  {
                    $match: {
                      $expr: {
                        $eq: ["$role", "Member"],
                      },
                    },
                  },
                ],
              },
            },
            {
              $unwind: {
                path: "$event_wise_participant_types",
                preserveNullAndEmptyArrays: false,
              },
            },
            {
              $lookup: {
                from: "rooms",
                let: { activity_rooms_id: "$room" },
                pipeline: [
                  {
                    $match: {
                      $expr: {
                        $eq: ["$_id", "$$activity_rooms_id"],
                      },
                    },
                  },
                  {
                    $lookup: {
                      from: "eventlocations",
                      let: { location_id: "$location" },
                      pipeline: [
                        {
                          $match: {
                            $expr: {
                              $eq: ["$_id", "$$location_id"],
                            },
                          },
                        },
                        {
                          $project: {
                            name: 1,
                            address: 1,
                            country: 1,
                            city: 1,
                            latitude: 1,
                            longitude: 1,
                            locationVisible: 1,
                            locationImages: 1,
                          },
                        },
                      ],
                      as: "location",
                    },
                  },
                  {
                    $unwind: "$location",
                  },
                  { $project: { location: 1 } },
                ],
                as: "room",
              },
            },
            {
              $unwind: "$room",
            },
            { $project: { room: 1 } },
          ],
          as: "sessions",
        },
      },
      {
        $lookup: {
          from: "eventlocations",
          let: { activity_location_id: "$location" },
          pipeline: [
            {
              $match: {
                $expr: {
                  $eq: ["$_id", "$$activity_location_id"],
                },
              },
            },
            {
              $project: {
                name: 1,
                address: 1,
                country: 1,
                city: 1,
                latitude: 1,
                longitude: 1,
                locationVisible: 1,
                locationImages: 1,
              },
            },
          ],
          as: "location",
        },
      },
      {
        $addFields: {
          sessionCount: {
            $cond: {
              if: { $isArray: "$sessions" },
              then: { $size: "$sessions" },
              else: 0,
            },
          },
        },
      },
      {
        $project: {
          _id: 1,
          name: 1,
          icon: 1,
          description: "$shortDescription",
          shortDescription: 1,
          longDescription: 1,
          date: 1,
          startTime: 1,
          endDate: 1,
          endTime: 1,
          reserved: 1,
          reserved_URL: 1,
          reservedLabelForListing: 1,
          reservedLabelForDetail: 1,
          location: 1,
          sessions: 1,
          member: 1,
          speaker: 1,
          partner: 1,
          guest: 1,
          sessionCount: 1,
          notifyChanges: 1,
          notifyChangeText: 1,
        },
      },
    ]);
    const activityData = activityDetail[0];
    const sessionData = await eventSession.findOne({
      _id: sessionID,
      event: eventID,
    });

    if (eventID !== null && activityID === null && sessionID === null) {
      // condition that if notification is for event activity or session
      if (scheduleFor === "event") {
        // Check if user has added notification for event or not
        const NotificationFor = {
          id: eventID,
          type: scheduleFor,
          setBy: "user",
        };

        const alreadyAdded = await User.findOne(
          {
            _id: userData._id,
            notificationFor: { $elemMatch: { id: eventID, setBy: "user" } },
          },
          { "notificationFor.$": 1 }
        );

        if (eventData !== undefined) {
          if (alreadyAdded === null) {
            notification_for = await User.findOneAndUpdate(
              { _id: userData._id },
              { $push: { notificationFor: NotificationFor } },
              { new: true }
            );

            const date = eventData.startDate;
            const eventTime = eventData.startTime;
            const timeZone = eventData.timeZone;
            const sign = timeZone.substring(4, 5);
            const utcHour = timeZone.substring(5, 7);
            const utcMinute = timeZone.substring(8, 10);
            const before30MinTime = moment(eventTime, "h:mm a")
              .subtract(15, "minutes")
              .format("HH:mm");

            // saprate date and time in hours and mins
            const year = moment(date, "MM-DD-YYYY").year();
            const month = moment(date, "MM-DD-YYYY").month(); // Month is zero-indexed
            const day = moment(date, "MM-DD-YYYY").get("date");
            const hours = moment(before30MinTime, "h:mm a").hours();
            const minutes = moment(before30MinTime, "h:mm a").minutes();

            var scheduleTime = new Date(year, month, day, hours, minutes);
            if (sign === "+") {
              scheduleTime = await subtractTime(
                scheduleTime,
                parseInt(utcHour),
                parseInt(utcMinute)
              );
            } else if (sign === "-") {
              scheduleTime = await addTime(
                scheduleTime,
                parseInt(utcHour),
                parseInt(utcMinute)
              );
            }

            // Schedule Notification code
            chatData = {
              receiverId: userData._id,
              receiverName: userName,
              receiverImage: userData.profileImg,
              eventId: eventData._id,
              eventName: eventData.title ? eventData.title : "",
              eventImage: eventData.thumbnail,
              eventDate: eventData.startDate,
              eventTime: eventData.startTime,
              chatType: "eventReminder",
            };

            notificationData = {
              receiverName: userName,
              eventName: eventData.title ? eventData.title : "",
              chatType: "eventReminder",
            };

            let notificationTemplate =
              await notification_template.user_event_reminder(notificationData);
            // let userDeviceToken = await User.findOne(
            //   { _id: userData._id },
            //   { deviceToken: 1 }
            // );

            let userDeviceToken = await user_edges.findOne(
              { user_id: userData._id, relation_id: req.relation_id },
              { deviceToken: 1 }
            );

            data = {
              receiverName: userName,
              notificationName: eventData.title ? eventData.title : "",
              notificationIdFor: eventData._id,
              notificationDate: eventData.startDate,
              notificationTime: eventData.startTime,
              scheduleTime: scheduleTime,
              notificationTemplate: notificationTemplate,
              userDeviceToken: userDeviceToken,
              chatData: chatData,
              userData: userData,
              scheduleFor: scheduleFor,
              createdBy: "user",
              messageType: "user_event_reminder",
            };
            await new Notification({
              title: notificationTemplate?.template?.title,
              body: notificationTemplate?.template?.body,
              createdBy: process.env.ADMIN_ID,
              createdFor: userData._id,
              read: true,
              role: "eventReminder",
            }).save();

            // scheduleNotification main function
            const scheduleNotification = await schedule(data);

            if (scheduleNotification.status === true) {
              return res.status(200).json({
                status: true,
                message: "Event notification schedule successfully.",
                data: {
                  id: scheduleNotification.data,
                  notificationFlag: true,
                },
              });
            } else {
              return res.status(200).json({
                status: false,
                message: "There is something worng when schedule notification!",
              });
            }
          } else {
            if (alreadyAdded !== null) {
              const scheduleData = await ScheduledNotification.findOne({
                createdFor: authUserId,
                idsFor: eventData._id,
                createdBy: "user",
              });
              if (scheduleData !== null) {
                await User.findOneAndUpdate(
                  { _id: userData._id },
                  { $pull: { notificationFor: NotificationFor } },
                  { new: true }
                );
                if (scheduleData) {
                  const cancelData =
                    await ScheduledNotification.findByIdAndRemove(
                      scheduleData._id,
                      { new: true }
                    );
                  if (cancelData) {
                    return res.status(200).json({
                      status: true,
                      message: "Notification schedule cancel successfully.",
                      data: { id: scheduleData._id, notificationFlag: false },
                    });
                  } else {
                    return res.status(200).json({
                      status: false,
                      message:
                        "There is something worng when cancel schedule notification!",
                    });
                  }
                }
              }
            }
          }
        } else {
          return res
            .status(200)
            .json({ status: false, message: "Event details not found!" });
        }
      }
    } else if (eventID !== null && activityID !== null && sessionID === null) {
      // condition that if notification is for event activity or session
      if (scheduleFor === "event") {
        // Check if user has added notification for event or not
        const NotificationFor = {
          id: eventID,
          type: scheduleFor,
          setBy: "user",
        };

        const alreadyAdded = await User.findOne(
          {
            _id: userData._id,
            notificationFor: { $elemMatch: { id: eventID, setBy: "user" } },
          },
          { "notificationFor.$": 1 }
        );
      
        if (eventData !== undefined) {
          if (alreadyAdded === null) {
            notification_for = await User.findOneAndUpdate(
              { _id: userData._id },
              { $push: { notificationFor: NotificationFor } },
              { new: true }
            );

            const date = eventData.startDate;
            const eventTime = eventData.startTime;
            const timeZone = eventData.timeZone;
            const sign = timeZone.substring(4, 5);
            const utcHour = timeZone.substring(5, 7);
            const utcMinute = timeZone.substring(8, 10);
            const before30MinTime = moment(eventTime, "h:mm a")
              .subtract(15, "minutes")
              .format("HH:mm");

            // saprate date and time in hours and mins
            const year = moment(date, "MM-DD-YYYY").year();
            const month = moment(date, "MM-DD-YYYY").month(); // Month is zero-indexed
            const day = moment(date, "MM-DD-YYYY").get("date");
            const hours = moment(before30MinTime, "h:mm a").hours();
            const minutes = moment(before30MinTime, "h:mm a").minutes();

            var scheduleTime = new Date(year, month, day, hours, minutes);
            if (sign === "+") {
              scheduleTime = await subtractTime(
                scheduleTime,
                parseInt(utcHour),
                parseInt(utcMinute)
              );
            } else if (sign === "-") {
              scheduleTime = await addTime(
                scheduleTime,
                parseInt(utcHour),
                parseInt(utcMinute)
              );
            }

            // Schedule Notification code
            chatData = {
              receiverId: userData._id,
              receiverName: userName,
              receiverImage: userData.profileImg,
              eventId: eventData._id,
              eventName: eventData.title ? eventData.title : "",
              eventImage: eventData.thumbnail,
              eventDate: eventData.startDate,
              eventTime: eventData.startTime,
              chatType: "eventReminder",
            };

            notificationData = {
              receiverName: userName,
              eventName: eventData.title ? eventData.title : "",
              chatType: "eventReminder",
            };

            let notificationTemplate =
              await notification_template.user_event_reminder(notificationData);
            // let userDeviceToken = await User.findOne(
            //   { _id: userData._id },
            //   { deviceToken: 1 }
            // );

            let userDeviceToken = await user_edges.findOne(
              { user_id: userData._id, relation_id: req.relation_id },
              { deviceToken: 1 }
            );

            data = {
              receiverName: userName,
              notificationName: eventData.title ? eventData.title : "",
              notificationIdFor: eventData._id,
              notificationDate: eventData.startDate,
              notificationTime: eventData.startTime,
              scheduleTime: scheduleTime,
              notificationTemplate: notificationTemplate,
              userDeviceToken: userDeviceToken,
              chatData: chatData,
              userData: userData,
              scheduleFor: scheduleFor,
              createdBy: "user",
              messageType: "user_event_reminder",
            };
            await new Notification({
              title: notificationTemplate?.template?.title,
              body: notificationTemplate?.template?.body,
              createdBy: process.env.ADMIN_ID,
              createdFor: userData._id,
              read: true,
              role: "eventReminder",
            }).save();

            // scheduleNotification main function
            const scheduleNotification = await schedule(data);

            if (scheduleNotification.status === true) {
              return res.status(200).json({
                status: true,
                message: "Event notification schedule successfully.",
                data: {
                  id: scheduleNotification.data,
                  notificationFlag: true,
                },
              });
            } else {
              return res.status(200).json({
                status: false,
                message: "There is something worng when schedule notification!",
              });
            }
          } else {
            if (alreadyAdded !== null) {
              const scheduleData = await ScheduledNotification.findOne({
                createdFor: authUserId,
                idsFor: eventData._id,
                createdBy: "user",
              });
              if (scheduleData !== null) {
                notification_for = await User.findOneAndUpdate(
                  { _id: userData._id },
                  { $pull: { notificationFor: NotificationFor } },
                  { new: true }
                );
                if (scheduleData) {
                  const cancelData = await ScheduledNotification.deleteMany({ createdFor: ObjectId(authUserId), idsFor: ObjectId(eventData._id), createdBy: "user", });
                  if (cancelData) {
                    return res.status(200).json({
                      status: true,
                      message: "Notification schedule cancel successfully.",
                      data: { id: scheduleData._id, notificationFlag: false },
                    });
                  } else {
                    return res.status(200).json({
                      status: false,
                      message:
                        "There is something worng when cancel schedule notification!",
                    });
                  }
                }
              }
            }
          }
        } else {
          return res
            .status(200)
            .json({ status: false, message: "Event details not found!" });
        }
      } else if (scheduleFor === "activity") {
        //* Check if activity details not found
        if (!activityData) {
          return res
            .status(200)
            .json({ status: false, message: "Activity details not found!" });
        }

        //* Fetch the notification for the activity
        const fetchNotification = await scheduleNotification.findOne({
          userId: authUserId,
          type: "activity",
          relationId,
          eventId: eventID,
          activityId: activityID,
          createdBy: "user",
          isDelete: false,
        });

        //* If notification is not found then create a new notification
        if (!fetchNotification) {
          //* Create a new notification for the activity
          const activityDate = activityData.date;
          const activityTime = activityData.startTime;

          //* Create a new notification for the activity
          let templateData = {
            activityName: activityData.name ? activityData.name : "",
            scheduleNotifyTime: "15 minutes.",
          };

          //* Create a new notification for the activity
          const data = {
            templateData,
            type: "activity",
            date: activityDate,
            time: activityTime,
            eventTimeZone: eventData.timeZone,
            userId: authUserId,
            relationId,
            eventId: eventData?._id,
            activityId: activityData?._id,
            duration: 15,
            notificationType: "user_activity_reminder",
            messageType: "activityReminder",
            createdBy: "user",
          };

          //* Create a new notification for the activity
          const notification = await createNotification({ data });

          if (!notification) {
            return res.status(200).json({
              status: false,
              message: "There is something worng when schedule notification!",
            });
          }

          return res.status(200).json({
            status: true,
            message: "Activity notification schedule successfully.",
            data: {
              id: notification,
              notificationFlag: true,
            },
          });
        } else {
          //* If notification is found then delete the notification

          //* Delete the notification for the activity
          const deleteNotification =
            await scheduleNotification.findByIdAndDelete(fetchNotification._id);

          if (!deleteNotification) {
            return res.status(200).json({
              status: false,
              message:
                "There is something worng when cancel schedule notification!",
            });
          }

          return res.status(200).json({
            status: true,
            message: "Notification schedule cancel successfully.",
            data: { id: deleteNotification?._id, notificationFlag: false },
          });
        }

        // Check if user has added notification for activity or not
        // const NotificationFor = {
        //   id: activityID,
        //   type: scheduleFor,
        //   setBy: "user",
        // };

        // const alreadyAdded = await User.findOne(
        //   {
        //     _id: ObjectId(userData._id),
        //     notificationFor: {
        //       $elemMatch: { id: ObjectId(activityID), setBy: "user" },
        //     },
        //   },
        //   { "notificationFor.$": 1 }
        // );

        // if (activityData !== undefined) {
        //   if (alreadyAdded === null) {
        //     notification_for = await User.findOneAndUpdate(
        //       { _id: userData._id },
        //       { $push: { notificationFor: NotificationFor } },
        //       { new: true }
        //     );

        //     const activityDate = activityData.date;
        //     const activityTime = activityData.startTime;
        //     const timeZone = eventData.timeZone;
        //     const sign = timeZone.substring(4, 5);
        //     const utcHour = timeZone.substring(5, 7);
        //     const utcMinute = timeZone.substring(8, 10);
        //     const before30MinTime = moment(activityTime, "h:mm a")
        //       .subtract(15, "minutes")
        //       .format("HH:mm");

        //     // saprate date and time in hours and mins
        //     const year = moment(activityDate, "MM-DD-YYYY").year();
        //     const month = moment(activityDate, "MM-DD-YYYY").month(); // Month is zero-indexed
        //     const day = moment(activityDate, "MM-DD-YYYY").get("date");
        //     const hours = moment(before30MinTime, "h:mm a").hours();
        //     const minutes = moment(before30MinTime, "h:mm a").minutes();

        //     var scheduleTime = new Date(year, month, day, hours, minutes);
        //     if (sign === "+") {
        //       scheduleTime = await subtractTime(
        //         scheduleTime,
        //         parseInt(utcHour),
        //         parseInt(utcMinute)
        //       );
        //     } else if (sign === "-") {
        //       scheduleTime = await addTime(
        //         scheduleTime,
        //         parseInt(utcHour),
        //         parseInt(utcMinute)
        //       );
        //     }

        //     // Schedule Notification code
        //     chatData = {
        //       receiverId: userData._id,
        //       receiverName: userName,
        //       receiverImage: userData.profileImg,
        //       eventId: eventData._id,
        //       eventName: eventData.title ? eventData.title : "",
        //       activityId: activityData._id,
        //       activityName: activityData.name ? activityData.name : "",
        //       recipentImage: activityData.icon,
        //       sessionCount: activityData.sessionCount,
        //       chatType: "activityReminder",
        //     };

        //     notificationData = {
        //       receiverName: userName,
        //       activityName: activityData.name ? activityData.name : "",
        //       eventName: eventData.title ? eventData.title : "",
        //       chatType: "activityReminder",
        //     };

        //     let notificationTemplate =
        //       await notification_template.user_activity_reminder(
        //         notificationData
        //       );
        //     // let userDeviceToken = await User.findOne(
        //     //   { _id: userData._id },
        //     //   { deviceToken: 1 }
        //     // );

        //     let userDeviceToken = await user_edges.findOne(
        //       { user_id: userData._id, relation_id: req.relation_id },
        //       { deviceToken: 1 }
        //     );

        //     data = {
        //       receiverName: userName,
        //       notificationName: activityData.name ? activityData.name : "",
        //       notificationIdFor: activityData._id,
        //       notificationDate: activityData.date,
        //       notificationTime: activityData.startTime,
        //       scheduleTime: scheduleTime,
        //       notificationTemplate: notificationTemplate,
        //       userDeviceToken: userDeviceToken,
        //       chatData: chatData,
        //       userData: userData,
        //       scheduleFor: scheduleFor,
        //       createdBy: "user",
        //       messageType: "user_activity_reminder",
        //     };
        //     await new Notification({
        //       title: notificationTemplate?.template?.title,
        //       body: notificationTemplate?.template?.body,
        //       createdBy: process.env.ADMIN_ID,
        //       createdFor: userData._id,
        //       read: true,
        //       role: "activityReminder",
        //     }).save();

        //     // scheduleNotification main function
        //     const scheduleNotification = await schedule(data);
        //     if (scheduleNotification.status === true) {
        //       return res.status(200).json({
        //         status: true,
        //         message: "Activity notification schedule successfully.",
        //         data: {
        //           id: scheduleNotification.data,
        //           notificationFlag: true,
        //         },
        //       });
        //     } else {
        //       return res.status(200).json({
        //         status: false,
        //         message: "There is something worng when schedule notification!",
        //       });
        //     }
        //   } else {
        //     if (alreadyAdded !== null) {
        //       const scheduleData = await ScheduledNotification.findOne({
        //         createdFor: ObjectId(authUserId),
        //         idsFor: ObjectId(activityID),
        //         createdBy: "user",
        //       });

        //       if (scheduleData !== null) {
        //         notification_for = await User.findOneAndUpdate(
        //           { _id: userData._id },
        //           { $pull: { notificationFor: NotificationFor } },
        //           { new: true }
        //         );
        //         const cancelData = await ScheduledNotification.deleteMany({
        //           createdFor: ObjectId(authUserId),
        //           idsFor: ObjectId(activityID),
        //           createdBy: "user",
        //         });
        //         if (cancelData) {
        //           return res.status(200).json({
        //             status: true,
        //             message: "Notification schedule cancel successfully.",
        //             data: { id: scheduleData._id, notificationFlag: false },
        //           });
        //         } else {
        //           return res.status(200).json({
        //             status: false,
        //             message:
        //               "There is something worng when cancel schedule notification!",
        //           });
        //         }
        //       }
        //     }
        //   }
        // } else {
        //   return res
        //     .status(200)
        //     .json({ status: false, message: "Activity details not found!" });
        // }
      }
    } else if (eventID !== null && activityID === null && sessionID !== null) {
      // condition that if notification is for event activity or session
      if (scheduleFor === "event") {
        // Check if user has added notification for event or not
        const NotificationFor = {
          id: eventID,
          type: scheduleFor,
          setBy: "user",
        };

        const alreadyAdded = await User.findOne(
          {
            _id: userData._id,
            notificationFor: { $elemMatch: { id: eventID, setBy: "user" } },
          },
          { "notificationFor.$": 1 }
        );

        if (eventData !== undefined) {
          if (alreadyAdded === null) {
            notification_for = await User.findOneAndUpdate(
              { _id: userData._id },
              { $push: { notificationFor: NotificationFor } },
              { new: true }
            );

            const date = eventData.startDate;
            const eventTime = eventData.startTime;
            const timeZone = eventData.timeZone;
            const sign = timeZone.substring(4, 5);
            const utcHour = timeZone.substring(5, 7);
            const utcMinute = timeZone.substring(8, 10);
            const before30MinTime = moment(eventTime, "h:mm a")
              .subtract(15, "minutes")
              .format("HH:mm");

            // saprate date and time in hours and mins
            const year = moment(date, "MM-DD-YYYY").year();
            const month = moment(date, "MM-DD-YYYY").month(); // Month is zero-indexed
            const day = moment(date, "MM-DD-YYYY").get("date");
            const hours = moment(before30MinTime, "h:mm a").hours();
            const minutes = moment(before30MinTime, "h:mm a").minutes();

            var scheduleTime = new Date(year, month, day, hours, minutes);
            if (sign === "+") {
              scheduleTime = await subtractTime(
                scheduleTime,
                parseInt(utcHour),
                parseInt(utcMinute)
              );
            } else if (sign === "-") {
              scheduleTime = await addTime(
                scheduleTime,
                parseInt(utcHour),
                parseInt(utcMinute)
              );
            }

            // Schedule Notification code
            chatData = {
              receiverId: userData._id,
              receiverName: userName,
              receiverImage: userData.profileImg,
              eventId: eventData._id,
              eventName: eventData.title ? eventData.title : "",
              eventImage: eventData.thumbnail,
              eventDate: eventData.startDate,
              eventTime: eventData.startTime,
              chatType: "eventReminder",
            };

            notificationData = {
              receiverName: userName,
              eventName: eventData.title ? eventData.title : "",
              chatType: "eventReminder",
            };

            let notificationTemplate =
              await notification_template.user_event_reminder(notificationData);
            // let userDeviceToken = await User.findOne(
            //   { _id: userData._id },
            //   { deviceToken: 1 }
            // );

            let userDeviceToken = await user_edges.findOne(
              { user_id: userData._id, relation_id: req.relation_id },
              { deviceToken: 1 }
            );

            data = {
              receiverName: userName,
              notificationName: eventData.title ? eventData.title : "",
              notificationIdFor: eventData._id,
              notificationDate: eventData.startDate,
              notificationTime: eventData.startTime,
              scheduleTime: scheduleTime,
              notificationTemplate: notificationTemplate,
              userDeviceToken: userDeviceToken,
              chatData: chatData,
              userData: userData,
              scheduleFor: scheduleFor,
              createdBy: "user",
              messageType: "user_event_reminder",
            };
            await new Notification({
              title: notificationTemplate?.template?.title,
              body: notificationTemplate?.template?.body,
              createdBy: process.env.ADMIN_ID,
              createdFor: userData._id,
              read: true,
              role: "eventReminder",
            }).save();

            // scheduleNotification main function
            const scheduleNotification = await schedule(data);

            if (scheduleNotification.status === true) {
              return res.status(200).json({
                status: true,
                message: "Event notification schedule successfully.",
                data: {
                  id: scheduleNotification.data,
                  notificationFlag: true,
                },
              });
            } else {
              return res.status(200).json({
                status: false,
                message: "There is something worng when schedule notification!",
              });
            }
          } else {
            if (alreadyAdded !== null) {
              const scheduleData = await ScheduledNotification.findOne({
                createdFor: authUserId,
                idsFor: eventData._id,
                createdBy: "user",
              });
              if (scheduleData !== null) {
                notification_for = await User.findOneAndUpdate(
                  { _id: userData._id },
                  { $pull: { notificationFor: NotificationFor } },
                  { new: true }
                );
                if (scheduleData) {
                  const cancelData = await ScheduledNotification.deleteMany({ createdFor: ObjectId(authUserId), idsFor: ObjectId(eventData._id), createdBy: "user", });
                  if (cancelData) {
                    return res.status(200).json({
                      status: true,
                      message: "Notification schedule cancel successfully.",
                      data: { id: scheduleData._id, notificationFlag: false },
                    });
                  } else {
                    return res.status(200).json({
                      status: false,
                      message:
                        "There is something worng when cancel schedule notification!",
                    });
                  }
                }
              }
            }
          }
        } else {
          return res
            .status(200)
            .json({ status: false, message: "Event details not found!" });
        }
      } else if (scheduleFor === "session") {
        //* Check if session details not found
        if (!sessionData) {
          return res
            .status(200)
            .json({ status: false, message: "Session details not found!" });
        }

         //* Fetch the notification for the activity
         const fetchNotification = await scheduleNotification.findOne({
          userId: authUserId,
          type: "session",
          relationId,
          eventId: eventID,
          sessionId: sessionID,
          createdBy: "user",
          isDelete: false,
        });

        //* If notification is not found then create a new notification
        if (!fetchNotification) {
          //* Create a new notification for the session
          const sessionDate = sessionData.date;
          const sessionTime = sessionData.startTime;

          //* Create a new notification for the session
          let templateData = {
            sessionName: sessionData.title ? sessionData.title : "",
            scheduleNotifyTime: "15 minutes.",
          };

          //* Create a new notification for the session
          const data = {
            templateData,
            type: "session",
            date: sessionDate,
            time: sessionTime,
            eventTimeZone: eventData.timeZone,
            userId: authUserId,
            relationId,
            eventId: eventData?._id,
            sessionId: sessionData?._id,
            duration: 15,
            notificationType: "user_session_reminder",
            messageType: "sessionReminder",
            createdBy: "user",
          };

          //* Create a new notification for the session
          const notification = await createNotification({ data });

          if (!notification) {
            return res.status(200).json({
              status: false,
              message: "There is something worng when schedule notification!",
            });
          }

          return res.status(200).json({
            status: true,
            message: "Session notification schedule successfully.",
            data: {
              id: notification,
              notificationFlag: true,
            },
          });
        } else {
          //* If notification is found then delete the notification

          //* Delete the notification for the session
          const deleteNotification =
            await scheduleNotification.findByIdAndDelete(fetchNotification._id);

          if (!deleteNotification) {
            return res.status(200).json({
              status: false,
              message:
                "There is something worng when cancel schedule notification!",
            });
          }

          return res.status(200).json({
            status: true,
            message: "Notification schedule cancel successfully.",
            data: { id: deleteNotification?._id, notificationFlag: false },
          });
        }

        // // Check if user has added notification for session or not
        // const NotificationFor = {
        //   id: sessionID,
        //   type: scheduleFor,
        //   setBy: "user",
        // };

        // const alreadyAdded = await User.findOne(
        //   {
        //     _id: userData._id,
        //     notificationFor: { $elemMatch: { id: sessionID, setBy: "user" } },
        //   },
        //   { "notificationFor.$": 1 }
        // );

        // if (sessionData !== undefined) {
        //   if (alreadyAdded === null) {
        //     notification_for = await User.findOneAndUpdate(
        //       { _id: userData._id },
        //       { $push: { notificationFor: NotificationFor } },
        //       { new: true }
        //     );

        //     const sessionDate = sessionData.date;
        //     const sessionTime = sessionData.startTime;
        //     const timeZone = eventData.timeZone;
        //     const sign = timeZone.substring(4, 5);
        //     const utcHour = timeZone.substring(5, 7);
        //     const utcMinute = timeZone.substring(8, 10);
        //     const before30MinTime = moment(sessionTime, "h:mm a")
        //       .subtract(15, "minutes")
        //       .format("HH:mm");

        //     // saprate date and time in hours and mins
        //     const year = moment(sessionDate, "MM-DD-YYYY").year();
        //     const month = moment(sessionDate, "MM-DD-YYYY").month(); // Month is zero-indexed
        //     const day = moment(sessionDate, "MM-DD-YYYY").get("date");
        //     const hours = moment(before30MinTime, "h:mm a").hours();
        //     const minutes = moment(before30MinTime, "h:mm a").minutes();

        //     var scheduleTime = new Date(year, month, day, hours, minutes);
        //     if (sign === "+") {
        //       scheduleTime = await subtractTime(
        //         scheduleTime,
        //         parseInt(utcHour),
        //         parseInt(utcMinute)
        //       );
        //     } else if (sign === "-") {
        //       scheduleTime = await addTime(
        //         scheduleTime,
        //         parseInt(utcHour),
        //         parseInt(utcMinute)
        //       );
        //     }

        //     // Schedule Notification code
        //     chatData = {
        //       receiverId: userData._id,
        //       receiverName: userName,
        //       receiverImage: userData.profileImg,
        //       eventId: eventData._id,
        //       eventName: eventData.title ? eventData.title : "",
        //       sessionId: sessionData._id,
        //       sessionName: sessionData.title ? sessionData.title : "",
        //       chatType: "sessionReminder",
        //     };

        //     notificationData = {
        //       receiverName: userName,
        //       sessionName: sessionData.title ? sessionData.title : "",
        //       eventName: eventData.title ? eventData.title : "",
        //       chatType: "sessionReminder",
        //     };

        //     let notificationTemplate =
        //       await notification_template.user_session_reminder(
        //         notificationData
        //       );
        //     // let userDeviceToken = await User.findOne(
        //     //   { _id: userData._id },
        //     //   { deviceToken: 1 }
        //     // );

        //     let userDeviceToken = await user_edges.findOne(
        //       { user_id: userData._id, relation_id: req.relation_id },
        //       { deviceToken: 1 }
        //     );

        //     data = {
        //       receiverName: userName,
        //       notificationName: sessionData.title ? sessionData.title : "",
        //       notificationIdFor: sessionData._id,
        //       notificationDate: sessionData.date,
        //       notificationTime: sessionData.startTime,
        //       scheduleTime: scheduleTime,
        //       notificationTemplate: notificationTemplate,
        //       userDeviceToken: userDeviceToken,
        //       chatData: chatData,
        //       userData: userData,
        //       scheduleFor: scheduleFor,
        //       createdBy: "user",
        //       messageType: "user_session_reminder",
        //     };
        //     await new Notification({
        //       title: notificationTemplate?.template?.title,
        //       body: notificationTemplate?.template?.body,
        //       createdBy: process.env.ADMIN_ID,
        //       createdFor: userData._id,
        //       read: true,
        //       role: "sessionReminder",
        //     }).save();

        //     // scheduleNotification main function
        //     const scheduleNotification = await schedule(data);
        //     if (scheduleNotification.status === true) {
        //       return res.status(200).json({
        //         status: true,
        //         message: "Session notification schedule successfully.",
        //         data: {
        //           id: scheduleNotification.data,
        //           notificationFlag: true,
        //         },
        //       });
        //     } else {
        //       return res.status(200).json({
        //         status: false,
        //         message: "There is something worng when schedule notification!",
        //       });
        //     }
        //   } else {
        //     if (alreadyAdded !== null) {
        //       const scheduleData = await ScheduledNotification.findOne({
        //         createdFor: authUserId,
        //         idsFor: sessionData._id,
        //         createdBy: "user",
        //       });
        //       if (scheduleData !== null) {
        //         notification_for = await User.findOneAndUpdate(
        //           { _id: userData._id },
        //           { $pull: { notificationFor: NotificationFor } },
        //           { new: true }
        //         );
        //         if (scheduleData) {
        //           const cancelData = await ScheduledNotification.deleteMany({ createdFor: ObjectId(authUserId), idsFor: ObjectId(sessionData._id), createdBy: "user", });
        //           if (cancelData) {
        //             return res.status(200).json({
        //               status: true,
        //               message: "Notification schedule cancel successfully.",
        //               data: { id: scheduleData._id, notificationFlag: false },
        //             });
        //           } else {
        //             return res.status(200).json({
        //               status: false,
        //               message:
        //                 "There is something worng when cancel schedule notification!",
        //             });
        //           }
        //         }
        //       }
        //     }
        //   }
        // } else {
        //   return res
        //     .status(200)
        //     .json({ status: false, message: "Session details not found!" });
        // }
      }
    }
  } catch (error) {
    return {
      status: false,
      message: "Inernal server error!",
      error: `${error.message}`,
    };
  }
};

/** User APIs ends **/

exports.getAllEventSuggestionList = async (req, res) => {
  try {

    var match = {
      isDelete: false,
      relation_id: ObjectId(req.relation_id)
    };

    const allEventData = await event
      .find(match, {
        _id: 0,
        title: 1,
      }).sort({ title: 1 }).lean();

    if (allEventData)
      return res.status(200).json({
        status: true,
        message: "All event retrive!",
        data: allEventData,
      });
    else
      return res.status(200).json({
        status: false,
        message: "Something went wrong while getting event!",
      });
  } catch (e) {
    return res
      .status(500)
      .json({ status: false, message: "Internal server error!", error: e });
  }
};
// update status of event
exports.updateStatusEvent = async (req, res) => {
  try {
    if (
      req.params.id !== undefined &&
      req.params.id !== null &&
      req.params.id !== "" &&
      req.query.status &&
      req.query.status !== undefined
    ) {
      const eventExist = await event.findById(req.params.id);
      if (!eventExist)
        return res
          .status(200)
          .json({ status: false, message: "Event details not found!" });

      const updateEventStatus = await event
        .findByIdAndUpdate(
          req.params.id,
          { status: req.query.status },
          { new: true }
        )
        .select("_id title status");
      if (updateEventStatus)
        return res
          .status(200)
          .json({
            status: true,
            message: `Event status updated successfully.`,
            data: updateEventStatus,
          });
      else
        return res
          .status(200)
          .json({
            status: false,
            message: `Something went wrong while updating status of event!`,
          });
    }
  } catch (error) {
    return res.status(200).json({ status: false, message: `${error.message}` });
  }
};

// User attendee Detail evnt Data sync to the event attendee collection, Route use for only sync data [ Migration use only ]
exports.syncEventAttendeeParticipantTypes = async (req, res) => {
  try {
    let communityId= ObjectId(req.currentEdge.relation_id._id)
    let totalSync = 0;
    let allExistingUsers = await User.find({ 
      attendeeDetail: { $exists: true }, 
      "attendeeDetail.evntData": { $exists: true }, 
      $or: [{ isDelete: false }, { isDelete: { $exists: false } }] 
    }).select({ attendeeDetail: 1, profileImg:1, partnerIcon: 1 });
    let eventParticipantTypes = await eventWiseParticipantTypes.find({ isDelete: false });

    for (let i = 0; i < allExistingUsers.length; i++) {
      if (allExistingUsers[i] && allExistingUsers[i]["attendeeDetail"] && allExistingUsers[i]["attendeeDetail"]["evntData"] && allExistingUsers[i]["attendeeDetail"]["evntData"].length != 0) {
        for (let j = 0; j < allExistingUsers[i]["attendeeDetail"]["evntData"].length; j++) {
          let userId = allExistingUsers[i]["_id"];
          let eventData = allExistingUsers[i]["attendeeDetail"]["evntData"][j];

          if (eventData.member && eventData.member == true) {
            let tempEventParticipantType = eventParticipantTypes.find(x => x.role.toLowerCase() === "member" && eventData["event"].toString() === x.event.toString() );
            if (tempEventParticipantType && tempEventParticipantType["_id"]) {
              let eventParticipantTypeObj = { event: eventData["event"], role: tempEventParticipantType["_id"], user: userId, isDelete: false, relation_id: communityId };
              let isExist = await EventParticipantAttendees.findOne(eventParticipantTypeObj);
              if (!isExist) {
                if (allExistingUsers[i]["profileImg"] && allExistingUsers[i]["profileImg"] != "") {
                  eventParticipantTypeObj["type_icon"] = allExistingUsers[i]["profileImg"];
                }
                let addEventParticipantAttendees = await EventParticipantAttendees.create(eventParticipantTypeObj)
                totalSync = totalSync + 1;
              }
            }
          }
          if (eventData.guest && eventData.guest == true) {
            let tempEventParticipantType = eventParticipantTypes.find(x => x.role.toLowerCase() === "guest"  && eventData["event"].toString() === x.event.toString() );
            if (tempEventParticipantType && tempEventParticipantType["_id"]) {
              let eventParticipantTypeObj = { event: eventData["event"], role: tempEventParticipantType["_id"], user: userId, isDelete: false, relation_id: communityId };
              let isExist = await EventParticipantAttendees.findOne(eventParticipantTypeObj);
              if (!isExist) {
                let addEventParticipantAttendees = await EventParticipantAttendees.create(eventParticipantTypeObj)
                totalSync = totalSync + 1;
              }
            }
          } 
          if (eventData.speaker && eventData.speaker == true) {
            let tempEventParticipantType = eventParticipantTypes.find(x => x.role.toLowerCase() === "speaker"  && eventData["event"].toString() === x.event.toString() );
            if (tempEventParticipantType && tempEventParticipantType["_id"]) {
              let eventParticipantTypeObj = { event: eventData["event"], role: tempEventParticipantType["_id"], user: userId, isDelete: false, relation_id: communityId };
              let isExist = await EventParticipantAttendees.findOne(eventParticipantTypeObj);
              if (!isExist) {
                let addEventParticipantAttendees = await EventParticipantAttendees.create(eventParticipantTypeObj)
                totalSync = totalSync + 1;
              }
            }
          } 
          if (eventData.partner && eventData.partner == true) {
            let tempEventParticipantType = eventParticipantTypes.find(x => x.role.toLowerCase() === "partner"  && eventData["event"].toString() === x.event.toString()  );
            if (tempEventParticipantType && tempEventParticipantType["_id"]) {
              let eventParticipantTypeObj = { event: eventData["event"], role: tempEventParticipantType["_id"], user: userId, isDelete: false, partner_order: eventData["partnerOrder"], relation_id: communityId };
              let isExist = await EventParticipantAttendees.findOne(eventParticipantTypeObj);
              if (!isExist) {
                if (allExistingUsers[i]["attendeeDetail"]["contactPartnerName"] && allExistingUsers[i]["attendeeDetail"]["contactPartnerName"] != "") {
                  eventParticipantTypeObj["contact_name"] = allExistingUsers[i]["attendeeDetail"]["contactPartnerName"];
                }
                if (allExistingUsers[i]["partnerIcon"] && allExistingUsers[i]["partnerIcon"] != "") {
                  eventParticipantTypeObj["type_icon"] = allExistingUsers[i]["partnerIcon"];
                }
                if (allExistingUsers[i]["attendeeDetail"]["description"] && allExistingUsers[i]["attendeeDetail"]["description"] != "") {
                  eventParticipantTypeObj["description"] = allExistingUsers[i]["attendeeDetail"]["description"];
                }
                if (allExistingUsers[i]["attendeeDetail"]["offer"] && allExistingUsers[i]["attendeeDetail"]["offer"] != "") {
                  eventParticipantTypeObj["offer"] = allExistingUsers[i]["attendeeDetail"]["offer"];
                }
                let addEventParticipantAttendees = await EventParticipantAttendees.create(eventParticipantTypeObj)
                totalSync = totalSync + 1;
              }
            }
          }
        }
      }
    }

    return res.status(200).json({ status: true, message: `Sync to event attendee successfully.`, data: { totalSync } });
  } catch (error) {
    return res.status(400).json({ status: false, message: `${error.message}` });
  }
};

/**
 * Import the event question for the attendee
 */
exports.importEventQuestion = async (req, res) => {
  try {
    /**
     * Total Count of the imported data
     * Total success count of the imported data
     * Total trace data and reason
     */
    const totalRecords = req.body.questions.length;
    let successCount = 0;
    const traceData = new Set();
    let relationId= ObjectId(req.relation_id)

    /** Find and Validate the eventId */
    const eventData = await event.findById(req.params.eventId).lean();
    if (!eventData)
      return res
        .status(404)
        .json({ status: false, message: "Event not found!" });

    /**
     * Loop for the import the data
     */
    for (let data of req?.body?.questions) {
      //* Check if the email is exist or not
      if (!data.email) {
        traceData.add({ email: data?.email, message: "Email is empty!" });
        continue;
      }

      //* Check that the question is exists or not
      if (!data.questions.length) {
        traceData.add({ email: data?.email, message: "Question is empty" });
        continue;
      }

      //* Validate the user
      const user = await User.findOne({
        "Preferred Email": data?.email,
      }).lean();
      if (!user) {
        traceData.add({ email: data?.email, message: "User not exists!" });
        continue;
      }

      //* Check if the user have the duplicate value or not
      const checkUserQuestion = await eventQuestion
        .findOne({
          eventId: ObjectId(req.params.eventId),
          userId: user._id,
          isDelete: false,
          relationId,
        })
        .lean();

      if (checkUserQuestion) {
        await eventQuestion.findByIdAndUpdate(checkUserQuestion._id, {
          $set: {
            questions: data?.questions
          }
        });
        successCount++;
      } else {
        //* Create the event Question data with the error handling
        const eventQuestionData = await eventQuestion.create({
          email: data?.email,
          userId: user?._id,
          eventId: req.params?.eventId,
          questions: data?.questions,
          relationId,
        });
        if (!eventQuestionData) {
          traceData.add({
            email: data?.email,
            message: "Somethind went wrong while creating the data!",
          });
          continue;
        }
        successCount++;
      }
    }

    return res.status(200).json({
      status: true,
      message: `Question imported successfully.`,
      data: {
        totalRecords,
        successCount,
        failCount: traceData.size,
        traceData: Array.from(traceData) || [],
      },
    });
  } catch (error) {
    return res.status(400).json({ status: false, message: `${error.message}` });
  }
};