const EventTicket = require("../../database/models/eventTicketsManagement/eventTicket");
const debugErrorLogs = require("../../middleware/debugErrorLogs");
const EventWiseParticipantTypes = require("../../database/models/eventWiseParticipantTypes");
const TicketPaymentRefund = require("../../database/models/eventTicketsManagement/ticketPaymentRefund");
const EventParticipantAttendees = require("../../database/models/eventParticipantAttendees");
const Communities = require("../../microservices/user/components/communities/database/models/communities");
const UserEdges = require("../../microservices/user/components/user-edges/database/models/user-edges");
const event = require("../../database/models/event");
const airTableSync = require("../../database/models/airTableSync");
const TicketSubmission = require("../../database/models/eventTicketsManagement/ticketSubmission");
const TicketAddonPurchase = require("../../database/models/eventTicketsManagement/ticketAddonPurchase");
const AddonPurchaseRefund = require("../../database/models/eventTicketsManagement/addonPurchaseRefund");
const ObjectId = require("mongoose").Types.ObjectId;
const stripe = require("stripe")(process.env.STRIPE_SECRET_KEY);
const { sendEmail } = require("../../config/common");
const {
  getEventTicketRegistrationEmailTemplate,
} = require("../../controller/eventManagement/eventTickerEmailController");
const axios = require("axios");
const moment = require("moment");
const { encrypt } = require("../../utils/crpyto");
const ticketPurchaseV2 = require("../../database/models/eventTicketsManagement/ticketPurchaseV2");
const ticketPaymentIntent = require("../../database/models/eventTicketsManagement/ticketPaymentIntent");
const S3FileUpload = require("../../libraries/file-upload-service");
const { BASE_URL, AWS_BUCKET } = require("../../config/config");
const { default: mongoose } = require("mongoose");
const {
  AddorRemoveFromChannelOnPurchaseTicket,
  RemoveFromChannelOnPurchaseTicket,
} = require("../ChatChannelHelperFunction");
const { normalizeDomain } = require("../../microservices/user/utils/comman");

const s3fileUploadService = new S3FileUpload();

//* Count the total Amount of the purchase order
const countCustomersTotalChargeableAmount = ({ amount }) => {
  try {
    const amountToReceive = amount; //* Amount the merchant wants to receive
    const stripeFeePercentage = 0.029; //* 2.9%
    const stripeFeeFixed = 0.3; //* $0.30, in dollars

    //* Calculate the total amount the user needs to pay
    const totalAmount =
      (amountToReceive + stripeFeeFixed) / (1 - stripeFeePercentage);

    return parseFloat(totalAmount.toFixed(2));
  } catch (error) {
    console.log({ error });
  }
};

//* Create a Payment Intent of the stripe
const createPaymentIntent = async ({
  amount,
  currency,
  adminStripeAccountId,
}) => {
  try {
    //* Create the payment intent in the stripe connected Account
    const paymentIntent = await stripe.paymentIntents.create(
      {
        amount: Math.round(amount * 100),
        currency: currency,
        automatic_payment_methods: {
          enabled: true,
        },
        payment_method_options: {
          card: {
            request_three_d_secure: "challenge", // Force 3D Secure for every transaction
          },
        },
      },
      {
        stripeAccount: adminStripeAccountId,
      }
    );
    return paymentIntent;
  } catch (error) {
    console.error("Error creating Payment Intent:", error);
    throw error;
  }
};

//* Get admin Stripe Account Id
const getAdminStripeAccountId = async ({ relation_id, authorizationToken }) => {
  try {
    //* get admin stripe Account Id
    const getCommunitiesOwner = await Communities.findOne({
      _id: relation_id,
      isDelete: false,
    }).lean();
    if (!getCommunitiesOwner) {
      return { status: false, message: `communities is not found!` };
    }

    //* Get the owner stripe Account Id
    const getOwnerStripAccountDBDetail = await UserEdges.findOne({
      relation_id: relation_id,
      user_id: getCommunitiesOwner.owner_id,
      type: "CO",
      isDelete: false,
    }).lean();
    if (!getOwnerStripAccountDBDetail) {
      return {
        status: false,
        message: `communities owner stripe Account is not found!`,
      };
    }
    const stripeAccountDBId = getOwnerStripAccountDBDetail.stripe_account_db_id;

    //* Get the stripe Account Id from the stripe
    let getStripeAccountDetail = {
      method: "get",
      maxBodyLength: Infinity,
      url: `https://${getCommunitiesOwner.nickname}.${BASE_URL}/api/billings/api/v1/stripe/fetch-account?stripe_account_db_id=${stripeAccountDBId}`,
      headers: {
        authorization: authorizationToken,
      },
    };
    const data = await axios.request(getStripeAccountDetail);

    const adminStripeAccountId = data.data && data.data.id ? data.data.id : "";

    if (adminStripeAccountId == "") {
      return null;
    }

    return { stripeAccountDBId, adminStripeAccountId };
  } catch (error) {
    console.error("Error creating Payment Intent:", error);
    throw error;
  }
};

//* Update Payment Intent function of the stripe
const updatePaymentIntent = async ({
  amount,
  updatePaymentIntentId,
  adminStripeAccountId,
}) => {
  try {
    //* Update the payment intent in the stripe connected Account
    const paymentIntentUpdate = await stripe.paymentIntents.update(
      updatePaymentIntentId,
      {
        amount: Math.round(amount * 100),
        currency: "usd",
        payment_method_options: {
          card: {
            request_three_d_secure: "challenge", // Force 3D Secure for every transaction
          },
        },
      },
      {
        stripeAccount: adminStripeAccountId,
      }
    );
    return paymentIntentUpdate;
  } catch (error) {
    console.error(
      "Error updating Payment Intent:",
      error?.message ?? error?.code
    );
    throw error;
  }
};

//* Cancle Payment Intent function of the stripe
const canclePaymentIntent = async ({
  updatePaymentIntentId,
  adminStripeAccountId,
}) => {
  try {
    //* Cancle the payment intent in the stripe connected Account
    const paymentIntentUpdate = await stripe.paymentIntents.cancel(
      updatePaymentIntentId,
      {
        stripeAccount: adminStripeAccountId,
      }
    );

    return paymentIntentUpdate;
  } catch (error) {
    console.error(
      "Error updating Payment Intent:",
      error?.message ?? error?.code
    );
    throw error;
  }
};

//* Create the refund function on the stripe
const createRefundIntent = async ({
  paymentIntentId,
  amount,
  metadata,
  adminStripeAccountId,
}) => {
  try {
    //* Create the refund intent on the stripe using the payment  intent on the connected account
    const refund = await stripe.refunds.create(
      {
        payment_intent: paymentIntentId,
        amount: Number(amount.toFixed(2)) * 100,
        metadata,
      },
      {
        stripeAccount: adminStripeAccountId,
      }
    );

    return refund || null;
  } catch (error) {
    console.log(
      "🚀 ~ file: eventTicketPaymentControllerV2.js:197 ~ error:",
      error
    );
    throw error;
  }
};

//* Validate the addon data
const validateAddonData = async ({ ticketData, isTicketExist }) => {
  try {
    for (let data of ticketData) {
      if (data?.applicationFormData) {
        //* Fetch the ticket data from the ticket array
        const ticket = isTicketExist.find((t) => t._id.equals(data.ticketId));

        //* Validate the addon and addon data
        if (
          ticket?.addons?.length &&
          data?.applicationFormData.length === data?.quantity
        ) {
          //* Iterate over the addon data from the body
          for (let selectedAddonData of data?.applicationFormData) {
            //* Only check if the ticket has addons
            if (
              Array.isArray(selectedAddonData?.addons) &&
              selectedAddonData?.addons.length
            ) {
              //* Iterate over the selected addons
              for (let selected of selectedAddonData?.addons) {
                //* Find the corresponding addon in the main addon list
                const matchingAddon = ticket?.addons.find(
                  (addon) => addon?._id.toString() === selected?._id.toString()
                );
                if (!matchingAddon) {
                  return {
                    status: false,
                    message: `Invalid addon selected for ${ticket.name}!`,
                  };
                }

                if (matchingAddon?.isSubAddon && !selected?.subAddon?.length) {
                  return {
                    status: false,
                    message: `Addon with _id ${selected?._id} is missing subAddon data`,
                  };
                }

                //* If the addon has sub-addons
                if (matchingAddon?.isSubAddon && selected?.subAddon?.length) {
                  //* Validate main addon price is null for sub-addons
                  if (matchingAddon?.price !== null) {
                    return {
                      status: false,
                      message: `Addon with _id ${selected._id} should have a null price as it has subAddons`,
                    };
                  }

                  //* Check the subAddon has the array
                  if (!Array.isArray(selected?.subAddon)) {
                    return {
                      status: false,
                      message: `Addon with _id ${selected?._id} is missing subAddon data`,
                    };
                  }

                  //* Create the subAddon map
                  const subAddonMap = new Map(
                    matchingAddon?.subAddon.map((sub) => [
                      sub?._id.toString(),
                      sub?.price,
                    ])
                  );

                  //* Validate subAddon price and existence
                  for (let sub of selected.subAddon) {
                    if (!subAddonMap.has(sub._id)) {
                      return {
                        status: false,
                        message: `SubAddon with _id ${sub._id} is not valid for addon with _id ${selected._id}`,
                      };
                    }

                    if (subAddonMap.get(sub._id) !== sub.price) {
                      return {
                        status: false,
                        message: `Price mismatch for SubAddon with _id ${sub._id} under addon with _id ${selected._id}`,
                      };
                    }
                  }
                } else {
                  //* Validate price for non-sub-addon
                  if (
                    // matchingAddon.price === null ||
                    selected.price !== matchingAddon.price
                  ) {
                    return {
                      status: false,
                      message: `Addon with _id ${selected._id} has a price mismatch or missing price`,
                    };
                  }
                }
              }
            }
          }
        }
      }

      return {
        status: true,
        message: "success",
      };
    }
  } catch (error) {
    console.error("Error in validateAddonData:", error);
    return {
      status: false,
      message: "An error occurred during validation",
    };
  }
};

//* Validate the addon data
const validateTicketPurchaseAddonData = async ({
  addonData,
  ticketAddonData,
}) => {
  try {
    for await (let data of addonData) {
      const { _id: addonId, price: addonPrice, isSubAddon } = data;

      if (!addonId)
        return {
          status: false,
          message: "Addon ID is required!",
        };

      const checkAddon = ticketAddonData.find(
        (addon) => addon?._id.toString() === addonId.toString()
      );

      if (!checkAddon)
        return {
          status: false,
          message: `Addon with ID ${addonId} is not found!`,
        };

      if (checkAddon?.isSubAddon !== isSubAddon)
        return {
          status: false,
          message: `SubAddon flag mismatch for addon with ID ${addonId}!`,
        };

      if (checkAddon?.isSubAddon) {
        if (!data?.subAddon?.length)
          return {
            status: false,
            message: `SubAddon data is required for addon with ID ${addonId}!`,
          };

        if (addonPrice !== null)
          return {
            status: false,
            message: `Main addon price should be null for sub-addon with ID ${addonId}!`,
          };

        //* Create the subAddon map
        const subAddonMap = new Map(
          checkAddon?.subAddon.map((sub) => [sub?._id.toString(), sub?.price])
        );

        //* Validate subAddon price and existence
        for (let sub of data?.subAddon) {
          if (!subAddonMap.has(sub._id)) {
            return {
              status: false,
              message: `SubAddon with _id ${sub._id} is not valid for addon with _id ${addonId}`,
            };
          }

          if (subAddonMap.get(sub._id) !== sub.price) {
            return {
              status: false,
              message: `Price mismatch for SubAddon with _id ${sub._id} under addon with _id ${addonId}`,
            };
          }
        }
      } else {
        if (addonPrice !== checkAddon?.price)
          return {
            status: false,
            message: `Price mismatch for addon with ID ${addonId}!`,
          };
      }
    }

    return {
      status: true,
      message: "success",
    };
  } catch (error) {
    console.error("Error in validateAddonData:", error);
    return {
      status: false,
      message: "An error occurred during validation",
    };
  }
};

//* Fucntion to calculate the ticket addon price
const calculateTicketAddonPrice = async ({ ticketData }) => {
  try {
    let totalAddonPrice = 0;

    //* Loop through each ticket
    ticketData.forEach((ticket) => {
      const { applicationFormData } = ticket;

      if (applicationFormData) {
        //* Loop through each form in the ticket
        applicationFormData.forEach((form) => {
          const { addons } = form;

          //* Loop through each addon
          addons.forEach((addon) => {
            if (addon.isSubAddon) {
              //* If addon is a sub-addon, sum the prices of sub-addons
              addon.subAddon.forEach((subAddon) => {
                totalAddonPrice = parseFloat(
                  (totalAddonPrice + subAddon?.price).toFixed(2)
                );
              });
            } else {
              //* If not a sub-addon, sum the main addon price
              totalAddonPrice = parseFloat(
                (totalAddonPrice + addon?.price).toFixed(2)
              );
            }
          });
        });
      }
    });

    return parseFloat(totalAddonPrice.toFixed(2));
  } catch (error) {
    console.error("Error calculating ticket addon price:", error);
    return null;
  }
};

const calculateSingleAddonPrice = async ({ ticketData }) => {
  try {
    let totalAddonPrice = 0;

    //* Loop through each ticket
    ticketData.forEach((ticket) => {
      const { addons } = ticket;

      //* Loop through each addon
      addons.forEach((addon) => {
        if (addon.isSubAddon) {
          //* If addon is a sub-addon, sum the prices of sub-addons
          addon.subAddon.forEach((subAddon) => {
            totalAddonPrice = parseFloat(
              (totalAddonPrice + subAddon?.price).toFixed(2)
            );
          });
        } else {
          //* If not a sub-addon, sum the main addon price
          totalAddonPrice = parseFloat(
            (totalAddonPrice + addon?.price).toFixed(2)
          );
        }
      });
    });

    return parseFloat(totalAddonPrice.toFixed(2));
  } catch (error) {
    console.error("Error calculating ticket addon price:", error);
    return null;
  }
};

const refundOtherAddonByPurchaseId = async ({
  ticketPurchaseData,
  paymentIntentId,
  adminStripeAccountData,
  relation_id,
  refundedBy,
}) => {
  let pipeline = [
    {
      $match: {
        ticketPurchaseId: ObjectId(ticketPurchaseData?._id),
        paymentIntentId: {
          $ne: ObjectId(paymentIntentId),
        },
        addonOrderStatus: {
          $nin: ["partial_payment_refund_initiated", "refund", "cancelled"],
        },
        isDelete: false,
      },
    },
    {
      $group: {
        _id: "$paymentIntentId",
        data: {
          $push: "$$ROOT",
        },
      },
    },
    {
      $project: {
        paymentIntentId: "$_id",
        data: 1,
        _id: 0,
      },
    },
  ];
  const fetchOtherAddonPurchase = await TicketAddonPurchase.aggregate(pipeline);

  //* Array of the refund data of the addon purchase
  const refundAddonPurchase = [];
  for await (let addonData of fetchOtherAddonPurchase) {
    //* Refund date
    const refundReqDate = new Date();

    //* Fetch the payment intent for the addon refund
    const fetchPaymentIntent = await ticketPaymentIntent.findOne({
      _id: addonData?.paymentIntentId,
      status: {
        $nin: ["refund", "cancelled"],
      },
      isDelete: false,
    });

    if (fetchPaymentIntent) {
      //* calculate total addon amount
      const totalAddonAmount = addonData?.data.reduce((sum, selection) => {
        return sum + selection.price;
      }, 0);

      //* create the refund intent for the addon
      const refund = await createRefundIntent({
        paymentIntentId: fetchPaymentIntent?.paymentIntentId,
        amount: Number(totalAddonAmount.toFixed(2)),
        metadata: {
          refundedBy,
          isAllOrderRefund: false,
          isOnlyAddonPurchaseRefund: true,
          isAddonRefundByTicketPurchaseRefund: true,
          query: JSON.stringify({
            purchaseTicketId: ticketPurchaseData?._id,
            userId: ticketPurchaseData?.userId,
            relation_id,
            eventId: ticketPurchaseData?.eventId,
            paymentIntentId: fetchPaymentIntent?._id,
            connectedAccountId: adminStripeAccountData?.adminStripeAccountId,
          }),
        },
        adminStripeAccountId: adminStripeAccountData?.adminStripeAccountId,
      });

      if (refund) {
        for await (let purchaseAddonData of addonData.data) {
          const addonPurchaseRefund = await AddonPurchaseRefund.create({
            addonPurchaseId: purchaseAddonData?._id,
            userId: purchaseAddonData?.userId,
            eventId: purchaseAddonData?.eventId,
            relation_id,
            paymentIntentId: fetchPaymentIntent?._id,
            ticketPurchaseId: purchaseAddonData?.ticketPurchaseId,
            amount: purchaseAddonData?.price,
            refundReqDate,
            refundId: refund?.id,
            stripeAccountDBId: adminStripeAccountData?.stripeAccountDBId,
            refundStatus: "partial_payment_refund_initiated",
            refundObj: refund,
            refundedBy,
          });

          if (addonPurchaseRefund) {
            await TicketAddonPurchase.findByIdAndUpdate(
              purchaseAddonData?._id,
              {
                $set: {
                  paymentRefundId: addonPurchaseRefund?._id,
                },
              }
            );
          }

          refundAddonPurchase.push(addonPurchaseRefund);
        }

        //* Revise the ticket payment intent
        await ticketPaymentIntent.findOneAndUpdate(
          {
            _id: fetchPaymentIntent?._id,
          },
          [
            {
              $set: {
                //* Decreament the addOn amount
                addOnAmount: {
                  //* Round the value to 2 decimal places
                  $round: [
                    {
                      //* Preval the value to 0 if the value is less than 0
                      $max: [
                        {
                          //* Subtract the addOnAmount from the totalAddonAmount
                          $subtract: [
                            "$addOnAmount",
                            Number(totalAddonAmount.toFixed(2)),
                          ],
                        },
                        0,
                      ],
                    },
                    2,
                  ],
                },

                //* Update the payment intent status
                status: "partial_payment_refund_initiated",
              },
            },
          ],
          {
            new: true,
          }
        );

        //* Update the addon purchase status
        await TicketAddonPurchase.updateMany(
          {
            paymentIntentId: fetchPaymentIntent?._id,
            ticketPurchaseId: ticketPurchaseData?._id,
            isDelete: false,
          },
          {
            $set: {
              addonOrderStatus: "partial_payment_refund_initiated",
            },
          }
        );
      }
    }
  }

  return refundAddonPurchase;
};

//* Create Payment Intent
exports.createPayment = async (req, res) => {
  try {
    const { ticketData, isFreeTicket = false } = req.body;
    const eventId = new ObjectId(req.params.eventId);
    const userId = req.authUserId;
    const relation_id = new ObjectId(req.relation_id);
    const purchaseDate = new Date();
    let isGuest = req.currentEdge.type == "GU" ? true : false;

    //* Find the paymentIntent for this Event and User
    let userPaymentIntent = await ticketPaymentIntent.findOne({
      userId,
      eventId,
      relation_id,
      status: {
        $nin: [
          "succeeded",
          "partial_payment_refund_initiated",
          "refund",
          "cancelled",
        ],
      },
      isDelete: false,
    });

    //* Get the admin stripe account data
    const adminStripeAccountData = await getAdminStripeAccountId({
      relation_id,
      authorizationToken: req?.headers?.authorization,
    });
    if (!adminStripeAccountData && !isFreeTicket) {
      return res
        .status(404)
        .send({ status: false, message: "Admin Stripe Account not found!" });
    }

    //* Check if the user has a payment intent and no ticket data
    if (userPaymentIntent && !ticketData.length) {
      return res.status(200).json({
        status: true,
        message: `Payment intent updated successfuly!`,
        data: {
          client_secret: encrypt(userPaymentIntent.client_secret),
          paymentIntentId: encrypt(userPaymentIntent.paymentIntentId),
          purchaseQuantity: userPaymentIntent.purchaseQuantity,
          adminStripeAccountId: encrypt(
            adminStripeAccountData.adminStripeAccountId
          ),
        },
      });
    }
    if (!ticketData.length) {
      return res
        .status(400)
        .send({ status: false, message: `Ticket data is required!` });
    }

    //* Get the ticket data from the database
    const isTicketExist = await EventTicket.find({
      _id: { $in: ticketData.map((t) => t?.ticketId) },
      isDelete: false,
      event: new ObjectId(eventId),
      ...(isFreeTicket && { type: "FREE" }),
    })
      .populate("addons")
      .lean();

    if (!isTicketExist.length || isTicketExist.length !== ticketData.length) {
      return res
        .status(404)
        .send({ status: false, message: `Ticket is not found!` });
    }

    //* check role "Member" exist
    let roleDetail = await EventWiseParticipantTypes.findOne({
      event: new ObjectId(eventId),
      isDelete: false,
      role: "Member",
    });
    if (!roleDetail) {
      return res
        .status(404)
        .json({ status: false, message: "Role detail not found!" });
    }

    //* Check the ticket quantity and availability
    for await (let data of ticketData) {
      const ticket = isTicketExist.find((t) => t._id.equals(data.ticketId));
      let totalQty = data?.quantity || 0;

      if (!ticket) {
        return res.status(400).send({
          status: false,
          message: `Ticket is not found for ${data.ticketId}`,
        });
      }

      //* Check the application form data length
      if (
        ticket?.applicationForm &&
        data?.applicationFormData &&
        data?.applicationFormData.length !== data?.quantity
      ) {
        return res.status(400).json({
          status: false,
          message: `Invalid application form data for the ${ticket.name} ticket!`,
        });
      }

      //* Check the ticket quantity and availability
      if (!ticket || totalQty > ticket.availableQuantity) {
        return res.status(400).json({
          status: false,
          message: `Insufficient quantity for ticket ${ticket.name}`,
        });
      }

      //* Check the ticket quantity and availability
      const ticketQty = await ticketPurchaseV2.countDocuments({
        ticketId: data?.ticketId,
        paymentIntentId: userPaymentIntent?._id,
        status: {
          $nin: [
            "succeeded",
            "partial_payment_refund_initiated",
            "refund",
            "cancelled",
          ],
        },
        isDelete: false,
      });
      if (ticketQty) totalQty += ticketQty || 0;

      if (
        !(
          +totalQty >= +ticket?.minimumTicket &&
          +totalQty <= +ticket?.maximumTicket
        ) &&
        +ticket?.minimumTicket < +ticket.availableQuantity
      ) {
        if (+ticket?.minimumTicket < +ticket.availableQuantity) {
          return res.status(400).json({
            status: false,
            message: `${ticket.name} purchase quantity should be between ${ticket.minimumTicket} and ${ticket.maximumTicket}, or up to the available quantity if less than the minimum.`,
          });
        }
      }
    }

    //* Validate the addon data
    const validateAddon = await validateAddonData({
      ticketData,
      isTicketExist,
    });
    if (!validateAddon?.status) {
      return res.status(400).json({
        status: false,
        message: validateAddon?.message,
      });
    }

    //* Free ticket purchase process
    if (isFreeTicket) {
      const subdomain = req.subdomains[0] ? req.subdomains[0] : "";
      const updateUserData = await airTableSync.findById(
        { _id: userId },
        { first_name: 1, last_name: 1, "Preferred Email": 1, display_name: 1 }
      );
      const eventData = await event
        .findById({ _id: eventId }, { isDelete: false })
        .lean();

      const purchaseQuantity = ticketData.reduce(
        (total, selection) => total + selection.quantity,
        0
      );

      //* create attendee in the event
      let participantObj = {
        user: new ObjectId(userId),
        event: new ObjectId(eventId),
        role: roleDetail._id,
        isDelete: false,
        relation_id: new ObjectId(relation_id),
      };
      let participantData = await EventParticipantAttendees.findOne(
        participantObj
      ).lean();

      if (!participantData) {
        participantData = await EventParticipantAttendees.findOneAndUpdate(
          {
            ...participantObj,
          },
          {
            __v: 0,
          },
          {
            new: true,
            upsert: true,
          }
        ).lean();
      }

      if (!participantData) {
        return res
          .status(400)
          .send({ status: false, message: "participant data is not found!" });
      }

      const storePaymentIntent = await ticketPaymentIntent.create({
        userId,
        relation_id,
        eventId,
        userParticipantId: participantData?._id,
        purchaseQuantity,
        ticketValue: 0,
        purchaseDate,
        stripeAccountDBId: null,
        paymentIntentId: null,
        client_secret: null,
        paymentIntent: {},
        stripe_processing_fee: 0,
        addOnAmount: 0,
        status: "succeeded",
        isFreeTicket: true,
      });
      if (!storePaymentIntent) {
        return res
          .status(400)
          .send({ status: false, message: "Failed to store Payment Intent!" });
      }

      const ticketPurchases = [];
      const applicationForms = [];
      const addonData = [];
      ticketData.forEach(async (data) => {
        const ticket = isTicketExist.find((t) => t._id.equals(data.ticketId));
        // Create separate entries for each ticket based on the quantity
        for (let i = 0; i < data.quantity; i++) {
          const ticketPurchase = new ticketPurchaseV2({
            userId,
            eventId,
            relation_id,
            userParticipantId: participantData?._id,
            ticketId: ticket?._id,
            quantity: 1,
            price: 0,
            paymentIntentId: storePaymentIntent?._id,
            purchaseDate,
            ticketOrderStatus: storePaymentIntent?.status,
            isFreeTicket: true,
          });

          //* Store the application form data in the database
          if (
            data?.applicationFormData &&
            data?.applicationFormData[i]?.submision.length
          ) {
            applicationForms.push(
              new TicketSubmission({
                relation_id,
                eventId,
                applicationFormId: ticket.applicationForm,
                userId,
                ticketId: ticket?._id,
                ticketPurchaseId: ticketPurchase._id,
                fields: data?.applicationFormData[i]?.submision,
              })
            );
          }

          //* Store the addon data in the database
          if (
            data?.applicationFormData &&
            data?.applicationFormData[i]?.addons.length
          ) {
            data?.applicationFormData[i]?.addons.forEach((addon) => {
              addonData.push(
                new TicketAddonPurchase({
                  relation_id,
                  eventId,
                  userId,
                  paymentIntentId: storePaymentIntent?._id,
                  ticketId: ticket?._id,
                  ticketPurchaseId: ticketPurchase._id,
                  addonId: addon?._id,
                  purchaseDate,
                  price: 0,
                  addonOrderStatus: storePaymentIntent?.status,
                  addon,
                  isFreeAddon: true,
                })
              );
            });
          }
          ticketPurchases.push(ticketPurchase);
        }
      });

      if (!ticketPurchases.length) {
        return res
          .status(400)
          .send({ status: false, message: "Failed to save ticket purchases!" });
      }

      //* Insert the ticket purchase data in the database
      const storeTickets = await ticketPurchaseV2.insertMany(ticketPurchases);

      //* Insert the application form data in the database
      if (applicationForms.length) {
        await TicketSubmission.insertMany(applicationForms);
      }
      //* Insert the addon data in the database
      if (addonData.length) {
        await TicketAddonPurchase.insertMany(addonData);
      }

      if (storeTickets.length) {
        // Minus the quantity in the event ticket available quantity
        ticketData.forEach(async (data) => {
          const ticket = isTicketExist.find((t) => t._id.equals(data.ticketId));

          await EventTicket.findByIdAndUpdate(ticket._id, {
            $inc: { availableQuantity: -data.quantity },
          });
        });
      } else {
        return res
          .status(400)
          .send({ status: false, message: "Failed to save ticket purchases!" });
      }
   
      const EmailticketData = await ticketPurchaseV2.aggregate([
        {
          $match: {
            paymentIntentId: ObjectId(storePaymentIntent?._id),
            ticketOrderStatus: "succeeded",
            isFreeTicket: true,
            isDelete: false,
          },
        },
        {
          $group: {
            _id: "$ticketId",
            qty: {
              $sum: 1,
            },
            amount: {
              $sum: "$price",
            },
          },
        },
        {
          $lookup: {
            from: "event_tickets",
            localField: "_id",
            foreignField: "_id",
            as: "ticketData",
            pipeline: [
              {
                $project: {
                  name: 1,
                  type: 1,
                },
              },
            ],
          },
        },
        {
          $unwind: {
            path: "$ticketData",
            preserveNullAndEmptyArrays: false,
          },
        },
        {
          $project: {
            qty: 1,
            amount: 1,
            ticketName: "$ticketData.name",
            type: "$ticketData.type",
          },
        },
        {
          $sort: {
            ticketName: 1,
          },
        },
      ]);
  
      const regMailData = await getEventTicketRegistrationEmailTemplate(
        updateUserData,
        eventData,
        EmailticketData,
        subdomain,
        storePaymentIntent,
        relation_id,
        isGuest
      );
      sendEmail(regMailData);

      return res.status(200).json({
        status: true,
        message: `Payment intent created successfuly!`,
        data: {
          isFreeTicket: true,
        },
      });
    }

    if (!userPaymentIntent) {
      //* Fresh purchase
      const totalAmount = ticketData.reduce((sum, selection) => {
        const ticket = isTicketExist.find((t) =>
          t._id.equals(selection.ticketId)
        );
        return sum + selection.quantity * ticket.actualPrice;
      }, 0);

      //* calculate total addon amount
      const totalAddonAmount = await calculateTicketAddonPrice({ ticketData });

      const purchaseQuantity = ticketData.reduce(
        (total, selection) => total + selection.quantity,
        0
      );

      //* Total amount without charge
      const totalAmountWithoutCharge = totalAmount + totalAddonAmount;

      const totalAmountWithCharge = parseFloat(
        countCustomersTotalChargeableAmount({
          amount: totalAmountWithoutCharge,
        })
      );

      //* Create payment Intent for the fresh ticket
      const paymentIntent = await createPaymentIntent({
        amount: totalAmountWithCharge,
        currency: "usd",
        adminStripeAccountId: adminStripeAccountData.adminStripeAccountId,
      });
      if (!paymentIntent) {
        return res
          .status(200)
          .send({ status: false, message: "Failed to create Payment Intent!" });
      }

      //* Store the payment Intent in the database
      const storePaymentIntent = await ticketPaymentIntent.create({
        userId,
        relation_id,
        eventId,
        purchaseQuantity,
        ticketValue: totalAmount,
        purchaseDate,
        stripeAccountDBId: adminStripeAccountData.stripeAccountDBId,
        paymentIntentId: paymentIntent?.id,
        client_secret: paymentIntent?.client_secret,
        paymentIntent,
        stripe_processing_fee: (
          totalAmountWithCharge - totalAmountWithoutCharge
        ).toFixed(2),
        addOnAmount: totalAddonAmount || 0,
        status: paymentIntent?.status,
      });
      if (!storePaymentIntent) {
        return res
          .status(200)
          .send({ status: false, message: "Failed to store Payment Intent!" });
      }

      const ticketPurchases = [];
      const applicationForms = [];
      const addonData = [];
      //* Store the ticket purchase data in the database
      ticketData.forEach(async (data) => {
        const ticket = isTicketExist.find((t) => t._id.equals(data.ticketId));
        // Create separate entries for each ticket based on the quantity
        for (let i = 0; i < data.quantity; i++) {
          //* Store the ticket purchase data in the database
          const ticketPurchase = new ticketPurchaseV2({
            userId,
            eventId,
            relation_id,
            ticketId: ticket?._id,
            quantity: 1,
            price: (ticket?.actualPrice).toFixed(2),
            paymentIntentId: storePaymentIntent?._id,
            purchaseDate,
            ticketOrderStatus: storePaymentIntent?.status,
          });

          //* Store the application form data in the database
          if (
            data?.applicationFormData &&
            data?.applicationFormData[i]?.submision.length
          ) {
            applicationForms.push(
              new TicketSubmission({
                relation_id,
                eventId,
                applicationFormId: ticket.applicationForm,
                userId,
                ticketId: ticket?._id,
                ticketPurchaseId: ticketPurchase._id,
                fields: data?.applicationFormData[i]?.submision,
              })
            );
          }

          //* Store the addon data in the database
          if (
            data?.applicationFormData &&
            data?.applicationFormData[i]?.addons.length
          ) {
            data?.applicationFormData[i]?.addons.forEach((addon) => {
              addonData.push(
                new TicketAddonPurchase({
                  relation_id,
                  eventId,
                  userId,
                  paymentIntentId: storePaymentIntent?._id,
                  ticketId: ticket?._id,
                  ticketPurchaseId: ticketPurchase._id,
                  addonId: addon?._id,
                  purchaseDate,
                  price: addon.isSubAddon
                    ? addon.subAddon.reduce((sum, selection) => {
                        return sum + selection.price;
                      }, 0)
                    : addon.price,
                  addonOrderStatus: storePaymentIntent?.status,
                  addon,
                })
              );
            });
          }
          ticketPurchases.push(ticketPurchase);
        }
      });

      if (!ticketPurchases.length) {
        return res
          .status(200)
          .send({ status: false, message: "Failed to save ticket purchases!" });
      }

      //* Insert the ticket purchase data in the database
      const storeTickets = await ticketPurchaseV2.insertMany(ticketPurchases);

      //* Insert the application form data in the database
      if (applicationForms.length) {
        await TicketSubmission.insertMany(applicationForms);
      }

      //* Insert the addon data in the database
      if (addonData.length) {
        await TicketAddonPurchase.insertMany(addonData);
      }

      //* Minus the quantity in the event ticket available quantity
      if (storeTickets.length) {
        //* Minus the quantity in the event ticket available quantity
        ticketData.forEach(async (data) => {
          const ticket = isTicketExist.find((t) => t._id.equals(data.ticketId));

          await EventTicket.findByIdAndUpdate(ticket._id, {
            $inc: { availableQuantity: -data.quantity },
          });
        });
      } else {
        return res
          .status(200)
          .send({ status: false, message: "Failed to save ticket purchases!" });
      }

      return res.status(200).json({
        status: true,
        message: `Payment intent created successfuly!`,
        data: {
          client_secret: encrypt(paymentIntent.client_secret),
          paymentIntentId: encrypt(storePaymentIntent.paymentIntentId),
          purchaseQuantity: purchaseQuantity,
          adminStripeAccountId: encrypt(
            adminStripeAccountData.adminStripeAccountId
          ),
        },
      });
    } else if (userPaymentIntent.status === "requires_payment_method") {
      //* Add more tickets in the created payment Intent
      const totalAmount = ticketData.reduce((sum, selection) => {
        const ticket = isTicketExist.find((t) =>
          t._id.equals(selection.ticketId)
        );
        return sum + selection.quantity * ticket.actualPrice;
      }, userPaymentIntent.ticketValue);

      //* calculate total addon amount
      const totalAddonAmount = await calculateTicketAddonPrice({ ticketData });

      const purchaseQuantity = ticketData.reduce(
        (total, selection) => total + selection.quantity,
        0
      );

      //* Total amount without charge
      const totalAmountWithoutCharge = totalAmount + totalAddonAmount;

      //* Total amount with charge
      const totalAmountWithCharge = parseFloat(
        countCustomersTotalChargeableAmount({
          amount: totalAmountWithoutCharge,
        })
      );

      //* Update payment Intent for the new added ticket
      const paymentIntent = await updatePaymentIntent({
        amount: totalAmountWithCharge,
        updatePaymentIntentId: userPaymentIntent.paymentIntentId,
        adminStripeAccountId: adminStripeAccountData.adminStripeAccountId,
      });
      if (!paymentIntent) {
        return res
          .status(200)
          .send({ status: false, message: "Failed to update Payment Intent!" });
      }

      //* Update the payment Intent in the database
      const revisePaymentIntent = await ticketPaymentIntent.findByIdAndUpdate(
        userPaymentIntent._id,
        {
          $set: {
            ticketValue: totalAmount,
            purchaseDate,
            client_secret: paymentIntent.client_secret,
            paymentIntent,
            stripe_processing_fee: (
              totalAmountWithCharge - totalAmountWithoutCharge
            ).toFixed(2),
          },
          $inc: {
            purchaseQuantity: purchaseQuantity,
            addOnAmount: totalAddonAmount || 0,
          },
        },
        { new: true }
      );
      if (!revisePaymentIntent) {
        return res.status(200).send({
          status: false,
          message: "Failed to update Payment Intent in db!",
        });
      }

      const ticketPurchases = [];
      const applicationForms = [];
      const addonData = [];

      //* Store the ticket purchase data in the database
      ticketData.forEach(async (data) => {
        const ticket = isTicketExist.find((t) => t._id.equals(data.ticketId));

        //* Update separate entries for each ticket based on the quantity
        for (let i = 0; i < data.quantity; i++) {
          const ticketPurchase = new ticketPurchaseV2({
            userId,
            eventId,
            relation_id,
            ticketId: ticket?._id,
            quantity: 1,
            price: (ticket?.actualPrice).toFixed(2),
            paymentIntentId: revisePaymentIntent?._id,
            purchaseDate,
            ticketOrderStatus: revisePaymentIntent?.status,
          });

          //* Store the application form data in the database
          if (
            data?.applicationFormData &&
            data?.applicationFormData[i]?.submision.length
          ) {
            applicationForms.push(
              new TicketSubmission({
                relation_id,
                eventId,
                application_form_id: ticket.applicationForm,
                userId,
                ticketId: ticket?._id,
                ticket_purchase_id: ticketPurchase._id,
                fields: data?.applicationFormData[i]?.submision,
              })
            );
          }

          //* Store the addon data in the database
          if (
            data?.applicationFormData &&
            data?.applicationFormData[i]?.addons.length
          ) {
            data?.applicationFormData[i]?.addons.forEach((addon) => {
              addonData.push(
                new TicketAddonPurchase({
                  relation_id,
                  eventId,
                  userId,
                  paymentIntentId: revisePaymentIntent?._id,
                  ticketId: ticket?._id,
                  ticketPurchaseId: ticketPurchase._id,
                  addonId: addon?._id,
                  purchaseDate,
                  price: addon.isSubAddon
                    ? addon.subAddon.reduce((sum, selection) => {
                        return sum + selection.price;
                      }, 0)
                    : addon.price,
                  addonOrderStatus: revisePaymentIntent?.status,
                  addon,
                })
              );
            });
          }
          ticketPurchases.push(ticketPurchase);
        }
      });
      if (!ticketPurchases.length) {
        return res
          .status(200)
          .send({ status: false, message: "Failed to save ticket purchases!" });
      }

      //* Insert the ticket purchase data in the database
      const storeTickets = await ticketPurchaseV2.insertMany(ticketPurchases);

      //* Insert the application form data in the database
      if (applicationForms.length) {
        await TicketSubmission.insertMany(applicationForms);
      }

      //* Insert the addon data in the database
      if (addonData.length) {
        await TicketAddonPurchase.insertMany(addonData);
      }

      //* Minus the quantity in the event ticket available quantity
      if (storeTickets.length) {
        ticketData.forEach(async (data) => {
          const ticket = isTicketExist.find((t) => t._id.equals(data.ticketId));

          await EventTicket.findByIdAndUpdate(ticket._id, {
            $inc: { availableQuantity: -data.quantity },
          });
        });
      } else {
        return res
          .status(200)
          .send({ status: false, message: "Failed to save ticket purchases!" });
      }

      return res.status(200).json({
        status: true,
        message: `Payment intent updated successfuly!`,
        data: {
          client_secret: encrypt(paymentIntent.client_secret),
          paymentIntentId: encrypt(revisePaymentIntent.paymentIntentId),
          purchaseQuantity: purchaseQuantity,
          adminStripeAccountId: encrypt(
            adminStripeAccountData.adminStripeAccountId
          ),
        },
      });
    } else if (userPaymentIntent.status === "card_declined") {
      //* Add more tickets in the created payment Intent
      const totalAmount = ticketData.reduce((sum, selection) => {
        const ticket = isTicketExist.find((t) =>
          t._id.equals(selection.ticketId)
        );
        return sum + selection.quantity * ticket.actualPrice;
      }, userPaymentIntent.ticketValue);

      //* calculate total addon amount
      const totalAddonAmount = await calculateTicketAddonPrice({ ticketData });

      const purchaseQuantity = ticketData.reduce(
        (total, selection) => total + selection.quantity,
        0
      );

      //* Total amount without charge
      const totalAmountWithoutCharge = totalAmount + totalAddonAmount;

      //* Total amount with charge
      const totalAmountWithCharge = parseFloat(
        countCustomersTotalChargeableAmount({ amount: totalAmount })
      );

      //* Update payment Intent for the new added ticket
      const paymentIntent = await updatePaymentIntent({
        amount: totalAmountWithCharge,
        updatePaymentIntentId: userPaymentIntent.paymentIntentId,
        adminStripeAccountId: adminStripeAccountData.adminStripeAccountId,
      });
      if (!paymentIntent) {
        return res
          .status(200)
          .send({ status: false, message: "Failed to update Payment Intent!" });
      }

      //* Update the payment Intent in the database
      const revisePaymentIntent = await ticketPaymentIntent.findByIdAndUpdate(
        userPaymentIntent._id,
        {
          $set: {
            ticketValue: totalAmount,
            purchaseDate,
            client_secret: paymentIntent.client_secret,
            paymentIntent,
            stripe_processing_fee: (
              totalAmountWithCharge - totalAmountWithoutCharge
            ).toFixed(2),
            status: "requires_payment_method",
          },
          $inc: {
            purchaseQuantity: purchaseQuantity,
            addOnAmount: totalAddonAmount || 0,
          },
        },
        { new: true }
      );
      if (!revisePaymentIntent) {
        return res.status(200).send({
          status: false,
          message: "Failed to update Payment Intent in db!",
        });
      }

      const ticketPurchases = [];
      const applicationForms = [];
      const addonData = [];

      //* Store the ticket purchase data in the database
      ticketData.forEach(async (data) => {
        const ticket = isTicketExist.find((t) => t._id.equals(data.ticketId));

        //* Update separate entries for each ticket based on the quantity
        for (let i = 0; i < data.quantity; i++) {
          //* Store the ticket purchase data in the database
          const ticketPurchase = new ticketPurchaseV2({
            userId,
            eventId,
            relation_id,
            ticketId: ticket?._id,
            quantity: 1,
            price: (ticket?.actualPrice).toFixed(2),
            paymentIntentId: revisePaymentIntent?._id,
            purchaseDate,
            ticketOrderStatus: revisePaymentIntent?.status,
          });

          //* Store the application form data in the database
          if (
            data?.applicationFormData &&
            data?.applicationFormData[i]?.submision.length
          ) {
            applicationForms.push(
              new TicketSubmission({
                relation_id,
                eventId,
                application_form_id: ticket.applicationForm,
                userId,
                ticketId: ticket?._id,
                ticket_purchase_id: ticketPurchase._id,
                fields: data?.applicationFormData[i]?.submision,
              })
            );
          }

          //* Store the addon data in the database
          if (
            data?.applicationFormData &&
            data?.applicationFormData[i]?.addons.length
          ) {
            data?.applicationFormData[i]?.addons.forEach((addon) => {
              addonData.push(
                new TicketAddonPurchase({
                  relation_id,
                  eventId,
                  userId,
                  paymentIntentId: revisePaymentIntent?._id,
                  ticketId: ticket?._id,
                  ticketPurchaseId: ticketPurchase._id,
                  addonId: addon?._id,
                  purchaseDate,
                  price: addon.isSubAddon
                    ? addon.subAddon.reduce((sum, selection) => {
                        return sum + selection.price;
                      }, 0)
                    : addon.price,
                  addonOrderStatus: revisePaymentIntent?.status,
                  addon,
                })
              );
            });
          }
          ticketPurchases.push(ticketPurchase);
        }
      });
      if (!ticketPurchases.length) {
        return res
          .status(200)
          .send({ status: false, message: "Failed to save ticket purchases!" });
      }

      //* Insert the ticket purchase data in the database
      const storeTickets = await ticketPurchaseV2.insertMany(ticketPurchases);

      //* Insert the application form data in the database
      if (applicationForms.length) {
        await TicketSubmission.insertMany(applicationForms);
      }

      //* Insert the addon data in the database
      if (addonData.length) {
        await TicketAddonPurchase.insertMany(addonData);
      }

      //* Update the ticket status in the database
      if (storeTickets.length) {
        await ticketPurchaseV2.findOneAndUpdate(
          {
            paymentIntentId: revisePaymentIntent._id,
          },
          {
            $set: {
              status: "requires_payment_method",
            },
          }
        );

        //* Minus the quantity in the event ticket available quantity
        ticketData.forEach(async (data) => {
          const ticket = isTicketExist.find((t) => t._id.equals(data.ticketId));

          await EventTicket.findByIdAndUpdate(ticket._id, {
            $inc: { availableQuantity: -data.quantity },
          });
        });
      } else {
        return res
          .status(200)
          .send({ status: false, message: "Failed to save ticket purchases!" });
      }

      return res.status(200).json({
        status: true,
        message: `Payment intent updated successfuly!`,
        data: {
          client_secret: encrypt(paymentIntent.client_secret),
          paymentIntentId: encrypt(revisePaymentIntent.paymentIntentId),
          purchaseQuantity: purchaseQuantity,
          adminStripeAccountId: encrypt(
            adminStripeAccountData.adminStripeAccountId
          ),
        },
      });
    } else {
      return res.status(200).send({
        status: false,
        message: "Somethings went wrong!",
        data: {},
      });
    }
  } catch (error) {
    console.log(
      "🚀 ~ file: eventTicketPaymentControllerV2.js:755 ~ exports.createPayment= ~ error:",
      error
    );
    return res.status(200).json({ status: false, message: `${error.message}` });
  }
};

//* Confirm Payment Intent
exports.confirmPaymentIntent = async (req, res) => {
  try {
    const userId = req.authUserId;
    const relation_id = req.relation_id;
    const paymentIntentId = req.query.paymentIntentId;
    const purchaseQuantity = req.query.purchaseQuantity;
    const adminStripeAccountId = req.query.adminStripeAccountId;
    const purchaseDate = new Date();
    let isGuest = req.currentEdge.type == "GU" ? true : false;

    if (!paymentIntentId) {
      return res.status(200).json({
        status: false,
        message: `Enter paymentIntentId ,userParticipantId ,adminStripeAccountId and purchaseQuantity`,
      });
    }

    //* Verify the payment intent from the stripe
    const verifyPayment = await stripe.paymentIntents.retrieve(
      paymentIntentId,
      { stripeAccount: adminStripeAccountId }
    );

    //* Setup the payment status
    let paymentStatus = "";
    if (
      verifyPayment &&
      verifyPayment.last_payment_error &&
      verifyPayment.last_payment_error !== null
    ) {
      paymentStatus = "card_declined";
    } else if (verifyPayment.status == "succeeded") {
      paymentStatus = "succeeded";
    } else {
      paymentStatus = "requires_payment_method";
    }

    //* Verify Payment Intent and update the status
    if (verifyPayment) {
      const isPaymentIntentExist = await ticketPaymentIntent
        .findOne({
          paymentIntentId: paymentIntentId,
          relation_id,
          isDelete: false,
        })
        .lean();
      if (!isPaymentIntentExist) {
        return res.status(200).json({
          status: false,
          message: `Payment Intent not found!`,
        });
      }

      //* get the ticket addon data
      let ticketData = await ticketPurchaseV2.find({
        paymentIntentId: isPaymentIntentExist?._id,
        isDelete: false,
      });

      //* update TicketPayment
      if (isPaymentIntentExist) {
        let updatePaymentData = await ticketPaymentIntent
          .findOneAndUpdate(
            {
              _id: isPaymentIntentExist._id,
              isDelete: false,
            },
            {
              $set: {
                status: paymentStatus,
                paymentIntent: verifyPayment,
                latestChargeId: verifyPayment.latest_charge,
                purchaseDate,
              },
            },
            {
              new: true,
            }
          )
          .lean();

        if (isPaymentIntentExist) {
          //* find and update or insert new entry into ticket purchase history
          ticketData = await ticketPurchaseV2.updateMany(
            {
              userId,
              relation_id,
              eventId: isPaymentIntentExist.eventId,
              paymentIntentId: isPaymentIntentExist._id,
              isDelete: false,
            },
            {
              $set: {
                ticketOrderStatus: updatePaymentData.status,
                purchaseDate,
              },
            },
            {
              new: true,
            }
          );
        }

        //* Update the ticket addon purchase status
        await TicketAddonPurchase.updateMany(
          {
            paymentIntentId: isPaymentIntentExist._id,
            isDelete: false,
          },
          {
            addonOrderStatus: updatePaymentData?.status,
            purchaseDate,
          }
        );

        //* add Ticket Purchase
        let participantData;

        //* email if ticket Purchase succeeded
        if (verifyPayment.status == "succeeded") {
          const subdomain = req.subdomains[0] ? req.subdomains[0] : "";
          const updateUserData = await airTableSync.findById(
            { _id: userId },
            {
              first_name: 1,
              last_name: 1,
              "Preferred Email": 1,
              display_name: 1,
            }
          );
          const eventData = await event
            .findById(
              { _id: isPaymentIntentExist.eventId },
              { isDelete: false }
            )
            .lean();

          //* check role "Member" exist
          let roleDetail = await EventWiseParticipantTypes.findOne({
            event: new ObjectId(isPaymentIntentExist?.eventId),
            isDelete: false,
            role: "Member",
          });
          if (!roleDetail) {
            return res
              .status(200)
              .json({ status: false, message: "Role detail not found!" });
          }

          //* create EventParticipantAttendees if is not exist
          let participantObj = {
            user: new ObjectId(userId),
            event: new ObjectId(isPaymentIntentExist?.eventId),
            role: roleDetail._id,
            isDelete: false,
            relation_id: new ObjectId(isPaymentIntentExist?.relation_id),
          };
          participantData = await EventParticipantAttendees.findOne(
            participantObj
          ).lean();

          let newParticipant;
          //* create EventParticipantAttendees if is not exist
          if (!participantData) {
            participantData = await EventParticipantAttendees.findOneAndUpdate(
              {
                ...participantObj,
              },
              {
                __v: 0,
              },
              {
                new: true,
                upsert: true,
              }
            ).lean();
          }

          //* Update the userParticipantId in the ticket purchase
          const purchaseParticipantUpdate = await ticketPurchaseV2.updateMany(
            {
              userId,
              relation_id,
              eventId: isPaymentIntentExist.eventId,
              paymentIntentId: isPaymentIntentExist._id,
            },
            {
              $set: {
                userParticipantId:
                  participantData?._id ?? new ObjectId(participantData?._id),
              },
            },
            { new: true }
          );

          //* Update the userParticipantId in ticket payment intent
          let updatePaymentData = await ticketPaymentIntent
            .findOneAndUpdate(
              {
                _id: isPaymentIntentExist._id,
              },
              {
                $set: {
                  userParticipantId:
                    participantData?._id ?? new ObjectId(participantData?._id),
                },
              }
            )
            .lean();

          AddorRemoveFromChannelOnPurchaseTicket(
            isPaymentIntentExist?.eventId,
            userId,
            isPaymentIntentExist?.relation_id
          );

          //* Get the ticket purchase data
          const ticketData = await ticketPurchaseV2.aggregate([
            {
              $match: {
                paymentIntentId: ObjectId(updatePaymentData?._id),
                ticketOrderStatus: "succeeded",
                isDelete: false,
              },
            },
            {
              $group: {
                _id: "$ticketId",
                qty: {
                  $sum: 1,
                },
                amount: {
                  $sum: "$price",
                },
                paymentIntentId: {
                  $first: "$paymentIntentId"
                }
              },
            },
            {
              $lookup: {
                from: "event_tickets",
                localField: "_id",
                foreignField: "_id",
                as: "ticketData",
                pipeline: [
                  {
                    $project: {
                      name: 1,
                      type: 1,
                    },
                  },
                ],
              },
            },
            {
              $lookup: {
                from: "ticket_addon_purchases",
                localField: "paymentIntentId",
                foreignField: "paymentIntentId",
                as: "ticket_addon_purchases",
                pipeline: [
                  {
                    $match: {
                      isDelete: false,
                      addonOrderStatus: "succeeded"
                    }
                  }
                ]
              }
            },
            {
              $unwind: {
                path: "$ticketData",
                preserveNullAndEmptyArrays: false,
              },
            },
            {
              $project: {
                qty: 1,
                amount: 1,
                ticketName: "$ticketData.name",
                type: "$ticketData.type",
                ticket_addon_purchases:1,

              },
            },
            {
              $sort: {
                ticketName: 1,
              },
            },
          ]);
          let addonData = ticketData.flatMap(ticket => {
            return ticket.ticket_addon_purchases.map(item => item.addon);
          });
          
          const regMailData = await getEventTicketRegistrationEmailTemplate(
            updateUserData,
            eventData,
            ticketData,
            subdomain,
            updatePaymentData,
            relation_id,
            isGuest,
            addonData
          );

          //* send email
          sendEmail(regMailData);
        }

        let data = {
          paymentIntentId: encrypt(`${updatePaymentData.paymentIntentId}`),
          amount: updatePaymentData?.amount,
          statusOfPayment: paymentStatus,
          userParticipantId: participantData?._id ?? null,
          ticketId: updatePaymentData?.ticketId,
          // ticketData: {
          //   ...ticketData,
          //   paymentId: encrypt(`${ticketData?.paymentId}` ?? ""),
          // },
        };

        return res.status(200).json({
          status: true,
          message: `Payment ${verifyPayment.status}!`,
          data: data,
        });
      } else {
        return res.status(200).json({
          status: false,
          message: `Payment intent not exist!`,
        });
      }
    } else {
      return res.status(200).json({
        status: false,
        message: `somthing went wrong!`,
      });
    }
  } catch (error) {
    console.log(
      "🚀 ~ file: eventTicketPaymentControllerV2.js:841 ~ exports.confirmPaymentIntent= ~ error:",
      error
    );
    return res.status(500).json({
      status: false,
      message: `Payment failed: ${error.message}`,
    });
  }
};

//* API for refund and remove the ticket by ticket purchase Id
exports.refundPurchaseTicketById = async (req, res) => {
  try {
    const userId = req?.authUserId;
    const relation_id = new ObjectId(req?.relation_id);
    const purchaseTicketId = req.params?.purchaseTicketId;

    const community = await Communities.findOne({
      _id: relation_id,
      isDelete: false,
    });

    if (!community)
      return res.status(404).json({
        status: false,
        message: `Community is not found!`,
      });

    if (!purchaseTicketId) {
      return res.status(400).json({
        status: false,
        message: `Purchase ticket id is required!`,
      });
    }

    const refundedBy = req.owner ? "admin" : "user";

    //* Get the purchase ticket data
    const findPurchaseTicket = await ticketPurchaseV2
      .findOne({
        _id: purchaseTicketId,
        relation_id,
        ...(refundedBy === "user" && { userId }),
        isDelete: false,
      })
      .populate({
        path: "paymentIntentId",
        select:
          "purchaseQuantity ticketValue stripeAccountDBId paymentIntentId stripe_processing_fee status",
      })
      .lean();

    //* If purchase ticket don't have the payment intent
    if (!findPurchaseTicket || !findPurchaseTicket.paymentIntentId) {
      return res.status(404).json({
        status: false,
        message: `Purchase ticket is not found!`,
      });
    }

    //* Validate the geniun ticket refund
    if (
      findPurchaseTicket?.ticketOrderStatus ===
        "partial_payment_refund_initiated" ||
      findPurchaseTicket?.ticketOrderStatus === "refund" ||
      findPurchaseTicket?.ticketOrderStatus === "cancelled"
    ) {
      return res.status(400).json({
        status: false,
        message: `This ticket is already in the process of being refunded or has been refunded.`,
      });
    }

    //* Get the admin stripe account
    const adminStripeAccountData = await getAdminStripeAccountId({
      relation_id,
      authorizationToken: req?.headers?.authorization,
    });
    if (!adminStripeAccountData && !findPurchaseTicket.isFreeTicket) {
      return res
        .status(200)
        .send({ status: false, message: "Admin Stripe Account not found!" });
    }

    //* If the purchase ticket have purchased
    if (findPurchaseTicket?.ticketOrderStatus === "succeeded") {
      //* If the free ticket purchased
      if (findPurchaseTicket.isFreeTicket) {
        const refundReqDate = new Date();

        //* Find the purchased ticket payment Intent and update the quantity
        const revisePaymentIntent = await ticketPaymentIntent.findByIdAndUpdate(
          findPurchaseTicket?.paymentIntentId?._id,
          {
            $inc: {
              purchaseQuantity: -1,
            },
          },
          { new: true }
        );
        if (!revisePaymentIntent) {
          return res.status(400).send({
            status: false,
            message: "Failed to update Payment Intent in db!",
          });
        }

        //* Update the purchased ticket data
        const updateTicketPurchase = await ticketPurchaseV2.findByIdAndUpdate(
          findPurchaseTicket?._id,
          {
            $set: {
              ticketOrderStatus: "cancelled",
              ticketCancelleDate: refundReqDate,
              paymentRefundId: null,
              refundedBy,
            },
          },
          {
            new: true,
          }
        );

        //* Remove ticket submission data using the ticket purchase ID.
        await TicketSubmission.findOneAndUpdate(
          { ticketPurchaseId: updateTicketPurchase._id, isDelete: false },
          { isDelete: true }
        );

        //* if the intent fully refunded
        if (revisePaymentIntent.purchaseQuantity === 0) {
          //* Update the ticket intent status
          const updateTicketPaymentIntent =
            await ticketPaymentIntent.findByIdAndUpdate(
              findPurchaseTicket?.paymentIntentId?._id,
              {
                $set: {
                  status: "cancelled",
                },
              },
              { new: true }
            );

          //* Find the other payment done by user
          const findPaymentIntent = await ticketPaymentIntent.find({
            userId: updateTicketPaymentIntent?.userId,
            relation_id,
            eventId: findPurchaseTicket.eventId,
            status: "succeeded",
            isDelete: false,
          });

          //* If the intent not found then remove the user participent attendee
          if (!findPaymentIntent.length) {
            await EventParticipantAttendees.findByIdAndUpdate(
              updateTicketPaymentIntent?.userParticipantId,
              {
                isDelete: true,
              }
            );
            RemoveFromChannelOnPurchaseTicket(
              findPurchaseTicket.eventId,
              userId,
              relation_id
            );
          }
        }

        if (!updateTicketPurchase) {
          return res.status(400).send({
            status: false,
            message: "Something went wrong to refund the ticket!",
          });
        } else {
          //* Update the ticket available qty
          await EventTicket.findByIdAndUpdate(updateTicketPurchase?.ticketId, {
            $inc: { availableQuantity: 1 },
          });
        }

        //* For the send the mail
        if (updateTicketPurchase.userId) {
          //* Find the user details
          const userData = await airTableSync.findById(
            { _id: updateTicketPurchase.userId },
            {
              first_name: 1,
              last_name: 1,
              "Preferred Email": 1,
              display_name: 1,
            }
          );

          //* Find the events details
          const eventData = await event
            .findById(
              { _id: updateTicketPurchase.eventId },
              { isDelete: false }
            )
            .lean();
          const communityLogo = req.currentEdge.relation_id.logo
            ? req.currentEdge.relation_id.logo
            : "";
          const mailData = {
            email: `${userData["Preferred Email"]}`,
            subject: `Ticket Payment Refunded`,
            html: `<div style="max-width: 600px; margin: 0 auto; font-family: Arial, sans-serif; background-color: #f5f5f5; padding: 20px; border-radius: 10px;">
                <div style="text-align: center; margin-bottom: 20px;">
                  <img src=${communityLogo} alt="Your Ticketing Platform Logo" style="max-width: 200px;">
                </div>
                <div style="background-color: #ffffff; padding: 30px; border-radius: 10px; box-shadow: 0px 3px 6px rgba(0, 0, 0, 0.1);">
                  <h2 style="text-align: center; margin-bottom: 20px; color: #333;">Ticket Payment Refunded</h2>
                  <p style="font-size: 16px; line-height: 1.6; margin-bottom: 20px;">
                    Dear ${
                      userData.first_name + " " + userData.last_name
                    },<br />
                  </p>
                  <p style="font-size: 16px; line-height: 1.6; margin-bottom: 20px;">
                  We have received your request to cancel your ticket for the ${
                    eventData.title
                  } scheduled on ${
              eventData.startDate + " " + eventData.startTime
            }. Your Ticket Payment Refund has been successfully Refunded.
                  </p>
                  <p style="font-size: 16px; line-height: 1.6; margin-bottom: 20px;">
                    If you have any questions or need further assistance, feel free to reply to this email.
                  </p>
                  <p style="font-size: 16px; line-height: 1.6; margin-bottom: 0;">
                    See you soon!<br>
                    Your Ticketing Platform Team
                  </p>
                </div>
              </div>`,
              relationId:req.currentEdge.relation_id._id ? req.currentEdge.relation_id._id : "",
              customsmtp:req.currentEdge.relation_id.customSMTP ? req.currentEdge.relation_id.customSMTP : false
          };
          sendEmail(mailData);
        }

        return res.status(200).send({
          status: true,
          message: "Your refund is initiated successfully!",
          data: updateTicketPurchase,
        });
      } else {
        //* Fetch the ticket addon purchase data
        const findTicketAddonPurchase = await TicketAddonPurchase.find({
          paymentIntentId: findPurchaseTicket?.paymentIntentId?._id,
          ticketPurchaseId: findPurchaseTicket?._id,
          isDelete: false,
          addonOrderStatus: "succeeded",
        });

        //* calculate total addon amount
        const totalAddonAmount = findTicketAddonPurchase.reduce(
          (sum, selection) => {
            return sum + selection.price;
          },
          0
        );

        //* Initiate the refund
        const refund = await createRefundIntent({
          paymentIntentId: findPurchaseTicket.paymentIntentId?.paymentIntentId,
          amount: Number(
            (findPurchaseTicket.price + totalAddonAmount).toFixed(2)
          ),
          metadata: {
            refundedBy,
            isAllOrderRefund: false,
            query: JSON.stringify({
              purchaseTicketId,
              userId: findPurchaseTicket?.userId,
              relation_id,
              eventId: findPurchaseTicket?.eventId,
              paymentIntentId: findPurchaseTicket.paymentIntentId?._id,
              connectedAccountId: adminStripeAccountData?.adminStripeAccountId,
            }),
          },
          adminStripeAccountId: adminStripeAccountData?.adminStripeAccountId,
        });

        if (!refund) {
          return res.status(400).json({
            status: false,
            message: `Somthing went wrong in refund payment!`,
            data: {},
          });
        }

        const refundReqDate = new Date();

        //* Store the refund data in the ticket purchase refund
        const ticketPaymentRefundData = await TicketPaymentRefund.create({
          ticketId: findPurchaseTicket?.ticketId,
          userId: findPurchaseTicket?.userId,
          eventId: findPurchaseTicket?.eventId,
          relation_id: new ObjectId(req?.relation_id),
          amount: findPurchaseTicket?.price,
          refundReqDate,
          refundId: refund.id,
          stripeAccountDBId: findPurchaseTicket?.stripeAccountDBId,
          refundStatus: "partial_payment_refund_initiated",
          refundObj: refund,
          paymentIntentId: findPurchaseTicket?.paymentIntentId?._id,
          ticketPurchaseId: findPurchaseTicket?._id,
          refundedBy,
        });
        if (!ticketPaymentRefundData) {
          return res.status(400).json({
            status: false,
            message: `Somthing went wrong to store refund data!`,
            data: {},
          });
        }

        //* Add the refund record for the addons
        if (findTicketAddonPurchase && findTicketAddonPurchase.length) {
          for await (let addonData of findTicketAddonPurchase) {
            //* add purchase refund data
            const addonPurchaseRefund = await AddonPurchaseRefund.create({
              addonPurchaseId: addonData?._id,
              userId: addonData?.userId,
              eventId: addonData?.eventId,
              relation_id,
              paymentIntentId: findPurchaseTicket?.paymentIntentId?._id,
              ticketPurchaseId: addonData?.ticketPurchaseId,
              amount: addonData?.price,
              refundReqDate,
              refundId: refund?.id,
              stripeAccountDBId: adminStripeAccountData?.stripeAccountDBId,
              refundStatus: "partial_payment_refund_initiated",
              refundObj: refund,
              refundedBy,
            });

            if (addonPurchaseRefund) {
              await TicketAddonPurchase.findByIdAndUpdate(addonData?._id, {
                $set: {
                  paymentRefundId: addonPurchaseRefund?._id,
                },
              });
            }
          }
        }

        //* Revise the ticket payment intent
        const revisePaymentIntent = await ticketPaymentIntent.findOneAndUpdate(
          {
            _id: findPurchaseTicket?.paymentIntentId?._id,
          },
          [
            {
              $set: {
                //* Decreament the purchase quantity
                purchaseQuantity: {
                  //* Preval the value to 0 if the value is less than 0
                  $max: [{ $subtract: ["$purchaseQuantity", 1] }, 0],
                },

                //* Decreament the ticket value
                ticketValue: {
                  //* Round the value to 2 decimal places
                  $round: [
                    {
                      //* Preval the value to 0 if the value is less than 0
                      $max: [
                        {
                          //* Subtract the ticketValue from the totalTicketValue
                          $subtract: [
                            "$ticketValue",
                            Number(findPurchaseTicket.price.toFixed(2)),
                          ],
                        },
                        0,
                      ],
                    },
                    2,
                  ],
                },

                //* Decreament the addOn amount
                addOnAmount: {
                  //* Round the value to 2 decimal places
                  $round: [
                    {
                      //* Preval the value to 0 if the value is less than 0
                      $max: [
                        {
                          //* Subtract the addOnAmount from the totalAddonAmount
                          $subtract: [
                            "$addOnAmount",
                            Number(totalAddonAmount.toFixed(2)),
                          ],
                        },
                        0,
                      ],
                    },
                    2,
                  ],
                },

                //* Update the payment intent status
                status: "partial_payment_refund_initiated",
              },
            },
          ],
          {
            new: true,
          }
        );
        if (!revisePaymentIntent) {
          return res.status(400).send({
            status: false,
            message: "Failed to update Payment Intent in db!",
          });
        }

        //* Update the purchase ticket data
        const updateTicketPurchase = await ticketPurchaseV2.findByIdAndUpdate(
          findPurchaseTicket?._id,
          {
            $set: {
              ticketOrderStatus: ticketPaymentRefundData?.refundStatus,
              ticketCancelleDate: refundReqDate,
              paymentRefundId: ticketPaymentRefundData?._id,
              refundedBy,
            },
          },
          {
            new: true,
          }
        );

        //* Update the ticket addon data
        await TicketAddonPurchase.updateMany(
          {
            paymentIntentId: findPurchaseTicket?.paymentIntentId?._id,
            ticketPurchaseId: findPurchaseTicket?._id,
            isDelete: false,
          },
          {
            $set: {
              addonOrderStatus: ticketPaymentRefundData?.refundStatus,
            },
          }
        );

        //* Remove the ticket submission form
        await TicketSubmission.updateMany(
          {
            ticketPurchaseId: findPurchaseTicket?._id,
            isDelete: false,
          },
          {
            $set: {
              isDelete: false,
            },
          }
        );

        //* Refund the other addon purchase which is in other payment intent
        const otherPIAddonRefund = await refundOtherAddonByPurchaseId({
          ticketPurchaseData: findPurchaseTicket,
          paymentIntentId: findPurchaseTicket?.paymentIntentId?._id,
          adminStripeAccountData: adminStripeAccountData,
          relation_id,
          refundedBy,
        });

        //* Manage the user attendee and the intent
        if (revisePaymentIntent.purchaseQuantity === 0) {
          //* Update the intent status
          const updateTicketPaymentIntent =
            await ticketPaymentIntent.findByIdAndUpdate(
              findPurchaseTicket?.paymentIntentId?._id,
              {
                $set: {
                  status: ticketPaymentRefundData?.refundStatus,
                },
              },
              { new: true }
            );

          //* Check other intent is exist by user
          const findPaymentIntent = await ticketPaymentIntent.find({
            userId,
            relation_id,
            eventId: findPurchaseTicket.eventId,
            status: "succeeded",
            isDelete: false,
          });

          //* Manage the user attendee data
          if (!findPaymentIntent.length) {
            await EventParticipantAttendees.findByIdAndUpdate(
              updateTicketPaymentIntent?.userParticipantId,
              {
                isDelete: true,
              }
            );
            RemoveFromChannelOnPurchaseTicket(
              findPurchaseTicket.eventId,
              userId,
              relation_id
            );
          }
        }

        //* Function for the make the refund for the other addon with the same purchase ticket

        //* Remove ticket submission data using the ticket purchase ID.
        await TicketSubmission.findOneAndUpdate(
          { ticketPurchaseId: updateTicketPurchase._id, isDelete: false },
          { isDelete: true }
        );
        if (!updateTicketPurchase) {
          return res.status(400).send({
            status: false,
            message: "Something went wrong to refund the ticket!",
          });
        } else {
          //* Revise the ticket available qty
          await EventTicket.findByIdAndUpdate(updateTicketPurchase?.ticketId, {
            $inc: { availableQuantity: 1 },
          });
        }

        //* Fetch user details
        const userData = await airTableSync.findById(
          { _id: updateTicketPurchase.userId },
          {
            first_name: 1,
            last_name: 1,
            "Preferred Email": 1,
            display_name: 1,
          }
        );

        //* fetch the event details
        const eventData = await event
          .findById({ _id: updateTicketPurchase.eventId }, { isDelete: false })
          .lean();

        const communityLogo = community?.logo
          ? `https://${AWS_BUCKET}.s3.us-east-2.amazonaws.com/groupos/${community._id}/${community.logo}`
          : "https://mds-community.s3.us-east-2.amazonaws.com/uploads/groupos-util/groupos_logo.png";

        const mailData = {
          email: `${userData["Preferred Email"]}`,
          subject: `Ticket Payment Refunded`,
          html: `<div style="max-width: 600px; margin: 0 auto; font-family: Arial, sans-serif; background-color: #f5f5f5; padding: 20px; border-radius: 10px;">
              <div style="text-align: center; margin-bottom: 20px;">
                <img src=${communityLogo} alt="Your Ticketing Platform Logo" style="max-width: 200px;">
              </div>
              <div style="background-color: #ffffff; padding: 30px; border-radius: 10px; box-shadow: 0px 3px 6px rgba(0, 0, 0, 0.1);">
                <h2 style="text-align: center; margin-bottom: 20px; color: #333;">Ticket Payment Refunded</h2>
                <p style="font-size: 16px; line-height: 1.6; margin-bottom: 20px;">
                  Dear ${userData.first_name + " " + userData.last_name},<br />
                </p>
                <p style="font-size: 16px; line-height: 1.6; margin-bottom: 20px;">
                We have received your request to cancel your ticket for the ${
                  eventData.title
                } scheduled on ${
            eventData.startDate + " " + eventData.startTime
          }. Your Ticket Payment Refund has been successfully Refunded.
                </p>
                <p style="font-size: 16px; line-height: 1.6; margin-bottom: 20px;">
                  If you have any questions or need further assistance, feel free to reply to this email.
                </p>
                <p style="font-size: 16px; line-height: 1.6; margin-bottom: 0;">
                  See you soon!<br>
                  Your Ticketing Platform Team
                </p>
              </div>
            </div>`,
          relationId: req.currentEdge.relation_id._id
            ? req.currentEdge.relation_id._id
            : "",
          customsmtp: req.currentEdge.relation_id.customSMTP
            ? req.currentEdge.relation_id.customSMTP
            : false,
        };
        sendEmail(mailData);

        return res.status(200).send({
          status: true,
          message: "Your refund is initiated successfully!",
          data: updateTicketPurchase,
        });
      }
    } else {
      const totalAmount =
        findPurchaseTicket?.paymentIntentId?.ticketValue -
        findPurchaseTicket?.price;

      const totalAmountWithCharge = parseFloat(
        countCustomersTotalChargeableAmount({ amount: totalAmount })
      );

      //* Update payment Intent for the new added ticket
      let paymentIntent;
      if (findPurchaseTicket?.paymentIntentId?.purchaseQuantity === 1) {
        paymentIntent = await canclePaymentIntent({
          updatePaymentIntentId:
            findPurchaseTicket.paymentIntentId?.paymentIntentId,
          adminStripeAccountId: adminStripeAccountData?.adminStripeAccountId,
        });
      } else {
        paymentIntent = await updatePaymentIntent({
          amount: totalAmountWithCharge,
          updatePaymentIntentId:
            findPurchaseTicket.paymentIntentId?.paymentIntentId,
          adminStripeAccountId: adminStripeAccountData?.adminStripeAccountId,
        });
      }
      if (!paymentIntent) {
        return res
          .status(200)
          .send({ status: false, message: "Failed to update Payment Intent!" });
      }

      const revisePaymentIntent = await ticketPaymentIntent.findByIdAndUpdate(
        findPurchaseTicket?.paymentIntentId?._id,
        {
          $set: {
            ticketValue: totalAmount,
            client_secret: paymentIntent?.client_secret,
            paymentIntent,
            stripe_processing_fee: (
              totalAmountWithCharge - totalAmount
            ).toFixed(2),
          },
          $inc: {
            purchaseQuantity: -1,
          },
        },
        { new: true }
      );
      if (!revisePaymentIntent) {
        return res.status(200).send({
          status: false,
          message: "Failed to update Payment Intent in db!",
        });
      } else {
        await ticketPurchaseV2.findByIdAndUpdate(findPurchaseTicket?._id, {
          $set: {
            isDelete: true,
          },
        });

        // Remove ticket submission data using the ticket purchase ID.
        await TicketSubmission.findOneAndUpdate(
          { ticketPurchaseId: findPurchaseTicket._id, isDelete: false },
          { isDelete: true }
        );

        await EventTicket.findByIdAndUpdate(findPurchaseTicket?.ticketId, {
          $inc: { availableQuantity: 1 },
        });

        if (revisePaymentIntent?.purchaseQuantity === 0) {
          await ticketPaymentIntent.findByIdAndUpdate(
            revisePaymentIntent?._id,
            {
              $set: {
                isDelete: true,
              },
            },
            { new: true }
          );
        }
      }

      return res.status(200).json({
        status: true,
        message: `Ticket removed successfully!`,
        data: {},
      });
    }
  } catch (error) {
    console.log(
      "🚀 ~ file: eventTicketPaymentControllerV2.js:1233 ~ exports.refundPurchaseTicketById= ~ error:",
      error
    );
    return res.status(200).json({ status: false, message: `${error.message}` });
  }
};

//* API for refund and remove the ticket by ticket purchase Id
exports.refundEventPurchaseTicketOrder = async (req, res) => {
  try {
    const relation_id = new ObjectId(req?.relation_id);
    const eventId = req.params?.eventId;
    let adminStripeAccountData = null;
    let sendFreeTicketEmail = false;

    if (!eventId) {
      return res.status(400).json({
        status: false,
        message: `Purchase ticket id is required!`,
      });
    }

    const community = await Communities.findOne({
      _id: relation_id,
      isDelete: false,
    });

    if (!community)
      return res.status(404).json({
        status: false,
        message: `Community is not found!`,
      });

    const refundedBy = req.owner ? "admin" : "user";
    const userId = refundedBy === "admin" ? req.query.userId : req?.authUserId;

    if (refundedBy === "admin" && !userId) {
      return res.status(400).json({
        status: false,
        message: `UserId is required!`,
      });
    }

    const findPaymentIntent = await ticketPaymentIntent.find({
      eventId,
      relation_id,
      userId,
      status: {
        $nin: ["partial_payment_refund_initiated", "refund", "cancelled"],
      },
      isDelete: false,
    });

    if (!findPaymentIntent.length) {
      return res.status(400).json({
        status: false,
        message: `You don't have the payment intent needed to proceed with the refund.!`,
      });
    }

    for await (let paymentIntentData of findPaymentIntent) {
      if (paymentIntentData.status === "succeeded") {
        if (paymentIntentData.isFreeTicket) {
          const findTicketQty = await ticketPurchaseV2.aggregate([
            {
              $match: {
                paymentIntentId: paymentIntentData._id,
                ticketOrderStatus: paymentIntentData.status,
                isDelete: false,
                isFreeTicket: true,
              },
            },
            {
              $group: {
                _id: "$ticketId",
                count: {
                  $sum: 1,
                },
              },
            },
          ]);

          const refundReqDate = new Date();

          const revisePaymentIntent =
            await ticketPaymentIntent.findByIdAndUpdate(
              paymentIntentData._id,
              {
                $set: {
                  status: "cancelled",
                  ticketValue: 0,
                  purchaseQuantity: 0,
                  addOnAmount: 0,
                  refundedBy,
                },
              },
              { new: true }
            );
          if (!revisePaymentIntent) {
            return res.status(400).send({
              status: false,
              message: "Failed to update Payment Intent in db!",
            });
          }

          await ticketPurchaseV2.updateMany(
            {
              paymentIntentId: paymentIntentData._id,
              ticketOrderStatus: "succeeded",
              isDelete: false,
            },
            {
              $set: {
                ticketOrderStatus: "cancelled",
                ticketCancelleDate: refundReqDate,
                refundedBy,
              },
            },
            {
              new: true,
            }
          );

          await TicketAddonPurchase.updateMany(
            {
              paymentIntentId: paymentIntentData._id,
              addonOrderStatus: "succeeded",
              isDelete: false,
            },
            {
              $set: {
                addonOrderStatus: "cancelled",
                addonCancelleDateaddonOrderStatus: refundReqDate,
                refundedBy,
              },
            }
          );

          if (revisePaymentIntent) {
            findTicketQty.forEach(async (data) => {
              await EventTicket.findByIdAndUpdate(data?._id, {
                $inc: { availableQuantity: data?.count },
              });
            });

            const findPaymentIntent = await ticketPaymentIntent.find({
              userId,
              relation_id,
              eventId: eventId,
              status: "succeeded",
              isDelete: false,
            });

            if (!findPaymentIntent.length) {
              await EventParticipantAttendees.findByIdAndUpdate(
                revisePaymentIntent?.userParticipantId,
                {
                  isDelete: true,
                }
              );

              listOfSocketEvents = RemoveFromChannelOnPurchaseTicket(
                eventId,
                userId,
                relation_id
              );
            }
          }

          if (
            revisePaymentIntent.eventId &&
            revisePaymentIntent.userId &&
            !sendFreeTicketEmail
          ) {
            const userData = await airTableSync.findById(
              { _id: revisePaymentIntent.userId },
              {
                first_name: 1,
                last_name: 1,
                "Preferred Email": 1,
                display_name: 1,
              }
            );
            const eventData = await event
              .findById(
                { _id: revisePaymentIntent.eventId },
                { isDelete: false }
              )
              .lean();

            const communityLogo = community?.logo
              ? `https://${AWS_BUCKET}.s3.us-east-2.amazonaws.com/groupos/${community._id}/${community.logo}`
              : "https://mds-community.s3.us-east-2.amazonaws.com/uploads/groupos-util/groupos_logo.png";

            const mailData = {
              email: `${userData["Preferred Email"]}`,
              subject: `Ticket Payment Refunded`,
              html: `<div style="max-width: 600px; margin: 0 auto; font-family: Arial, sans-serif; background-color: #f5f5f5; padding: 20px; border-radius: 10px;">
                  <div style="text-align: center; margin-bottom: 20px;">
                    <img src=${communityLogo} alt="Your Ticketing Platform Logo" style="max-width: 200px;">
                  </div>
                  <div style="background-color: #ffffff; padding: 30px; border-radius: 10px; box-shadow: 0px 3px 6px rgba(0, 0, 0, 0.1);">
                    <h2 style="text-align: center; margin-bottom: 20px; color: #333;">Ticket Payment Refunded</h2>
                    <p style="font-size: 16px; line-height: 1.6; margin-bottom: 20px;">
                      Dear ${
                        userData.first_name + " " + userData.last_name
                      },<br />
                    </p>
                    <p style="font-size: 16px; line-height: 1.6; margin-bottom: 20px;">
                    We have received your request to cancel your ticket for the ${
                      eventData.title
                    } scheduled on ${
                eventData.startDate + " " + eventData.startTime
              }. Your Ticket Payment Refund has been successfully Refunded.
                    </p>
                    <p style="font-size: 16px; line-height: 1.6; margin-bottom: 20px;">
                      If you have any questions or need further assistance, feel free to reply to this email.
                    </p>
                    <p style="font-size: 16px; line-height: 1.6; margin-bottom: 0;">
                      See you soon!<br>
                      Your Ticketing Platform Team
                    </p>
                  </div>
                </div>`,
                relationId:req.currentEdge.relation_id._id ? req.currentEdge.relation_id._id : "",
                customsmtp:req.currentEdge.relation_id.customSMTP ? req.currentEdge.relation_id.customSMTP : false
            };
            sendEmail(mailData);
            sendFreeTicketEmail = true;
          }
        } else {
          const findTicketQty = await ticketPurchaseV2.aggregate([
            {
              $match: {
                paymentIntentId: ObjectId(paymentIntentData._id),
                ticketOrderStatus: paymentIntentData.status,
                isDelete: false,
              },
            },
            {
              $lookup: {
                from: "ticket_addon_purchases",
                localField: "_id",
                foreignField: "ticketPurchaseId",
                as: "addon_purchases_data",
                pipeline: [
                  {
                    $match: {
                      paymentIntentId: ObjectId(paymentIntentData._id),
                      addonOrderStatus: "succeeded",
                      isDelete: false,
                    },
                  },
                ],
              },
            },
            {
              $group: {
                _id: "$ticketId",
                count: {
                  $sum: 1,
                },
                addon_purchases_data: {
                  $first: "$addon_purchases_data",
                },
              },
            },
          ]);

          //* fetch the stripe account data
          if (
            !adminStripeAccountData ||
            adminStripeAccountData?.stripeAccountDBId !==
              paymentIntentData.stripeAccountDBId
          ) {
            //* Get the admin stripe account
            adminStripeAccountData = await getAdminStripeAccountId({
              relation_id,
              authorizationToken: req?.headers?.authorization,
            });
            if (!adminStripeAccountData) {
              return res.status(200).send({
                status: false,
                message: "Admin Stripe Account not found!",
              });
            }
          }

          //* create the refund intent for the addon
          const refund = await createRefundIntent({
            paymentIntentId: paymentIntentData?.paymentIntentId,
            amount: Number(
              (
                paymentIntentData?.ticketValue +
                Number(paymentIntentData?.addOnAmount || 0)
              ).toFixed(2)
            ),
            metadata: {
              refundedBy,
              isAllOrderRefund: true,
              query: JSON.stringify({
                userId: paymentIntentData?.userId,
                relation_id,
                eventId: paymentIntentData?.eventId,
                paymentIntentId: paymentIntentData?._id,
                connectedAccountId:
                  adminStripeAccountData?.adminStripeAccountId,
              }),
            },
            adminStripeAccountId: adminStripeAccountData?.adminStripeAccountId,
          });

          if (!refund) {
            return res.status(400).json({
              status: false,
              message: `Somthing went wrong in refund payment!`,
              data: {},
            });
          }

          const refundReqDate = new Date();
          const ticketPaymentRefundData = await TicketPaymentRefund.create({
            userId: userId,
            eventId: paymentIntentData.eventId,
            relation_id: new ObjectId(req?.relation_id),
            amount: paymentIntentData?.ticketValue,
            addonAmount: paymentIntentData.addOnAmount,
            refundReqDate,
            refundId: refund.id,
            stripeAccountDBId: paymentIntentData?.stripeAccountDBId,
            paymentIntentId: paymentIntentData?._id,
            refundStatus: "partial_payment_refund_initiated",
            refundObj: refund,
            refundedBy,
            isAllOrderRefund: true,
          });
          if (!ticketPaymentRefundData) {
            return res.status(400).json({
              status: false,
              message: `Somthing went wrong to store refund data!`,
              data: {},
            });
          }

          await ticketPurchaseV2.updateMany(
            {
              paymentIntentId: paymentIntentData._id,
              ticketOrderStatus: "succeeded",
              isDelete: false,
            },
            {
              $set: {
                ticketOrderStatus: ticketPaymentRefundData?.refundStatus,
                ticketCancelleDate: refundReqDate,
                paymentRefundId: ticketPaymentRefundData?._id,
                refundedBy,
              },
            },
            {
              new: true,
            }
          );

          const findAddonPurchase = await TicketAddonPurchase.find({
            paymentIntentId: paymentIntentData._id,
            addonOrderStatus: paymentIntentData.status,
            isDelete: false,
          });

          for await (let addonData of findAddonPurchase) {
            //* add purchase refund data
            const addonPurchaseRefund = await AddonPurchaseRefund.create({
              addonPurchaseId: addonData?._id,
              userId: addonData?.userId,
              eventId: addonData?.eventId,
              relation_id,
              paymentIntentId: paymentIntentData?._id,
              ticketPurchaseId: addonData?.ticketPurchaseId,
              amount: addonData?.price,
              refundReqDate,
              refundId: refund?.id,
              stripeAccountDBId: paymentIntentData?.stripeAccountDBId,
              refundStatus: ticketPaymentRefundData?.refundStatus,
              refundObj: refund,
              refundedBy,
            });

            if (addonPurchaseRefund) {
              await TicketAddonPurchase.findByIdAndUpdate(addonData?._id, {
                $set: {
                  paymentRefundId: addonPurchaseRefund?._id,
                  addonOrderStatus: ticketPaymentRefundData?.refundStatus,
                },
              });
            }
          }

          const revisePaymentIntent =
            await ticketPaymentIntent.findByIdAndUpdate(
              paymentIntentData._id,
              {
                $set: {
                  status: "partial_payment_refund_initiated",
                  ticketValue: 0,
                  purchaseQuantity: 0,
                  addOnAmount: 0,
                  refundedBy,
                },
              },
              { new: true }
            );
          if (!revisePaymentIntent) {
            return res.status(400).send({
              status: false,
              message: "Failed to update Payment Intent in db!",
            });
          }

          await ticketPurchaseV2.updateMany(
            {
              paymentIntentId: paymentIntentData._id,
              ticketOrderStatus: "succeeded",
              isDelete: false,
            },
            {
              $set: {
                ticketOrderStatus: ticketPaymentRefundData?.refundStatus,
                ticketCancelleDate: refundReqDate,
                paymentRefundId: ticketPaymentRefundData?._id,
                refundedBy,
              },
            },
            {
              new: true,
            }
          );

          if (findTicketQty.length) {
            await TicketSubmission.updateMany(
              {
                ticketPurchaseId: {
                  $in: findTicketQty.map((data) => data?._id),
                },
                isDelete: false,
              },
              {
                $set: {
                  isDelete: true,
                },
              }
            );
          }

          if (revisePaymentIntent) {
            findTicketQty.forEach(async (data) => {
              await EventTicket.findByIdAndUpdate(data?._id, {
                $inc: { availableQuantity: data?.count },
              });
            });

            const findPaymentIntent = await ticketPaymentIntent.find({
              userId,
              relation_id,
              eventId: eventId,
              status: "succeeded",
              isDelete: false,
            });

            if (!findPaymentIntent.length) {
              await EventParticipantAttendees.findByIdAndUpdate(
                revisePaymentIntent?.userParticipantId,
                {
                  isDelete: true,
                }
              );
              listOfSocketEvents = RemoveFromChannelOnPurchaseTicket(
                eventId,
                userId,
                relation_id
              );
            }
          }
        }
      }

      if (paymentIntentData.status !== "succeeded") {
        const findTicketQty = await ticketPurchaseV2.aggregate([
          {
            $match: {
              paymentIntentId: paymentIntentData._id,
              ticketOrderStatus: paymentIntentData.status,
              isDelete: false,
            },
          },
          {
            $group: {
              _id: "$ticketId",
              count: {
                $sum: 1,
              },
            },
          },
        ]);

        if (findTicketQty?.length) {
          const updatePaymentIntent =
            await ticketPaymentIntent.findByIdAndUpdate(
              paymentIntentData?._id,
              {
                $set: {
                  purchaseQuantity: 0,
                  ticketValue: 0,
                  isDelete: true,
                },
              },
              { new: true }
            );

          if (updatePaymentIntent) {
            await ticketPurchaseV2.updateMany(
              {
                paymentIntentId: updatePaymentIntent._id,
                ticketOrderStatus: paymentIntentData.status,
              },
              {
                $set: {
                  isDelete: true,
                },
              }
            );

            await TicketAddonPurchase.updateMany(
              {
                paymentIntentId: updatePaymentIntent._id,
                addonOrderStatus: paymentIntentData.status,
                isDelete: false,
              },
              {
                $set: {
                  isDelete: true,
                },
              }
            );

            findTicketQty.forEach(async (data) => {
              await EventTicket.findByIdAndUpdate(data?._id, {
                $inc: { availableQuantity: data?.count },
              });
            });
          }
        } else {
          await ticketPaymentIntent.findByIdAndUpdate(
            paymentIntentData?._id,
            {
              $set: {
                purchaseQuantity: 0,
                ticketValue: 0,
                isDelete: true,
              },
            },
            { new: true }
          );

          await TicketAddonPurchase.updateMany(
            {
              paymentIntentId: paymentIntentData?._id,
              addonOrderStatus: paymentIntentData.status,
              isDelete: false,
            },
            {
              $set: {
                isDelete: true,
              },
            }
          );
        }
      }
    }

    // Remove ticket submission data using event ID, relation ID, and user ID.
    await TicketSubmission.updateMany(
      { userId, relation_id, eventId, isDelete: false },
      { isDelete: true }
    );

    // Remove the user from the event participant attendees
    await EventParticipantAttendees.findOneAndUpdate(
      { user: userId, relation_id, event: eventId, isDelete: false },
      {
        isDelete: true,
      }
    );
    listOfSocketEvents = RemoveFromChannelOnPurchaseTicket(
      eventId,
      userId,
      relation_id
    );
    return res.status(200).json({
      status: true,
      message: `Ticket order removed successfully`,
      data: {},
    });
  } catch (error) {
    console.log(
      "🚀 ~ file: eventTicketPaymentControllerV2.js:1233 ~ exports.refundPurchaseTicketById= ~ error:",
      error
    );
    return res.status(200).json({ status: false, message: `${error.message}` });
  }
};

//* Event ticket list of user
exports.getAllBookTicketsListforUser = async (req, res) => {
  try {
    const userId = req.authUserId;
    const authrizationToken = req.headers?.authorization ?? "";

    let page = req.query.page ? +req.query.page : 1;
    let limit = parseInt(req.query.limit ? +req.query.limit : 10);
    let search = req?.query?.search ?? "";
    let skip = parseInt((page - 1) * limit) || 0;

    // user Ticket list
    let match = {
      userId: new ObjectId(userId),
      relation_id: new ObjectId(req.relation_id),
      isDelete: false,
    };

    const stripeData = await getAdminStripeAccountId({
      relation_id: match?.relation_id,
      authorizationToken: authrizationToken,
    });

    const aggregate = [
      {
        $match: {
          ...match,
        },
      },
      {
        $lookup: {
          from: "events",
          localField: "eventId",
          foreignField: "_id",
          as: "eventId",
        },
      },
      { $unwind: "$eventId" },
      {
        $lookup: {
          from: "ticket_purchase_v2",
          localField: "_id",
          foreignField: "paymentIntentId",
          as: "ticketData",
          pipeline: [
            {
              $match: {
                isDelete: false,
              },
            },
            {
              $group: {
                _id: null,
                totalPrice: {
                  $sum: "$price",
                },
              },
            },
            {
              $project: {
                _id: 0,
                totalPrice: {
                  $ifNull: ["$totalPrice", 0],
                },
              },
            },
          ],
        },
      },
      {
        $unwind: {
          path: "$ticketData",
          preserveNullAndEmptyArrays: true,
        },
      },
      {
        $addFields: {
          totalPrice: {
            $add: ["$ticketData.totalPrice", "$stripe_processing_fee"],
          },
        },
      },
      { $sort: { purchaseDate: -1 } }, // Ensure this is valid in ticket_payments
      {
        $match: {
          "eventId.title": {
            $regex: `${search}`,
            $options: "i",
          },
        },
      },
    ];

    const userPaymentIntentCount = await ticketPaymentIntent.aggregate(
      aggregate
    );
    const userPaymentIntent = await ticketPaymentIntent.aggregate([
      ...aggregate,
      { $skip: skip },
      { $limit: limit },
    ]);

    // const totalCount = userTicket[0].totalCountRecords;

    // userTicket[0]?.filteredResults.map((value) => {
    //   value.paymentId = encrypt(`${value?.paymentId}` ?? "");
    //   value.paymentIntentId = encrypt(`${value?.paymentIntentId}` ?? "");
    //   value.client_secret = encrypt(`${value?.client_secret}` ?? "");
    // });

    if (userPaymentIntent === 0) {
      return res
        .status(200)
        .send({ status: false, message: `Ticket is not found!` });
    } else {
      return res.status(200).send({
        status: true,
        message: "Tickets listing retrieved successfully",
        data: userPaymentIntent,
        page: page,
        count: userPaymentIntentCount.length,
        adminStripeAccountId: encrypt(stripeData?.adminStripeAccountId ?? ""),
      });
    }
  } catch (error) {
    await debugErrorLogs.createErrorLogs(error, "ticketBookByUser", {});
    return res
      .status(500)
      .json({ status: false, message: "Internal server error!", error: error });
  }
};

//* Payment Refunds Retrieve from the webhooks for the single Ticket
exports.paymentRefundsRetrieve = async (obj) => {
  try {
    const paymentIntentId = obj.paymentIntentId;
    const metadata = JSON.parse(obj?.metadata?.query);

    if (metadata) {
      const purchaseTicketId = metadata.purchaseTicketId;
      const connectedAccountId = metadata?.connectedAccountId;

      const fetchPurchaseTicket = await ticketPurchaseV2
        .findOne({
          _id: purchaseTicketId,
          ticketOrderStatus: "partial_payment_refund_initiated",
        })
        .populate("paymentRefundId", {
          refundObj: 0,
        })
        .populate("paymentIntentId", {
          paymentIntent: 0,
        })
        .populate("eventId", "_id title startDate startTime -relation_id")
        .populate("userId", {
          _id: 1,
          first_name: 1,
          last_name: 1,
          display_name: 1,
          "Preferred Email": 1,
        })
        .lean();

      if (
        fetchPurchaseTicket &&
        fetchPurchaseTicket.paymentRefundId &&
        fetchPurchaseTicket.paymentRefundId.refundId
      ) {
        const refundId = fetchPurchaseTicket?.paymentRefundId?.refundId;
        const refundIdTemp = fetchPurchaseTicket?.paymentRefundId?._id;

        const refund = await stripe.refunds.retrieve(refundId, {
          stripeAccount: connectedAccountId,
        });

        if (
          refund.destination_details &&
          refund.destination_details.card &&
          (refund.destination_details.card.type === "refund" ||
            refund.destination_details.card.type === "reversal")
        ) {
          const updateTicketPaymentRefund =
            await TicketPaymentRefund.findByIdAndUpdate(
              refundIdTemp,
              {
                $set: {
                  refundedDate: new Date(),
                  refundStatus: refund.destination_details.card.type,
                  refundObj: refund,
                },
              },
              { new: true }
            );

          if (updateTicketPaymentRefund) {
            await ticketPurchaseV2.findByIdAndUpdate(fetchPurchaseTicket._id, {
              $set: {
                ticketOrderStatus: updateTicketPaymentRefund.refundStatus,
              },
            });

            await TicketAddonPurchase.updateMany(
              {
                paymentIntentId: fetchPurchaseTicket?.paymentIntentId?._id,
                ticketPurchaseId: fetchPurchaseTicket?._id,
                isDelete: false,
              },
              {
                $set: {
                  addonOrderStatus: updateTicketPaymentRefund.refundStatus,
                },
              }
            );

            await AddonPurchaseRefund.updateMany(
              {
                paymentIntentId: fetchPurchaseTicket?.paymentIntentId?._id,
                ticketPurchaseId: fetchPurchaseTicket?._id,
                isDelete: false,
              },
              {
                $set: {
                  refundedDate: new Date(),
                  refundStatus: refund.destination_details.card.type,
                  refundObj: refund,
                },
              },
              { new: true }
            );

            if (
              fetchPurchaseTicket?.paymentIntentId?.purchaseQuantity === 0 &&
              !fetchPurchaseTicket?.paymentIntentId?.addOnAmount
            ) {
              await ticketPaymentIntent.findByIdAndUpdate(
                fetchPurchaseTicket?.paymentIntentId?._id,
                {
                  $set: {
                    status: updateTicketPaymentRefund.refundStatus,
                  },
                }
              );
            }
          }

          if (fetchPurchaseTicket.userId) {
            const userData = fetchPurchaseTicket.userId;
            const eventData = fetchPurchaseTicket.eventId;
            const communitiyLogo = req.currentEdge.relation_id.logo || "";
            const mailData = {
              email: `${userData["Preferred Email"]}`,
              subject: `Ticket Payment Refunded`,
              html: `<div style="max-width: 600px; margin: 0 auto; font-family: Arial, sans-serif; background-color: #f5f5f5; padding: 20px; border-radius: 10px;">
                  <div style="text-align: center; margin-bottom: 20px;">
                    <img src=${communitiyLogo} alt="Your Ticketing Platform Logo" style="max-width: 200px;">
                  </div>
                  <div style="background-color: #ffffff; padding: 30px; border-radius: 10px; box-shadow: 0px 3px 6px rgba(0, 0, 0, 0.1);">
                    <h2 style="text-align: center; margin-bottom: 20px; color: #333;">Ticket Payment Refunded</h2>
                    <p style="font-size: 16px; line-height: 1.6; margin-bottom: 20px;">
                      Dear ${
                        userData.first_name + " " + userData.last_name
                      },<br />
                    </p>
                    <p style="font-size: 16px; line-height: 1.6; margin-bottom: 20px;">
                    We have received your request to cancel your ticket for the ${
                      eventData.title
                    } scheduled on ${
                eventData.startDate + " " + eventData.startTime
              }. Your Ticket Payment Refund has been successfully Refunded.
                    </p>
                    <p style="font-size: 16px; line-height: 1.6; margin-bottom: 20px;">
                      If you have any questions or need further assistance, feel free to reply to this email.
                    </p>
                    <p style="font-size: 16px; line-height: 1.6; margin-bottom: 0;">
                      See you soon!<br>
                      Your Ticketing Platform Team
                    </p>
                  </div>
                </div>`,
                relationId:req.currentEdge.relation_id._id ? req.currentEdge.relation_id._id : "",
                customsmtp:req.currentEdge.relation_id.customSMTP ? req.currentEdge.relation_id.customSMTP : false
            };
            await sendEmail(mailData);
          }

          return {
            status: true,
            message: "Refund Successful!",
            data: updateTicketPaymentRefund,
          };
        }
      }
    } else {
      return {
        status: false,
        message: "Invalid metadata!",
      };
    }
  } catch (error) {
    await debugErrorLogs.createErrorLogs(error, "ticketBookByUser", {});
    return {
      status: false,
      message: "Internal server error!",
      error: error?.code ?? error?.type,
    };
  }
};

//* Payment Refunds Retrieve from the webhooks for the whole Order
exports.orderPaymentRefundsRetrieve = async (obj) => {
  try {
    const metadata = JSON.parse(obj?.metadata?.query);

    if (metadata) {
      const connectedAccountId = metadata?.connectedAccountId;
      const paymentIntentId = metadata?.paymentIntentId;
      const relation_id = metadata?.relation_id;
      const eventId = metadata?.eventId;
      const userId = metadata?.userId;

      const findTicketRefund = await TicketPaymentRefund.findOne({
        refundId: obj?.refundId,
        paymentIntentId,
        isDelete: false,
      })
        .populate("eventId", "_id title startDate startTime -relation_id")
        .lean();

      const findUser = await airTableSync
        .findById(userId, {
          _id: 1,
          first_name: 1,
          last_name: 1,
          display_name: 1,
          "Preferred Email": 1,
        })
        .lean();

      if (
        findTicketRefund &&
        findTicketRefund.refundId &&
        findTicketRefund.refundStatus === "partial_payment_refund_initiated"
      ) {
        const refundId = findTicketRefund?.refundId;
        const refundIdTemp = findTicketRefund?._id;

        const refund = await stripe.refunds.retrieve(refundId, {
          stripeAccount: connectedAccountId,
        });

        if (
          refund?.destination_details &&
          refund?.destination_details?.card &&
          refund?.destination_details?.card?.type == "refund"
        ) {
          const updateTicketPaymentRefund =
            await TicketPaymentRefund.findByIdAndUpdate(
              refundIdTemp,
              {
                $set: {
                  refundedDate: new Date(),
                  refundStatus: refund?.destination_details?.card?.type,
                  refundObj: refund,
                },
              },
              { new: true }
            );

          if (updateTicketPaymentRefund) {
            const updatePaymentIntent =
              await ticketPaymentIntent.findByIdAndUpdate(paymentIntentId, {
                $set: {
                  status: refund?.destination_details?.card?.type,
                },
              });

            if (updatePaymentIntent) {
              await ticketPurchaseV2.updateMany(
                {
                  paymentIntentId,
                  ticketOrderStatus: "partial_payment_refund_initiated",
                },
                {
                  $set: {
                    ticketOrderStatus: updateTicketPaymentRefund.refundStatus,
                  },
                }
              );

              await TicketAddonPurchase.updateMany(
                {
                  paymentIntentId,
                  addonOrderStatus: "partial_payment_refund_initiated",
                },
                {
                  $set: {
                    addonOrderStatus: updateTicketPaymentRefund.refundStatus,
                  },
                }
              );

              await AddonPurchaseRefund.updateMany(
                {
                  paymentIntentId,
                  refundStatus: "partial_payment_refund_initiated",
                },
                {
                  $set: {
                    refundStatus: updateTicketPaymentRefund.refundStatus,
                  },
                }
              );
            }
          }

          if (findTicketRefund.eventId && findUser) {
            const userData = findUser;
            const eventData = findTicketRefund.eventId;
            const communityLogo = req.currentEdge.relation_id.logo
              ? req.currentEdge.relation_id.logo
              : "";
            const mailData = {
              email: `${userData["Preferred Email"]}`,
              subject: `Ticket Payment Refunded`,
              html: `<div style="max-width: 600px; margin: 0 auto; font-family: Arial, sans-serif; background-color: #f5f5f5; padding: 20px; border-radius: 10px;">
                  <div style="text-align: center; margin-bottom: 20px;">
                    <img src=${communityLogo} alt="Your Ticketing Platform Logo" style="max-width: 200px;">
                  </div>
                  <div style="background-color: #ffffff; padding: 30px; border-radius: 10px; box-shadow: 0px 3px 6px rgba(0, 0, 0, 0.1);">
                    <h2 style="text-align: center; margin-bottom: 20px; color: #333;">Ticket Payment Refunded</h2>
                    <p style="font-size: 16px; line-height: 1.6; margin-bottom: 20px;">
                      Dear ${
                        userData.first_name + " " + userData.last_name
                      },<br />
                    </p>
                    <p style="font-size: 16px; line-height: 1.6; margin-bottom: 20px;">
                    We have received your request to cancel your ticket for the ${
                      eventData.title
                    } scheduled on ${
                eventData.startDate + " " + eventData.startTime
              }. Your Ticket Payment Refund has been successfully Refunded.
                    </p>
                    <p style="font-size: 16px; line-height: 1.6; margin-bottom: 20px;">
                      If you have any questions or need further assistance, feel free to reply to this email.
                    </p>
                    <p style="font-size: 16px; line-height: 1.6; margin-bottom: 0;">
                      See you soon!<br>
                      Your Ticketing Platform Team
                    </p>
                  </div>
                </div>`,
                relationId:req.currentEdge.relation_id._id ? req.currentEdge.relation_id._id : "",
                customsmtp:req.currentEdge.relation_id.customSMTP ? req.currentEdge.relation_id.customSMTP : false
            };
            await sendEmail(mailData);
          }

          return {
            status: true,
            message: "Refund Successful!",
            data: updateTicketPaymentRefund,
          };
        }
      }
    } else {
      return {
        status: false,
        message: "Invalid metadata!",
      };
    }
  } catch (error) {
    await debugErrorLogs.createErrorLogs(error, "ticketBookByUser", {});
    return {
      status: false,
      message: "Internal server error!",
      error: error?.code ?? error?.type,
    };
  }
};

//* Payment Refunds Retrieve from the webhooks for the single Ticket
exports.addonRefundsRetrieve = async (obj) => {
  try {
    const paymentIntentId = obj.paymentIntentId;
    const metadata = JSON.parse(obj?.metadata?.query);

    if (metadata) {
      const ticketAddonPurchaseId = metadata.ticketAddonPurchaseId;
      const connectedAccountId = metadata?.connectedAccountId;

      const fetchAddonPurchaseTicket = await TicketAddonPurchase.findOne({
        _id: ticketAddonPurchaseId,
        ticketOrderStatus: "partial_payment_refund_initiated",
        isDelete: false,
      })
        .populate("paymentRefundId", {
          refundObj: 0,
        })
        .populate("paymentIntentId", {
          paymentIntent: 0,
        })
        .populate("eventId", "_id title startDate startTime -relation_id")
        .populate("userId", {
          _id: 1,
          first_name: 1,
          last_name: 1,
          display_name: 1,
          "Preferred Email": 1,
        })
        .lean();

      if (
        fetchAddonPurchaseTicket &&
        fetchAddonPurchaseTicket.paymentRefundId &&
        fetchAddonPurchaseTicket.paymentRefundId.refundId
      ) {
        const refundId = fetchAddonPurchaseTicket?.paymentRefundId?.refundId;
        const refundIdTemp = fetchAddonPurchaseTicket?.paymentRefundId?._id;

        const refund = await stripe.refunds.retrieve(refundId, {
          stripeAccount: connectedAccountId,
        });

        if (
          refund.destination_details &&
          refund.destination_details.card &&
          (refund.destination_details.card.type === "refund" ||
            refund.destination_details.card.type === "reversal")
        ) {
          const updateAddonPurchaseRefund =
            await AddonPurchaseRefund.findByIdAndUpdate(
              refundIdTemp,
              {
                $set: {
                  refundedDate: new Date(),
                  refundStatus: refund.destination_details.card.type,
                  refundObj: refund,
                },
              },
              { new: true }
            );

          if (updateAddonPurchaseRefund) {
            await TicketAddonPurchase.findByIdAndUpdate(
              fetchAddonPurchaseTicket._id,
              {
                $set: {
                  addonOrderStatus: updateAddonPurchaseRefund.refundStatus,
                },
              }
            );
            if (
              fetchAddonPurchaseTicket?.paymentIntentId?.purchaseQuantity ===
                0 &&
              !fetchAddonPurchaseTicket?.paymentIntentId?.addOnAmount
            ) {
              await ticketPaymentIntent.findByIdAndUpdate(
                fetchAddonPurchaseTicket?.paymentIntentId?._id,
                {
                  $set: {
                    status: updateAddonPurchaseRefund.refundStatus,
                  },
                }
              );
            }
          }

          if (fetchAddonPurchaseTicket.userId) {
            const userData = fetchAddonPurchaseTicket.userId;
            const eventData = fetchAddonPurchaseTicket.eventId;
            const communityLogo = req.currentEdge.relation_id.logo
              ? req.currentEdge.relation_id.logo
              : "";
            const mailData = {
              email: `${userData["Preferred Email"]}`,
              subject: `Ticket Payment Refunded`,
              html: `<div style="max-width: 600px; margin: 0 auto; font-family: Arial, sans-serif; background-color: #f5f5f5; padding: 20px; border-radius: 10px;">
                  <div style="text-align: center; margin-bottom: 20px;">
                    <img src=${communityLogo} alt="Your Ticketing Platform Logo" style="max-width: 200px;">
                  </div>
                  <div style="background-color: #ffffff; padding: 30px; border-radius: 10px; box-shadow: 0px 3px 6px rgba(0, 0, 0, 0.1);">
                    <h2 style="text-align: center; margin-bottom: 20px; color: #333;">Ticket Payment Refunded</h2>
                    <p style="font-size: 16px; line-height: 1.6; margin-bottom: 20px;">
                      Dear ${
                        userData.first_name + " " + userData.last_name
                      },<br />
                    </p>
                    <p style="font-size: 16px; line-height: 1.6; margin-bottom: 20px;">
                    We have received your request to cancel your ticket for the ${
                      eventData.title
                    } scheduled on ${
                eventData.startDate + " " + eventData.startTime
              }. Your Ticket Payment Refund has been successfully Refunded.
                    </p>
                    <p style="font-size: 16px; line-height: 1.6; margin-bottom: 20px;">
                      If you have any questions or need further assistance, feel free to reply to this email.
                    </p>
                    <p style="font-size: 16px; line-height: 1.6; margin-bottom: 0;">
                      See you soon!<br>
                      Your Ticketing Platform Team
                    </p>
                  </div>
                </div>`,
            };
            await sendEmail(mailData);
          }

          return {
            status: true,
            message: "Refund Successful!",
            data: updateAddonPurchaseRefund,
          };
        }
      }
    } else {
      return {
        status: false,
        message: "Invalid metadata!",
      };
    }
  } catch (error) {
    await debugErrorLogs.createErrorLogs(error, "ticketBookByUser", {});
    return {
      status: false,
      message: "Internal server error!",
      error: error?.code ?? error?.type,
    };
  }
};

//* Payment refund retrive when the other addon refund by the single ticket remove
exports.otherAddonRefundsRetrieveForTicket = async (obj) => {
  try {
    const paymentIntentId = obj.paymentIntentId;
    const metadata = JSON.parse(obj?.metadata?.query);

    if (metadata) {
      const connectedAccountId = metadata?.connectedAccountId;
      const paymentIntentId = metadata?.paymentIntentId;
      const userId = metadata?.userId;
      const eventId = metadata.eventId;
      const relation_id = metadata.relation_id;

      const fetchAddonPurchaseTicketData = await TicketAddonPurchase.find({
        paymentIntentId,
        addonOrderStatus: "partial_payment_refund_initiated",
        isDelete: false,
      })
        .populate("paymentRefundId", {
          refundObj: 0,
        })
        .populate("paymentIntentId", {
          paymentIntent: 0,
        })
        .lean();

      const addonPurchaseRefundData = [];
      for await (let fetchAddonPurchaseTicket of fetchAddonPurchaseTicketData) {
        if (
          fetchAddonPurchaseTicket &&
          fetchAddonPurchaseTicket.paymentRefundId &&
          fetchAddonPurchaseTicket.paymentRefundId.refundId
        ) {
          const refundId = fetchAddonPurchaseTicket?.paymentRefundId?.refundId;
          const refundIdTemp = fetchAddonPurchaseTicket?.paymentRefundId?._id;

          const refund = await stripe.refunds.retrieve(refundId, {
            stripeAccount: connectedAccountId,
          });

          if (
            refund.destination_details &&
            refund.destination_details.card &&
            (refund.destination_details.card.type === "refund" ||
              refund.destination_details.card.type === "reversal")
          ) {
            const updateAddonPurchaseRefund =
              await AddonPurchaseRefund.findByIdAndUpdate(
                refundIdTemp,
                {
                  $set: {
                    refundedDate: new Date(),
                    refundStatus: refund.destination_details.card.type,
                    refundObj: refund,
                  },
                },
                { new: true }
              );
            if (updateAddonPurchaseRefund) {
              await TicketAddonPurchase.findByIdAndUpdate(
                fetchAddonPurchaseTicket._id,
                {
                  $set: {
                    addonOrderStatus: updateAddonPurchaseRefund.refundStatus,
                  },
                }
              );
              if (
                fetchAddonPurchaseTicket?.paymentIntentId?.purchaseQuantity ===
                  0 &&
                !fetchAddonPurchaseTicket?.paymentIntentId?.addOnAmount
              ) {
                await ticketPaymentIntent.findByIdAndUpdate(
                  fetchAddonPurchaseTicket?.paymentIntentId?._id,
                  {
                    $set: {
                      status: updateAddonPurchaseRefund.refundStatus,
                    },
                  }
                );
              }
            }

            addonPurchaseRefundData.push(updateAddonPurchaseRefund);
          }
        }
      }

      // if (addonPurchaseRefundData.length && userId && eventId) {
      //   const userData = await airTableSync.findById(userId, {
      //     _id: 1,
      //     first_name: 1,
      //     last_name: 1,
      //     display_name: 1,
      //     "Preferred Email": 1,
      //   });
      //   const eventData = await event.findById(eventId, {
      //     _id: 1,
      //     title: 1,
      //     startDate: 1,
      //     startTime: 1,
      //     relation_id: 0,
      //   });
      //   let community = await Communities.findById({
      //     _id: relation_id,
      //   });

      //   const communitiyLogo = community?.logo
      //     ? `https://${AWS_BUCKET}.s3.us-east-2.amazonaws.com/groupos/${community._id}/${community.logo}`
      //     : "https://mds-community.s3.us-east-2.amazonaws.com/uploads/groupos-util/groupos_logo.png";

      //   const mailData = {
      //     email: `${userData["Preferred Email"]}`,
      //     subject: `Ticket Payment Refunded`,
      //     html: `<div style="max-width: 600px; margin: 0 auto; font-family: Arial, sans-serif; background-color: #f5f5f5; padding: 20px; border-radius: 10px;">
      //           <div style="text-align: center; margin-bottom: 20px;">
      //             <img src=${communitiyLogo} alt="Your Ticketing Platform Logo" style="max-width: 200px;">
      //           </div>
      //           <div style="background-color: #ffffff; padding: 30px; border-radius: 10px; box-shadow: 0px 3px 6px rgba(0, 0, 0, 0.1);">
      //             <h2 style="text-align: center; margin-bottom: 20px; color: #333;">Ticket Payment Refunded</h2>
      //             <p style="font-size: 16px; line-height: 1.6; margin-bottom: 20px;">
      //               Dear ${
      //                 userData.first_name + " " + userData.last_name
      //               },<br />
      //             </p>
      //             <p style="font-size: 16px; line-height: 1.6; margin-bottom: 20px;">
      //             We have received your request to cancel your ticket for the ${
      //               eventData.title
      //             } scheduled on ${
      //       eventData.startDate + " " + eventData.startTime
      //     }. Your Ticket Payment Refund has been successfully Refunded.
      //             </p>
      //             <p style="font-size: 16px; line-height: 1.6; margin-bottom: 20px;">
      //               If you have any questions or need further assistance, feel free to reply to this email.
      //             </p>
      //             <p style="font-size: 16px; line-height: 1.6; margin-bottom: 0;">
      //               See you soon!<br>
      //               Your Ticketing Platform Team
      //             </p>
      //           </div>
      //         </div>`,
      //   };
      //   const data = await sendEmail(mailData);
      // }

      return {
        status: true,
        message: "Refund Successful!",
        data: addonPurchaseRefundData,
      };
    } else {
      return {
        status: false,
        message: "Invalid metadata!",
      };
    }
  } catch (error) {
    await debugErrorLogs.createErrorLogs(error, "ticketBookByUser", {});
    return {
      status: false,
      message: "Internal server error!",
      error: error?.code ?? error?.type,
    };
  }
};

//* Event ticket list of user
exports.getAllBookTicketsSuggestionListforUser = async (req, res) => {
  try {
    const userId = req.authUserId;

    // user Ticket list
    let match = {
      userId: new ObjectId(userId),
      relation_id: new ObjectId(req.relation_id),
      isDelete: false,
    };

    const aggregate = [
      {
        $match: match,
      },
      {
        $group: {
          _id: "$eventId",
        },
      },
      {
        $lookup: {
          from: "events",
          localField: "_id",
          foreignField: "_id",
          as: "eventId",
          pipeline: [
            ...(req?.query?.search
              ? [
                  {
                    $match: {
                      $or: [
                        {
                          title: {
                            $regex: ".*" + req.query.search + ".*",
                            $options: "i",
                          },
                        },
                      ],
                    },
                  },
                ]
              : []),
            {
              $project: {
                title: 1,
              },
            },
          ],
        },
      },
      {
        $unwind: {
          path: "$eventId",
          preserveNullAndEmptyArrays: false,
        },
      },
      {
        $sort: {
          createdAt: -1,
        },
      },
    ];

    const userTicket = await ticketPurchaseV2.aggregate(aggregate);

    if (userTicket.length === 0) {
      return res
        .status(200)
        .send({ status: false, message: `Ticket list is not found!` });
    } else {
      return res.status(200).send({
        status: true,
        message: "Tickets listing retrieved successfully",
        data: userTicket,
      });
    }
  } catch (error) {
    await debugErrorLogs.createErrorLogs(error, "ticketBookByUser", {});
    return res
      .status(500)
      .json({ status: false, message: "Internal server error!", error: error });
  }
};

//* Get the user event wise tickets
exports.fetchUserTicketByEventId = async (req, res) => {
  try {
    const { eventId } = req.params;
    const userId = req?.userId;

    const aggregate = [
      {
        $match: {
          userId: ObjectId(userId),
          eventId: ObjectId(eventId),
          isDelete: false,
        },
      },
      {
        $lookup: {
          from: "event_tickets",
          localField: "ticketId",
          foreignField: "_id",
          as: "ticketId",
        },
      },
      {
        $unwind: {
          path: "$ticketId",
          preserveNullAndEmptyArrays: true,
        },
      },
      {
        $lookup: {
          from: "ticket_payment_refunds",
          localField: "paymentRefundId",
          foreignField: "_id",
          as: "paymentRefundId",
          pipeline: [
            {
              $project: {
                refundObj: 0,
              },
            },
          ],
        },
      },
      {
        $unwind: {
          path: "$paymentRefundId",
          preserveNullAndEmptyArrays: true,
        },
      },
      {
        $lookup: {
          from: "ticket_submissions",
          localField: "_id",
          foreignField: "ticketPurchaseId",
          as: "ticket_submissions",
        },
      },
      {
        $unwind: {
          path: "$ticket_submissions",
          preserveNullAndEmptyArrays: true,
        },
      },
      {
        $lookup: {
          from: "ticket_addon_purchases",
          localField: "_id",
          foreignField: "ticketPurchaseId",
          as: "ticket_addon_purchases",
          pipeline: [
            {
              $match: {
                isDelete: false,
              },
            },
            {
              $lookup: {
                from: "event_addon_v2",
                localField: "addonId",
                foreignField: "_id",
                as: "addonData",
              },
            },
            {
              $unwind: {
                path: "$addonData",
                preserveNullAndEmptyArrays: true,
              },
            },
            {
              $lookup: {
                from: "event_addon_groups",
                localField: "addonData.addonGroupId", // Join on addonGroupId inside addonData
                foreignField: "_id",
                as: "event_addon_group_data",
              },
            },
            {
              $unwind: {
                path: "$event_addon_group_data",
                preserveNullAndEmptyArrays: true,
              },
            },
            {
              $addFields: {
                addon: {
                  _id: "$addonData._id",
                  name: "$addonData.name",
                  price: "$addonData.price",
                  image: "$addonData.image",
                  isSubAddon: "$addonData.isSubAddon",
                },
                event_addon_group_data: [
                  {
                    addonGroupName: "$event_addon_group_data.name",
                  },
                ],
              },
            },
            {
              $project: {
                addon: 1,
                event_addon_group_data: 1,
                // Include other fields from ticket_addon_purchases as necessary
                relation_id: 1,
                eventId: 1,
                userId: 1,
                userParticipantId: 1,
                paymentIntentId: 1,
                ticketId: 1,
                ticketPurchaseId: 1,
                price: 1,
                purchaseDate: 1,
                addonOrderStatus: 1,
                addonCancelleDate: 1,
                refundedBy: 1,
                isFreeAddon: 1,
                isDelete: 1,
                __v: 1,
                createdAt: 1,
                updatedAt: 1,
              },
            },
          ],
        },
      },
      {
        $sort: {
          createdAt: -1,
        },
      },
    ];

    let data = await ticketPurchaseV2.aggregate(aggregate);

    data = await Promise.all(
      data.map(async (value) => ({
        ...value,
        ticketId: {
          ...value.ticketId,
          thumbnail: await s3fileUploadService.generatePresignedUrl({
            key: value?.ticketId?.thumbnail,
          }),
        },
        ticket_addon_purchases: await Promise.all(
          value.ticket_addon_purchases.map(async (addon) => ({
            ...addon,
            addon: {
              ...addon.addon,
              image: addon.addon.image
                ? await s3fileUploadService.generatePresignedUrl({
                    key: addon.addon.image,
                  })
                : null,
            },
          }))
        ),
      }))
    );

    return res.status(200).json({
      status: data.length ? true : false,
      message: "Fetched user's purchased tickets!",
      data: data,
    });
  } catch (error) {
    return res
      .status(500)
      .json({ status: false, message: "Internal server error!", error: error });
  }
};

//* Get the user ticket details by event id for the public
exports.fetchUserTicketByEventIdV2 = async (req, res) => {
  try {
    const { eventId } = req.params;
    let userId = req.query.userId;

    const aggregate = [
      {
        $match: {
          userId: ObjectId(userId),
          eventId: ObjectId(eventId),
          isDelete: false,
        },
      },
      {
        $lookup: {
          from: "event_tickets",
          localField: "ticketId",
          foreignField: "_id",
          as: "ticketId",
        },
      },
      {
        $unwind: {
          path: "$ticketId",
          preserveNullAndEmptyArrays: true,
        },
      },
      {
        $lookup: {
          from: "ticket_payment_refunds",
          localField: "paymentRefundId",
          foreignField: "_id",
          as: "paymentRefundId",
          pipeline: [
            {
              $project: {
                refundObj: 0,
              },
            },
          ],
        },
      },
      {
        $unwind: {
          path: "$paymentRefundId",
          preserveNullAndEmptyArrays: true,
        },
      },
      {
        $lookup: {
          from: "ticket_submissions",
          localField: "_id",
          foreignField: "ticketPurchaseId",
          as: "ticket_submissions",
        },
      },
      {
        $unwind: {
          path: "$ticket_submissions",
          preserveNullAndEmptyArrays: true,
        },
      },
      {
        $sort: {
          createdAt: -1,
        },
      },
    ];

    let data = await ticketPurchaseV2.aggregate(aggregate);

    data = await Promise.all(
      data.map(async (value) => {
        const thumbnailUrl = await s3fileUploadService.generatePresignedUrl({
          key: value?.ticketId?.thumbnail,
        });
        return {
          ...value,
          ticketId: {
            ...value.ticketId,
            thumbnail: thumbnailUrl,
          },
        };
      })
    );

    return res.status(200).json({
      status: data.length ? true : false,
      message: "Fetched user's purchased tickets!",
      data: data,
    });
  } catch (error) {
    return res
      .status(500)
      .json({ status: false, message: "Internal server error!", error: error });
  }
};

//* User event wise ticket calculation
exports.calculateUserTicketByEventId = async (req, res) => {
  try {
    const { eventId } = req.params;
    const userId = req?.userId;

    if (!eventId) {
      return res
        .status(400)
        .json({ status: false, message: "Event Id is required!", data: {} });
    }

    const aggregate = [
      {
        $match: {
          userId: ObjectId(userId),
          relation_id: ObjectId(req.relation_id),
          eventId: ObjectId(eventId),
          ticketOrderStatus: "succeeded",
          isDelete: false,
        },
      },
      {
        $facet: {
          ticketData: [
            {
              $lookup: {
                from: "event_tickets",
                localField: "ticketId",
                foreignField: "_id",
                as: "ticketDetails",
              },
            },
            {
              $unwind: "$ticketDetails",
            },
            {
              $lookup: {
                from: "ticket_addon_purchases",
                localField: "_id",
                foreignField: "ticketPurchaseId",
                as: "ticket_addon_purchases_data",
                pipeline: [
                  {
                    $match: {
                      addonOrderStatus: "succeeded",
                      isDelete: false,
                    },
                  },
                  {
                    $group: {
                      _id: "$ticketPurchaseId",
                      addonAmount: {
                        $sum: "$price",
                      },
                      addonCount: {
                        $sum: 1,
                      },
                    },
                  },
                  {
                    $project: {
                      addonCount: 1,
                      addonAmount: {
                        $round: [
                          {
                            $ifNull: ["$addonAmount", 0],
                          },
                          2,
                        ],
                      },
                    },
                  },
                ],
              },
            },
            {
              $unwind: {
                path: "$ticket_addon_purchases_data",
                preserveNullAndEmptyArrays: true,
              },
            },
            {
              $group: {
                _id: "$ticketDetails.name",
                ticketCount: {
                  $sum: 1,
                },
                addonCount: {
                  $sum: "$ticket_addon_purchases_data.addonCount",
                },
                totalTicketAmount: {
                  $sum: "$price",
                },
                addonAmount: {
                  $sum: "$ticket_addon_purchases_data.addonAmount",
                },
              },
            },
            {
              $project: {
                _id: 0,
                ticketName: "$_id",
                ticketCount: 1,
                addonCount: 1,
                totalTicketAmount: 1,
                addonAmount: {
                  $round: [
                    {
                      $ifNull: ["$addonAmount", 0],
                    },
                    2,
                  ],
                },
              },
            },
          ],
          stripeFeeData: [
            {
              $lookup: {
                from: "ticket_payment_intents",
                localField: "paymentIntentId",
                foreignField: "_id",
                as: "paymentIntent",
              },
            },
            {
              $unwind: "$paymentIntent",
            },
            {
              $group: {
                _id: "$paymentIntent._id",
                stripeProcessingFee: {
                  $first: "$paymentIntent.stripe_processing_fee",
                },
              },
            },
            {
              $group: {
                _id: null,
                totalStripeProcessingFee: {
                  $sum: "$stripeProcessingFee",
                },
              },
            },
          ],
        },
      },
      {
        $project: {
          tickets: "$ticketData",
          totalStripeProcessingFee: {
            $arrayElemAt: ["$stripeFeeData.totalStripeProcessingFee", 0],
          },
        },
      },
      {
        $addFields: {
          totalTicketAmount: {
            $sum: "$tickets.totalTicketAmount",
          },
          totalAddonCount: {
            $sum: "$tickets.addonCount", // Add totalAddonCount here
          },
        },
      },
      {
        $addFields: {
          totalAddonAmount: {
            $ifNull: [
              {
                $reduce: {
                  input: "$tickets",
                  initialValue: 0,
                  in: {
                    $add: [
                      "$$value",
                      {
                        $ifNull: ["$$this.addonAmount", 0],
                      },
                    ],
                  },
                },
              },
              0,
            ],
          },
        },
      },
      {
        $addFields: {
          grandTotalAmount: {
            $add: [
              "$totalTicketAmount",
              "$totalStripeProcessingFee",
              "$totalAddonAmount",
            ],
          },
        },
      },
      {
        $project: {
          tickets: {
            ticketName: 1,
            ticketCount: 1,
            totalTicketAmount: 1, // Only include ticket-specific data
          },
          totalTicketAmount: 1,
          totalAddonCount: 1, // Include totalAddonCount in the final output
          totalAddonAmount: 1,
          totalStripeProcessingFee: 1,
          grandTotalAmount: 1,
        },
      },
    ];

    let data = await ticketPurchaseV2.aggregate(aggregate);
    return res.status(200).json({
      status: true,
      message: "Fetched user's purchased tickets!",
      data: data,
    });
  } catch (error) {
    return res
      .status(500)
      .json({ status: false, message: "Internal server error!", error: error });
  }
};

//* Get purchased ticket by event attendee
exports.fetchTicketByAttendee = async (req, res) => {
  try {
    const { userId, eventId } = req.body;
    let condition = {
      userId: ObjectId(userId),
      eventId: ObjectId(eventId),
      relation_id: ObjectId(req.relation_id),
      ticketOrderStatus: "succeeded",
      isDelete: false,
    };

    const pipeline = [
      { $match: condition },
      {
        $group: {
          _id: "$ticketId",
          userId: {
            $first: "$userId",
          },
          purchaseQuantity: {
            $sum: 1,
          },
          amount: {
            $sum: "$price",
          },
          ticketId: {
            $first: "$ticketId",
          },
          userParticipantId: {
            $first: "$userParticipantId",
          },
          relation_id: {
            $first: "$relation_id",
          },
          paymentIntentId: {
            $first: "$paymentIntentId",
          },
          purchaseDate: {
            $first: "$purchaseDate",
          },
          ticketOrderStatus: {
            $first: "$ticketOrderStatus",
          },
        },
      },
      {
        $lookup: {
          from: "event_tickets",
          localField: "ticketId",
          foreignField: "_id",
          as: "ticketData",
          pipeline: [
            {
              $match: {
                isDelete: false,
              },
            },
            {
              $project: {
                name: 1,
                description: 1,
                thumbnail: 1,
                type: 1,
                ticketAccess: 1,
              },
            },
          ],
        },
      },
      {
        $unwind: {
          path: "$ticketData",
          preserveNullAndEmptyArrays: true,
        },
      },
      {
        $project: {
          userId: 1,
          purchaseQuantity: 1,
          amount: 1,
          ticketId: 1,
          userParticipantId: 1,
          relation_id: 1,
          paymentIntentId: 1,
          purchaseDate: 1,
          ticketOrderStatus: 1,
          ticket: "$ticketData.name",
          description: "$ticketData.description",
          thumbnail: "$ticketData.thumbnail",
          ticketAccess: "$ticketData.ticketAccess",
        },
      },
      {
        $sort: {
          purchaseDate: 1,
        },
      },
    ];
    const ticketData = await ticketPurchaseV2.aggregate(pipeline);
    return res.status(200).send({
      status: true,
      message: "Success",
      data: ticketData,
    });
  } catch (error) {
    await debugErrorLogs.createErrorLogs(error, "fetchTicketByAttendee", {});
    return res
      .status(500)
      .json({ status: false, message: "Internal server error!", error: error });
  }
};

//* Get ticket Info, sold ticket and no.of user which is purchased ticket(By event Id)
exports.fetchTicketAndUserByEvent = async (req, res) => {
  try {
    const { eventId } = req.params;
    let condition = {
      eventId: ObjectId(eventId),
      relation_id: ObjectId(req.relation_id),
      isDelete: false,
    };

    let pipeline = [
      {
        $match: condition,
      },
      {
        $lookup: {
          from: "ticket_purchases",
          localField: "_id",
          foreignField: "ticketId",
          as: "purchase_ticket",
          pipeline: [
            {
              $match: {
                $expr: {
                  $and: [
                    { $eq: ["$isDelete", false] },
                    { $eq: ["$ticketOrderStatus", "success"] },
                  ],
                },
              },
            },
            {
              $lookup: {
                from: "event_participant_attendees",
                localField: "userParticipantId",
                foreignField: "_id",
                as: "event_participant_attendees",
                pipeline: [
                  {
                    $match: {
                      $expr: {
                        $and: [
                          {
                            $eq: ["$isDelete", false],
                          },
                        ],
                      },
                    },
                  },
                ],
              },
            },
            {
              $unwind: {
                path: "$event_participant_attendees",
                preserveNullAndEmptyArrays: false,
              },
            },
            {
              $sort: {
                purchaseDate: -1,
              },
            },
            {
              $group: {
                _id: null,
                purchaseQuantity: {
                  $first: "$purchaseQuantity",
                },
                userId: {
                  $addToSet: "$userId",
                },
                sold_ticket: {
                  $sum: "$purchaseQuantity",
                },
              },
            },
          ],
        },
      },
      {
        $unwind: {
          path: "$purchase_ticket",
          preserveNullAndEmptyArrays: false,
        },
      },
      {
        $addFields: {
          TotalUser: {
            $size: "$purchase_ticket.userId",
          },
          sold_ticket: "$purchase_ticket.sold_ticket",
        },
      },
      {
        $project: {
          _id: 0,
          name: 1,
          TotalUser: 1,
          sold_ticket: 1,
        },
      },
    ];

    const ticketData = await eventTicket.aggregate(pipeline);
    if (!ticketData || ticketData.length == 0) {
      return res.status(200).send({
        status: false,
        message: `There is no information of ticket for this event!`,
      });
    } else {
      return res.status(200).send({
        status: true,
        message: `Fetch event ticket purchased qty and no of user purchased`,
        data: ticketData,
      });
    }
  } catch (error) {
    await debugErrorLogs.createErrorLogs(
      error,
      "fetchTicketAndUserByEvent",
      {}
    );
    return res
      .status(500)
      .json({ status: false, message: "Internal server error!", error: error });
  }
};

//* API for the get the user order list in admin side
exports.getAllBookTicketsListforAdmin = async (req, res) => {
  try {
    let page = req.query.page ? parseInt(req.query.page) : 1;
    let limit = req.query.limit ? parseInt(req.query.limit) : 10;
    let skip = (page - 1) * limit;

    let sortBy = req?.query?.sortBy;
    sortBy =
      sortBy === "email"
        ? "Preferred Email"
        : sortBy === "username"
        ? "display_name"
        : sortBy === "ticket_name"
        ? "name"
        : "createdAt";

    let sortOrder = req?.query?.sortOrder === "asc" ? 1 : -1;

    let sort = { [`${sortBy}`]: sortOrder };

    let match = {
      eventId: ObjectId(req.query.eventId),
      relation_id: ObjectId(req.relation_id),
      ...(req.query.filterByTicketId && {
        ticketId: ObjectId(req.query.filterByTicketId),
      }),
      ...(req.query?.filterByPurchaseDate && {
        purchaseDate: {
          $gte: moment(req.query?.filterByPurchaseDate).startOf("day").toDate(),
          $lte: moment(req.query?.filterByPurchaseDate).endOf("day").toDate(),
        },
      }),
      ...(req.query.filterByStatus && {
        ticketOrderStatus: req.query.filterByStatus,
      }),
      ...(req.query.filterByUserId && {
        userId: ObjectId(req.query.filterByUserId),
      }),
      isDelete: false,
    };

    let pipeline = [
      {
        $match: match,
      },
      {
        $lookup: {
          from: "event_tickets",
          localField: "ticketId",
          foreignField: "_id",
          as: "ticketId",
          pipeline: [
            {
              $project: {
                name: 1,
                type: 1,
              },
            },
          ],
        },
      },
      {
        $unwind: {
          path: "$ticketId",
          preserveNullAndEmptyArrays: false,
        },
      },
      {
        $lookup: {
          from: "ticket_addon_purchases",
          localField: "_id",
          foreignField: "ticketPurchaseId",
          as: "ticket_addon_purchases_data",
          pipeline: [
            {
              $match: {
                isDelete: false,
              },
            },
          ],
        },
      },
      {
        $group: {
          _id: "$userId",
          ticket: {
            $push: "$$ROOT",
          },
        },
      },
      {
        $lookup: {
          from: "airtable-syncs",
          localField: "_id",
          foreignField: "_id",
          as: "user",
          pipeline: [
            ...(req.query.search
              ? [
                  {
                    $match: {
                      $or: [
                        {
                          first_name: {
                            $regex: ".*" + req.query.search + ".*",
                            $options: "i",
                          },
                        },
                        {
                          last_name: {
                            $regex: ".*" + req.query.search + ".*",
                            $options: "i",
                          },
                        },
                        {
                          "Preferred Email": {
                            $regex: ".*" + req.query.search + ".*",
                            $options: "i",
                          },
                        },
                        {
                          display_name: {
                            $regex: ".*" + req.query.search + ".*",
                            $options: "i",
                          },
                        },
                      ],
                    },
                  },
                ]
              : []),
            {
              $project: {
                first_name: 1,
                last_name: 1,
                display_name: 1,
                "Preferred Email": 1,
              },
            },
          ],
        },
      },
      {
        $unwind: {
          path: "$user",
          preserveNullAndEmptyArrays: false,
        },
      },
      {
        $project: {
          ticket: 1,
          first_name: "$user.first_name",
          last_name: "$user.last_name",
          display_name: "$user.display_name",
          "Preferred Email": "$user.Preferred Email",
        },
      },
    ];

    let [all, count] = await Promise.all([
      ticketPurchaseV2.aggregate([
        ...pipeline,
        { $sort: sort },
        { $skip: skip },
        { $limit: limit },
      ]),
      ticketPurchaseV2.aggregate(pipeline),
    ]);

    return res.status(200).send({
      status: true,
      message: "ticket data get successfully!",
      data: all,
      totalPages: Math.ceil(count.length / limit) || 0,
      currentPage: page,
      totalTicket: count.length || 0,
    });
  } catch (error) {
    await debugErrorLogs.createErrorLogs(error, "getTicketSuggestionList", {});
    return res.status(500).json({ status: false, message: `${error.message}` });
  }
};


//* API for the get the user order suggestion list in admin side
exports.getAllBookTicketsSuggestionListforAdmin = async (req, res) => {
  try {
    let sortBy = req?.query?.sortBy;
    sortBy =
      sortBy === "email"
        ? "Preferred Email"
        : sortBy === "username"
        ? "display_name"
        : sortBy === "ticket_name"
        ? "name"
        : "createdAt";

    let sortOrder = req?.query?.sortOrder === "asc" ? 1 : -1;

    let sort = { [`${sortBy}`]: sortOrder };

    let match = {
      eventId: ObjectId(req.query.eventId),
      relation_id: ObjectId(req.relation_id),
      ...(req.query.filterByTicketId && {
        ticketId: ObjectId(req.query.filterByTicketId),
      }),
      ...(req.query?.filterByPurchaseDate && {
        purchaseDate: {
          $gte: moment(req.query?.filterByPurchaseDate).startOf("day").toDate(),
          $lte: moment(req.query?.filterByPurchaseDate).endOf("day").toDate(),
        },
      }),
      ...(req.query.filterByStatus && {
        ticketOrderStatus: req.query.filterByStatus,
      }),
      ...(req.query.filterByUserId && {
        userId: ObjectId(req.query.filterByUserId),
      }),
      isDelete: false,
    };

    let pipeline = [
      {
        $match: match,
      },
      {
        $lookup: {
          from: "event_tickets",
          localField: "ticketId",
          foreignField: "_id",
          as: "ticketId",
          pipeline: [
            {
              $project: {
                name: 1,
                type: 1,
              },
            },
          ],
        },
      },
      {
        $unwind: {
          path: "$ticketId",
          preserveNullAndEmptyArrays: false,
        },
      },
      {
        $lookup: {
          from: "ticket_addon_purchases",
          localField: "_id",
          foreignField: "ticketPurchaseId",
          as: "ticket_addon_purchases_data",
          pipeline: [
            {
              $match: {
                isDelete: false,
              },
            },
          ],
        },
      },
      {
        $group: {
          _id: "$userId",
          ticket: {
            $push: "$$ROOT",
          },
        },
      },
      {
        $lookup: {
          from: "airtable-syncs",
          localField: "_id",
          foreignField: "_id",
          as: "user",
          pipeline: [
            ...(req.query.search
              ? [
                  {
                    $match: {
                      $or: [
                        {
                          first_name: {
                            $regex: ".*" + req.query.search + ".*",
                            $options: "i",
                          },
                        },
                        {
                          last_name: {
                            $regex: ".*" + req.query.search + ".*",
                            $options: "i",
                          },
                        },
                        {
                          "Preferred Email": {
                            $regex: ".*" + req.query.search + ".*",
                            $options: "i",
                          },
                        },
                        {
                          display_name: {
                            $regex: ".*" + req.query.search + ".*",
                            $options: "i",
                          },
                        },
                      ],
                    },
                  },
                ]
              : []),
            {
              $project: {
                first_name: 1,
                last_name: 1,
                display_name: 1,
                "Preferred Email": 1,
              },
            },
          ],
        },
      },
      {
        $unwind: {
          path: "$user",
          preserveNullAndEmptyArrays: false,
        },
      },
      {
        $project: {
          ticket: 1,
          first_name: "$user.first_name",
          last_name: "$user.last_name",
          display_name: "$user.display_name",
          "Preferred Email": "$user.Preferred Email",
        },
      },
      {
        $sort: {
          first_name: 1 
        }
      }
    ];

    let all = await ticketPurchaseV2.aggregate([...pipeline, { $sort: sort }]);

    return res.status(200).send({
      status: true,
      message: "ticket data get successfully!",
      data: all,
    });
  } catch (error) {
    await debugErrorLogs.createErrorLogs(error, "getTicketSuggestionList", {});
    return res.status(500).json({ status: false, message: error.message });
  }
};

//* API for check the paymentIntent is available or not
exports.checkPaymentIntentAvailability = async (req, res) => {
  try {
    const { paymentIntentId } = req.params;
    if (!paymentIntentId) {
      return res
        .status(400)
        .json({ status: false, message: "Payment Intent ID is required!" });
    }

    const findPaymentIntent = await ticketPaymentIntent.findOne({
      paymentIntentId,
      status: "requires_payment_method",
      isDelete: false,
    });

    return res.status(200).json({
      status: findPaymentIntent ? true : false,
      message: findPaymentIntent
        ? "Payment Intent is found!"
        : "Payment Intent is not found",
      data: {},
    });
  } catch (error) {
    await debugErrorLogs.createErrorLogs(error, "getTicketSuggestionList", {});
    return res.status(500).json({ status: false, message: `${error.message}` });
  }
};

//* Function for the release the paid ticket Qty (Cron function)
exports.cronRemovePaymentIntent = async (req, res) => {
  try {
    const modifyDate = moment().subtract(15, "minute").toISOString();

    const findPaymentIntent = await ticketPaymentIntent.aggregate([
      {
        $match: {
          status: {
            $nin: [
              "succeeded",
              "refund",
              "partial_payment_refund_initiated",
              "cancelled",
            ],
          },
          isDelete: false,
          updatedAt: {
            $lte: new Date(modifyDate),
          },
        },
      },
      {
        $project: {
          paymentIntent: 0,
        },
      },
      {
        $lookup: {
          from: "ticket_purchase_v2",
          localField: "_id",
          foreignField: "paymentIntentId",
          as: "ticketData",
          pipeline: [
            {
              $match: {
                ticketOrderStatus: {
                  $nin: [
                    "succeeded",
                    "refund",
                    "partial_payment_refund_initiated",
                    "cancelled",
                  ],
                },
                isDelete: false,
              },
            },
            {
              $group: {
                _id: "$ticketId",
                qty: {
                  $sum: 1,
                },
                purchaseTicketId: {
                  $first: "$_id",
                },
              },
            },
          ],
        },
      },
    ]);

    for await (let data of findPaymentIntent) {
      if (data?.ticketData && data?.ticketData.length) {
        for await (let ticket of data.ticketData) {
          await EventTicket.findByIdAndUpdate(ticket?._id, {
            $inc: {
              availableQuantity: ticket?.qty,
            },
          });

          await TicketSubmission.findOneAndUpdate(
            {
              ticketPurchaseId: ticket?.purchaseTicketId,
              isDelete: false,
            },
            {
              $set: {
                isDelete: true,
              },
            }
          );
        }
      }

      await ticketPurchaseV2.updateMany(
        {
          paymentIntentId: data?._id,
          ticketOrderStatus: {
            $nin: [
              "succeeded",
              "refund",
              "partial_payment_refund_initiated",
              "cancelled",
            ],
          },
          isDelete: false,
        },
        {
          $set: {
            isDelete: true,
          },
        }
      );

      await TicketAddonPurchase.updateMany(
        {
          paymentIntentId: data?._id,
          addonOrderStatus: {
            $nin: [
              "succeeded",
              "refund",
              "partial_payment_refund_initiated",
              "cancelled",
            ],
          },
          isDelete: false,
        },
        {
          $set: {
            isDelete: true,
          },
        }
      );

      await ticketPaymentIntent.findByIdAndUpdate(data?._id, {
        $set: {
          ticketValue: 0,
          purchaseQuantity: 0,
          addOnAmount: 0,
          isDelete: true,
        },
      });
    }
  } catch (error) {
    console.log(
      "🚀 ~ file: eventTicketPaymentControllerV2.js:2906 ~ exports.cronRemovePaymentIntent= ~ error:",
      error
    );
    return res.status(500).json({ status: false, message: `${error.message}` });
  }
};

//* Function for the add the addon in the ticket
exports.addAddonInTicket = async (req, res) => {
  try {
    const { ticketData, isFreeTicket = false } = req.body;
    const eventId = new ObjectId(req.params.eventId);
    const userId = req.authUserId;
    const relation_id = new ObjectId(req.relation_id);
    const purchaseDate = new Date();

    //* Check the ticket data is available or not
    if (!ticketData.length)
      return res
        .status(400)
        .send({ status: false, message: `Ticket data is required!` });

    const fetchTicketPurchase = await ticketPurchaseV2
      .find({
        _id: { $in: ticketData.map((t) => t?.ticketPurchaseId) },
        eventId,
        relation_id,
        isDelete: false,
        ...(isFreeTicket && { isFreeTicket }),
      })
      .populate("ticketId")
      .populate("paymentIntentId", "-paymentIntent");

    if (fetchTicketPurchase.length !== ticketData.length) {
      return res.status(404).send({
        status: false,
        message: `Ticket Purchase not found!`,
      });
    }

    //* Find the paymentIntent for this Event and User
    let userPaymentIntent = await ticketPaymentIntent.findOne({
      userId,
      eventId,
      relation_id,
      status: {
        $nin: [
          "succeeded",
          "partial_payment_refund_initiated",
          "refund",
          "cancelled",
        ],
      },
      ...(isFreeTicket && { isFreeTicket }),
      isDelete: false,
    });

    //* Get the admin stripe account data
    const adminStripeAccountData = await getAdminStripeAccountId({
      relation_id,
      authorizationToken: req?.headers?.authorization,
    });
    if (!adminStripeAccountData && !isFreeTicket) {
      return res
        .status(404)
        .send({ status: false, message: "Admin Stripe Account not found!" });
    }

    //* Validate the ticket data and addon data according the ticket
    for await (let data of ticketData) {
      const findTicketPurchase = fetchTicketPurchase.find((t) =>
        t._id.equals(data?.ticketPurchaseId)
      );

      //* Check the ticket purchase & ticket purchase data is available or not
      if (!findTicketPurchase || !findTicketPurchase?.paymentIntentId) {
        return res.status(404).send({
          status: false,
          message: `${data?.ticketPurchaseId} Ticket Purchase not found!`,
        });
      }

      //* Check the ticket data and addon data is available or not
      if (
        !findTicketPurchase?.ticketId ||
        !findTicketPurchase?.ticketId?.addons?.length
      ) {
        return res.status(404).send({
          status: false,
          message: `No addon available for this ${data?.ticketPurchaseId} ticket!`,
        });
      }

      //* Validate the addon data
      if (!data?.addons?.length) {
        return res.status(400).send({
          status: false,
          message: `Addon data is required for ${data?.ticketPurchaseId} ticket!`,
        });
      }

      //* Validate the addon data
      const validateAddon = await validateTicketPurchaseAddonData({
        addonData: data?.addons,
        ticketAddonData: findTicketPurchase?.ticketId?.addons,
      });
      if (!validateAddon?.status) {
        return res.status(400).json({
          status: false,
          message: validateAddon?.message,
        });
      }
    }

    //* Free ticket purchase process
    if (isFreeTicket) {
      const addonData = [];
      //* Store the ticket addon purchase data in the database
      ticketData.forEach(async (data) => {
        const findTicketPurchase = fetchTicketPurchase.find((t) =>
          t._id.equals(data?.ticketPurchaseId)
        );

        //* Store the addon data in the database
        data.addons.forEach(async (addon) => {
          addonData.push(
            new TicketAddonPurchase({
              relation_id,
              eventId,
              userId,
              paymentIntentId: findTicketPurchase?.paymentIntentId?._id,
              ticketPurchaseId: findTicketPurchase._id,
              purchaseDate,
              price: 0,
              addonOrderStatus: findTicketPurchase?.ticketOrderStatus,
              addon,
            })
          );
        });
      });

      if (!addonData.length) {
        return res.status(400).send({
          status: false,
          message: `Addon data is required!`,
        });
      }

      //* Store the addon data in the database
      await TicketAddonPurchase.insertMany(addonData);
      return res.status(200).json({
        status: true,
        message: `Payment intent created successfuly!`,
        data: {
          isFreeTicket: true,
        },
      });
    }

    //* Calculate the addon amount
    const addonAmount = await calculateSingleAddonPrice({
      ticketData,
    });

    //* Fresh purchase for the create the new paymentIntent
    if (!userPaymentIntent) {
      //* Calculate the total amount
      const totalAmountWithCharge = parseFloat(
        countCustomersTotalChargeableAmount({ amount: addonAmount })
      );

      //* Create payment Intent for the fresh ticket
      const paymentIntent = await createPaymentIntent({
        amount: totalAmountWithCharge,
        currency: "usd",
        adminStripeAccountId: adminStripeAccountData.adminStripeAccountId,
      });

      if (!paymentIntent) {
        return res
          .status(200)
          .send({ status: false, message: "Failed to create Payment Intent!" });
      }

      //* Store the payment Intent in the database
      const storePaymentIntent = await ticketPaymentIntent.create({
        userId,
        relation_id,
        eventId,
        purchaseQuantity: 0,
        ticketValue: 0,
        purchaseDate,
        stripeAccountDBId: adminStripeAccountData.stripeAccountDBId,
        paymentIntentId: paymentIntent?.id,
        client_secret: paymentIntent?.client_secret,
        paymentIntent,
        stripe_processing_fee: (totalAmountWithCharge - addonAmount).toFixed(2),
        addOnAmount: addonAmount,
        status: paymentIntent?.status,
        isOnlyAddonIntent: true,
      });
      if (!storePaymentIntent) {
        return res
          .status(200)
          .send({ status: false, message: "Failed to store Payment Intent!" });
      }

      const addonData = [];
      //* Store the ticket addon purchase data in the database
      ticketData.forEach(async (data) => {
        const findTicketPurchase = fetchTicketPurchase.find((t) =>
          t._id.equals(data?.ticketPurchaseId)
        );

        //* Store the addon data in the database
        data.addons.forEach(async (addon) => {
          addonData.push(
            new TicketAddonPurchase({
              relation_id,
              eventId,
              userId,
              paymentIntentId: storePaymentIntent?._id,
              ticketPurchaseId: findTicketPurchase._id,
              purchaseDate,
              price: addon.isSubAddon
                ? addon.subAddon.reduce((sum, selection) => {
                    return sum + selection.price;
                  }, 0)
                : addon?.price,
              addonOrderStatus: storePaymentIntent?.status,
              addon,
            })
          );
        });
      });

      if (!addonData.length) {
        return res.status(400).send({
          status: false,
          message: `Addon data is required!`,
        });
      }

      //* Store the addon data in the database
      await TicketAddonPurchase.insertMany(addonData);

      return res.status(200).json({
        status: true,
        message: `Payment intent created successfuly!`,
        data: {
          client_secret: encrypt(paymentIntent.client_secret),
          paymentIntentId: encrypt(storePaymentIntent.paymentIntentId),
          purchaseQuantity: 0,
          adminStripeAccountId: encrypt(
            adminStripeAccountData.adminStripeAccountId
          ),
        },
      });
    } else if (
      userPaymentIntent &&
      (userPaymentIntent.status === "requires_payment_method" ||
        userPaymentIntent.status === "card_declined")
    ) {
      //* Calculate the total addon amount
      const totalAddonAmount = parseFloat(
        ((userPaymentIntent?.addOnAmount || 0) + addonAmount).toFixed(2)
      );

      //* Calculate the total amount without charge
      const totalAmountWithoutCharge = parseFloat(
        ((userPaymentIntent?.ticketValue || 0) + totalAddonAmount).toFixed(2)
      );

      //* Calculate the total amount
      const totalAmountWithCharge = parseFloat(
        countCustomersTotalChargeableAmount({
          amount: totalAmountWithoutCharge,
        })
      );

      //* Update the paymentIntent for the addon
      const paymentIntent = await updatePaymentIntent({
        amount: totalAmountWithCharge,
        updatePaymentIntentId: userPaymentIntent.paymentIntentId,
        adminStripeAccountId: adminStripeAccountData.adminStripeAccountId,
      });

      if (!paymentIntent) {
        return res
          .status(200)
          .send({ status: false, message: "Failed to update Payment Intent!" });
      }

      //* Store the payment Intent in the database
      const storePaymentIntent = await ticketPaymentIntent.findByIdAndUpdate(
        userPaymentIntent._id,
        {
          $set: {
            stripe_processing_fee: (
              totalAmountWithCharge - totalAmountWithoutCharge
            ).toFixed(2),
            addOnAmount: totalAddonAmount,
            status: paymentIntent?.status,
            paymentIntent,
          },
        },
        { new: true }
      );

      if (!storePaymentIntent) {
        return res
          .status(200)
          .send({ status: false, message: "Failed to store Payment Intent!" });
      }

      const addonData = [];
      //* Store the ticket addon purchase data in the database
      ticketData.forEach(async (data) => {
        const findTicketPurchase = fetchTicketPurchase.find((t) =>
          t._id.equals(data?.ticketPurchaseId)
        );

        //* Store the addon data in the database
        data.addons.forEach(async (addon) => {
          addonData.push(
            new TicketAddonPurchase({
              relation_id,
              eventId,
              userId,
              paymentIntentId: storePaymentIntent?._id,
              ticketPurchaseId: findTicketPurchase._id,
              purchaseDate,
              price: addon.isSubAddon
                ? addon.subAddon.reduce((sum, selection) => {
                    return sum + selection.price;
                  }, 0)
                : addon?.price,
              addonOrderStatus: storePaymentIntent?.status,
              addon,
            })
          );
        });
      });

      if (!addonData.length) {
        return res.status(400).send({
          status: false,
          message: `Addon data is required!`,
        });
      }

      //* Store the addon data in the database
      await TicketAddonPurchase.insertMany(addonData);

      return res.status(200).json({
        status: true,
        message: `Payment intent updated successfuly!`,
        data: {
          client_secret: encrypt(paymentIntent.client_secret),
          paymentIntentId: encrypt(storePaymentIntent.paymentIntentId),
          purchaseQuantity: 0,
          adminStripeAccountId: encrypt(
            adminStripeAccountData.adminStripeAccountId
          ),
        },
      });
    } else {
      return res.status(200).send({
        status: false,
        message: "Somethings went wrong!",
        data: {},
      });
    }
  } catch (error) {
    console.log(
      "🚀 ~ file: eventTicketPaymentControllerV2.js:755 ~ exports.createPayment= ~ error:",
      error
    );
    return res.status(200).json({ status: false, message: `${error.message}` });
  }
};

//* Function for the remove addon fromt the purchase ticket
exports.removeAddonPurchaseTicket = async (req, res) => {
  try {
    const addonPurchaseId = new ObjectId(req.params.addonPurchaseId);
    const userId = req.authUserId;
    const relation_id = new ObjectId(req.relation_id);
    const edgeType = req?.currentEdge?.type;
    const refundedBy = req.owner ? "admin" : "user";
    const refundReqDate = new Date();

    //* Validation for the addon purchase Id
    if (!addonPurchaseId || !mongoose.Types.ObjectId.isValid(addonPurchaseId)) {
      return res.status(400).json({
        status: false,
        message: "Ticket addon purchase id is require!",
        data: {},
      });
    }

    //* Fetch the addon purchase data
    const fetchAddonPurchase = await TicketAddonPurchase.findOne({
      _id: addonPurchaseId,
      relation_id,
      ...(edgeType !== "CO" && { userId }),
      isDelete: false,
    })
      .populate({
        path: "paymentIntentId",
        select: "-paymentIntent",
      })
      .lean();
    if (!fetchAddonPurchase) {
      return res.status(400).json({
        status: false,
        message: "Ticket addon purchase data is not found!",
        data: {},
      });
    }

    //* Validate the addon status
    if (
      fetchAddonPurchase?.addonOrderStatus ===
        "partial_payment_refund_initiated" ||
      fetchAddonPurchase?.addonOrderStatus === "refund" ||
      fetchAddonPurchase?.addonOrderStatus === "cancelled"
    ) {
      return res.status(400).json({
        status: false,
        message: `This ticket addon purchase is already in the process of being refunded or has been refunded.`,
      });
    }

    //* If the addon purchase is for the free or free event
    if (fetchAddonPurchase?.isFreeAddon) {
      //* Update the addon purchase data
      const updateAddonPurchase = await TicketAddonPurchase.findByIdAndUpdate(
        fetchAddonPurchase?._id,
        {
          $set: {
            addonOrderStatus: "cancelled",
            addonCancelleDate: refundReqDate,
            paymentRefundId: null,
            refundedBy,
          },
        },
        {
          new: true,
        }
      );
      if (!updateAddonPurchase) {
        return res.status(400).json({
          success: false,
          message: "Ticket addon purchase was not updated!",
        });
      }

      res.status(200).json({
        status: true,
        message: `Ticket removed successfully!`,
        data: {},
      });
    } else {
      //* If the addon purchase is free or the price is 0
      if (!fetchAddonPurchase.price) {
        //* Update the addon purchase data
        const updateAddonPurchase = await TicketAddonPurchase.findByIdAndUpdate(
          fetchAddonPurchase?._id,
          {
            $set: {
              addonOrderStatus:
                fetchAddonPurchase?.addonOrderStatus === "succeeded"
                  ? "refund"
                  : "cancelled",
              addonCancelleDate: refundReqDate,
              paymentRefundId: null,
              refundedBy,
            },
          },
          {
            new: true,
          }
        );

        if (!updateAddonPurchase) {
          return res.status(400).json({
            success: false,
            message: "Ticket addon purchase was not updated!",
          });
        }

        res.status(200).json({
          status: true,
          message: `Ticket removed successfully!`,
          data: {},
        });
      } else {
        //* If payment intent is not found in the addon purchase
        if (!fetchAddonPurchase?.paymentIntentId) {
          return res.status(400).json({
            success: false,
            message: "Payment intent is not found!",
            data: {},
          });
        }

        //* Fetch the admin stripe connected account
        const adminStripeAccountData = await getAdminStripeAccountId({
          relation_id,
          authorizationToken: req?.headers?.authorization,
        });
        if (!adminStripeAccountData) {
          return res.status(200).send({
            status: false,
            message: "Admin Stripe Account not found!",
          });
        }

        //* If the the addon purchase payment is not done yet or not done the payment
        if (fetchAddonPurchase?.addonOrderStatus !== "succeeded") {
          //* Updated addon amount
          const finalAddonAmount =
            fetchAddonPurchase?.paymentIntentId?.addOnAmount -
            fetchAddonPurchase?.price;

          //* Total intent amount excluding the charge
          const totalAmount =
            parseFloat(finalAddonAmount.toFixed(2)) +
            parseFloat(
              fetchAddonPurchase.paymentIntentId.ticketValue.toFixed(2)
            );

          //* Final payment intent amount including the stripe processing fee
          const totalAmountWithCharge = parseFloat(
            countCustomersTotalChargeableAmount({
              amount: parseFloat(totalAmount.toFixed(2)),
            })
          );

          //* Update the payment intent for the addon update
          let paymentIntent = null;
          if (!totalAmount) {
            //* Cancle the payment intent
            paymentIntent = await canclePaymentIntent({
              updatePaymentIntentId:
                fetchAddonPurchase?.paymentIntentId?.paymentIntentId,
              adminStripeAccountId:
                adminStripeAccountData?.adminStripeAccountId,
            });
          } else {
            //* Update the payment intent
            paymentIntent = await updatePaymentIntent({
              amount: totalAmountWithCharge,
              updatePaymentIntentId:
                fetchAddonPurchase?.paymentIntentId?.paymentIntentId,
              adminStripeAccountId:
                adminStripeAccountData?.adminStripeAccountId,
            });
          }
          if (!paymentIntent) {
            return res.status(200).send({
              status: false,
              message: "Failed to update Payment Intent!",
            });
          }

          //* Update the payment intent
          const revisePaymentIntent =
            await ticketPaymentIntent.findByIdAndUpdate(
              fetchAddonPurchase?.paymentIntentId?._id,
              {
                $set: {
                  client_secret: paymentIntent?.client_secret,
                  paymentIntent,
                  stripe_processing_fee: (
                    totalAmountWithCharge - totalAmount
                  ).toFixed(2),
                  addOnAmount: parseFloat(finalAddonAmount.toFixed(2)),
                },
              },
              { new: true }
            );
          if (!revisePaymentIntent) {
            return res.status(200).send({
              status: false,
              message: "Failed to update Payment Intent in db!",
            });
          }

          //* Update the Addon purchase data
          await TicketAddonPurchase.findByIdAndUpdate(fetchAddonPurchase?._id, {
            $set: {
              addonOrderStatus: "cancelled",
              addonCancelleDate: refundReqDate,
            },
          });

          res.status(200).json({
            status: true,
            message: `Ticket removed successfully!`,
            data: {},
          });
        } else {
          //* Initilize the refund process
          const refund = await createRefundIntent({
            paymentIntentId:
              fetchAddonPurchase?.paymentIntentId?.paymentIntentId,
            amount: parseFloat(fetchAddonPurchase.price.toFixed(2)),
            metadata: {
              refundedBy,
              isAllOrderRefund: false,
              isOnlyAddonPurchaseRefund: true,
              query: JSON.stringify({
                purchaseTicketId: null,
                ticketAddonPurchaseId: fetchAddonPurchase._id,
                userId: fetchAddonPurchase?.userId,
                relation_id,
                eventId: fetchAddonPurchase?.eventId,
                paymentIntentId: fetchAddonPurchase?.paymentIntentId?._id,
                connectedAccountId:
                  adminStripeAccountData?.adminStripeAccountId,
              }),
            },
            adminStripeAccountId: adminStripeAccountData?.adminStripeAccountId,
          });
          if (!refund) {
            return res.status(400).json({
              status: false,
              message: `Somthing went wrong in refund payment!`,
              data: {},
            });
          }

          //* add purchase refund data
          const addonPurchaseRefundData = await AddonPurchaseRefund.create({
            addonPurchaseId: fetchAddonPurchase?._id,
            userId: fetchAddonPurchase?.userId,
            eventId: fetchAddonPurchase?.eventId,
            relation_id,
            paymentIntentId: fetchAddonPurchase?.paymentIntentId?._id,
            ticketPurchaseId: fetchAddonPurchase?.ticketPurchaseId,
            amount: fetchAddonPurchase?.price,
            refundReqDate,
            refundId: refund?.id,
            stripeAccountDBId: adminStripeAccountData?.stripeAccountDBId,
            refundStatus: "partial_payment_refund_initiated",
            refundObj: refund,
            refundedBy,
          });
          if (!addonPurchaseRefundData) {
            return res.status(400).json({
              status: false,
              message: `Somthing went wrong to store refund data!`,
              data: {},
            });
          }

          //* Update the payment Intent addon amount
          const revisePaymentIntent = await ticketPaymentIntent.updateOne(
            {
              _id: fetchAddonPurchase?.paymentIntentId?._id,
            },
            [
              {
                $set: {
                  //* Decreament the addOn amount
                  addOnAmount: {
                    //* Round the value to 2 decimal places
                    $round: [
                      {
                        //* Preval the value to 0 if the value is less than 0
                        $max: [
                          {
                            //* Subtract the addOnAmount from the totalAddonAmount
                            $subtract: [
                              "$addOnAmount",
                              Number(fetchAddonPurchase.price.toFixed(2)),
                            ],
                          },
                          0,
                        ],
                      },
                      2,
                    ],
                  },
                },
              },
            ],
            {
              new: true,
            }
          );
          if (!revisePaymentIntent) {
            return res.status(400).send({
              status: false,
              message: "Failed to update Payment Intent in db!",
            });
          }

          //* Update the addon purchase data
          const updateAddonPurchase =
            await TicketAddonPurchase.findByIdAndUpdate(
              fetchAddonPurchase?._id,
              {
                $set: {
                  addonOrderStatus: "partial_payment_refund_initiated",
                  addonCancelleDate: refundReqDate,
                  paymentRefundId: addonPurchaseRefundData?._id,
                  refundedBy,
                },
              },
              {
                new: true,
              }
            );

          if (!updateAddonPurchase) {
            return res.status(400).send({
              status: false,
              message:
                "Something went wrong while updating the addon purchase!",
            });
          }

          res.status(200).send({
            status: true,
            message: "Your refund is initiated successfully!",
            data: updateAddonPurchase,
          });
        }
      }
    }

    //* Check the scenario for the update the payment intent status
    if (fetchAddonPurchase?.paymentIntentId) {
      const fetchPaymentIntent = await ticketPaymentIntent.findOne({
        _id: fetchAddonPurchase?.paymentIntentId?._id,
        isDelete: false,
      });

      //* Check the payment intent dependancy and yet to received the payment
      if (
        fetchPaymentIntent &&
        !fetchPaymentIntent?.purchaseQuantity &&
        !fetchPaymentIntent?.addOnAmount
      ) {
        //* Update the payment intent status
        const updateTicketPaymentIntent =
          await ticketPaymentIntent.findByIdAndUpdate(
            fetchPaymentIntent?._id,
            {
              $set: {
                status: fetchPaymentIntent.isFreeAddon ? "cancelled" : "refund",
              },
            },
            { new: true }
          );

        //* find the other payment intent for the user
        const findPaymentIntent = await ticketPaymentIntent.find({
          userId: updateTicketPaymentIntent?.userId,
          relation_id,
          eventId: findPurchaseTicket.eventId,
          status: "succeeded",
          isDelete: false,
        });

        //* User haven't any purchase data then delete the user attendee
        if (!findPaymentIntent.length) {
          await EventParticipantAttendees.findByIdAndUpdate(
            updateTicketPaymentIntent?.userParticipantId,
            {
              isDelete: true,
            }
          );
        }
      }
    }
    return;
  } catch (error) {
    console.log(
      "🚀 ~ file: eventTicketPaymentControllerV2.js:4982 ~ exports.removeAddonPurchaseTicket= ~ error:",
      error
    );
    return res.status(200).json({ status: false, message: `${error.message}` });
  }
};
