const { ObjectId } = require("mongodb");
const eventAddonGroup = require("../../database/models/eventAddon/eventAddonGroup");
const event = require("../../database/models/event");
const debugErrorLogs = require("../../middleware/debugErrorLogs");
const { validationResult } = require("express-validator");

/** Admin Routes */
//* Create event add-on group
exports.createEventAddonGroup = async (req, res) => {
  try {
    const { name, eventId, type } = req.body;
    const { relation_id } = req;

    if (!name || !eventId || !type) {
      return res.status(400).json({
        success: false,
        message: "Invalid data!",
        data: {},
      });
    }

    let isExist = await eventAddonGroup.aggregate([
      { $addFields: { nameLower: { $toLower: "$name" } } },
      {
        $match: {
          isDelete: false,
          nameLower: name.toLowerCase(),
          eventId: new ObjectId(eventId),
          relationId: ObjectId(relation_id),
        },
      },
    ]);
    if (isExist && isExist.length != 0) {
      return res.status(400).json({
        status: false,
        message:
          "This add-on group name already exists for this event. Please choose a different name.",
      });
    }

    let getEvent = await event
      .findOne({ _id: new ObjectId(eventId), isDelete: false })
      .lean();
    if (!getEvent) {
      return res
        .status(400)
        .json({ status: false, message: `Event not found.`, data: {} });
    }

    const ids = await eventAddonGroup
      .findOne({
        isDelete: false,
        eventId: new ObjectId(eventId),
        relationId: ObjectId(relation_id),
      })
      .sort({ order: -1 });

    const createEventAddonGroup = await eventAddonGroup.create({
      ...req.body,
      relationId: relation_id,
      order: ids ? Number(ids.order + 1) : 0,
    });

    if (!createEventAddonGroup) {
      return res.status(400).json({
        status: false,
        message: `Something went wrong while creating event addon group!`,
        data: {},
      });
    }

    return res.status(200).json({
      status: true,
      message: `Event add-on group created successfully.`,
      data: createEventAddonGroup,
    });
  } catch (error) {
    await debugErrorLogs.createErrorLogs(error, "createEventAddonV2", {});
    return res
      .status(500)
      .json({ status: false, message: "Internal server error!", error: error });
  }
};

//* Edit event Add-on-group
exports.editEventAddonGroup = async (req, res) => {
  try {
    const { id } = req.params;
    const { name, description, eventId, type } = req.body;
    const { relation_id } = req;

    if (!name || !eventId || !type) {
      return res.status(400).json({
        success: false,
        message: "Invalid data!",
        data: {},
      });
    }

    const existAddonGroup = await eventAddonGroup
      .findOne({ _id: new ObjectId(id), isDelete: false })
      .lean();
    if (!existAddonGroup) {
      return res.status(400).json({
        status: false,
        message: `Event add-on group not found!`,
        data: {},
      });
    }

    let isExist = await eventAddonGroup.aggregate([
      { $addFields: { nameLower: { $toLower: "$name" } } },
      {
        $match: {
          _id: { $nin: [ObjectId(id)] },
          isDelete: false,
          relationId: ObjectId(relation_id),
          eventId: new ObjectId(eventId),
          nameLower: name.toLowerCase(),
        },
      },
    ]);
    if (isExist && isExist.length != 0) {
      return res.status(400).json({
        status: false,
        message: "This add-on group name already exists.",
        data: {},
      });
    }

    let getEvent = await event
      .findOne({ _id: new ObjectId(eventId), isDelete: false })
      .lean();
    if (!getEvent) {
      return res
        .status(400)
        .json({ status: false, message: `Event not found!` });
    }

    const editEventAddonGroup = await eventAddonGroup.findByIdAndUpdate(
      id,
      {
        $set: {
          ...req.body,
        },
      },
      { new: true }
    );
    if (!editEventAddonGroup) {
      return res.status(400).json({
        status: false,
        message: `Something went wrong while edit event add-on group!`,
        data: {},
      });
    }

    return res.status(200).json({
      status: true,
      message: `Event add-on group edited successfully`,
      data: editEventAddonGroup,
    });
  } catch (error) {
    await debugErrorLogs.createErrorLogs(error, "editEventAddonV2", {});
    return res
      .status(500)
      .json({ status: false, message: "Internal server error!", error: error });
  }
};

//* Delete event Add-on group (By admin - soft delete)
exports.deleteEventAddonGroup = async (req, res) => {
  try {
    // Extract event addon ID from params
    const { id } = req.params;
    const { relation_id } = req;

    // Check if the event addon exists and is not deleted
    const existAddonGroup = await eventAddonGroup
      .findOne({
        _id: new ObjectId(id),
        isDelete: false,
      })
      .lean();

    if (!existAddonGroup) {
      return res.status(400).send({
        status: false,
        message: "Event add-on group not found.",
      });
    }

    // Find all related event add-ons and update their order
    const ids = await eventAddonGroup
      .find({
        isDelete: false,
        eventId: existAddonGroup.eventId,
        relationId: ObjectId(relation_id),
      })
      .sort({ order: 1 });

    // Update the order of other event add-ons in parallel
    const updateOrderPromises = ids.map(async (item, i) => {
      return eventAddonGroup.findByIdAndUpdate(
        ObjectId(item._id),
        { order: i + 1 },
        { new: true }
      );
    });

    // Wait for all order updates to complete
    await Promise.all(updateOrderPromises);

    // Mark the event add-on as deleted
    const deleteEventAddonGroup = await eventAddonGroup.findByIdAndUpdate(
      id,
      { isDelete: true },
      { new: true }
    );

    // If deletion failed, return an error message
    if (!deleteEventAddonGroup) {
      return res.status(400).json({
        status: false,
        message: "Something went wrong while deleting event add-on group!",
      });
    }

    // Successfully deleted the event add-on
    return res.status(200).json({
      status: true,
      message: "Event add-on group deleted successfully.",
    });
  } catch (error) {
    // Log the error and return an internal server error response
    await debugErrorLogs.createErrorLogs(error, "deleteEventAddonV2", {});
    return res.status(500).json({
      status: false,
      message: "Internal server error!",
      error: error.message,
    });
  }
};

//* Get all event addon group by event id for indivisual event
exports.getAllEventAddonGroup = async (req, res) => {
  try {
    const { eventId } = req.params;
    const page = parseInt(req.query.page) || 1;
    const limit = parseInt(req.query.limit) || 10;
    const skip = (page - 1) * limit;
    const sortField = req.query.sortField ? req.query.sortField : "order";
    const sortType = req.query.sortType === "Desc" ? -1 : 1;
    const search = req.query.search;
    let sortOrder = {};
    sortOrder[sortField] = sortType;
    const { relation_id } = req;

    let condition = {
      eventId: new ObjectId(eventId),
      relationId: ObjectId(relation_id),
      isDelete: false,
    };
    const { status, dateFilter } = req.query;

    let fromDate = new Date();
    let toDate = new Date();
    if (dateFilter) {
      switch (dateFilter) {
        case "todays":
          fromDate.setHours(0, 0, 0, 0);
          toDate.setHours(23, 59, 59, 999);
          break;
        case "past7days":
          fromDate.setDate(fromDate.getDate() - 6);
          fromDate.setHours(0, 0, 0, 0);
          toDate.setHours(23, 59, 59, 999);
          break;
        case "past30days":
          fromDate.setDate(fromDate.getDate() - 29);
          fromDate.setHours(0, 0, 0, 0);
          toDate.setHours(23, 59, 59, 999);
          break;
        case "past90days":
          fromDate.setDate(fromDate.getDate() - 89);
          fromDate.setHours(0, 0, 0, 0);
          toDate.setHours(23, 59, 59, 999);
          break;
        case "past365days":
          fromDate.setDate(fromDate.getDate() - 364);
          fromDate.setHours(0, 0, 0, 0);
          toDate.setHours(23, 59, 59, 999);
          break;
        case "custom":
          if (reqFromDate && reqToDate) {
            fromDate = new Date(reqFromDate);
            toDate = new Date(reqToDate);
          } else {
            return res.status(400).json({
              status: false,
              message:
                "Custom date filter requires 'reqFromDate' and 'reqToDate'!",
            });
          }
      }
    }

    if (search) {
      condition = {
        ...condition,
        $or: [
          { name: { $regex: search, $options: "i" } },
          { "addon_data.name": { $regex: search, $options: "i" } }
        ]
      };
    }

    let pipeline = [
      {
        $lookup: {
          from: "event_addon_v2",
          localField: "_id",
          foreignField: "addonGroupId",
          as: "addon_data",
          pipeline: [
            {
              $match: {
                isDelete: false,
                ...(req.query.status && { status: req.query.status }),
              },
            },
            ...(req.query.dateFilter
              ? [
                  {
                    $addFields: {
                      createdAtDate: {
                        $dateFromParts: {
                          year: { $year: "$createdAt" },
                          month: { $month: "$createdAt" },
                          day: { $dayOfMonth: "$createdAt" },
                        },
                      },
                    },
                  },
                  {
                    $match: {
                      createdAtDate: {
                        $gte: new Date(fromDate),
                        $lte: new Date(toDate),
                      },
                    },
                  },
                ]
              : []),
            {
              $lookup: {
                from: "ticket_addon_purchases",
                let: { addonId: "$_id" },
                pipeline: [
                  {
                    $match: {
                      $expr: {
                        $and: [
                          { $eq: ["$addonId", "$$addonId"] },
                          { $eq: ["$isDelete", false] },
                        ],
                      },
                    },
                  },
                  {
                    $group: {
                      _id: "$addonOrderStatus",
                      count: { $sum: 1 },
                    },
                  },
                ],
                as: "ticket_addon_status_counts",
              },
            },
            {
              $addFields: {
                addon_purchase_count: {
                  $ifNull: [
                    {
                      $let: {
                        vars: {
                          matched: {
                            $arrayElemAt: [
                              {
                                $filter: {
                                  input: "$ticket_addon_status_counts",
                                  as: "item",
                                  cond: { $eq: ["$$item._id", "succeeded"] },
                                },
                              },
                              0,
                            ],
                          },
                        },
                        in: "$$matched.count",
                      },
                    },
                    0,
                  ],
                },
                addon_refund_count: {
                  $ifNull: [
                    {
                      $let: {
                        vars: {
                          matched: {
                            $arrayElemAt: [
                              {
                                $filter: {
                                  input: "$ticket_addon_status_counts",
                                  as: "item",
                                  cond: { $eq: ["$$item._id", "refund"] },
                                },
                              },
                              0,
                            ],
                          },
                        },
                        in: "$$matched.count",
                      },
                    },
                    0,
                  ],
                },
              },
            },
            {
              $project: {
                ticket_addon_status_counts: 0,
              },
            },
            {
              $sort: {
                order: 1,
              },
            },
          ],
        },
      },
      {
        $addFields: {
          total_addon_purchase_count: {
            $sum: {
              $map: {
                input: "$addon_data",
                as: "addon",
                in: {
                  $ifNull: ["$$addon.addon_purchase_count", 0],
                },
              },
            },
          },
          total_addon_refund_count: {
            $sum: {
              $map: {
                input: "$addon_data",
                as: "addon",
                in: {
                  $ifNull: ["$$addon.addon_refund_count", 0],
                },
              },
            },
          },
          total_gross_sells: {
            $sum: {
              $map: {
                input: "$addon_data",
                as: "addon",
                in: {
                  $add: [
                    { $ifNull: ["$$addon.addon_purchase_count", 0] },
                    { $ifNull: ["$$addon.addon_refund_count", 0] },
                  ],
                },
              },
            },
          },
        },
      },
      {
        $match: condition,
      },
    ];
    
    let countAddon = 0;
    const [allAddonGroupData, addOnGroupCount] = await Promise.all([
      eventAddonGroup.aggregate([
        ...pipeline,
        ...[{ $sort: sortOrder }, { $skip: skip }, { $limit: limit }],
      ]),
      eventAddonGroup.aggregate([...pipeline, ...[{ $count: "total" }]]),
    ]);
    if (addOnGroupCount && addOnGroupCount[0] && addOnGroupCount[0]["total"]) {
      countAddon = addOnGroupCount[0]["total"];
    }
    if (!allAddonGroupData) {
      return res
        .status(200)
        .json({ status: false, message: `Event add-on group not found.` });
    }

    return res.status(200).send({
      status: true,
      message: "Event add-on group retrieved successfully.",
      data: allAddonGroupData,
      totalPages: Math.ceil(countAddon / limit),
      currentPage: page,
      totalAddonGroups: countAddon,
    });
  } catch (error) {
    await debugErrorLogs.createErrorLogs(error, "getAllEventAddonV2", {});
    return res
      .status(500)
      .json({ status: false, message: "Internal server error!", error: error });
  }
};

//* Get event addon groups by id
exports.getEventAddonGroupById = async (req, res) => {
  try {
    const { id } = req.params;
    const { relation_id } = req;

    if (!id) {
      return res.status(400).json({
        success: false,
        message: "Id is required!",
        data: {},
      });
    }

    let pipeline = [
      {
        $match: {
          _id: ObjectId(id),
          relationId: ObjectId(relation_id),
          isDelete: false,
        },
      },
      {
        $lookup: {
          from: "event_addon_v2",
          localField: "_id",
          foreignField: "addonGroupId",
          as: "addon_data",
          pipeline: [
            {
              $match: {
                isDelete: false,
              },
            },
            {
              $lookup: {
                from: "ticket_addon_purchases",
                localField: "_id",
                foreignField: "addonId",
                as: "ticket_addon_purchases_data",
                pipeline: [
                  {
                    $match: {
                      addonOrderStatus: "succeeded",
                      isDelete: false,
                    },
                  },
                  {
                    $group: {
                      _id: null,
                      count: {
                        $sum: 1,
                      },
                    },
                  },
                  {
                    $project: {
                      _id: 0,
                      count: {
                        $ifNull: ["$count", 0],
                      },
                    },
                  },
                ],
              },
            },
            {
              $unwind: {
                path: "$ticket_addon_purchases_data",
                preserveNullAndEmptyArrays: true,
              },
            },
            {
              $addFields: {
                addon_purchase_count: {
                  $ifNull: ["$ticket_addon_purchases_data.count", 0],
                },
              },
            },
            {
              $project: {
                ticket_addon_purchases_data: 0,
              },
            },
          ],
        },
      },
      {
        $addFields: {
          total_addon_purchase_count: {
            $sum: {
              $map: {
                input: "$addon_data",
                as: "addon",
                in: {
                  $ifNull: ["$$addon.addon_purchase_count", 0],
                },
              },
            },
          },
        },
      },
    ];
    const findEventAddonGroup = await eventAddonGroup.aggregate(pipeline);

    return res.status(200).send({
      status: true,
      message: "Event add-on group retrieved successfully.",
      data: findEventAddonGroup && findEventAddonGroup.length && findEventAddonGroup[0]?findEventAddonGroup[0]: {},
    });
  } catch (error) {
    await debugErrorLogs.createErrorLogs(error, "getAllEventAddonV2", {});
    return res
      .status(500)
      .json({ status: false, message: "Internal server error!", error: error });
  }
};

//* Get all event addon group by event id
exports.getAllEventAddonGroupSuggestion = async (req, res) => {
  try {
    const { eventId } = req.params;
    const { relation_id } = req;
    const { search } = req.query;
    let condition = {
      eventId: new ObjectId(eventId),
      relationId: ObjectId(relation_id),
      isDelete: false,
    };

    if (search) {
      condition["$or"] = [
        {
          name: {
            $regex: ".*" + search + ".*",
            $options: "i",
          },
        },
      ];
    }

    const allAddonGroupData = await eventAddonGroup.find(
      { ...condition },
      { name: 1 }
    );

    if (allAddonGroupData.length)
      return res.status(200).json({
        status: true,
        message: "Event add-on group suggestion retrieved successfully!",
        data: allAddonGroupData,
      });
    else
      return res.status(200).json({
        status: false,
        message: "Something went wrong while getting all event locations!",
        data: {},
      });
  } catch (e) {
    return res
      .status(500)
      .json({ status: false, message: "Something went wrong!", error: e });
  }
};

//* Make the reorder API for the addon group
exports.eventAddonGroupReorder = async (req, res) => {
  try {
    const ids = req.body.ids;
    if (!ids.length) {
      return res.status(400).json({
        success: true,
        message: "Addon group ids are required!",
      });
    }

    if (ids.length > 0) {
      let resOrder = ids.map(async (item, i) => {
        await eventAddonGroup.findByIdAndUpdate(
          ObjectId(item),
          { order: i + 1 },
          { new: true }
        );
      });
      await Promise.all([...resOrder]);

      return res.status(200).json({
        status: true,
        message: "Event add-on group list rearrange succesfully!",
      });
    } else {
      return res.status(200).json({
        status: false,
        message: "Something went wrong while add-on group!",
      });
    }
  } catch (error) {
    return res
      .status(200)
      .json({ status: false, message: "Something went wrong!", error: error });
  }
};
