const Room = require("../../database/models/eventRoom");
const Session = require("../../database/models/eventSession");
const eventActivity = require("../../database/models/eventActivity");
const eventActivityIcon = require("../../database/models/eventActivityIcon");
const eventFaqs = require("../../database/models/eventFaqs");
const eventContactSupport = require("../../database/models/eventContactSupport");
const { ObjectId } = require("mongodb");
const event = require("../../database/models/event");
const User = require("../../database/models/airTableSync");
const { user_edges } = require("../../microservices/user/components/user-edges/database/models");
const { send_notification, notification_template, addTime, subtractTime, send_notification_v2 } = require("../../utils/notification");
const { checkIfMsgReadSocket } = require("../chatcontroller");
const { schedule, reScheduleNotificationForActivitySession, scheduleNotificationFormAdmin, reScheduleNotificationFormAdmin, reScheduleNotificationFormAdminV2, scheduleJobNotification } = require("./eventAttendeeManageController");
const Notification = require("../../database/models/notification");
const EventWiseParticipentType = require("../../database/models/eventWiseParticipantTypes");
const ScheduledNotification = require("../../database/models/scheduledNotification");
const scheduleLib = require("node-schedule");
require("dotenv").config();
const moment = require("moment");
const AWS = require("aws-sdk");
require('moment-timezone');
const bucketName = process.env.AWS_BUCKET;
const folderName = `uploads/event-activity/${process.env.AWS_ACTIVITY_ICON_FOLDER}/`;
const domainName = `https://mds-community.s3.amazonaws.com/uploads/event-activity/${process.env.AWS_ACTIVITY_ICON_FOLDER}/`
const collabaorators = require("../../database/models/collaborator/inviteCollaborator");
const eventParticipantAttendees = require("../../database/models/eventParticipantAttendees");
const eventParticipantTypes = require("../../database/models/eventParticipantTypes");
const { userAccessRulesCommonCondition, dateValidation } = require("../userAccessRules/restrictionAccess");
const { getAllTiersfromBilling } = require("../userAccessRules/tiers");
const {
    copyEventActivityIcon,
  } = require("../../utils/mediaUpload");
const eventWiseParticipantTypes = require("../../database/models/eventWiseParticipantTypes");
const scheduleNotification = require("../../database/models/notification/scheduleNotification");
const { createNotification, updateNotification, updateUserNotification } = require("../../microservices/user/utils/comman");

var s3 = new AWS.S3({
    accessKeyId: process.env.AWS_ID,
    secretAccessKey: process.env.AWS_SECRET,
    Bucket: process.env.AWS_BUCKET,
});

/** CURD opreation of event activity start **/

exports.eventSuggestionList = async (req, res) => {
  try {
    const { eventId } = req.params;
    const authUser = req.authUserId;
    let ruleCondition = await userAccessRulesCommonCondition({ userId: authUser, relation_id: req.relation_id });
    let dateValidationNew = await dateValidation();
    if (!eventId || eventId === undefined || eventId === null) {
      return res.status(200).json({
        status: false,
        message: "Eventid is not provided!",
        data: {},
      });
    }
    var localDate;

      if (req.query && req.query.localDate) {
        localDate = moment(
          new Date(req.query.localDate),
          "YYYY-MM-DD"
        ).toDate();
      } else {
        localDate = moment().toDate();
      }

    const aggregate = [
      {
        $match: {
          _id: new ObjectId(eventId),
          category: { $exists: true },
          ...ruleCondition,
        },
      },
      {
        $sort: {
          createdAt: -1,
        },
      },
      {
        $lookup: {
          from: "event_categories",
          localField: "category",
          foreignField: "_id",
          as: "categoryDetails",
        },
      },
      {
        $lookup: {
          from: "events",
          localField: "category",
          foreignField: "category",
          as: "relatedEvents",
          pipeline: [
            {
              $match: {
                _id: { $ne: new ObjectId(eventId) },
                relation_id: new ObjectId(req.relation_id),
                status: "published",
                isDelete: false,
                ...ruleCondition,
              },
            },
            ...dateValidationNew,
            {
              $addFields: {
                Date: {
                  $let: {
                    vars: {
                      year: { $substr: ["$endDate", 6, 10] },
                      month: { $substr: ["$endDate", 0, 2] },
                      dayOfMonth: { $substr: ["$endDate", 3, 2] },
                      startMinute: { $substr: ["$endTime", 3, 2] },
                      startHours: {
                        $toString: {
                          $cond: {
                            if: { $eq: [{ $substr: ["$endTime", 6, 2] }, "am"] },
                            then: {
                              $cond: {
                                if: { $eq: [{ $substr: ["$endTime", 0, 2] }, "12"] },
                                then: "00", // Midnight (12 AM) should be converted to "00"
                                else: { $substr: ["$endTime", 0, 2] },
                              },
                            },
                            else: {
                              $cond: {
                                if: { $eq: [{ $substr: ["$endTime", 0, 2] }, "12"] },
                                then: "12", // Noon (12 PM) should remain "12"
                                else: {
                                  $add: [
                                    { $toInt: { $substr: ["$endTime", 0, 2] } },
                                    12, // Adding 12 to convert PM times to 24-hour format
                                  ],
                                },
                              },
                            },
                          },
                        },
                      },
                      timeZoneTemp: {
                        $cond: {
                          if: {
                            $or: [
                              { $eq: ["$timeZone", ""] },
                              {
                                $eq: [
                                  "$timeZone",
                                  "(UTC) Dublin, Edinburgh, Lisbon, London",
                                ],
                              },
                            ],
                          },
                          then: "-00:00",
                          else: { $substr: ["$timeZone", 4, 6] },
                        },
                      },
                    },
                    in: {
                      $toDate: {
                        $concat: [
                          "$$year",
                          "-",
                          "$$month",
                          "-",
                          "$$dayOfMonth",
                          "T",
                          "$$startHours",
                          ":",
                          "$$startMinute",
                          ":",
                          "00.000",
                          "$$timeZoneTemp",
                        ],
                      },
                    },
                  },
                },
              },
            },
            {
              $match: {
                Date: { $gt: localDate },
              },
            },
            {
              $lookup: {
                from: "event_tickets",
                localField: "_id",
                foreignField: "eventId",
                as: "result",
                pipeline: [
                  {
                    $match: {
                      isDelete: false
                    }
                  }
                ]
              },
            },{
              $lookup: {
                from: "eventpackages",
                let: { event_id: "$_id" },
                pipeline: [
                  {
                    $match: {
                      $expr: {
                        $eq: ["$event", "$$event_id"],
                      },
                      isDelete: false,
                    },
                  },
                  { $project: { price: 1, _id: 0 } },
                ],
                as: "priceData"
              }
            },
            // {
            //   $lookup: {
            //     from: "eventlocations",
            //     localField: "location",
            //     foreignField: "_id",
            //     as: "location",
            //   },
            // },
            // {
            //   $unwind: {
            //     path: "$location",
            //     preserveNullAndEmptyArrays: true,
            //   },
            // },
            {
              $lookup: {
                from: "eventactivities",
                localField: "_id",
                foreignField: "event",
                as: "activities",
                pipeline: [
                    {
                        $match: {
                            isDelete: false
                        }
                    }
                ]
              },
            },
            {
              $lookup: {
                from: "event_participant_attendees",
                localField: "_id",
                foreignField: "event",
                as: "event_participant_attendees_result",
                pipeline: [
                  {
                    $match: {
                      $expr: {
                        $eq: [
                          ObjectId(req?.userId),
                          "$user"
                        ]
                      }
                    }
                  },
                  {
                    $match: {
                      $expr: {
                        $eq: ["$isDelete", false]
                      }
                    }
                  }
                ]
              },
            },
            {
              $addFields: {
                price: {
                  $ifNull: [
                    {
                      $cond: {
                        if: { $eq: ["$ticketPlatform", "internal"] }, // Check if ticketPlatform is "internal"
                        then: { // If true, get price from priceData
                          $min: {
                            $filter: {
                              input: "$result.actualPrice",
                              cond: { $gt: ["$$this", 0] }
                            }
                          }
                        },
                        else: { // If false, get price from priceData1
                          $min: {
                            $filter: {
                              input: "$priceData.price",
                              cond: { $gt: ["$$this", 0] }
                            }
                          }
                        }
                      }
                    },
                    0
                  ]
                },
                totalTicketCount: {
                  $cond: {
                    if: { $eq: ["$ticketPlatform", "internal"] },
                    then: {
                      $size: "$result"
                    },
                    else: {
                      $size: "$priceData"
                    }
                  }
                },
                registationFlag: {
                    $let: {
                      vars: {},
                      in: {
                        $gt: [
                          {
                            $size:
                              "$event_participant_attendees_result"
                          },
                          0
                        ]
                      }
                    }
                  }
              },
            },
            {
                $addFields: {
                  price: {
                    $cond: {
                      if: {
                        $or: [
                          {
                            $eq: ["$price", null],
                          },
                          {
                            $eq: ["$ticketType", "free"],
                          },
                        ],
                      },
                      then: 0,
                      else: "$price",
                    },
                  },
                },
            },
          ],
        },
      },
      ...(req.currentEdge && req.currentEdge.type === "M" ? [{
        $addFields: {
          relatedEvents: {
            $cond: {
              if: {
                $gt: [{ $size: "$restrictedAccessUserId" }, 0]
              },
              then: {
                $filter: {
                  input: "$relatedEvents",
                  as: "event",
                  cond: {
                    $cond: {
                      if: { $in: [ObjectId(req.userId), "$restrictedAccessUserId"] },
                      then: true,
                      else: false
                    }
                  }
                }
              },
              else: "$relatedEvents"
            }
          }
        }
      }] : []),
      {
        $project: {
          title: 1,
          category: 1,
          ticketType: 1,
          categoryDetails: { $arrayElemAt: ["$categoryDetails", 0] },
          relatedEvents: "$relatedEvents",
          ticketPlatform:1,
        },
      },
    ];

    const getEventSuggestionList = await event.aggregate(aggregate);

    return res
      .status(200)
      .json({
        status: true,
        message: "Event suggestion list fetch succesfully",
        data: getEventSuggestionList[0]?.relatedEvents,
      });
  } catch (error) {
    console.log({ error });
    return res
      .status(500)
      .json({ status: false, message: "Something went wrong!", error: error });
  }
};

exports.eventSuggestionListV2 = async (req, res) => {
    try {
      const { eventId } = req.params;
      let eventData = await event.findById({_id: eventId})
      let dateValidationNew = await dateValidation();
      let relation_id = req.query.relation_id;
      if (!eventId || eventId === undefined || eventId === null) {
        return res.status(200).json({
          status: false,
          message: "Eventid is not provided!",
          data: {},
        });
      }
      if (!eventData) {
        return res.status(200).json({
          status: false,
          message: "Event data nor found!",
          data: {},
        });
      }
      var localDate;

      if (req.query && req.query.localDate) {
        localDate = moment(
          new Date(req.query.localDate),
          "YYYY-MM-DD"
        ).toDate();
      } else {
        localDate = moment().toDate();
      }
  
      const aggregate = [
        {
          $match: {
            _id: new ObjectId(eventId),
            category: { $exists: true },
            isPublic: true,
            relation_id: ObjectId(relation_id)
          },
        },
        {
          $sort: {
            createdAt: -1,
          },
        },
        {
          $lookup: {
            from: "event_categories",
            localField: "category",
            foreignField: "_id",
            as: "categoryDetails",
          },
        },
        {
          $lookup: {
            from: "events",
            localField: "category",
            foreignField: "category",
            as: "relatedEvents",
            pipeline: [
              {
                $match: {
                  _id: { $ne: new ObjectId(eventId) },
                  relation_id: new ObjectId(relation_id),
                  status: "published",
                  isDelete: false,
                  isPublic: true
                },
              },
              ...dateValidationNew,
              {
                $addFields: {
                  Date: {
                    $let: {
                      vars: {
                        year: { $substr: ["$endDate", 6, 10] },
                        month: { $substr: ["$endDate", 0, 2] },
                        dayOfMonth: { $substr: ["$endDate", 3, 2] },
                        startMinute: { $substr: ["$endTime", 3, 2] },
                        startHours: {
                          $toString: {
                            $cond: {
                              if: { $eq: [{ $substr: ["$endTime", 6, 2] }, "am"] },
                              then: {
                                $cond: {
                                  if: { $eq: [{ $substr: ["$endTime", 0, 2] }, "12"] },
                                  then: "00", // Midnight (12 AM) should be converted to "00"
                                  else: { $substr: ["$endTime", 0, 2] },
                                },
                              },
                              else: {
                                $cond: {
                                  if: { $eq: [{ $substr: ["$endTime", 0, 2] }, "12"] },
                                  then: "12", // Noon (12 PM) should remain "12"
                                  else: {
                                    $add: [
                                      { $toInt: { $substr: ["$endTime", 0, 2] } },
                                      12, // Adding 12 to convert PM times to 24-hour format
                                    ],
                                  },
                                },
                              },
                            },
                          },
                        },
                        timeZoneTemp: {
                          $cond: {
                            if: {
                              $or: [
                                { $eq: ["$timeZone", ""] },
                                {
                                  $eq: [
                                    "$timeZone",
                                    "(UTC) Dublin, Edinburgh, Lisbon, London",
                                  ],
                                },
                              ],
                            },
                            then: "-00:00",
                            else: { $substr: ["$timeZone", 4, 6] },
                          },
                        },
                      },
                      in: {
                        $toDate: {
                          $concat: [
                            "$$year",
                            "-",
                            "$$month",
                            "-",
                            "$$dayOfMonth",
                            "T",
                            "$$startHours",
                            ":",
                            "$$startMinute",
                            ":",
                            "00.000",
                            "$$timeZoneTemp",
                          ],
                        },
                      },
                    },
                  },
                },
              },
              {
                $match: {
                  Date: { $gt: localDate },
                },
              },
              {
                $lookup: {
                  from: "event_tickets",
                  let: {
                    event_id: "$_id"
                  },
                  pipeline: [
                    {
                      $match: {
                        $expr: {
                          $eq: ["$eventId", "$$event_id"]
                        },
                        isDelete: false
                      }
                    },
                    {
                      $project: {
                        actualPrice: 1,
                        _id: 0
                      }
                    }
                  ],
                  as: "priceData"
                }
              },
              {
                $lookup: {
                  from: "eventpackages",
                  let: {
                    event_id: "$_id"
                  },
                  pipeline: [
                    {
                      $match: {
                        $expr: {
                          $eq: ["$event", "$$event_id"]
                        },
                        isDelete: false
                      }
                    },
                    {
                      $project: {
                        price: 1,
                        _id: 0
                      }
                    }
                  ],
                  as: "priceData1"
                }
              },
              {
                $addFields: {
                  totalTicketCount: {
                    $cond: {
                      if: {
                        $eq: ["$ticketPlatform", "internal"]
                      },
                      then: {
                        $size: "$priceData"
                      },
                      else: {
                        $size: "$priceData1"
                      }
                    }
                  },
                  price: {
                    $ifNull: [
                      {
                        $cond: {
                          if: {
                            $eq: [
                              "$ticketPlatform",
                              "internal"
                            ]
                          },
                          // Check if ticketPlatform is "internal"
                          then: {
                            // If true, get price from priceData
                            $min: {
                              $filter: {
                                input:
                                  "$priceData.actualPrice",
                                cond: {
                                  $gt: ["$$this", 0]
                                }
                              }
                            }
                          },
                          else: {
                            // If false, get price from priceData1
                            $min: {
                              $filter: {
                                input: "$priceData1.price",
                                cond: {
                                  $gt: ["$$this", 0]
                                }
                              }
                            }
                          }
                        }
                      },
                      0
                    ]
                  }
                }
              },        
              {
                $unset: ["priceData", "priceData1"]
              },
              {
                $lookup: {
                  from: "event_tickets",
                  localField: "_id",
                  foreignField: "eventId",
                  as: "result",
                  pipeline: [
                    {
                      $match: {
                        isDelete: false
                      }
                    }
                  ]
                },
              },
              // {
              //   $lookup: {
              //     from: "eventlocations",
              //     localField: "location",
              //     foreignField: "_id",
              //     as: "location",
              //   },
              // },
              // {
              //   $unwind: {
              //     path: "$location",
              //     preserveNullAndEmptyArrays: true,
              //   },
              // },
              {
                $lookup: {
                  from: "eventactivities",
                  localField: "_id",
                  foreignField: "event",
                  as: "activities",
                  pipeline: [
                      {
                          $match: {
                              isDelete: false
                          }
                      }
                  ]
                },
              },
              {
                $lookup: {
                  from: "event_participant_attendees",
                  localField: "_id",
                  foreignField: "event",
                  as: "event_participant_attendees_result",
                  pipeline: [
                    {
                      $match: {
                        $expr: {
                          $eq: ["$isDelete", false]
                        }
                      }
                    }
                  ]
                },
              },
              {
                $addFields: {
                  price_old: {
                    $min: {
                      $map: {
                        input: "$result",
                        as: "ticket",
                        in: {
                          $ifNull: ["$$ticket.actualPrice", Infinity],
                        },
                      },
                    },
                  },
                  registationFlag: {
                      $let: {
                        vars: {},
                        in: {
                          $gt: [
                            {
                              $size:
                                "$event_participant_attendees_result"
                            },
                            0
                          ]
                        }
                      }
                    }
                },
              },
              {
                  $addFields: {
                    price_old: {
                      $cond: {
                        if: {
                          $or: [
                            {
                              $eq: ["$price_old", null],
                            },
                            {
                              $eq: ["$ticketType", "free"],
                            },
                          ],
                        },
                        then: 0,
                        else: "$price_old",
                      },
                    },
                  },
              },
            ],
          },
        },
        {
          $project: {
            title: 1,
            category: 1,
            ticketType: 1,
            categoryDetails: { $arrayElemAt: ["$categoryDetails", 0] },
            relatedEvents: "$relatedEvents",
          },
        },
      ];
  
      const getEventSuggestionList = await event.aggregate(aggregate);
      let filteredEvents = getEventSuggestionList[0]?.relatedEvents || [];
      if( eventData && eventData.ticketType === "free" ){
        filteredEvents = filteredEvents.filter(
          (event) => event.ticketType === "free"
        );
      } else {
        filteredEvents = filteredEvents.filter(
          (event) => event.ticketType === "paid"
        );
      }
      return res
        .status(200)
        .json({
          status: true,
          message: "Event suggestion list fetch succesfully",
          data: filteredEvents,
        });
    } catch (error) {
      console.log({ error });
      return res
        .status(500)
        .json({ status: false, message: "Something went wrong!", error: error });
    }
  };

// delete event activity
exports.deleteEventActivity = async (req, res) => {
    try {
        const getActivity = await eventActivity.findOne({ _id: new ObjectId(req.params.id), isDelete: false }).lean();
        if (getActivity) {
            // const alreadyAddedUsers = await User.find({ notificationFor: { $elemMatch: { id: getActivity._id }, }, }, { "notificationFor.$": 1 });
            // const alreadyAddedUsers = await scheduleNotification.find({ activityId: req?.params?.id, relationId: req?.relation_id, type: "activity", isDelete: false }).lean()
            await scheduleNotification.deleteMany({ activityId: req?.params?.id, relationId: req?.relation_id, type: "activity", isDelete: false })

            // if (alreadyAddedUsers.length) {
                // const NotificationFor = {
                //     id: getActivity._id,
                //     type: "activity",
                //     setBy: "user",
                // };

                // const NotificationForAdmin = {
                //     id: getActivity._id,
                //     type: "activity",
                //     setBy: "admin",
                // };

                // let resCancel = alreadyAddedUsers.map(async (user, i) => {
                //     const scheduleData = await ScheduledNotification.findOne({ createdFor: user._id, idsFor: getActivity._id, createdBy: "user" });
                //     if (scheduleData !== null) {
                //         await User.findOneAndUpdate(
                //             { _id: user._id },
                //             { $pull: { notificationFor: NotificationFor } },
                //             { new: true }
                //         );
                //         await ScheduledNotification.findByIdAndRemove(scheduleData._id);
                //     }
                //     const scheduleDataAdmin = await ScheduledNotification.findOne({ createdFor: user._id, idsFor: getActivity._id, createdBy: "admin" });
                //     if (scheduleDataAdmin !== null) {
                //         await User.findOneAndUpdate(
                //             { _id: user._id },
                //             { $pull: { notificationFor: NotificationForAdmin } },
                //             { new: true }
                //         );
                //         await ScheduledNotification.findByIdAndRemove(scheduleDataAdmin._id);
                //     }
                // });
                // await Promise.all([...resCancel]);
            // }

            const activityData = await eventActivity.findByIdAndUpdate(req.params.id, { isDelete: true }, { new: true });
            if (activityData)
                return res.status(200).json({ status: true, message: "Event activity deleted successfully!", data: activityData });
            else
                return res.status(200).json({ status: false, message: "Something went wrong while deleteing event activity!", });
        } else {
            return res.status(200).json({ status: false, message: "Event activity not found!" });
        }
    } catch (e) {
        return res.status(500).json({ status: false, message: "Something went wrong!", error: e });
    }
};

// get all event activity
exports.getAllEventActivity = async (req, res) => {
    try {
        const allActivityData = await eventActivity.find({ isDelete: false }).sort({ createdAt: -1 });
        if (allActivityData)
            return res.status(200).json({ status: true, message: "All event activity retrive!", data: allActivityData, });
        else
            return res.status(200).json({ status: false, message: "Something went wrong while getting event activity!", });
    } catch (e) {
        return res.status(200).json({ status: false, message: "Something went wrong!", error: e });
    }
};

// get all event activity by event id
exports.getAllEventActivityByEventId = async (req, res) => {
    try {
        // const allActivityData = await eventActivity.find({ isDelete: false, event: req.params.eventId }).collation({ locale: "en" }).sort({ [`${sortField}`]: 1 });
        let match = {
            isDelete: false,
            event: ObjectId(req.params.eventId),
        }
        
        if(req.query.filter){
            match = {...match,status:req.query.filter}
        }

        if(req.query.selectedDataFilter){
            match = {...match,date:req.query.selectedDataFilter}
        }
        const sortType = req.query.sortType === "Dec" ? -1 : 1;
        const allActivityData = await eventActivity.aggregate([
            {
                $match: match
            },
            ...(req.query.search
                ? [
                    {
                      $match: { name: { $regex: ".*" + req.query.search + ".*", $options: "i" } },
                    },
                  ]
            : []),
            {
                $lookup: {
                    from: "eventlocations",
                    localField: "location",
                    foreignField: "_id",
                    as: "LocationData",
                },
            },
            {
                $lookup: {
                    from: "event_wise_participant_types",
                        let: {
                        event: "$event",
                        accessRoles: "$accessRoles"
                    },
                    pipeline: [
                        {
                            $match: {
                                $expr: {
                                    $and: [
                                        {
                                            $eq: ["$isDelete", false]
                                        },
                                        {
                                            $eq: ["$event", "$$event"]
                                        },
                                        {
                                            $in: ["$_id", "$$accessRoles"]
                                        }
                                    ]
                                }
                            }
                        },
                        {
                            $group: {
                                _id: {
                                    roleId: "$_id",
                                    roleName: "$role"
                                },
                                count: { $sum: 1 }
                            }
                        },
            
                    ],
                        as: "eventAttendeeTypes"
                }
            },
            {
              $addFields: {
                  StartDateAndTime: {
                      $cond: {
                          if: { $eq: ["$date", ""] },
                          then: { $toDate: "1970-01-01T00:00:00.000Z" }, // Default date when 'date' is empty
                          else: {
                              $let: {
                                  vars: {
                                      year_: { $substr: ["$date", 6, 4] },
                                      month_: { $substr: ["$date", 0, 2] },
                                      dayOfMonth_: { $substr: ["$date", 3, 2] }
                                  },
                                  in: {
                                      $toDate: {
                                          $concat: ["$$year_", "-", "$$month_", "-", "$$dayOfMonth_", " ", "$startTime"]
                                      }
                                  }
                              }
                          }
                      }
                  }
              }
          },
          {
              $addFields: {
                  EndDateAndTime: {
                      $cond: {
                          if: { $eq: ["$date", ""] },
                          then: { $toDate: "1970-01-01T00:00:00.000Z" }, // Default date when 'date' is empty
                          else: {
                              $let: {
                                  vars: {
                                      year_: { $substr: ["$date", 6, 4] },
                                      month_: { $substr: ["$date", 0, 2] },
                                      dayOfMonth_: { $substr: ["$date", 3, 2] }
                                  },
                                  in: {
                                      $toDate: {
                                          $concat: ["$$year_", "-", "$$month_", "-", "$$dayOfMonth_", " ", "$endTime"]
                                      }
                                  }
                              }
                          }
                      }
                  }
              }
          },
          {
              $project: {
                _id: 1,
                isCheckInAllow: 1,
                name: 1,
                date: 1,
                startTime: 1,
                endTime: 1,
                StartDateAndTime: 1,
                EndDateAndTime: 1,
                status:1,
                LocationName: { $arrayElemAt: ["$LocationData.name", 0] },
                usersCount: {$size: "$userId"},
                roles: {
                    $map: {
                      input: "$eventAttendeeTypes",
                      as: "role",
                      in: {
                        roleId: "$$role._id.roleId",
                        roleName: "$$role._id.roleName",
                        count: "$$role.count"
                      }
                    }
                }
              }
          },
          {
              $addFields: {
                  sortFieldLower: (req.query.sortField === "name" ? { $toLower: "$name" } : req.query.sortField === "location" ? { $toLower: "$LocationName" } : req.query.sortField === "status" ? { $toLower: "$status" } : "$StartDateAndTime" )
              }
          },
          {
                 $sort: (req.query.sortField === "date" ? { "StartDateAndTime": sortType } : req.query.sortField === "startTime" ? { "StartDateAndTime": sortType } : req.query.sortField === "endTime" ? { "EndDateAndTime": sortType } : { sortFieldLower: sortType })
          },
          {
              $project: {
                sortFieldLower: 0
              }
          }
      ]);
      if (allActivityData)
          return res.status(200).json({ status: true, message: "all event activity retrive!", data: allActivityData, });
      else
          return res.status(200).json({ status: false, message: "Something went wrong while getting event activity!", });
  } catch (e) {
      return res.status(200).json({ status: false, message: "Something went wrong!", error: e });
  }
};

// get all event activity by event id
exports.getAllEventActivitySuggestion = async (req, res) => {
    try {
        let match = {
            isDelete: false,
            event: ObjectId(req.params.eventId),
        }

        const allActivityData = await eventActivity.aggregate([
            {
                $match: match
            },
            {
              $addFields: {
                nameLower: { $toLower: "$name" }
              }
            },
            {
              $sort: {
                nameLower: 1
              }
            },
            {
                $project: {
                    name: 1,
                }
            },
        ]);
        if (allActivityData.length)
            return res.status(200).json({ status: true, message: "all event activity suggestion retrive!", data: allActivityData, });
        else
            return res.status(200).json({ status: false, message: "Something went wrong while getting event activity suggestion!", });
    } catch (e) {
        return res.status(200).json({ status: false, message: "Something went wrong!", error: e });
    }
};

// get all event activity by event id
exports.cloneActivitysByEventIdBy = async (req, res) => {
    try {
        const eventId = req.params.eventId
        const { activityId } = req.body;
        
        const objData = await eventActivity.findOne({
            _id: activityId,
            event:eventId,
            isDelete: false,
        })
        if (!objData)
            return res.status(200).json({ status: false, message: "Event activity not Found !!" });

        let obj = objData.toObject();
        if (objData.icon) {
            obj.icon = await copyEventActivityIcon(objData.icon);
        }
        delete obj._id; 
        obj.name = "Copy - " + obj.name;
        obj.session = [];
        obj.location = null;
    
        const eventData = new eventActivity(obj);
        const cloneEvent = await eventData.save();

        if (cloneEvent)
            return res.status(200).json({ status: true, message: "clone event activity successfully!", data: cloneEvent, });
        else
            return res.status(200).json({ status: false, message: "Something went wrong while getting event activity!", });
    } catch (e) {
        return res.status(200).json({ status: false, message: "Something went wrong!", error: e });
    }
};

// clone activity by Id
exports.cloneActivity = async (req, res) => {
    try {
        const { id } = req.body;
        const objData = await eventActivity.findOne({
            _id: id,
            isDelete: false,
        }).select("-_id -__v -updatedAt -createdAt");
        if (!objData){
            return res.status(200).json({ status: false, message: "Event activity not Found !!" });
        }else{
            
            let obj = objData.toObject();
            obj.name = "Copy - " + obj.name;
            obj.session = [];
            const eventData = new eventActivity(obj);
            const cloneEvent = await eventData.save();
    
            if (cloneEvent){
                return res.status(200).json({ status: true, message: "clone event activity successfully!", data: cloneEvent, });
            }else{
                return res.status(200).json({ status: false, message: "Something went wrong while getting event activity!", });
            }
        }
    } catch (e) {
        return res.status(200).json({ status: false, message: "Something went wrong!", error: e });
    }
};

// get event activity by id
exports.getActivityDetail = async (req, res) => {
    try {
      const obj = { relation_id: req.relation_id };
      const eventActivityTemp = await eventActivity
        .findOne({ _id: new ObjectId(req.params.id), isDelete: false })
        .lean();
      let activityData = { ...eventActivityTemp };
      const tiers = await getAllTiersfromBilling(obj, (expand = true));
      const restrictedTierIds = Array.isArray(activityData.event.restrictedAccessTierId)
        ? activityData.event.restrictedAccessTierId.map((id) => id.toString())
        : [];
      const filteredTiers = tiers
        .filter((tier) => restrictedTierIds.includes(tier._id.toString()))
        .map((tier) => ({
          _id: tier._id,
          name: tier.name,
        }));
      activityData.event.restrictedAccessTierId = filteredTiers;
      if (activityData)
        return res
          .status(200)
          .json({
            status: true,
            message: "Event activity detail retrive!",
            data: activityData,
          });
      else
        return res
          .status(200)
          .json({
            status: false,
            message: "Something went wrong while getting event activity!",
          });
    } catch (e) {
      return res
        .status(200)
        .json({ status: false, message: "Something went wrong!", error: e });
    }
  };  

// Save images sapratly
exports.saveFiles = async (req, res) => {
    try {
        const { image } = req;

        if (image) {
            return res.status(200).json({ status: true, media: image, message: "Files saved successfully!", });
        } else
            return res.status(200).json({ status: false, message: "Something went wrong!" });
    } catch (error) {
        return res.status(200).json({ status: false, message: "Something went wrong!" });
    }
};
/** CURD opreation of event activity end **/

/** CURD opreation of event FAQs start **/
// create faq
exports.createFaq = async (req, res) => {
    try {
        const body = req.body;
        if (body) {
            const ids = await eventFaqs.find({ event:body.event,isDelete: false }, { _id: 1, order: 1 }).sort({ order: -1 });
            let newOrder = (ids && ids.length > 0) ? ids[0].order + 1 : 1

            const newFaq = new eventFaqs({ ...body,order:newOrder });
            const faqData = await newFaq.save();
            if (faqData)
                return res.status(200).json({ status: true, message: "FAQs added successfully!", data: faqData, });
            else
                return res.status(200).json({ status: false, message: "Something went wrong while adding FAQs!", });
        } else {
            return res.status(200).json({ status: false, message: "All fields are required!", });
        }
    } catch (error) {
        return res.status(500).json({ status: false, message: "Internal server error!", error: error });
    }
};

// update faq
exports.editFaq = async (req, res) => {
    try {
        const body = req.body;
        const getFaq = await eventFaqs.findOne({ _id: new ObjectId(req.params.id), isDelete: false }).lean();
        if (getFaq) {
            const faqData = await eventFaqs.findByIdAndUpdate(req.params.id,
                {
                    question: body.question ?? getFaq.question,
                    answer: body.answer ?? getFaq.answer,
                    event: body.event ?? getFaq.event,
                },
                { new: true }
            );
            if (faqData)
                return res.status(200).json({ status: true, message: "FAQs updated successfully!", data: faqData, });
            else
                return res.status(200).json({ status: false, message: "Something went wrong while updating FAQs!", });
        } else {
            return res.status(200).json({ status: false, message: "FAQs not found!" });
        }
    } catch (error) {
        return res.status(500).json({ status: false, message: "Internal server error!", error: error });
    }
};

// delete faq
exports.deleteFaq = async (req, res) => {
    try {
        const getFaq = await eventFaqs.findOne({ _id: new ObjectId(req.params.id), isDelete: false }).lean();
        if (getFaq) {
            const faqData = await eventFaqs.findByIdAndUpdate(req.params.id, { isDelete: true }, { new: true });
            if (faqData)
                return res.status(200).json({ status: true, message: "FAQs deleted successfully!", data: faqData });
            else
                return res.status(200).json({ status: false, message: "Something went wrong while deleteing FAQs!", });
        } else {
            return res.status(200).json({ status: false, message: "FAQs not found!" });
        }
    } catch (error) {
        return res.status(500).json({ status: false, message: "Internal server error!", error: error });
    }
};

// listing of faqs
exports.getAllFaqs = async (req, res) => {
    try {
        const allFaqsData = await eventFaqs.find({ isDelete: false }).sort({ createdAt: -1 });
        if (allFaqsData)
            return res.status(200).json({ status: true, message: "All FAQs retrive!", data: allFaqsData, });
        else
            return res.status(200).json({ status: false, message: "FAQs not found!", });
    } catch (error) {
        return res.status(500).json({ status: false, message: "Internal server error!", error: error });
    }
};

// get all event packege by event id
exports.getAllEventFaqsByEventId = async (req, res) => {
    try {
        const allFaqsData = await eventFaqs.find({ isDelete: false, event: req.params.eventId }).sort({ order: 1 });
        if (allFaqsData)
            return res.status(200).json({ status: true, message: "All FAQs retrive!", data: allFaqsData, });
        else
            return res.status(200).json({ status: false, message: "Something went wrong while getting all FAQs!", });
    } catch (e) {
        return res.status(500).json({ status: false, message: "Internal server error!", error: e });
    }
};

// get all event packege by event id
exports.reorderFaqsOfEventId = async (req, res) => {
    try {
        const ids = req.body.ids
        if (ids.length > 0) {
            let resOrder = ids.map(async (item, i) => {
                await eventFaqs.findByIdAndUpdate(ObjectId(item), { order: i + 1 }, { new: true })
            });
            await Promise.all([...resOrder]);
        }
        const allFaqsData = await eventFaqs.find({ isDelete: false, event: req.params.eventId }).sort({ order: 1 });
        
        if (allFaqsData)
            return res.status(200).json({ status: true, message: "Reordered FAQs retrive!", data: allFaqsData, });
        else
            return res.status(200).json({ status: false, message: "Something went wrong while getting FAQs!", });
    } catch (e) {
        return res.status(500).json({ status: false, message: "Internal server error!", error: e });
    }
};

// faq details data
exports.getFaqDetail = async (req, res) => {
    try {
        const faqData = await eventFaqs.findOne({ _id: new ObjectId(req.params.id), isDelete: false });
        if (faqData)
            return res.status(200).json({ status: true, message: "FAQs detail retrive!", data: faqData, });
        else
            return res.status(200).json({ status: false, message: "FAQs detail not found!", });
    } catch (error) {
        return res.status(500).json({ status: false, message: "Internal server error!", error: error });
    }
};
/** CURD opreation of event FAQs end **/

/** CURD opreation of event contact support details start **/
// create contact support details
exports.createContactSupport = async (req, res) => {
    try {
        const body = req.body;
        if (body) {
            const newcontactData = new eventContactSupport({ ...body });
            const contactData = await newcontactData.save();
            if (contactData)
                return res.status(200).json({ status: true, message: "Contact support added successfully!", data: contactData, });
            else
                return res.status(200).json({ status: false, message: "Something went wrong while adding contact support!", });
        } else {
            return res.status(200).json({ status: false, message: "All fields are required!", });
        }
    } catch (error) {
        return res.status(500).json({ status: false, message: "Internal server error!", error: error });
    }
};

// update contact support details
exports.editContactSupport = async (req, res) => {
    try {
        const body = req.body;
        const getContactData = await eventContactSupport.findOne({ _id: new ObjectId(req.params.id), isDelete: false }).lean();
        if (getContactData) {
            const contactData = await eventContactSupport.findByIdAndUpdate(req.params.id,
                {
                    email: body.email ?? getContactData.email,
                    phone: body.phone ?? getContactData.phone,
                    localPhone: body.localPhone ?? getContactData.localPhone,
                    event: body.event ?? getContactData.event,
                },
                { new: true }
            );
            if (contactData)
                return res.status(200).json({ status: true, message: "Contact support data updated successfully!", data: contactData, });
            else
                return res.status(200).json({ status: false, message: "Something went wrong while updating contact data!", });
        } else {
            return res.status(200).json({ status: false, message: "Contact not found!" });
        }
    } catch (error) {
        return res.status(500).json({ status: false, message: "Internal server error!", error: error });
    }
};

// delete contact support details
exports.deleteContactSupport = async (req, res) => {
    try {
        const getContactData = await eventContactSupport.findOne({ _id: new ObjectId(req.params.id), isDelete: false }).lean();
        if (getContactData) {
            const contactData = await eventContactSupport.findByIdAndUpdate(req.params.id, { isDelete: true }, { new: true });
            if (contactData)
                return res.status(200).json({ status: true, message: "Contact data deleted successfully!", data: contactData });
            else
                return res.status(200).json({ status: false, message: "Something went wrong while deleteing contact data!", });
        } else {
            return res.status(200).json({ status: false, message: "Contact not found!" });
        }
    } catch (error) {
        return res.status(500).json({ status: false, message: "Internal server error!", error: error });
    }
};

// listing of contact support details
exports.getAllContactSupports = async (req, res) => {
    try {
        const allContactData = await eventContactSupport.find({ isDelete: false }).sort({ createdAt: -1 });
        if (allContactData)
            return res.status(200).json({ status: true, message: "All contacts retrive!", data: allContactData, });
        else
            return res.status(200).json({ status: false, message: "Contacts not found!", });
    } catch (error) {
        return res.status(500).json({ status: false, message: "Internal server error!", error: error });
    }
};

// listing of contact support list by event id
exports.getAllContactSupportsByEventId = async (req, res) => {
    try {
        const allContactData = await eventContactSupport.find({ event: ObjectId(req.params.id), isDelete: false }).sort({ createdAt: -1 });
        if (allContactData)
            return res.status(200).json({ status: true, message: "All contacts retrive!", data: allContactData, });
        else
            return res.status(200).json({ status: false, message: "Contacts not found!", });
    } catch (error) {
        return res.status(500).json({ status: false, message: "Internal server error!", error: error });
    }
};

// contact support details details data
exports.getContactSupportDetail = async (req, res) => {
    try {
        const contactData = await eventContactSupport.findOne({ _id: new ObjectId(req.params.id), isDelete: false });
        if (contactData)
            return res.status(200).json({ status: true, message: "Contact detail retrive!", data: contactData, });
        else
            return res.status(200).json({ status: false, message: "Contact detail not found!", });
    } catch (error) {
        return res.status(500).json({ status: false, message: "Internal server error!", error: error });
    }
};
/** CURD opreation of event contact support details end **/

exports.sendNotificationForNotifyUserV2 = async (eventId, notifyMsg, notifyFor, notifyForId, relationId) => {
    try {
        let chatData = {}, data = {}, notifyForData = {}, members = [];
        let eventData = await event.findOne({ _id: new ObjectId(eventId), isDelete: false });
        if (notifyFor === "activity") {
            let activityData = await eventActivity.aggregate([
                {
                    $match: { _id: new ObjectId(notifyForId), isDelete: false,status: "published" },
                },
                {
                    $lookup: {
                        from: "sessions",
                        let: { activity_session_id: "$session" },
                        pipeline: [
                            {
                                $match: {
                                    $expr: {
                                        $in: ["$_id", "$$activity_session_id"],
                                    },
                                    // member: true,
                                },
                            },
                            {
                                $lookup: {
                                  from: "event_wise_participant_types",
                                  localField: "accessRoles",
                                  foreignField: "_id",
                                  as: "event_wise_participant_types",
                                  pipeline: [
                                    {
                                      $match: {
                                        $expr: {
                                          $eq: ["$role", "Member"],
                                        },
                                      },
                                    },
                                  ],
                                },
                              },
                              {
                                $unwind: {
                                  path: "$event_wise_participant_types",
                                  preserveNullAndEmptyArrays: false,
                                },
                            },
                            {
                                $lookup: {
                                    from: "rooms",
                                    let: { activity_rooms_id: "$room" },
                                    pipeline: [
                                        {
                                            $match: {
                                                $expr: {
                                                    $eq: ["$_id", "$$activity_rooms_id"],
                                                },
                                            },
                                        },
                                        {
                                            $lookup: {
                                                from: "eventlocations",
                                                let: { location_id: "$location" },
                                                pipeline: [
                                                    {
                                                        $match: {
                                                            $expr: {
                                                                $eq: ["$_id", "$$location_id"],
                                                            },
                                                        },
                                                    },
                                                    { $project: { name: 1, address: 1, country: 1, city: 1, latitude: 1, longitude: 1, locationVisible: 1, locationImages: 1 } },
                                                ],
                                                as: "location"
                                            },
                                        },
                                        {
                                            $unwind: "$location",
                                        },
                                        { $project: { location: 1, } },
                                    ],
                                    as: "room"
                                }
                            },
                            {
                                $unwind: "$room",
                            },
                            { $project: { room: 1 } },
                        ],
                        as: "sessions"
                    },
                },
                {
                    $lookup: {
                        from: "eventlocations",
                        let: { activity_location_id: "$location" },
                        pipeline: [
                            {
                                $match: {
                                    $expr: {
                                        $eq: ["$_id", "$$activity_location_id"],
                                    },
                                },
                            },
                            { $project: { name: 1, address: 1, country: 1, city: 1, latitude: 1, longitude: 1, locationVisible: 1, locationImages: 1 } },
                        ],
                        as: "location"
                    }
                },
                {
                    $addFields: {
                        sessionCount: {
                            $cond: {
                                if: { $isArray: "$sessions" },
                                then: { $size: "$sessions" },
                                else: 0,
                            },
                        },
                    },
                },
                {
                    $project: {
                        _id: 1,
                        name: 1,
                        icon: 1,
                        description: "$shortDescription",
                        shortDescription: 1,
                        longDescription: 1,
                        date: 1,
                        startTime: 1,
                        endDate: 1,
                        endTime: 1,
                        reserved: 1,
                        reserved_URL: 1,
                        location: 1,
                        sessions: 1,
                        member: 1,
                        speaker: 1,
                        partner: 1,
                        guest: 1,
                        sessionCount: 1,
                        notifyChanges: 1,
                        notifyChangeText: 1,
                        accessRoles: 1,
                        userId: 1,
                    },
                },
            ]);
            notifyForData = activityData[0];
        } else if (notifyFor === "session") {
            notifyForData = await Session.findOne({ _id: new ObjectId(notifyForId), isDelete: false });
            notifyForData = await Room.findOne({ _id: new ObjectId(notifyForId), isDelete: false });
        } else if (notifyFor === "room") {
        }

        if (notifyFor === "room") {
            let pipeline = [
                {
                  '$match': {
                    'event': new ObjectId(eventId), 
                    'isDelete': false
                  }
                }, {
                  '$lookup': {
                    'from': 'airtable-syncs', 
                    'localField': 'user', 
                    'foreignField': '_id', 
                    'as': 'airtable-syncs_result', 
                    'pipeline': [
                      {
                        '$match': {
                          '$or': [
                            {
                              'isDelete': false
                            }, {
                              'isDelete': {
                                '$exists': false
                              }
                            }
                          ]
                        }
                      }
                    ]
                  }
                }, {
                  '$unwind': {
                    'path': '$airtable-syncs_result', 
                    'preserveNullAndEmptyArrays': false
                  }
                }, {
                  '$project': {
                    '_id': {
                      '$ifNull': [
                        '$airtable-syncs_result._id', ''
                      ]
                    }, 
                    'deviceToken': {
                      '$ifNull': [
                        '$airtable-syncs_result.deviceToken', ''
                      ]
                    }, 
                    'email': {
                      '$ifNull': [
                        '$airtable-syncs_result.email', ''
                      ]
                    },
                    'first_name': {
                        '$ifNull': [
                          '$airtable-syncs_result.first_name', ''
                        ]
                    }, 
                    'last_name': {
                        '$ifNull': [
                            '$airtable-syncs_result.last_name', ''
                        ]
                    },
                    'display_name': {
                        '$ifNull': [
                            '$airtable-syncs_result.display_name', ''
                        ]
                    },
                    'profileImg': {
                      '$ifNull': [
                        '$airtable-syncs_result.profileImg', ''
                      ]
                    }, 
                    'firebaseId': {
                      '$ifNull': [
                        '$airtable-syncs_result.firebaseId', ''
                      ]
                    }, 
                    'attendeeDetail': {
                      'name': {
                        '$ifNull': [
                          '$airtable-syncs_result.attendeeDetail.name', ''
                        ]
                      }, 
                      'firstName': {
                        '$ifNull': [
                          '$airtable-syncs_result.attendeeDetail.firstName', ''
                        ]
                      }, 
                      'lastName': {
                        '$ifNull': [
                          '$airtable-syncs_result.attendeeDetail.lastName', ''
                        ]
                      }, 
                      'photo': {
                        '$ifNull': [
                          '$airtable-syncs_result.attendeeDetail.photo', ''
                        ]
                      }
                    }
                  }
                }, {
                  '$group': {
                    '_id': '$_id', 
                    'data': {
                      '$first': '$$ROOT'
                    }
                  }
                }, {
                  '$replaceRoot': {
                    'newRoot': '$data'
                  }
                }
              ]
              members = await eventParticipantAttendees.aggregate(pipeline);
            } else {
            if ( notifyForData && notifyForData.accessRoles && notifyForData.accessRoles.length ) {
                let accessRolesTemp = [];
                accessRolesTemp = notifyForData.accessRoles.map((id) => { return ObjectId(id) });

                let allRoleTemp = await eventWiseParticipantTypes.find({
                    event: new ObjectId(eventId),
                    isDelete: false,
                    $or: [
                      { role: "Staff" },
                      {
                        _id: {
                          $in: accessRolesTemp,
                        },
                      },
                    ],
                  });
                  for (let i = 0; i < allRoleTemp.length; i++) {
                    if (allRoleTemp[i]["role"] == "Member") {
                      let obj = allRoleTemp.find((o) => o.role == "Staff");
                      if (obj) {
                        accessRolesTemp.push(ObjectId(obj._id));
                      }
                    }
                  }
                
                let pipeline = [
                    {
                      '$match': {
                        'event': new ObjectId(eventId), 
                        'isDelete': false,
                        'role': { $in : accessRolesTemp },
                      }
                    }, {
                      '$lookup': {
                        'from': 'airtable-syncs', 
                        'localField': 'user', 
                        'foreignField': '_id', 
                        'as': 'airtable-syncs_result', 
                        'pipeline': [
                          {
                            '$match': {
                              '$or': [
                                {
                                  'isDelete': false
                                }, {
                                  'isDelete': {
                                    '$exists': false
                                  }
                                }
                              ]
                            }
                          }
                        ]
                      }
                    }, {
                      '$unwind': {
                        'path': '$airtable-syncs_result', 
                        'preserveNullAndEmptyArrays': false
                      }
                    }, {
                      '$project': {
                        '_id': {
                          '$ifNull': [
                            '$airtable-syncs_result._id', ''
                          ]
                        }, 
                        'deviceToken': {
                          '$ifNull': [
                            '$airtable-syncs_result.deviceToken', []
                          ]
                        }, 
                        'email': {
                          '$ifNull': [
                            '$airtable-syncs_result.email', ''
                          ]
                        },
                        'first_name': {
                            '$ifNull': [
                              '$airtable-syncs_result.first_name', ''
                            ]
                        }, 
                        'last_name': {
                            '$ifNull': [
                                '$airtable-syncs_result.last_name', ''
                            ]
                        }, 
                        'display_name': {
                            '$ifNull': [
                                '$airtable-syncs_result.display_name', ''
                            ]
                        }, 
                        'profileImg': {
                          '$ifNull': [
                            '$airtable-syncs_result.profileImg', ''
                          ]
                        }, 
                        'firebaseId': {
                          '$ifNull': [
                            '$airtable-syncs_result.firebaseId', ''
                          ]
                        }, 
                        'attendeeDetail': {
                          'name': {
                            '$ifNull': [
                              '$airtable-syncs_result.attendeeDetail.name', ''
                            ]
                          }, 
                          'firstName': {
                            '$ifNull': [
                              '$airtable-syncs_result.attendeeDetail.firstName', ''
                            ]
                          }, 
                          'lastName': {
                            '$ifNull': [
                              '$airtable-syncs_result.attendeeDetail.lastName', ''
                            ]
                          }, 
                          'photo': {
                            '$ifNull': [
                              '$airtable-syncs_result.attendeeDetail.photo', ''
                            ]
                          }
                        }
                      }
                    }, {
                      '$group': {
                        '_id': '$_id', 
                        'data': {
                          '$first': '$$ROOT'
                        }
                      }
                    }, {
                      '$replaceRoot': {
                        'newRoot': '$data'
                      }
                    }
                  ]
                  let userIdTemp = [];
                  let userDataTemp = [];
                  if(notifyForData && notifyForData.userId && notifyForData.userId.length > 0 ){
                    userIdTemp = notifyForData.userId.map((id) => { return ObjectId(id) });
                    let pipelineUser = [
                        {
                        '$match': {
                            '_id': {
                            '$in': userIdTemp
                            }, 
                            '$or': [
                            {
                                'isDelete': false
                            }, {
                                'isDelete': {
                                '$exists': false
                                }
                            }
                            ]
                        }
                        }, {
                        '$project': {
                            '_id': {
                            '$ifNull': [
                                '$_id', ''
                            ]
                            }, 
                            'deviceToken': {
                            '$ifNull': [
                                '$deviceToken', []
                            ]
                            }, 
                            'email': {
                            '$ifNull': [
                                '$email', ''
                            ]
                            }, 
                            'first_name': {
                                '$ifNull': [
                                  '$first_name', ''
                                ]
                            }, 
                            'last_name': {
                                '$ifNull': [
                                    '$last_name', ''
                                ]
                            },
                            'display_name': {
                                '$ifNull': [
                                    '$display_name', ''
                                ]
                            },
                            'profileImg': {
                            '$ifNull': [
                                '$profileImg', ''
                            ]
                            }, 
                            'firebaseId': {
                            '$ifNull': [
                                '$firebaseId', ''
                            ]
                            }, 
                            'attendeeDetail': {
                            'name': {
                                '$ifNull': [
                                '$attendeeDetail.name', ''
                                ]
                            }, 
                            'firstName': {
                                '$ifNull': [
                                '$attendeeDetail.firstName', ''
                                ]
                            }, 
                            'lastName': {
                                '$ifNull': [
                                '$attendeeDetail.lastName', ''
                                ]
                            }, 
                            'photo': {
                                '$ifNull': [
                                '$attendeeDetail.photo', ''
                                ]
                            }
                            }
                        }
                        }
                    ];
                    userDataTemp = await User.aggregate(pipelineUser);
                }

                  let dataTemp = await eventParticipantAttendees.aggregate(pipeline);
                  if (dataTemp.length > 0){
                    members = members.concat(dataTemp);
                  }
                //   if (userDataTemp.length > 0 && members.length > 0 ){
                //     for (let j = 0; j < userDataTemp.length; j++) {
                //         let userTemp = members.find(e => e._id.toString() === userDataTemp[j]._id.toString());
                //         if(!userTemp){
                //             members.push(userTemp)
                //         }
                //     }
                //   } else if ( members.length === 0 ){
                //     members = members.concat(userDataTemp);
                //   }

                if(userDataTemp && userDataTemp.length){
                    members = members.concat(userDataTemp);
                }
            }else {
            if (notifyForData.member === true) {
                const menberData = await User.aggregate([
                    {
                        $match: {
                            "attendeeDetail.evntData": { $elemMatch: { event: new ObjectId(eventId), [`member`]: true } },
                        },
                    },
                    {
                        $project: {
                            _id: 1,
                            deviceToken: 1,
                            email: 1,
                            first_name: 1,
                            last_name: 1,
                            display_name: 1,
                            profileImg: 1,
                            firebaseId: 1,
                            attendeeDetail: {
                                name: "$attendeeDetail.name" ? "$attendeeDetail.name" : "",
                                firstName: "$attendeeDetail.firstName" ? "$attendeeDetail.firstName" : "",
                                lastName: "$attendeeDetail.lastName" ? "$attendeeDetail.lastName" : "",
                                photo: "$attendeeDetail.photo" ? "$attendeeDetail.photo" : "",
                            },
                        }
                    }
                ]);
                if (menberData.length > 0)
                    members = members.concat(menberData);
            } 
            if (notifyForData.speaker === true) {
                const speakerData = await User.aggregate([
                    {
                        $match: {
                            "attendeeDetail.evntData": { $elemMatch: { event: new ObjectId(eventId), [`speaker`]: true } },
                        },
                    },
                    {
                        $project: {
                            _id: 1,
                            deviceToken: 1,
                            email: 1,
                            first_name: 1,
                            last_name: 1,
                            display_name: 1,
                            profileImg: 1,
                            firebaseId: 1,
                            attendeeDetail: {
                                name: "$attendeeDetail.name" ? "$attendeeDetail.name" : "",
                                firstName: "$attendeeDetail.firstName" ? "$attendeeDetail.firstName" : "",
                                lastName: "$attendeeDetail.lastName" ? "$attendeeDetail.lastName" : "",
                                photo: "$attendeeDetail.photo" ? "$attendeeDetail.photo" : "",
                            },
                        }
                    }
                ]);
                if (speakerData.length > 0)
                    members = members.concat(speakerData);
            } 
            if (notifyForData.partner === true) {
                const peartnerData = await User.aggregate([
                    {
                        $match: {
                            "attendeeDetail.evntData": { $elemMatch: { event: new ObjectId(eventId), [`partner`]: true } },
                        },
                    },
                    {
                        $project: {
                            _id: 1,
                            deviceToken: 1,
                            email: 1,
                            first_name: 1,
                            last_name: 1,
                            display_name: 1,
                            profileImg: 1,
                            firebaseId: 1,
                            attendeeDetail: {
                                name: "$attendeeDetail.name" ? "$attendeeDetail.name" : "",
                                firstName: "$attendeeDetail.firstName" ? "$attendeeDetail.firstName" : "",
                                lastName: "$attendeeDetail.lastName" ? "$attendeeDetail.lastName" : "",
                                photo: "$attendeeDetail.photo" ? "$attendeeDetail.photo" : "",
                            },
                        }
                    }
                ]);
                if (peartnerData.length > 0)
                    members = members.concat(peartnerData);
            }
            if (notifyForData.guest === true) {
                const guestData = await User.aggregate([
                    {
                        $match: {
                            "attendeeDetail.evntData": { $elemMatch: { event: new ObjectId(eventId), [`guest`]: true } },
                        },
                    },
                    {
                        $project: {
                            _id: 1,
                            deviceToken: 1,
                            email: 1,
                            first_name: 1,
                            last_name: 1,
                            display_name: 1,
                            profileImg: 1,
                            firebaseId: 1,
                            attendeeDetail: {
                                name: "$attendeeDetail.name" ? "$attendeeDetail.name" : "",
                                firstName: "$attendeeDetail.firstName" ? "$attendeeDetail.firstName" : "",
                                lastName: "$attendeeDetail.lastName" ? "$attendeeDetail.lastName" : "",
                                photo: "$attendeeDetail.photo" ? "$attendeeDetail.photo" : "",
                            },
                        }
                    }
                ]);
                if (guestData.length > 0)
                    members = members.concat(guestData);
            }
        }
        }
        chatData = {
            "senderId": process.env.ADMIN_ID,
            "senderName": "Admin",
            "eventId": eventData._id,
            "eventName": eventData.title,
            "chatType": "nofifyUser",
        }

        if (notifyFor === "activity") {
            chatData.activityId = notifyForData._id;
            chatData.activityName = notifyForData.name;
            chatData.sessionCount = notifyForData.sessionCount;
        } else if (notifyFor === "session") {
            chatData.sessionId = notifyForData._id;
            chatData.sessionName = notifyForData.name;
        }

        data = {
            "eventName": eventData.title,
            "chatType": "nofifyUser",
            "notifyMsg": notifyMsg,
        }

        let notificationTemplate = await notification_template.notify_user_for_update(data);
        if (members.length > 0) {
            let createNotificationArray = [];
            let memberIds = [];
            for (let i = 0; i < members.length; i++) {
                let member = members[i];
                memberIds.push(new ObjectId(member["_id"]));
            }
            let pipeline = [
                {
                    '$match': {
                        '_id': {
                            '$in': memberIds
                        }
                    }
                }, {
                    '$project': {
                        'deviceToken': 1,
                        'isCollaborator': 1,
                        'Preferred Email': 1
                    }
                }, {
                    '$lookup': {
                        'from': 'invitecollaborators',
                        'localField': 'Preferred Email',
                        'foreignField': 'email',
                        'as': 'invitecollaborators_result',
                        'pipeline': [
                            {
                                '$match': {
                                    'isDelete': false
                                }
                            }, {
                                '$lookup': {
                                    'from': 'membership_plans',
                                    'localField': 'memberShipPlanDetails.planId',
                                    'foreignField': '_id',
                                    'as': 'memberShipPlanDetails.planId',
                                    'pipeline': [
                                        {
                                            '$lookup': {
                                                'from': 'accessresources',
                                                'localField': 'accessResources',
                                                'foreignField': '_id',
                                                'as': 'accessResources'
                                            }
                                        }
                                    ]
                                }
                            }
                        ]
                    }
                }, {
                    '$lookup': {
                        'from': 'chatlists', 
                        'localField': '_id', 
                        'foreignField': 'userId', 
                        'as': 'chatlists_result'
                    }
                }, {
                    '$addFields': {
                        'unReadCount': {
                            '$size': '$chatlists_result'
                        }
                    }
                }, {
                    '$unset': 'chatlists_result'
                }
            ]
            let userMemberData = await User.aggregate(pipeline);
            for (let i = 0; i < userMemberData.length; i++) {
                let isCollaboratorUser = false
                let isCollaboratorEventAccess = false
                let userMember = userMemberData[i];
                if (userMember.isCollaborator !== undefined && userMember.isCollaborator !== null && userMember.isCollaborator) {
                    isCollaboratorUser = true
                    try {
                        if (userMember.invitecollaborators_result && userMember.invitecollaborators_result.length > 0 && userMember.invitecollaborators_result[0]) {
                            isCollaboratorEventAccess = userMember.invitecollaborators_result[0]["memberShipPlanDetails"]["planId"][0]["accessResources"].filter(item => item.name.toLowerCase().includes("event")).length > 0 ? true : false;
                        }
                    } catch (error) {
                        console.log({ error });
                    }
                }
                if (!isCollaboratorUser || (isCollaboratorUser && isCollaboratorEventAccess)) {
                    const userEdgeData = await user_edges.findOne({user_id: new ObjectId(userMember?._id), relation_id: new ObjectId(relationId)}, {deviceToken: 1});
                    let userDeviceToken = userEdgeData?.deviceToken || [];
                    if (userDeviceToken && userDeviceToken.length !== 0) {
                        if (userDeviceToken) {
                            createNotificationArray.push({
                                title: notificationTemplate?.template?.title,
                                body: notificationTemplate?.template?.body,
                                createdBy: process.env.ADMIN_ID,
                                createdFor: userMember._id,
                                role: "nofifyUser"
                            });
                            let data = {
                                "notification": notificationTemplate?.template?.title,
                                "description": notificationTemplate?.template?.body,
                                "device_token": userDeviceToken,
                                "collapse_key": eventData._id,
                                "sub_title": "",
                                "notification_data": {
                                    "type": "notify_user_for_update",
                                    "content": chatData
                                }
                            }

                            send_notification(data);
                        }
                    }
                }
            }
            await Notification.insertMany(createNotificationArray);
        }

    } catch (error) {
        return { status: false, message: "Inernal server error!", error: `${error.message}`, };
    }
};

// send notification in case of notify user when edit any session, room or activity for specific event
exports.sendNotificationForNotifyUserAPI = async (req, res) => {
    try {
        let chatData = {}, data = {}, notifyForData = {}, members = [];
        const body = req.body;
        const notifyFor = body.notifyFor;
        const notifyMsg = body.notifyMsg;
        let eventData = await event.findOne({ _id: new ObjectId(body.eventId), isDelete: false });
        if (notifyFor === "activity") {
            let activityData = await eventActivity.aggregate([
                {
                    $match: { _id: new ObjectId(body.notifyForId), isDelete: false },
                },
                {
                    $lookup: {
                        from: "sessions",
                        let: { activity_session_id: "$session" },
                        pipeline: [
                            {
                                $match: {
                                    $expr: {
                                        $in: ["$_id", "$$activity_session_id"],
                                    },
                                    member: true,
                                },
                            },
                            {
                                $lookup: {
                                    from: "rooms",
                                    let: { activity_rooms_id: "$room" },
                                    pipeline: [
                                        {
                                            $match: {
                                                $expr: {
                                                    $eq: ["$_id", "$$activity_rooms_id"],
                                                },
                                            },
                                        },
                                        {
                                            $lookup: {
                                                from: "eventlocations",
                                                let: { location_id: "$location" },
                                                pipeline: [
                                                    {
                                                        $match: {
                                                            $expr: {
                                                                $eq: ["$_id", "$$location_id"],
                                                            },
                                                        },
                                                    },
                                                    { $project: { name: 1, address: 1, country: 1, city: 1, latitude: 1, longitude: 1, locationVisible: 1, locationImages: 1 } },
                                                ],
                                                as: "location"
                                            },
                                        },
                                        {
                                            $unwind: "$location",
                                        },
                                        { $project: { location: 1, } },
                                    ],
                                    as: "room"
                                }
                            },
                            {
                                $unwind: "$room",
                            },
                            { $project: { room: 1 } },
                        ],
                        as: "sessions"
                    },
                },
                {
                    $lookup: {
                        from: "eventlocations",
                        let: { activity_location_id: "$location" },
                        pipeline: [
                            {
                                $match: {
                                    $expr: {
                                        $eq: ["$_id", "$$activity_location_id"],
                                    },
                                },
                            },
                            { $project: { name: 1, address: 1, country: 1, city: 1, latitude: 1, longitude: 1, locationVisible: 1, locationImages: 1 } },
                        ],
                        as: "location"
                    }
                },
                {
                    $addFields: {
                        sessionCount: {
                            $cond: {
                                if: { $isArray: "$sessions" },
                                then: { $size: "$sessions" },
                                else: 0,
                            },
                        },
                    },
                },
                {
                    $project: {
                        _id: 1,
                        name: 1,
                        icon: 1,
                        description: "$shortDescription",
                        shortDescription: 1,
                        longDescription: 1,
                        date: 1,
                        startTime: 1,
                        endDate: 1,
                        endTime: 1,
                        reserved: 1,
                        reserved_URL: 1,
                        location: 1,
                        sessions: 1,
                        member: 1,
                        speaker: 1,
                        partner: 1,
                        guest: 1,
                        sessionCount: 1,
                        notifyChanges: 1,
                        notifyChangeText: 1,
                    },
                },
            ]);
            notifyForData = activityData[0];
        } else if (notifyFor === "session") {
            notifyForData = await Session.findOne({ _id: new ObjectId(body.notifyForId), isDelete: false });
        } else if (notifyFor === "room") {
            notifyForData = await Room.findOne({ _id: new ObjectId(body.notifyForId), isDelete: false });
        }

        if (notifyFor === "room") {
            members = await User.find({ "attendeeDetail.evntData": { $elemMatch: { event: new ObjectId(body.eventId) } }, isDelete: false }, {
                _id: 1,
                deviceToken: 1,
                email: 1,
                first_name: 1,
                last_name: 1,
                display_name: 1,
                profileImg: 1,
                firebaseId: 1,
                attendeeDetail: {
                    name: "$attendeeDetail.name" ? "$attendeeDetail.name" : "",
                    firstName: "$attendeeDetail.firstName" ? "$attendeeDetail.firstName" : "",
                    lastName: "$attendeeDetail.lastName" ? "$attendeeDetail.lastName" : "",
                    photo: "$attendeeDetail.photo" ? "$attendeeDetail.photo" : "",
                },
            });
        } else {
            if (notifyForData.member === true) {
                const menberData = await User.aggregate([
                    {
                        $match: {
                            "attendeeDetail.evntData": { $elemMatch: { event: new ObjectId(body.eventId), [`member`]: true } },
                        },
                    },
                    {
                        $project: {
                            _id: 1,
                            deviceToken: 1,
                            email: 1,
                            first_name: 1,
                            last_name: 1,
                            display_name: 1,
                            profileImg: 1,
                            firebaseId: 1,
                            attendeeDetail: {
                                name: "$attendeeDetail.name" ? "$attendeeDetail.name" : "",
                                firstName: "$attendeeDetail.firstName" ? "$attendeeDetail.firstName" : "",
                                lastName: "$attendeeDetail.lastName" ? "$attendeeDetail.lastName" : "",
                                photo: "$attendeeDetail.photo" ? "$attendeeDetail.photo" : "",
                            },
                        }
                    }
                ]);
                if (menberData.length > 0)
                    members = members.concat(menberData);
            } else if (notifyForData.speaker === true) {
                const speakerData = await User.aggregate([
                    {
                        $match: {
                            "attendeeDetail.evntData": { $elemMatch: { event: new ObjectId(body.eventId), [`speaker`]: true } },
                        },
                    },
                    {
                        $project: {
                            _id: 1,
                            deviceToken: 1,
                            email: 1,
                            first_name: 1,
                            last_name: 1,
                            display_name: 1,
                            profileImg: 1,
                            firebaseId: 1,
                            attendeeDetail: {
                                name: "$attendeeDetail.name" ? "$attendeeDetail.name" : "",
                                firstName: "$attendeeDetail.firstName" ? "$attendeeDetail.firstName" : "",
                                lastName: "$attendeeDetail.lastName" ? "$attendeeDetail.lastName" : "",
                                photo: "$attendeeDetail.photo" ? "$attendeeDetail.photo" : "",
                            },
                        }
                    }
                ]);
                if (speakerData.length > 0)
                    members = members.concat(speakerData);
            } else if (notifyForData.partner === true) {
                const peartnerData = await User.aggregate([
                    {
                        $match: {
                            "attendeeDetail.evntData": { $elemMatch: { event: new ObjectId(body.eventId), [`partner`]: true } },
                        },
                    },
                    {
                        $project: {
                            _id: 1,
                            deviceToken: 1,
                            email: 1,
                            first_name: 1,
                            last_name: 1,
                            display_name: 1,
                            profileImg: 1,
                            firebaseId: 1,
                            attendeeDetail: {
                                name: "$attendeeDetail.name" ? "$attendeeDetail.name" : "",
                                firstName: "$attendeeDetail.firstName" ? "$attendeeDetail.firstName" : "",
                                lastName: "$attendeeDetail.lastName" ? "$attendeeDetail.lastName" : "",
                                photo: "$attendeeDetail.photo" ? "$attendeeDetail.photo" : "",
                            },
                        }
                    }
                ]);
                if (peartnerData.length > 0)
                    members = members.concat(peartnerData);
            } else if (notifyForData.guest === true) {
                const guestData = await User.aggregate([
                    {
                        $match: {
                            "attendeeDetail.evntData": { $elemMatch: { event: new ObjectId(body.eventId), [`guest`]: true } },
                        },
                    },
                    {
                        $project: {
                            _id: 1,
                            deviceToken: 1,
                            email: 1,
                            first_name: 1,
                            last_name: 1,
                            display_name: 1,
                            profileImg: 1,
                            firebaseId: 1,
                            attendeeDetail: {
                                name: "$attendeeDetail.name" ? "$attendeeDetail.name" : "",
                                firstName: "$attendeeDetail.firstName" ? "$attendeeDetail.firstName" : "",
                                lastName: "$attendeeDetail.lastName" ? "$attendeeDetail.lastName" : "",
                                photo: "$attendeeDetail.photo" ? "$attendeeDetail.photo" : "",
                            },
                        }
                    }
                ]);
                if (guestData.length > 0)
                    members = members.concat(guestData);
            }
        }

        chatData = {
            "senderId": process.env.ADMIN_ID,
            "senderName": "Admin",
            "eventId": eventData._id,
            "eventName": eventData.title,
            "chatType": "nofifyUser",
        }

        if (notifyFor === "activity") {
            chatData.activityId = notifyForData._id;
            chatData.activityName = notifyForData.name;
            chatData.sessionCount = notifyForData.sessionCount;
        } else if (notifyFor === "session") {
            chatData.sessionId = notifyForData._id;
            chatData.sessionName = notifyForData.name;
        }

        data = {
            "eventName": eventData.title,
            "chatType": "nofifyUser",
            "notifyMsg": notifyMsg,
        }

        let notificationTemplate = await notification_template.notify_user_for_update(data);
        if (members.length > 0) {
            let sendNotify = members?.map(async member => {

                let isCollaboratorUser = false
                let isCollaboratorEventAccess = false
                let userMember = await User.findOne({ _id: new ObjectId(member._id) }, { deviceToken: 1, isCollaborator: 1, "Preferred Email": 1 });
                if (userMember.isCollaborator !== undefined && userMember.isCollaborator !== null && userMember.isCollaborator) {
                    isCollaboratorUser = true
                    const collabaorator = await collabaorators.findOne({ email: userMember["Preferred Email"], isDelete: false }).populate('memberShipPlanDetails.planId').lean()
                    if (collabaorator) {
                        isCollaboratorEventAccess = collabaorator.memberShipPlanDetails.planId.accessResources.filter(item => item.name.toLowerCase().includes("event")).length > 0 ? true : false
                    }


                }
                if (!isCollaboratorUser || (isCollaboratorUser && isCollaboratorEventAccess)) {

                const userEdgeData = await user_edges.findOne({user_id: new ObjectId(member._id), relation_id: new ObjectId(req.relation_id)}, {deviceToken: 1});

                let userDeviceToken = userEdgeData?.deviceToken || [];
                    if (userDeviceToken.length !== 0) {
                        if (userMember) {
                            await new Notification({ title: notificationTemplate?.template?.title, body: notificationTemplate?.template?.body, createdBy: process.env.ADMIN_ID, createdFor: member._id, role: "nofifyUser" }).save();

                            let data = {
                                "notification": notificationTemplate?.template?.title,
                                "description": notificationTemplate?.template?.body,
                                "device_token": userDeviceToken,
                                "collapse_key": eventData._id,
                                "sub_title": "",
                                "notification_data": {
                                    "type": "notify_user_for_update",
                                    "content": chatData
                                }
                            }

                            send_notification(data);
                        }
                    }
                }
            });
            await Promise.all([...sendNotify]);
            return res.status(200).json({ status: true, message: "notification send...", });
        }

    } catch (error) {
        return { status: false, message: "Inernal server error!", error: `${error.message}`, };
    }
};

// get icon images from the S3 bucket
async function getImageUrlsFromS3Folder(bucket, folder) {
    const params = {
        Bucket: bucket,
        Prefix: folder,
    };

    try {
        const response = await s3.listObjectsV2(params).promise();
        const imageUrls = response.Contents.map(
            (item) => `https://${bucket}.s3.amazonaws.com/${item.Key}`
        );
        return imageUrls;
    } catch (err) {
        console.error('Error fetching image URLs from S3:', err);
        return [];
    }
}

// get all activity images uploaded
exports.getAllActivityImages = async (req, res) => {
    try {
        const eventActivityIconData = await eventActivityIcon.find({ relation_id: ObjectId(req.relation_id) }).select("icon").lean();

        if (eventActivityIconData.length > 0) {
            return res.status(200).json({ status: true, message: "Event activity detail retrive!", data: eventActivityIconData, });
        }
        else {
            return res.status(200).json({ status: false, message: "No existing icons found of any event activity!", });
        }
    } catch (error) {
        return res.status(200).json({ status: false, message: "Internal server error!", error: error });
    }
};

// get all activity images uploaded
exports.deleteActivityIcon = async (req, res) => {
    try {
        const { _id } = req.body;
        const activityIcon = await eventActivityIcon.findById({_id: _id, relation_id: ObjectId(req.relation_id)});
        if (!activityIcon) {
            return res.status(200).json({ status: false, message: "No existing icons found of any event activity!", });
        }

        let existIcon = await eventActivity
            .find({ icon: activityIcon.icon, isDelete: false, })
            .select(
                "-session -location -shortDescription -longDescription -notifyChanges -notifyChangeText -isEndOrNextDate -endDate -description -date -startTime -endTime -member -speaker -partner -guest -isDelete -createdAt -updatedAt -__v -reserved -reserved_URL"
            )
            .lean();

            const filteredIcons = [];
            existIcon.forEach(item => {  
                if (item.event && item.event.relation_id && item.event.relation_id._id && item.event.relation_id._id && (item.event.relation_id._id).toString()==(req.relation_id).toString()) {
                    filteredIcons.push({
                        _id: item?._id, 
                        name: item?.name, 
                        event: {
                            _id: item?.event._id, 
                            title: item?.event.title 
                        }
                    });
                
                } else {
                }
            });
            
        if (filteredIcons.length > 0) {
            return res.status(200).json({ status: false, message: "You can't delete any assigned icon!", data: filteredIcons });
        } else {
            if(activityIcon.icon) {
                await s3.deleteObject({
                    Bucket: process.env.AWS_BUCKET,
                    Key: activityIcon.icon,
                }).promise();
            }
            await eventActivityIcon.findByIdAndDelete(_id);
            return res.status(200).json({ status: true, message: "Icon deleted succesfully!", });
        }
    } catch (error) {
        return res.status(500).json({ status: false, message: "Internal server error!", error: error });
    }
};

// create event activity  version 2
exports.createEventActivityV2 = async (req, res) => {
    try {
        const body = req.body;
        var startTimeArr = [], endTimeArr = [], startDateArr = [], endDateArr = [];
        if (body) {
            if (body.session) {
                if (body.session.length > 1) {

                    let resOrder = body.session.map(async ids => {
                        const sessionDetail = await Session.findOne({ _id: new ObjectId(ids), isDelete: false, });
                        startDateArr.push(moment(sessionDetail.date, "MM-DD-YYYY"));
                        endDateArr.push(moment(sessionDetail.endDate, "MM-DD-YYYY"));
                        startTimeArr.push(sessionDetail.startTime);
                        endTimeArr.push(sessionDetail.endTime);
                    });
                    await Promise.all([...resOrder]);

                    // Convert each time string in the array to Date objects
                    const minTimeObjects = startTimeArr.map(startTimeStr => moment(startTimeStr, "h:mm a"));
                    const maxTimeObjects = endTimeArr.map(endTimeStr => moment(endTimeStr, "h:mm a"));

                    // Find the minimum time in the array
                    const minTime = new Date(Math.min(...minTimeObjects));
                    const maxTime = new Date(Math.max(...maxTimeObjects));
                    const minDate = moment.min(startDateArr);
                    const maxDate = moment.max(endDateArr);

                    // Format the minimum time as a string
                    const minTimeString = minTime.toLocaleTimeString([], { hour: '2-digit', minute: '2-digit' });
                    const maxTimeString = maxTime.toLocaleTimeString([], { hour: '2-digit', minute: '2-digit' });
                    const minDateString = moment(minDate).format("MM-DD-YYYY");
                    const maxDateString = moment(maxDate).format("MM-DD-YYYY");

                    body.startTime = minTimeString;
                    body.endTime = maxTimeString;
                    body.date = minDateString;
                    body.endDate = maxDateString;

                } else {
                    const sessionData = await Session.findOne({ _id: new ObjectId(req.body.session[0]), isDelete: false, });
                    body.startTime = sessionData.startTime;
                    body.endTime = sessionData.endTime;
                    body.date = sessionData.date;
                    body.endDate = sessionData.endDate;
                }
            }

            let description = body.longDescription ? `<div "font-family: 'Muller';">${body.longDescription}</div>`:"";
            let shortDescription = body.shortDescription ? `${body.shortDescription}`: "";
            if (req.icon === undefined && body.exist_icon !== "" && body.exist_icon !== null && body.exist_icon !== "null")
                req.icon = body.exist_icon;
            let accessRoles = [];
            let groupId = [];
            let membershipPlanId = [];
            let userId = [];
            let matchCondition = [];
            if (req.body.accessRoles && req.body.accessRoles.length == 0) {
                accessRoles = []
            } 
            if (req.body.accessRoles && req.body.accessRoles.length != 0) {
                accessRoles = req.body.accessRoles.map((id) => { if (ObjectId.isValid(id)) { return ObjectId(id) } });
            }
            if (body.groupId) {
                groupId = body.groupId.map((id) => { return ObjectId(id) });
                matchCondition.push( { 'airtable-syncs_result.accessible_groups': { '$in': groupId } })
            }
            if (body.membershipPlanId) {
                membershipPlanId = body.membershipPlanId.map((id) => { return ObjectId(id) });
                matchCondition.push( { 'airtable-syncs_result.purchased_plan': { '$in': membershipPlanId } })
            }
            if (body.userId) {
                userId = body.userId.map((id) => { return ObjectId(id) });
                matchCondition.push( { 'user': { '$in': userId } })
            }
            let notifyChanges = false;
            if (body && body.notifyChanges && ( body.notifyChanges == true || body.notifyChanges == 'true' )) {
                notifyChanges = true;
            }

            const newActivity = new eventActivity({
                name: body.name,
                icon: req.icon,
                shortDescription: shortDescription,
                longDescription: description,
                date: body.date,
                startTime: body.startTime,
                endTime: body.endTime,
                member: body.member,
                speaker: body.speaker,
                partner: body.partner,
                guest: body.guest,
                reserved: body.reserved,
                reserved_URL: body.reserved_URL,
                reservedLabelForListing: body.reservedLabelForListing,
                reservedLabelForDetail: body.reservedLabelForDetail,
                session: body.session,
                event: body.event,
                location: body.location !== null && body.location !== "" ? body.location : null,
                notifyChanges: notifyChanges,
                notifyChangeText: body.notifyChangeText !== null && body.notifyChangeText !== "" ? body.notifyChangeText : "",
                isEndOrNextDate: body.isEndOrNextDate !== null && body.isEndOrNextDate !== "" ? body.isEndOrNextDate : false,
                endDate: body.endDate !== null && body.endDate !== "" ? body.endDate : body.date,
                scheduleNotify: body.scheduleNotify !== null && body.scheduleNotify !== "" ? body.scheduleNotify : false,
                scheduleNotifyTime: body.scheduleNotifyTime !== null && body.scheduleNotifyTime !== "" ? body.scheduleNotifyTime : "",
                accessRoles: accessRoles,
                groupId: groupId,
                membershipPlanId: membershipPlanId,
                userId: userId,
                status: body.status ? body.status : "draft",
                isCheckInAllow: req.body.isCheckInAllow == "true" ? true : false,
            });
            const activityData = await newActivity.save();

            if (activityData) {
                const activityIcon = await eventActivityIcon.findOne({ icon: req.icon, relation_id: ObjectId(req.relation_id),});
                if (!activityIcon && req.icon) {
                    await eventActivityIcon.create({ icon: req.icon, relation_id: ObjectId(req.relation_id) })
                }
            };

            if(body.status === "published"){
                if (activityData.scheduleNotify === true) {
                    const scheduleNotifyTime = activityData.scheduleNotifyTime;
                    const fetchEvent = await event.findOne(
            { _id: activityData.event },
            {
              timeZone: 1,
              tag: 0,
              category: 0,
              subcategory: 0,
              relation_id: 0,
            }
          );

          const date = activityData.date;
          const time = activityData.startTime;

          const templateData = {
            activityName: activityData.name ? activityData.name : "",
            scheduleNotifyTime:
              scheduleNotifyTime === "120"
                ? "2 hours"
                : scheduleNotifyTime === "60"
                ? "1 hours"
                : `${scheduleNotifyTime} minutes.`,
          };

          // let notificationData = {
          //   receiverId: userData._id,
          //   receiverName: userName,
          //   receiverImage: userData.profileImg,
          //   eventId: eventData._id,
          //   eventName: eventData.title ? eventData.title : "",
          //   activityId: activityData._id,
          //   activityName: activityData.name ? activityData.name : "",
          //   activityImage: activityData.icon,
          //   sessionCount: activityData.sessionCount,
          //   chatType: "activityReminder",
          // };


          const data = {
            templateData,
            type: "activity",
            date,
            time,
            eventTimeZone: fetchEvent.timeZone,
            relationId: req.relation_id,
            eventId: fetchEvent?._id,
            activityId: activityData?._id,
            duration: scheduleNotifyTime,
            notificationType: "user_activity_reminder",
            messageType: "activityReminder",
            createdBy: "admin"
          };

          await createNotification({ data })
          //           let pipeline = [
          //               {
          //                 '$match': {
          //                   'event': ObjectId(body.event)
          //                 }
          //               }, {
          //                 '$lookup': {
          //                   'from': 'airtable-syncs', 
          //                   'localField': 'user', 
          //                   'foreignField': '_id', 
          //                   'as': 'airtable-syncs_result', 
          //                   'pipeline': [
          //                     {
          //                       '$match': {
          //                         '$expr': {
          //                           '$eq': [
          //                             '$isDelete', false
          //                           ]
          //                         }
          //                       }
          //                     }, {
          //                       '$project': {
          //                         '_id': 1, 
          //                         'purchased_plan': 1, 
          //                         'accessible_groups': 1
          //                       }
          //                     }
          //                   ]
          //                 }
          //               }, {
          //                 '$unwind': {
          //                   'path': '$airtable-syncs_result', 
          //                   'preserveNullAndEmptyArrays': false
          //                 }
          //               }, {
          //                 '$match': {
          //                   '$or': matchCondition
          //                 }
          //               }, {
          //                 '$group': {
          //                   '_id': '$user', 
          //                   'items': {
          //                     '$first': '$$ROOT'
          //                   }
          //                 }
          //               }, {
          //                 '$replaceRoot': {
          //                   'newRoot': '$items'
          //                 }
          //               }
          //             ]
          //             let uniqueUsers = await eventParticipantAttendees.aggregate(pipeline);
          //           if (uniqueUsers.length > 0) {
          //               const eventID = activityData.event ? activityData.event : null;
          //               const activityID = activityData._id ? activityData._id : null;
          //               let schedule = uniqueUsers.map(async (user, i) => {
                            // await scheduleNotificationFormAdmin(user.user, eventID, activityID, "", "activity", scheduleNotifyTime)
          //               });
          //               await Promise.all([...schedule]);
          //           }
                }
            }

            if (activityData)
                return res.status(200).json({ status: true, message: "Event activity added successfully!", data: activityData, });
            else
                return res.status(200).json({ status: false, message: "Something went wrong while adding event activity!", });
        } else {
            return res.status(200).json({ status: false, message: "All fields are required!", });
        }
    } catch (error) {
        return res.status(200).json({ status: false, message: "Something went wrong!", error: error });
    }
};

// edit event activity version 2
exports.editEventActivityV2 = async (req, res) => {
    try {

        if(!req.body?.scheduleNotify) { 
          req.body.scheduleNotify = "false"
          req.body.scheduleNotifyTime = null
        }

        if(!req.body?.notifyChanges) req.body.notifyChanges = false

        if(!req?.params?.id) {
          return res.status(400).json({ status: false, message: "Activity id is required!", data: {} });
        }
        // let [getActivity, alreadyAdded] = await Promise.all([
        //     eventActivity.findOne({ _id: new ObjectId(req.params.id), isDelete: false }).lean(),
        //     User.find({ notificationFor: { $elemMatch: { id: new ObjectId(req.params.id) } }, setBy: "user" }, { _id: 1, "notificationFor.$": 1 }).lean()
        // ]);

        let [getActivity, alreadyAdded] = await Promise.all([
            eventActivity.findOne({ _id: new ObjectId(req.params.id), isDelete: false }).lean(),
            scheduleNotification.find({ activityId: req?.params?.id, relationId: req?.relation_id, type: "activity", isDelete: false }).lean()
        ]);

        if(!getActivity) {
          return res.status(404).json({ status: false, message: "Activity not found!", data: {} });
        }

        const fetchEvent = await event.findOne(
          { _id: getActivity.event, isDelete: false },
          {
            timeZone: 1,
            title: 1,
            tag: 0,
            category: 0,
            subcategory: 0,
            relation_id: 0,
          }
        );

        // if (req.body.status === "published" && req.body.scheduleNotify === "true") {
        //   const { scheduleNotifyTime, date: activityDate, startTime: activityTime, name } = req.body;
        //   const { _id: eventId, timeZone: eventTimeZone } = fetchEvent;
        //   const { _id: activityId, date: existingDate, startTime: existingTime, scheduleNotifyTime: existingScheduleTime, name: existingName } = getActivity || {};
        
        //   //* Construct notification data
        //   const notificationData = {
        //     activityName: name || "",
        //     scheduleNotifyTime: 
        //       scheduleNotifyTime === "120" ? "2 hours" :
        //       scheduleNotifyTime === "60" ? "1 hour" :
        //       `${scheduleNotifyTime} minutes`,
        //   };
        
        //   //* Prepare notification payload
        //   const data = {
        //     notificationData,
        //     type: "activity",
        //     activityDate,
        //     activityTime,
        //     eventTimeZone,
        //     relationId: req.relation_id,
        //     eventId,
        //     activityId,
        //     duration: Number(scheduleNotifyTime),
        //     createdBy: "admin",
        //   };
        
        //   //* Check if notification already exists
        //   if (alreadyAdded.length) {
        //     const isDateUpdated = activityDate !== existingDate;
        //     const isTimeUpdated = activityTime !== existingTime;
        //     const isDurationUpdated = scheduleNotifyTime !== existingScheduleTime;
        //     const isNameUpdated = name !== existingName;
        
        //     if (isDateUpdated || isTimeUpdated || isDurationUpdated || isNameUpdated) {
        //       const filter = { activityId: req.params.id, relationId: req.relation_id, type: "activity", isDelete: false };
        //       await updateNotification({ filter, data, isDateUpdated, isTimeUpdated, isDurationUpdated, isNameUpdated });
        //     }
        //   } else {
        //     await createNotification({ data });
        //   }
        // }

        // if(req.body.status === "draft"){
        //     if (alreadyAdded.length) {
        //         const scheduleData = await ScheduledNotification.find({
        //             idsFor: ObjectId(req.params.id),
        //             createdBy: "user",
        //         });
        
        //         const NotificationFor = {
        //             id: req.params.id,
        //             type: "activity",
        //             setBy: "user",
        //           };
                  
        //         if (scheduleData && scheduleData.length > 0) {
        //           const notification_for = await User.updateMany(
        //             { notificationFor: { $elemMatch: { id: new ObjectId(req.params.id) } }, setBy: "user" },
        //             { $pull: { notificationFor: NotificationFor } },
        //             { new: true }
        //           );
        //           const cancelData = await ScheduledNotification.deleteMany({ idsFor: ObjectId(req.params.id), createdBy: "user" });
        //         }
        //     }
        // }

        if (getActivity) {  
            var startTimeArr = [], endTimeArr = [], startDateArr = [], endDateArr = [];
            if (req.body.session) {
                if (req.body.session.length > 0) {

                    let resOrder = req.body.session.map(async ids => {
                        const sessionDetail = await Session.findOne({ _id: new ObjectId(ids), isDelete: false, });
                        startDateArr.push(moment(sessionDetail.date, "MM-DD-YYYY"));
                        endDateArr.push(moment(sessionDetail.endDate, "MM-DD-YYYY"));
                        startTimeArr.push(sessionDetail.startTime);
                        endTimeArr.push(sessionDetail.endTime);
                    });
                    await Promise.all([...resOrder]);

                    // Convert each time string in the array to Date objects
                    const minTimeObjects = startTimeArr.map(startTimeStr => moment(startTimeStr, "h:mm a"));
                    const maxTimeObjects = endTimeArr.map(endTimeStr => moment(endTimeStr, "h:mm a"));

                    // Find the minimum time in the array
                    const minTime = new Date(Math.min(...minTimeObjects));
                    const maxTime = new Date(Math.max(...maxTimeObjects));
                    const minDate = moment.min(startDateArr);
                    const maxDate = moment.max(endDateArr);

                    // Format the minimum time as a string
                    const minTimeString = minTime.toLocaleTimeString([], { hour: '2-digit', minute: '2-digit' });
                    const maxTimeString = maxTime.toLocaleTimeString([], { hour: '2-digit', minute: '2-digit' });
                    const minDateString = moment(minDate).format("MM-DD-YYYY");
                    const maxDateString = moment(maxDate).format("MM-DD-YYYY");

                    req.body.startTime = minTimeString;
                    req.body.endTime = maxTimeString;
                    req.body.date = minDateString;
                    req.body.endDate = maxDateString;

                } else {
                    const sessionData = await Session.findOne({ _id: new ObjectId(req.body.session[0]), isDelete: false, });
                    req.body.startTime = sessionData.startTime;
                    req.body.endTime = sessionData.endTime;
                    req.body.date = sessionData.date;
                    req.body.endDate = sessionData.endDate;
                }
            }

            let description = req.body.longDescription ? `<div "font-family: 'Muller';">${req.body.longDescription}</div>`:"";
            let shortDescription = req.body.shortDescription ? `${req.body.shortDescription}` : "";
            if (req.icon === undefined && req.body.exist_icon !== "" && req.body.exist_icon !== null && req.body.exist_icon !== "null")
                req.icon = req.body.exist_icon;

            // if (req.body.date !== getActivity.date || req.body.startTime !== getActivity.startTime) {
            //     if(req.body.status === "published"){
            //         if (alreadyAdded.length > 0) {
            //             const eventID = getActivity.event ? getActivity.event : null;
            //             const activityID = getActivity._id ? getActivity._id : null;
            //             let resOrder = alreadyAdded.map(async (user, i) => {
            //                 const userData = await User.findOne({ _id: ObjectId(user._id) });
            //                 await reScheduleNotificationForActivitySession(userData._id, eventID._id, activityID, "", "activity");
            //             });
            //             Promise.all([...resOrder]);
            //         }
            //     }
            // }

            let accessRoles = [];
            let groupId = [];
            let membershipPlanId = [];
            let userId = [];
            let matchCondition = [];
            if (req.body.accessRoles && req.body.accessRoles.length == 0) {
                accessRoles = []
            } else if (req.body.accessRoles && req.body.accessRoles.length != 0) {
                accessRoles = req.body.accessRoles.map((id) => { if (ObjectId.isValid(id)) { return ObjectId(id) } });
            } else if (getActivity.accessRoles && getActivity.accessRoles.length) {
                // accessRoles = getActivity.accessRoles.map((id) => { if(ObjectId.isValid(id)){ return ObjectId(id)} });
            }
            if (req.body.groupId && req.body.groupId.length == 0) {
                groupId = []
            } else if (req.body.groupId && req.body.groupId.length != 0) {
                groupId = req.body.groupId.map((id) => { if (ObjectId.isValid(id)) { return ObjectId(id) } });
            } else if (getActivity.groupId && getActivity.groupId.length) {
                // groupId = getActivity.groupId.map((id) => { if(ObjectId.isValid(id)){ return ObjectId(id)} });
            }
            if (req.body.membershipPlanId && req.body.membershipPlanId.length == 0) {
                membershipPlanId = [];
            } else if (req.body.membershipPlanId && req.body.membershipPlanId.length != 0) {
                membershipPlanId = req.body.membershipPlanId.map((id) => { if (ObjectId.isValid(id)) { return ObjectId(id) } });
            } else if (getActivity.membershipPlanId && getActivity.membershipPlanId.length) {
                // membershipPlanId = getActivity.membershipPlanId.map((id) => { if(ObjectId.isValid(id)){ return ObjectId(id)} });
            }
            if (req.body.userId && req.body.userId.length == 0) {
                userId = [];
            } else if (req.body.userId && req.body.userId.length != 0) {
                userId = req.body.userId.map((id) => { if (ObjectId.isValid(id)) { return ObjectId(id) } });
            } else if (getActivity.userId && getActivity.userId.length) {
                // userId = getActivity.userId.map((id) => { if(ObjectId.isValid(id)){ return ObjectId(id)} });
            }
            matchCondition.push( { 'role': { '$in': accessRoles } });
            matchCondition.push( { 'airtable-syncs_result.accessible_groups': { '$in': groupId } });
            matchCondition.push( { 'airtable-syncs_result.purchased_plan': { '$in': membershipPlanId } });
            matchCondition.push( { 'user': { '$in': userId } });

            let updateObj = {
                name: req.body.name ?? getActivity.name,
                icon: req.icon ?? getActivity.icon,
                shortDescription: shortDescription ?? getActivity.shortDescription,
                longDescription: description ?? getActivity.longDescription,
                date: req.body.date ?? getActivity.date,
                startTime: req.body.startTime ?? getActivity.startTime,
                endTime: req.body.endTime ?? getActivity.endTime,
                member: req.body.member ?? getActivity.member,
                speaker: req.body.speaker ?? getActivity.speaker,
                partner: req.body.partner ?? getActivity.partner,
                guest: req.body.guest ?? getActivity.guest,
                session: req.body.session ?? [],
                reserved: req.body.reserved ?? getActivity.reserved,
                reserved_URL: req.body.reserved_URL ?? getActivity.reserved_URL,
                reservedLabelForListing: req.body.reservedLabelForListing ?? getActivity.reservedLabelForListing,
                reservedLabelForDetail: req.body.reservedLabelForDetail ?? getActivity.reservedLabelForDetail,
                event: req.body.event ?? getActivity.event,
                location: req.body.location !== undefined && req.body.location !== "" ? req.body.location : null,
                notifyChanges: req.body?.notifyChanges !== null && req.body?.notifyChanges !== "" ? req.body?.notifyChanges : getActivity.notifyChanges === undefined ? false : getActivity.notifyChanges,
                notifyChangeText: req.body?.notifyChangeText !== null && req.body?.notifyChangeText !== "" ? req.body?.notifyChangeText : getActivity.notifyChangeText === undefined ? "" : getActivity.notifyChangeText,
                isEndOrNextDate: req.body?.isEndOrNextDate !== null && req.body?.isEndOrNextDate !== "" ? req.body?.isEndOrNextDate : getActivity.isEndOrNextDate === undefined ? false : getActivity.isEndOrNextDate,
                endDate: req.body?.endDate !== null && req.body.endDate !== "" ? req.body.endDate : getActivity.endDate === undefined ? null : req.body.date ?? getActivity.date,
                scheduleNotify: req.body?.scheduleNotify !== null && req.body?.scheduleNotify !== "" ? req.body?.scheduleNotify : getActivity.scheduleNotify === undefined ? false : getActivity.scheduleNotify,
                scheduleNotifyTime: req.body?.scheduleNotifyTime !== null && req.body?.scheduleNotifyTime !== "" ? req.body?.scheduleNotifyTime : getActivity.scheduleNotifyTime === undefined ? "" : getActivity.scheduleNotifyTime,
                accessRoles: accessRoles,
                groupId: groupId,
                membershipPlanId: membershipPlanId,
                userId: userId,
                status: req.body.status ?? getActivity.status,
                isCheckInAllow: req.body.isCheckInAllow == "true" ? true : false,
            }

            // const activityData = await eventActivity.findByIdAndUpdate(req.params.id,
            //     updateObj,
            //     { new: true }
            // ).lean();
            let activityData = await eventActivity.findOneAndUpdate({_id: ObjectId(req.params.id)}, { $set: updateObj }, {new:true}).lean();
            if (activityData) {
                const activityIcon = await eventActivityIcon.findOne({ icon: req.icon, relation_id: ObjectId(req.relation_id),});
                if (!activityIcon) {
                    await eventActivityIcon.create({ icon: req.icon, relation_id: ObjectId(req.relation_id) })
                }
            };
            
            //* Delete the already created notification for the activity if the status is draft 
            if (
              (activityData?.status === "draft") ||
              (activityData?.status === "published" && activityData?.scheduleNotify === false)
            ) {
              await scheduleNotification.deleteMany({
                activityId: req?.params?.id,
                relationId: req?.relation_id,
                type: "activity",
                isDelete: false,
                ...(activityData?.status === "published" && { createdBy: "admin" })
              });
            }

            // if(req.body.status === "published"){
            //     const scheduleNotifyTime = activityData.scheduleNotifyTime;
            //     const eventID = activityData.event && activityData.event._id ? new ObjectId(activityData.event._id) : null;
            //     const activityID = activityData._id ? new ObjectId(activityData._id) : null;
            //     await scheduleJobNotification(eventID, activityID, "", "activity", {matchCondition});
            //     if (activityData.scheduleNotify === true) {
            //         // await reScheduleNotificationFormAdminV2(eventID, activityID, "", "activity", scheduleNotifyTime, {matchCondition});
            //         reScheduleNotificationFormAdminV2(eventID, activityID, "", "activity", scheduleNotifyTime, {matchCondition});
            //     } else if (activityData.scheduleNotify === false) {
            //         const alreadyAddedUsers = await User.find({ notificationFor: { $elemMatch: { id: activityData._id }, }, }, { "notificationFor.$": 1 });
            //         if (alreadyAddedUsers.length > 0) {
            //             const NotificationFor = {
            //                 id: getActivity._id,
            //                 type: "activity",
            //                 setBy: "admin",
            //             };
            //             let resCancel = alreadyAddedUsers.map(async (user, i) => {
            //                 const scheduleData = await ScheduledNotification.findOne({ createdFor: user._id, idsFor: getActivity._id, createdBy: "admin" });
            //                 if (scheduleData !== null) {
            //                     await User.findOneAndUpdate(
            //                         { _id: user._id },
            //                         { $pull: { notificationFor: NotificationFor } },
            //                         { new: true }
            //                     );
            //                     await ScheduledNotification.findByIdAndRemove(scheduleData._id);
            //                 }
            //             });
            //             await Promise.all([...resCancel]);
            //         }
            //     }
    
            //     if (activityData.notifyChanges === true) {
                    // await this.sendNotificationForNotifyUserV2(activityData.event._id, activityData.notifyChangeText, "activity", activityData._id);
            //     }
            // }

            if (activityData?.status === "published" && activityData?.scheduleNotify === true) {
              const { scheduleNotifyTime, date: activityDate, startTime: activityTime, name } = activityData;
              const { _id: eventId, timeZone: eventTimeZone } = fetchEvent;
              const { _id: activityId, date: existingDate, startTime: existingTime, scheduleNotifyTime: existingScheduleTime, name: existingName } = getActivity;
            
              //* Construct notification data
              const templateData = {
                activityName: name || "",
                scheduleNotifyTime: 
                  scheduleNotifyTime === "120" ? "2 hours" :
                  scheduleNotifyTime === "60" ? "1 hour" :
                  `${scheduleNotifyTime} minutes`,
              };
            
              //* Prepare notification payload
              const data = {
                templateData,
                type: "activity",
                date: activityDate,
                time: activityTime,
                eventTimeZone,
                relationId: req.relation_id,
                eventId,
                activityId,
                duration: Number(scheduleNotifyTime),
                notificationType: "user_activity_reminder",
                messageType: "activityReminder",
                createdBy: "admin",
              };
            
              //* Check if notification already exists
              if (alreadyAdded.length) {
                const isDateUpdated = activityDate !== existingDate;
                const isTimeUpdated = activityTime !== existingTime;
                const isDurationUpdated = scheduleNotifyTime !== existingScheduleTime;
                const isNameUpdated = name !== existingName;
                
                if (isDateUpdated || isTimeUpdated || isDurationUpdated || isNameUpdated) {
                  const filter = { activityId: req?.params?.id, relationId: req?.relation_id, type: "activity", isDelete: false };
                  await updateNotification({ filter, data, isDateUpdated, isTimeUpdated, isDurationUpdated, isNameUpdated });
                }
                
                const getAdminNotificationData = alreadyAdded.find(x => x.createdBy === "admin");
                if(!getAdminNotificationData) {
                  await createNotification({ data });
                }
              } else {
                await createNotification({ data });
              }
            }

            if(activityData && (activityData?.date !== getActivity?.date || activityData?.startTime !== getActivity?.startTime) ) {
              const { _id: eventId, timeZone: eventTimeZone } = fetchEvent;

              const activityDate = activityData?.date;
              const activityTime = activityData?.startTime;

              //* Create a new notification for the session
              let templateData = {
                activityName: activityData.name ? activityData.name : "",
                scheduleNotifyTime: "15 minutes.",
              };
            
              //* Create a new notification for the session
              const data = {
                templateData,
                type: "activity",
                date: activityDate,
                time: activityTime,
                eventTimeZone,
                relationId: req?.relation_id,
                eventId,
                activityId: activityData?._id,
                duration: 15,
                notificationType: "user_activity_reminder",
                messageType: "activityReminder",
                createdBy: "user",
              };

              const isDateUpdated = activityData?.date !== getActivity?.date;
              const isTimeUpdated = activityData?.startTime !== getActivity?.startTime;
              const isDurationUpdated = false;
              const isNameUpdated = false;

              if(isDateUpdated || isTimeUpdated) {
                const filter = { activityId: activityData?._id, relationId: req?.relation_id, type: "activity", isDelete: false };
                await updateUserNotification({ filter, data, isDateUpdated, isTimeUpdated, isDurationUpdated, isNameUpdated });
              }
            }

            //* Send the instant notification for the notify the user 
            if (activityData.notifyChanges === true) {
              activityData.accessRoles = activityData.accessRoles.map((data) => { if (ObjectId.isValid(data._id)) { return ObjectId(data._id) } });
              activityData.userId = activityData.userId.map((data) => { if (ObjectId.isValid(data._id)) { return ObjectId(data._id) } });

              const findStaff = await EventWiseParticipentType.findOne({
                role: "Staff",
                event: fetchEvent._id,
                isDelete: false
              }, {_id: 1 })

              //* create the set and make the unique array
              let uniqueArray = [...new Set([...activityData.accessRoles, findStaff?._id])];
              activityData.accessRoles = uniqueArray;

              let pipeline = [
                {
                  $match: {
                    event: ObjectId(fetchEvent?._id),
                    isDelete: false,
                    $or: [
                      {
                        role: {
                          $in: activityData?.accessRoles || []
                        }
                      },
                      {
                        user: {
                          $in: activityData.userId || []
                        }
                      }
                    ]
                  }
                },
                {
                  $group: {
                    _id: null,
                    user: {
                      $addToSet: "$user"
                    }
                  }
                },
                {
                  $addFields: {
                    uniqueArray: {
                      $ifNull: ["$user", []]
                    }
                  }
                },
                {
                  $lookup: {
                    from: "user_edges",
                    let: {
                      userArray: "$uniqueArray",
                      relationId: ObjectId(req?.relation_id)
                    },
                    pipeline: [
                      {
                        $match: {
                          $expr: {
                            $and: [
                              {
                                $in: ["$user_id", "$$userArray"]
                              },
                              {
                                $eq: [
                                  "$relation_id",
                                  "$$relationId"
                                ]
                              }
                            ]
                          },
                          isDelete: false,
                          deviceToken: {
                            $exists: true,
                            $ne: []
                          }
                        }
                      },
                      {
                        $unwind: "$deviceToken"
                      }
                    ],
                    as: "user_edge_data"
                  }
                },
                {
                  $addFields: {
                    deviceToken: {
                      $ifNull: [
                        "$user_edge_data.deviceToken",
                        []
                      ]
                    }
                  }
                },
                {
                  $project: {
                    _id: 0,
                    deviceToken: 1
                  }
                }
              ];
              let deviceTokenData = await eventParticipantAttendees.aggregate(pipeline);

              const notificationData = {
                senderId: process.env.ADMIN_ID,
                senderName: "Admin",
                eventId: fetchEvent._id,
                eventName: fetchEvent.title,
                chatType: "nofifyUser",
                activityId: activityData._id,
                activityName: activityData.name,
                sessionCount: activityData.sessionCount,
              }

              const templateData = {
                eventName: fetchEvent.title,
                chatType: "nofifyUser",
                notifyMsg: activityData.notifyChangeText,
              }

              let notificationTemplate = await notification_template.notify_user_for_update(templateData);
              const data = {
                title: notificationTemplate?.template?.title,
                body: notificationTemplate?.template?.body,
                imageUrl: null,
                icon: null,
                device_token: deviceTokenData[0]?.deviceToken || [],
                collapse_key: null,
                sub_title: null,
                notification_data: {
                  type: "notify_user_for_update",
                  content: notificationData
                }
              }

              if(deviceTokenData[0]?.deviceToken.length) send_notification_v2(data);
            }
            
            if (activityData)
                return res.status(200).json({ status: true, message: "Event activity updated successfully!", data: activityData, });
            else
                return res.status(200).json({ status: false, message: "Something went wrong while updating event activity!", });
        } else {
            return res.status(200).json({ status: false, message: "Event activity not found!" });
        }
    } catch (error) {
        return res.status(200).json({ status: false, message: "Something went wrong!", error: error });
    }
};

// sync activity for dynamic role from sync old data to dynaic structure
exports.syncActivityForDynamicRole = async (req, res) => {
    try {
      let allRole = await eventParticipantTypes.find({isDelete: false, isDefault: true});
      let allEventActivity = await eventActivity.aggregate([
        {
          '$match': {
            'isDelete': false
          }
        }, {
          '$lookup': {
            'from': 'events', 
            'localField': 'event', 
            'foreignField': '_id', 
            'as': 'events_result'
          }
        }, {
          '$unwind': {
            'path': '$events_result', 
            'preserveNullAndEmptyArrays': false
          }
        }
      ]);
      let totalSync = 0;
    //   for (let i = 0; i < 0; i++) {
      for (let i = 0; i < allEventActivity.length; i++) {
        let data = allEventActivity[i];
        let event = data.event;        
        for (let j = 0; j < allRole.length; j++) {
            let typeDetail = allRole[j];
            let role = allRole[j]["role"]
            let existType = await eventWiseParticipantTypes.findOne({ role: role, event : event, isDelete : false });            
            if (!existType) {
                let addObj = { role: typeDetail.role, description: typeDetail.description, isDefault: typeDetail.isDefault, event: event, baseRole: typeDetail._id } ;  
                await eventWiseParticipantTypes.create(addObj);
            }
            console.log({i, j});
        }        
      }
      for (let i = 0; i <  allEventActivity.length; i++) {
        let isUpdatable = false;
        let accessRoles = [];
        let data = allEventActivity[i];
        let allRole = await eventWiseParticipantTypes.find({ event: data["event"] , isDelete: false });
        if (data["member"] == true) {
          let tempRole = allRole.find((x) => x.role.toLowerCase() === "member");
          if (tempRole && tempRole["_id"]) {
            accessRoles.push(ObjectId(tempRole["_id"]));
            isUpdatable = true;
          }
        }
        if (data["speaker"] == true) {
          let tempRole = allRole.find((x) => x.role.toLowerCase() === "speaker");
          if (tempRole && tempRole["_id"]) {
            accessRoles.push(ObjectId(tempRole["_id"]));
            isUpdatable = true;
          }
        }
        if (data["partner"] == true) {
          let tempRole = allRole.find((x) => x.role.toLowerCase() === "partner");
          if (tempRole && tempRole["_id"]) {
            accessRoles.push(ObjectId(tempRole["_id"]));
            isUpdatable = true;
          }
        }
        if (data["guest"] == true) {
          let tempRole = allRole.find((x) => x.role.toLowerCase() === "guest");
          if (tempRole && tempRole["_id"]) {
            accessRoles.push(ObjectId(tempRole["_id"]));
            isUpdatable = true;
          }
        }
        if (!data["accessRoles"] && isUpdatable === false) {
            const updatedSession = await eventActivity.findOneAndUpdate(
              { _id: data._id },
              { $set: { accessRoles: [] } },
              { new: true }
            );
        }
        if (isUpdatable === true) {
          const updatedSession = await eventActivity.findOneAndUpdate(
            { _id: data._id },
            { $set: { accessRoles: accessRoles } },
            { new: true }
          );
          totalSync = totalSync + 1;
        }
        console.log({ i, totalSync });
      }
      
      return res
        .status(200)
        .json({
          status: true,
          message: "Session data sync successfully!",
          data: {totalSync},
        });
    } catch (error) {
      return res
        .status(500)
        .json({ status: false, message: "Internal server error!", error: error });
    }
  };