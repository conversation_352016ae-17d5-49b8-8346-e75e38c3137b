const eventPackage = require("../../database/models/eventPackage");
const eventLocation = require("../../database/models/eventLocation");
const event = require("../../database/models/event");
const eventView = require("../../database/models/eventView");
const User = require("../../database/models/airTableSync");
const { ObjectId } = require("mongodb");
const Notification = require("../../database/models/notification");
const AWS = require("aws-sdk");
const moment = require("moment");
const { send_notification, notification_template, addTime, subtractTime } = require("../../utils/notification");
const scheduleLib = require("node-schedule");
const ScheduledNotification = require("../../database/models/scheduledNotification");
const { schedule, reScheduleNotificationForActivitySession, rearrangeAttendee } = require("./eventAttendeeManageController");
const { userAccessRulesCommonCondition } = require("../../controller/userAccessRules/restrictionAccess");
const { getEventCount, getEventCountV2 } = require("../../controller/eventManagement/eventController");

require('moment-timezone');
const { keys } = require("lodash");

const s3 = new AWS.S3({
    accessKeyId: process.env.AWS_ID,
    secretAccessKey: process.env.AWS_SECRET,
    Bucket: process.env.AWS_BUCKET,
});

/** User APIs starts **/
// get upcomming event list and past event list
exports.getUpCommingEventListV2 = async (req, res) => {
    try {
        let sortType = req.query && req.query.sortType && req.query.sortType == "Dec" ? -1 : 1;
        var localDate = new Date(req.query.localDate);
        localDate = moment(localDate, "YYYY-MM-DD").toDate();
        const page = parseInt(req.query.page);
        const limit = parseInt(req.query.limit);
        const skip = (page - 1) * limit;
        const authUser = req.authUserId;
        const userData = await User.findById(authUser).select("firebaseId accessible_groups purchased_plan attendeeDetail.evntData");
        let ruleCondition =  await userAccessRulesCommonCondition({userId:authUser,relation_id:req.relation_id});

        var match = {
            isDelete: false,
            status: "published",
            relation_id: ObjectId(req.relation_id),
        }

        let search = "";
        if (req.query.search) {
        search = req.query.search;
        match = {
                ...match,
                $or: [
                    { title: { $regex: ".*" + search + ".*", $options: "i" }, },
                    { 'tag.name': { $regex: ".*" + search + ".*", $options: "i" }, },
                ]
            };
        }
        var type = "";
        if (req.query.type) {
            type = req.query.type;
            match = { ...match, locationType: type, };
        }

        var city = "";
        if (req.query.city) {
            city = req.query.city
            match = {
              ...match,
              "location.city": req.query.city
            };
        };

        let categoryId = req.query.categoryId ? req.query.categoryId : ""
        let subCategoryId = req.query.subCategoryId ? req.query.subCategoryId : ""

        const aggregatePipeline = [
            {
              $match: {
                isDelete: false,
                status: "published",
              }
            }, 
            {
                $addFields: {
                  Date: {
                    $let: {
                      vars: {
                        year: { $substr: ["$endDate", 6, 10] },
                        month: { $substr: ["$endDate", 0, 2] },
                        dayOfMonth: { $substr: ["$endDate", 3, 2] },
                        startMinute: { $substr: ["$endTime", 3, 2] },
                        startHours: {
                          $toString: {
                            $cond: {
                              if: { $eq: [{ $substr: ["$endTime", 6, 2] }, "am"] },
                              then: {
                                $cond: {
                                  if: { $eq: [{ $substr: ["$endTime", 0, 2] }, "12"] },
                                  then: "00", // Midnight (12 AM) should be converted to "00"
                                  else: { $substr: ["$endTime", 0, 2] }, // No change needed for AM times other than midnight
                                },
                              },
                              else: {
                                $cond: {
                                  if: { $eq: [{ $substr: ["$endTime", 0, 2] }, "12"] },
                                  then: "12", // Noon (12 PM) should remain "12"
                                  else: {
                                    $add: [
                                      {
                                        $toInt: { $substr: ["$endTime", 0, 2] },
                                      },
                                      12, // Adding 12 to convert PM times to 24-hour format
                                    ],
                                  },
                                },
                              },
                            },
                          },
                        },
                        timeZoneTemp: {
                          $cond: {
                            if: {
                              $or: [
                                { $eq: ["$timeZone", ""] },
                                {
                                  $eq: [
                                    "$timeZone",
                                    "(UTC) Dublin, Edinburgh, Lisbon, London"
                                  ]
                                }
                              ]
                            },
                            then: "-00:00",
                            else: {
                              $substr: ["$timeZone", 4, 6]
                            }
                          }
                        }
                      },
                      in: {
                        $toDate: {
                          $concat: [
                            "$$year",
                            "-",
                            "$$month",
                            "-",
                            "$$dayOfMonth",
                            "T",
                            "$$startHours",
                            ":",
                            "$$startMinute",
                            ":",
                            "00.000",
                            "$$timeZoneTemp"
                          ],
                        },
                      },
                    },
                  },
                },
              },
              {
                $lookup: {
                  from: "contentarchive_tags",
                  localField: "tag",
                  foreignField: "_id",
                  pipeline: [
                    {
                      $match: {
                        isDelete: false,
                      },
                    },
                  ],
                  as: "tag",
                },
            },
            {
                $match: {
                    ...match,
                    Date: { $gt: localDate },
                    
                },
            },
            {
                $match: {
                  ...ruleCondition
                },
            },
            ...(req.query.categoryId ? [
                {
                  $match: {
                    category: ObjectId(req.query.categoryId),
                  },
                },
              ] : []),
            ...(req.query.categoryId && req.query.subCategoryId? [
                {
                  $match: {
                    category: ObjectId(req.query.categoryId),
                    subcategory:ObjectId(req.query.subCategoryId)
                  },
                },
              ] : []),
            { $sort: { Date: -1 } },
        ];

        const aggregationFinal = [
          ...aggregatePipeline,
          {
              $lookup: {
                  from: "event_tickets",
                  let: { event_id: "$_id" },
                  pipeline: [
                      {
                          $match: {
                              $expr: {
                                  $eq: ["$eventId", "$$event_id"],
                              },
                              isDelete: false,
                          },
                      },
                      { $project: { actualPrice: 1, _id: 0 } },
                  ],
                  as: "priceData"
              }
          },
          {
            $lookup: {
              from: "eventpackages",
              let: { event_id: "$_id" },
              pipeline: [
                {
                  $match: {
                    $expr: {
                      $eq: ["$event", "$$event_id"],
                    },
                    isDelete: false,
                  },
                },
                { $project: { price: 1, _id: 0 } },
              ],
              as: "priceData1"
            }
          },
          { $lookup: {
            from: "eventactivities",
            localField: "_id",
            foreignField: "event",
            pipeline: [
              {
                $match: {
                  isDelete: false
                }
              }
            ],
            as: "activities"
            }
          },
          {
            $addFields: {
            activityCount:  { $size: '$activities' }
            }
          },
          {
              $lookup: {
                  from: "event_participant_attendees",
                  localField: "_id",
                  foreignField: "event",
                  as: "event_participant_attendees_result",
                  pipeline: [
                    {
                      $match: {
                        $expr: {
                          $eq: [
                            ObjectId(userData._id),
                            "$user",
                          ],
                        },
                      },
                    },
                    {
                      $match: {
                        $expr: {
                          $eq: [
                            "$isDelete",
                            false
                          ],
                        },
                      },
                    },
                  ],
              },
          },
          {
            $addFields: {
              totalTicketCount: {
                $cond: {
                  if: { $eq: ["$ticketPlatform", "internal"] },
                  then: { 
                    $size: "$priceData"
                  },
                  else: { 
                    $size: "$priceData1"
                  }
                }
              }
            }
          },
          {
              $project: {
                  _id: 1, title: 1, thumbnail: 1, eventUrl: 1, startDate: 1, startTime: 1, endDate: 1, endTime: 1, timeZone: 1, tag : 1, city: "$location.city", country: "$location.country", 
                  totalTicketCount:1,
                  formatedStartDate: {
                    $dateFromString: {
                      dateString: {
                        $concat: [
                          { $substr: ["$startDate", 6, 4] },
                          "-",
                          { $substr: ["$startDate", 0, 2] },
                          "-",
                          { $substr: ["$startDate", 3, 2] },
                          "T",
                          {
                            $toString: {
                              $cond: {
                                if: {
                                  $eq: [
                                    {
                                      $substr: [
                                        "$startTime",
                                        6,
                                        2
                                      ]
                                    },
                                    "am"
                                  ]
                                },
                                then: {
                                  $cond: {
                                    if: {
                                      $eq: [
                                        {
                                          $substr: [
                                            "$startTime",
                                            0,
                                            2
                                          ]
                                        },
                                        "12"
                                      ]
                                    },
                                    then: "00",
                                    else: {
                                      $substr: [
                                        "$startTime",
                                        0,
                                        2
                                      ]
                                    }
                                  }
                                },
                                else: {
                                  $cond: {
                                    if: {
                                      $eq: [
                                        {
                                          $substr: [
                                            "$startTime",
                                            0,
                                            2
                                          ]
                                        },
                                        "12"
                                      ]
                                    },
                                    then: "12",
                                    else: {
                                      $toString: {
                                        $add: [
                                          {
                                            $toInt: {
                                              $substr: [
                                                "$startTime",
                                                0,
                                                2
                                              ]
                                            }
                                          },
                                          12
                                        ]
                                      }
                                    }
                                  }
                                }
                              }
                            }
                          },
                          ":",
                          { $substr: ["$startTime", 3, 2] },
                          ":00.000",
                          {
                            $cond: {
                              if: {
                                $or: [
                                  { $eq: ["$timeZone", ""] },
                                  {
                                    $eq: [
                                      "$timeZone",
                                      "(UTC) Dublin, Edinburgh, Lisbon, London"
                                    ]
                                  }
                                ]
                              },
                              then: "-00:00",
                              else: {
                                $substr: ["$timeZone", 4, 6]
                              }
                            }
                          }
                        ]
                      }
                    }
                  },
                  locationType: 1,
                  eventLocation : "$location",
                  Date:1,
                  activityCount: "$activityCount",
                  price: {
                    $ifNull: [
                      {
                        $cond: {
                          if: { $eq: ["$ticketPlatform", "internal"] }, // Check if ticketPlatform is "internal"
                          then: { // If true, get price from priceData
                            $min: {
                              $filter: {
                                input: "$priceData.actualPrice",
                                cond: { $gt: ["$$this", 0] }
                              }
                            }
                          },
                          else: { // If false, get price from priceData1
                            $min: {
                              $filter: {
                                input: "$priceData1.price",
                                cond: { $gt: ["$$this", 0] }
                              }
                            }
                          }
                        }
                      },
                      0
                    ]
                  },
              registationFlag: {
                      $let: {
                          vars: { }, 
                          in: {
                              $gt: [{ $size: "$event_participant_attendees_result" }, 0]
                          }
                      }
                  },
                  ticketPlatform:1,
              isCheckInAllow: 1
              },
          },
          {
          $addFields: {
              sortFieldLower:
              req.query.sortField === "startDate" ? { $toDate: "$formatedStartDate" } : req.query.sortField === "title" ? { $toLower:"$title"} : req.query.sortField === "price" ? { $toInt:"$price"} :  { $toDate: "$formatedStartDate" }
              },
          },
          { $sort: { sortFieldLower: sortType } },
          { $skip: skip },
          { $limit: limit },
      ];
      
        const eventListData = await event.aggregate(aggregationFinal);

        // const count = await event.aggregate([...aggregatePipeline]);
        
        // get upcomming ,past ,myevent count
        let allEventCount = await getEventCountV2(req.relation_id,localDate,authUser,"upcomming",categoryId,subCategoryId,"",type,search,city);

        if (eventListData.length > 0) {
            return res.status(200).json({
                status: true, message: "Event list retrive!",
                data: {
                    upCommingEvents: eventListData,
                    totalPages: Math.ceil(allEventCount.upCommingEventcount / limit),
                    currentPage: page,
                    totalEvents: allEventCount.upCommingEventcount,
                    allCount:allEventCount
                },
            });
        } else {
            return res.status(200).json({
                status: true, message: "There is no upcomming events for this user!",
                data: {
                    upCommingEvents: [],
                    totalPages: Math.ceil(allEventCount.upCommingEventcount / limit),
                    currentPage: page,
                    totalEvents: allEventCount.upCommingEventcount,
                    allCount:allEventCount
                },
            });
        }
    } catch (error) {
        return res.status(500).json({ status: false, message: "Internal server error!", error: error });
    }
};

// get event by for user
exports.getUpcomingEventByIdV2 = async (req, res) => {
    try {
        const authUser = req.authUserId;
        const eventId = new ObjectId(req.params.id);
        const userData = await User.findById(authUser).select("attendeeDetail.evntData");
        let ruleCondition =  await userAccessRulesCommonCondition({userId:authUser,relation_id:req.relation_id});
        const aggregate = [
          {
              $match: {
                  _id: eventId,
                  isDelete: false,
                  $or: [
                    { status: "paused" },
                    { status: "published" }
                  ],
                  relation_id: ObjectId(req.relation_id),
              },
          },
          {
              $match: {
                  // ...ruleCondition
                  ...(req.owner ? {}: {...ruleCondition})
              },
          },
          {
              $lookup: {
                  from: "eventpackages",
                  let: { event_id: "$_id" },
                  pipeline: [
                      {
                          $match: {
                              $expr: {
                                  $eq: ["$event", "$$event_id"],
                              },
                              isDelete: false,
                          },
                      },
                      { $sort: { order: 1 } },
                      {
                          $project: {
                              _id: 1, name: 1, description: 1, price: 1, event: 1, isDelete: 1, createdAt: 1, updatedAt: 1,
                              url: {
                                  $cond: [
                                      {
                                          "$ifNull":
                                              ["$url", false]
                                      },
                                      "$url", null
                                  ]
                              },
                          }
                      }
                  ],
                  as: "packages"
              }
          },
          {
            $lookup: {
                from: "event_tickets",
                let: { event_id: "$_id" },
                pipeline: [
                    {
                        $match: {
                            $expr: {
                                $eq: ["$eventId", "$$event_id"],
                            },
                            isDelete: false,
                        },
                    },
                    { $project: { actualPrice: 1, _id: 0 } },
                ],
                as: "priceData"
            }
        },
          {
            $lookup: {
              from: "eventpackages",
              let: { event_id: "$_id" },
              pipeline: [
                {
                  $match: {
                    $expr: {
                      $eq: ["$event", "$$event_id"],
                    },
                    isDelete: false,
                  },
                },
                { $project: { price: 1, _id: 0 } },
              ],
              as: "priceData1"
            }
          },
          {
              $lookup: {
                from: "contentarchive_tags",
                localField: "tag",
                foreignField: "_id",
                pipeline: [
                  {
                    $match: {
                      isDelete: false,
                    },
                  },
                ],
                as: "tag",
              },
          },
          {
              $lookup: {
                  from: "event_participant_attendees",
                  localField: "_id",
                  foreignField: "event",
                  as: "event_participant_attendees_result",
                  pipeline: [
                    {
                      $match: {
                        $expr: {
                          $eq: [
                            ObjectId(userData._id),
                            "$user",
                          ],
                        },
                      },
                    },
                    {
                      $match: {
                        $expr: {
                          $eq: [
                            "$isDelete",
                            false
                          ],
                        },
                      },
                    },
                  ],
              },
          },
          {
            $lookup: {
              from: "event_tickets",
              localField: "_id",
              foreignField: "eventId",
              as: "result",
              pipeline: [
                {
                  $match: {
                    isDelete: false
                  }
                }
              ]
            }
          },
          {
            $addFields: {
              totalTicketCount: {
                $cond: {
                  if: { $eq: ["$ticketPlatform", "internal"] },
                  then: { 
                    $size: "$priceData"
                  },
                  else: { 
                    $size: "$priceData1"
                  }
                }
              }
            }
          },
           {
              $project: {
                  title: 1,
                  thumbnail: 1,
                  eventUrl: 1,
                  // type: ["$type.name"],
                  locationType:1,
                  tag:1,
                  startDate: 1,
                  startTime: 1,
                  endDate: 1,
                  endTime: 1,
                  shortDescription: 1,
                  longDescription: 1,
                  timeZone: 1,
                  ticketType: 1,
                  location: {
                      $cond: [
                          // {
                          //     "$ifNull":
                          //         ["$isLocation", false]
                          // },
                          // ["$location"], []
                          { $eq: ["$location", null] },
                          [],
                          ["$location"]
                      ]
                  },
                  contactSupport: 1,
                  restrictionAccess:1,
                  packages: 1,
                  isPreRegister: 1,
                  preRegisterBtnLink: 1,
                  preRegisterBtnTitle: 1,
                  preRegisterDescription: 1,
                  preRegisterEndDate: 1,
                  preRegisterStartDate: 1,
                  preRegisterTitle: 1,
                  onlineLocationDetail: 1,
                  ticketPlatform: 1,
                  isCheckInAllow: 1,
                  price: {
                    $ifNull: [
                      {
                        $cond: {
                          if: { $eq: ["$ticketPlatform", "internal"] }, // Check if ticketPlatform is "internal"
                          then: { // If true, get price from priceData
                            $min: {
                              $filter: {
                                input: "$priceData.actualPrice",
                                cond: { $gt: ["$$this", 0] }
                              }
                            }
                          },
                          else: { // If false, get price from priceData1
                            $min: {
                              $filter: {
                                input: "$priceData1.price",
                                cond: { $gt: ["$$this", 0] }
                              }
                            }
                          }
                        }
                      },
                      0
                    ]
                  },
                  totalTicketCount:1,
                  registationFlag: {
                      $let: {
                          vars: { }, 
                          in: {
                              $gt: [{ $size: "$event_participant_attendees_result" }, 0]
                          }
                      }
                  },
              }
          },
          {
            $addFields: {
              price: {
                $cond: {
                  if: {
                    $or: [
                      {
                        $eq: ["$price", null],
                      },
                      {
                        $eq: ["$ticketType", "free"],
                      },
                    ],
                  },
                  then: 0,
                  else: "$price",
                },
              },
            },
          },
      ];

      const eventData = await event.aggregate(aggregate);

        let eventViewData = await eventView.findOne({userId: req.authUserId, event: eventId});
        if (!eventViewData) {
            let newEventViewData = new eventView({
                userId: req.authUserId,
                event: eventId,
                viewCount: 1,
                lastViewAt: new Date()
            });
            await newEventViewData.save();
        } else {
            await eventView.findOneAndUpdate({ userId: req.authUserId, event: eventId },
                { lastViewAt: new Date(), $inc: { viewCount: 1 }, },
                { new: true });
        }
        if (eventData.length > 0) {
            var eventDetails = eventData[0];
            return res.status(200).json({ status: true, message: "Event detail retrive!", data: eventDetails, });
        } else {
            return res.status(200).json({ status: false, message: "Something went wrong while getting event!", });
        }
    } catch (error) {
        return res.status(500).json({ status: false, message: "Internal server error!", error: error });
    }
};

exports.getUpcomingEventByIdV3 = async (req, res) => {
try {
    const eventId = new ObjectId(req.params.id);
    let relation_id = req.query.relation_id;
    const aggregate = [
      {
          $match: {
              _id: eventId,
              isDelete: false,
              $or: [
                { status: "paused" },
                { status: "published" }
              ],
              relation_id: ObjectId(relation_id),
              isPublic: true
          },
      },
      {
          $lookup: {
              from: "eventpackages",
              let: { event_id: "$_id" },
              pipeline: [
                  {
                      $match: {
                          $expr: {
                              $eq: ["$event", "$$event_id"],
                          },
                          isDelete: false,
                      },
                  },
                  { $sort: { order: 1 } },
                  {
                      $project: {
                          _id: 1, name: 1, description: 1, price: 1, event: 1, isDelete: 1, createdAt: 1, updatedAt: 1,
                          url: {
                              $cond: [
                                  {
                                      "$ifNull":
                                          ["$url", false]
                                  },
                                  "$url", null
                              ]
                          },
                      }
                  }
              ],
              as: "packages"
          }
      },
      {
        $lookup: {
            from: "event_tickets",
            let: { event_id: "$_id" },
            pipeline: [
                {
                    $match: {
                        $expr: {
                            $eq: ["$eventId", "$$event_id"],
                        },
                        isDelete: false,
                    },
                },
                { $project: { actualPrice: 1, _id: 0 } },
            ],
            as: "priceData"
        }
    },
      {
        $lookup: {
          from: "eventpackages",
          let: { event_id: "$_id" },
          pipeline: [
            {
              $match: {
                $expr: {
                  $eq: ["$event", "$$event_id"],
                },
                isDelete: false,
              },
            },
            { $project: { price: 1, _id: 0 } },
          ],
          as: "priceData1"
        }
      },
      {
          $lookup: {
            from: "contentarchive_tags",
            localField: "tag",
            foreignField: "_id",
            pipeline: [
              {
                $match: {
                  isDelete: false,
                },
              },
            ],
            as: "tag",
          },
      },
      {
          $lookup: {
              from: "event_participant_attendees",
              localField: "_id",
              foreignField: "event",
              as: "event_participant_attendees_result",
              pipeline: [
                {
                  $match: {
                    $expr: {
                      $eq: [
                        "$isDelete",
                        false
                      ],
                    },
                  },
                },
              ],
          },
      },
      {
        $lookup: {
          from: "event_tickets",
          localField: "_id",
          foreignField: "eventId",
          as: "result",
          pipeline: [
            {
              $match: {
                isDelete: false
              }
            }
          ]
        }
      },
      {
        $addFields: {
          totalTicketCount: {
            $cond: {
              if: { $eq: ["$ticketPlatform", "internal"] },
              then: { 
                $size: "$priceData"
              },
              else: { 
                $size: "$priceData1"
              }
            }
          }
        }
      },
       {
          $project: {
              title: 1,
              thumbnail: 1,
              eventUrl: 1,
              // type: ["$type.name"],
              locationType:1,
              tag:1,
              startDate: 1,
              startTime: 1,
              endDate: 1,
              endTime: 1,
              shortDescription: 1,
              longDescription: 1,
              timeZone: 1,
              ticketType: 1,
              location: {
                  $cond: [
                      // {
                      //     "$ifNull":
                      //         ["$isLocation", false]
                      // },
                      // ["$location"], []
                      { $eq: ["$location", null] },
                      [],
                      ["$location"]
                  ]
              },
              contactSupport: 1,
              restrictionAccess:1,
              packages: 1,
              isPreRegister: 1,
              preRegisterBtnLink: 1,
              preRegisterBtnTitle: 1,
              preRegisterDescription: 1,
              preRegisterEndDate: 1,
              preRegisterStartDate: 1,
              preRegisterTitle: 1,
              onlineLocationDetail: 1,
              ticketPlatform: 1,
              isCheckInAllow: 1,
              price: {
                $ifNull: [
                  {
                    $cond: {
                      if: { $eq: ["$ticketPlatform", "internal"] }, // Check if ticketPlatform is "internal"
                      then: { // If true, get price from priceData
                        $min: {
                          $filter: {
                            input: "$priceData.actualPrice",
                            cond: { $gt: ["$$this", 0] }
                          }
                        }
                      },
                      else: { // If false, get price from priceData1
                        $min: {
                          $filter: {
                            input: "$priceData1.price",
                            cond: { $gt: ["$$this", 0] }
                          }
                        }
                      }
                    }
                  },
                  0
                ]
              },
              registationFlag: { $literal: false },
              totalTicketCount:1,
              // registationFlag: {
              //     $let: {
              //         vars: { }, 
              //         in: {
              //             $gt: [{ $size: "$event_participant_attendees_result" }, 0]
              //         }
              //     }
              // },
          }
      },
      {
        $addFields: {
          price: {
            $cond: {
              if: {
                $or: [
                  {
                    $eq: ["$price", null],
                  },
                  {
                    $eq: ["$ticketType", "free"],
                  },
                ],
              },
              then: 0,
              else: "$price",
            },
          },
        },
      },
  ];
  const eventData = await event.aggregate(aggregate);

    let eventViewData = await eventView.findOne({ event: eventId});
    if (!eventViewData) {
        let newEventViewData = new eventView({
            event: eventId,
            viewCount: 1,
            lastViewAt: new Date()
        });
        await newEventViewData.save();
    } else {
        await eventView.findOneAndUpdate({  event: eventId },
            { lastViewAt: new Date(), $inc: { viewCount: 1 }, },
            { new: true });
    }
    if (eventData.length > 0) {
        var eventDetails = eventData[0];
        return res.status(200).json({ status: true, message: "Event detail retrive!", data: eventDetails, });
    } else {
        return res.status(200).json({ status: false, message: "Something went wrong while getting event!", });
    }
} catch (error) {
    return res.status(500).json({ status: false, message: "Internal server error!", error: error });
}
};

// get Filter Event Count For User
exports.getFilterEventCountForUser = async (req, res) => {
    try {
        var localDate = new Date(req.query.localDate);
        localDate = moment(localDate, "YYYY-MM-DD").toDate();
        const authUser = req.authUserId;
        let categoryId = req.query.categoryId ? req.query.categoryId : ""
        let subCategoryId = req.query.subCategoryId ? req.query.subCategoryId : ""
        let year = req.query.year ? req.query.year : ""
        let eventType = req.query.eventType ? req.query.eventType : "past"
        let type = req.query.type ? req.query.type : ""
        let city = req.query.city ? req.query.city : ""

        // get upcomming ,past ,myevent count
        let eventCount = 0
        if(eventType==="upcoming"){
            let allEventCount = await getEventCountV2(req.relation_id,localDate,authUser,"upcomming",categoryId,subCategoryId,"",type,"",city);
            eventCount = allEventCount.upCommingEventcount
        }else if(eventType==="past"){
            let allEventCount = await getEventCountV2(req.relation_id,localDate,authUser,"past",categoryId,subCategoryId,year,"","",city);
            eventCount = allEventCount.pastEventcount
        }else if(eventType==="myevent"){
            let allEventCount = await getEventCountV2(req.relation_id,localDate,authUser,"myevent",categoryId,subCategoryId,"","","",city);
            eventCount = allEventCount.myEventcount
        }

        return res.status(200).json({ status: true, message: "Event count retrive!", eventCount: eventCount });
    } catch (error) {
        return res.status(500).json({ status: false, message: "Internal server error!", error: error });
    }
};