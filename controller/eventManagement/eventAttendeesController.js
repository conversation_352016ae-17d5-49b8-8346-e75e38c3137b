/*
This file created by BPA.
Code is already developed by respective developers ( BPA only done function separation in separate files ).
*/

const moment = require("moment");
const { ObjectId } = require("mongodb");
const eventAttendees = require("../../database/models/eventAttendees");
const eventSession = require("../../database/models/eventSession");
const chat = require("../../database/models/chat");
const User = require("../../database/models/airTableSync");
const chatChannel = require("../../database/models/chatChannel/chatChannel");
const chatChannelMembers = require("../../database/models/chatChannel/chatChannelMembers");
const rearrangePatneer = require("./eventAttendeeManageController");
const { validationResult } = require("express-validator");
const EventParticipantAttendees = require("../../database/models/eventParticipantAttendees");
const EventParticipantTypes = require("../../database/models/eventParticipantTypes");
const eventWiseParticipantTypes = require("../../database/models/eventWiseParticipantTypes");
const { deleteImage } = require("../../utils/mediaUpload");
const publishMessage = require("../../microservices/user/components/users/entry-points/message-queue/publisher");
const userEdges = require("../../microservices/user/components/user-edges/database/models/user-edges");
const userRole = require("../../microservices/user/components/roles/database/models/user-roles");
const axios = require("axios");
const {  BASE_URL } = require("../../config/config");

/** Admin APIs starts **/

// export event attendees data
exports.exportAttendees = async (req, res) => {
  try {
    const data = await eventAttendees
      .find({ isDelete: false })
      .select(
        "title name email company phone facebook linkedin type auth0Id profession evntData"
      )
      .sort({ createdAt: -1 });
    if (data)
      return res.status(200).json({
        status: true,
        message: "All Attendees list retrive!",
        data: data,
      });
    else
      return res
        .status(200)
        .json({ status: true, message: "No attendees data found!", data: [] });
  } catch (error) {
    return res
      .status(500)
      .json({ status: false, message: "Internal server error!", error: error });
  }
};

// import event attendees data
exports.importAttendeesIMP = async (req, res) => {
  try {
    const body = req.body;
    const allAttendees = body.allAttendees;
    const io = req.app.get("socketio");
    let alreadyEmail = [];
    let listOfSocketEvents = [];
    let arrayIndex = 0;
    var partnerCount = await User.countDocuments({
      "attendeeDetail.evntData": {
        $elemMatch: { event: req.body.eventId, partner: true },
      },
    });

    for (let index = 0; index < allAttendees.length; index++) {
      if (
        alreadyEmail.filter((attendee) => {
          if (
            attendee.email.toLowerCase().trim() ===
            allAttendees[index].email.toLowerCase().trim()
          )
            return attendee;
        }).length > 0
      ) {
        let existIndex = alreadyEmail.findIndex((attendee) => {
          if (attendee.email === allAttendees[index].email) return attendee;
        });
        alreadyEmail[existIndex] = {
          ...alreadyEmail[existIndex],
          role: [
            ...alreadyEmail[existIndex].role,
            allAttendees[index].type.toLowerCase(),
          ],
        };
      } else {
        alreadyEmail[arrayIndex] = {
          ...allAttendees[index],
          role: [allAttendees[index].type.toLowerCase()],
        };
        arrayIndex++;
      }
    }

    for (let index = 0; index < alreadyEmail.length; index++) {
      var eventDetails = {
        event: alreadyEmail[index].eventId,
      };

      for (let index2 = 0; index2 < alreadyEmail[index].role.length; index2++) {
        if (alreadyEmail[index].role[index2] === "partner")
          eventDetails = {
            ...eventDetails,
            [alreadyEmail[index].role[index2]]: true,
            partnerOrder: ++partnerCount,
          };
        else
          eventDetails = {
            ...eventDetails,
            [alreadyEmail[index].role[index2]]: true,
          };
      }
      alreadyEmail[index] = {
        ...alreadyEmail[index],
        eventDetails: eventDetails,
      };
    }
    const resultArray = [];

    for (let listIndex = 0; listIndex < alreadyEmail.length; listIndex++) {
      let emailExist = await User.findOne({
        "Preferred Email": alreadyEmail[listIndex].email.trim(),
        $or: [{ isDelete: false }, { isDelete: { $exists: false } }],
      })
        .select("attendeeDetail")
        .lean();

      if (emailExist) {
        let updatingData = await updateAttendee(
          emailExist._id,
          emailExist,
          alreadyEmail[listIndex],
          req.admin_Id,
          io
        );
        listOfSocketEvents.push(updatingData);
        resultArray.push(updatingData);
      } else {
        const attendeeData = new User({
          "Preferred Email": alreadyEmail[listIndex].email.toLowerCase().trim(),
          email: alreadyEmail[listIndex].email.toLowerCase().trim(),
          passcode: alreadyEmail[listIndex].passcode,
          isDelete: false,
          attendeeDetail: {
            title: alreadyEmail[listIndex].title,
            name: alreadyEmail[listIndex].name,
            firstName: alreadyEmail[listIndex].firstName,
            lastName: alreadyEmail[listIndex].lastName,
            email: alreadyEmail[listIndex].email.toLowerCase(),
            company: alreadyEmail[listIndex].company,
            profession: alreadyEmail[listIndex].profession,
            phone: alreadyEmail[listIndex].phone,
            facebook: alreadyEmail[listIndex].facebook,
            linkedin: alreadyEmail[listIndex].linkedin,
            firebaseId: alreadyEmail[listIndex].firebaseId,
            evntData: alreadyEmail[listIndex].eventDetails,
          },
        });
        const member = await attendeeData.save();
        let updatingData = await addAttendeeInChannelAtImport(
          alreadyEmail[listIndex].eventId,
          alreadyEmail[listIndex].role,
          member,
          req.admin_Id,
          io
        );
        listOfSocketEvents.push(updatingData);
        resultArray.push(member);
      }
    }
    res
      .status(200)
      .json({
        status: true,
        message: "Import Successfully done!",
        listOfSocketEvents: listOfSocketEvents,
      });
  } catch (e) {
    res.status(200).json({ status: false, message: "Something went wrong!" });
  }
};
// function to update attendee detail
async function updateAttendee(attendeeId, emailExist, data, adminId, io) {
  if (
    emailExist &&
    emailExist.attendeeDetail &&
    emailExist.attendeeDetail.evntData &&
    emailExist.attendeeDetail.evntData.filter((eventData) => {
      if (
        eventData.event &&
        data.eventId &&
        eventData.event.toString() === data.eventId.toString()
      )
        return eventData;
    }).length
  ) {
    let attendeeEventData = {
      passcode:
        data.passcode && data.passcode.length
          ? data.passcode
          : emailExist.attendeeDetail?.passcode,
      isDelete: false,
      attendeeDetail: {
        title:
          data.title && data.title.length
            ? data.title
            : emailExist.attendeeDetail?.title,
        email:
          data.email.toLowerCase().trim() &&
            data.email.toLowerCase().trim().length
            ? data.email.toLowerCase().trim()
            : emailExist.attendeeDetail?.email,
        name:
          data.name && data.name.length
            ? data.name
            : emailExist.attendeeDetail?.name,
        firstName:
          data.firstName && data.firstName.length
            ? data.firstName
            : emailExist.attendeeDetail?.firstName,
        lastName:
          data.lastName && data.lastName.length
            ? data.lastName
            : emailExist.attendeeDetail?.lastName,
        company:
          data.company && data.company.length
            ? data.company
            : emailExist.attendeeDetail?.company,
        profession:
          data.profession && data.profession.length
            ? data.profession
            : emailExist.attendeeDetail?.profession,
        phone:
          data.phone && data.phone.length
            ? data.phone
            : emailExist.attendeeDetail?.phone,
        facebook:
          data.facebook && data.facebook.length
            ? data.facebook
            : emailExist.attendeeDetail?.facebook,
        linkedin:
          data.linkedin && data.linkedin.length
            ? data.linkedin
            : emailExist.attendeeDetail?.linkedin,
        firebaseId:
          data.firebaseId && data.firebaseId.length
            ? data.firebaseId
            : emailExist.attendeeDetail?.firebaseId,
        evntData: emailExist.attendeeDetail.evntData.map((eventInnerData) => {
          if (
            data.eventId &&
            eventInnerData.event &&
            data.eventId.toString() === eventInnerData.event.toString()
          ) {
            let roleWise = {};
            for (let roleIndex = 0; roleIndex < data.role.length; roleIndex++) {
              roleWise = { ...roleWise, [data.role[roleIndex]]: true };
            }
            if (data.role.includes("partner")) {
              return {
                ...eventInnerData,
                ...roleWise,
                partnerOrder: data.partnerOrder,
              };
            } else {
              return {
                ...eventInnerData,
                ...roleWise,
              };
            }
          } else {
            return eventInnerData;
          }
        }),
      },
    };
    const updateAttendeeDetail = await User.findByIdAndUpdate(
      attendeeId,
      attendeeEventData,
      { new: true }
    );
    let socketEventList = await addAttendeeInChannelAtImport(
      data.eventId,
      data.role,
      updateAttendeeDetail,
      adminId,
      io
    );
    return socketEventList;
  } else if (
    emailExist &&
    emailExist.attendeeDetail &&
    emailExist.attendeeDetail.evntData &&
    emailExist.attendeeDetail.evntData.filter((eventData) => {
      if (
        eventData.event &&
        data.eventId &&
        eventData.event.toString() === data.eventId.toString()
      )
        return eventData;
    }).length === 0
  ) {
    let roleWise = {};
    for (let roleIndex = 0; roleIndex < data.role.length; roleIndex++) {
      roleWise = { ...roleWise, [data.role[roleIndex]]: true };
    }
    let attendeeEventData = {
      passcode:
        data.passcode && data.passcode.length
          ? data.passcode
          : emailExist.attendeeDetail?.passcode,
      isDelete: false,
      attendeeDetail: {
        title:
          data.title && data.title.length
            ? data.title
            : emailExist.attendeeDetail?.title,
        email:
          data.email.toLowerCase().trim() &&
            data.email.toLowerCase().trim().length
            ? data.email.toLowerCase().trim()
            : emailExist.attendeeDetail?.email,
        name:
          data.name && data.name.length
            ? data.name
            : emailExist.attendeeDetail?.name,
        firstName:
          data.firstName && data.firstName.length
            ? data.firstName
            : emailExist.attendeeDetail?.firstName,
        lastName:
          data.lastName && data.lastName.length
            ? data.lastName
            : emailExist.attendeeDetail?.lastName,
        company:
          data.company && data.company.length
            ? data.company
            : emailExist.attendeeDetail?.company,
        profession:
          data.profession && data.profession.length
            ? data.profession
            : emailExist.attendeeDetail?.profession,
        phone:
          data.phone && data.phone.length
            ? data.phone
            : emailExist.attendeeDetail?.phone,
        facebook:
          data.facebook && data.facebook.length
            ? data.facebook
            : emailExist.attendeeDetail?.facebook,
        linkedin:
          data.linkedin && data.linkedin.length
            ? data.linkedin
            : emailExist.attendeeDetail?.linkedin,
        firebaseId:
          data.firebaseId && data.firebaseId.length
            ? data.firebaseId
            : emailExist.attendeeDetail?.firebaseId,
        evntData: [
          ...emailExist.attendeeDetail.evntData,
          {
            event: data.eventDetails.event,
            partnerOrder: data.role.includes("partner") ? data.partnerOrder : 0,
            ...roleWise,
          },
        ],
      },
    };
    const updateAttendeeDetail = await User.findByIdAndUpdate(
      attendeeId,
      attendeeEventData,
      { new: true }
    );
    let socketEventList = await addAttendeeInChannelAtImport(
      data.eventId,
      data.role,
      updateAttendeeDetail,
      adminId,
      io
    );
    return socketEventList;
  } else if (emailExist && !emailExist.attendeeDetail) {
    let roleWise = {};
    for (let roleIndex = 0; roleIndex < data.role.length; roleIndex++) {
      roleWise = { ...roleWise, [data.role[roleIndex]]: true };
    }
    let attendeeEventData = {
      passcode: data.passcode,
      isDelete: false,
      attendeeDetail: {
        title: data.title,
        email: data.email.toLowerCase(),
        name: data.name,
        firstName: data.firstName,
        lastName: data.lastName,
        company: data.company,
        profession: data.profession,
        phone: data.phone,
        facebook: data.facebook,
        linkedin: data.linkedin,
        firebaseId: data.firebaseId,
        evntData: [
          {
            event: data.eventDetails.event,
            partnerOrder: data.role.includes("partner") ? data.partnerOrder : 0,
            ...roleWise,
          },
        ],
      },
    };
    const updateAttendeeDetail = await User.findByIdAndUpdate(
      attendeeId,
      attendeeEventData,
      { new: true }
    );
    let socketEventList = await addAttendeeInChannelAtImport(
      data.eventId,
      data.role,
      updateAttendeeDetail,
      adminId,
      io
    );
    return socketEventList;
  }
}

// function to add attendee in channel while importing
async function addAttendeeInChannelAtImport(
  eventId,
  attendeeRole,
  userData,
  adminId,
) {
  const newChatChannelData = await chatChannel.find({
    eventId: ObjectId(eventId),
    $or: [
      { restrictedAccess: { $in: attendeeRole } },
      { accessPermission: "all" }
    ],
    isDelete: false,
  });


  if (newChatChannelData.length > 0) {
    const newMembers = await Promise.all(
      newChatChannelData.map(async (newChannel) => {
        try {
          if (userData) {
            const checkChannelMemberExists = await chatChannelMembers.find({
              userId: ObjectId(userData._id),
              channelId: ObjectId(newChannel._id),
              status: 2,
            });
            if (checkChannelMemberExists.length === 0) {
              const channelMember = new chatChannelMembers({
                userId: userData._id,
                channelId: newChannel._id,
                status: 2,
                user_type: "airtable-syncs",
              });   
              await channelMember.save();
    
              const getAllChannelMembers = await chatChannelMembers.find({
                channelId: newChannel._id,
                status: 2,
                user_type: "airtable-syncs",
              });
              const channelMembersChat = getAllChannelMembers.map((ids) => ({
                id: ids.userId._id,
                readmsg: false,
              }));

              const allMembersIdsSocket = [
                ...channelMembersChat.filter(
                  (ids) => ids.id.toString() !== userData._id.toString()
                ),
                { id: userData._id, readmsg: false },
              ];

              const messageData = {
                message: "",
                recipient_type: "chatChannel",
                sender_type: "adminuser",
                recipient: newChannel._id,
                sender: adminId,
                type: "chatChannel",
                group_member: channelMembersChat,
                activity_status: true,
                activity: {
                  type: "addChannelMembers",
                  userId: [userData._id],
                },
                userTimeStamp: moment.utc().format("YYYY-MM-DDTHH:mm:ss.SSSSSS[Z]"),
              };
    
              return {
                messageData: messageData,
                allMembersIds: [userData._id],
                channelData: newChannel,
                deleteMultipleRecordFromChatList: [],
                addMemberIds: [userData._id],
                eventType: "add-channel-member-receive",
              };
            }
          }
          return null; // Explicit return for skipped cases

        } catch (error) {
          console.error("Error processing channel: ", newChannel._id, error);
          return { error: error.message, channel: newChannel._id };
        }
      })
    );
    
    const validMembers = newMembers.filter(Boolean);
    return validMembers;
  }
}

// get event attendees by event Id
exports.getAttendeesByEventId = async (req, res) => {
  try {
    const sortField =
      req.query.sortField === "name"
        ? "name"
        : req.query.sortField === "email"
          ? "email"
          : req.query.sortField === "company"
            ? "company"
            : req.query.sortField === "type"
              ? "type"
              : "";

    const eventId = new ObjectId(req.params.id);
    let attendeeList = [];

    let match = {
      "attendeeDetail.evntData": {
        $elemMatch: { event: eventId, member: true },
      },
    };

    if (req.query && req.query.search) {
      match = {
        ...match,
        ...{
          $or: [
            {
              "attendeeDetail.name": {
                $regex: ".*" + req.query.search + ".*",
                $options: "i",
              },
            },
            {
              "attendeeDetail.firstName": {
                $regex: ".*" + req.query.search + ".*",
                $options: "i",
              },
            },
            {
              "attendeeDetail.lastName": {
                $regex: ".*" + req.query.search + ".*",
                $options: "i",
              },
            },
            {
              "attendeeDetail.email": {
                $regex: ".*" + req.query.search + ".*",
                $options: "i",
              },
            },
          ],
        },
      };
    }
    let pipeline = [
      {
        $match: match,
      },
      {
        $project: {
          _id: 1,
          firebaseId: 1,
          email: "$Preferred Email",
          type: "Member",
          title: "$attendeeDetail.title",
          firstName: "$attendeeDetail.firstName"
            ? "$attendeeDetail.firstName"
            : "",
          lastName: "$attendeeDetail.lastName"
            ? "$attendeeDetail.lastName"
            : "",
          name: "$attendeeDetail.name",
          company: "$attendeeDetail.company",
          profession: "$attendeeDetail.profession",
          phone: "$attendeeDetail.phone",
          facebook: "$attendeeDetail.facebook",
          linkedin: "$attendeeDetail.linkedin",
          description: "$attendeeDetail.description" ?? "",
          offer: "$attendeeDetail.offer" ?? "",
          event: eventId,
          passcode: "$passcode" ? "$passcode" : "",
          profileImg: "$profileImg" ? "$profileImg" : "",
        },
      },
    ];
    const menberData = await User.aggregate(pipeline);
    if (menberData.length > 0) attendeeList = attendeeList.concat(menberData);

    const speakerData = await User.aggregate([
      {
        $match: {
          "attendeeDetail.evntData": {
            $elemMatch: { event: eventId, speaker: true },
          },
        },
      },
      ...(req.query && req.query.search
        ? [
          {
            $match: {
              $or: [
                {
                  "attendeeDetail.name": {
                    $regex: ".*" + req.query.search + ".*",
                    $options: "i",
                  },
                },
                {
                  "attendeeDetail.firstName": {
                    $regex: ".*" + req.query.search + ".*",
                    $options: "i",
                  },
                },
                {
                  "attendeeDetail.lastName": {
                    $regex: ".*" + req.query.search + ".*",
                    $options: "i",
                  },
                },
                {
                  "attendeeDetail.email": {
                    $regex: ".*" + req.query.search + ".*",
                    $options: "i",
                  },
                },
              ],
            },
          },
        ]
        : []),
      {
        $project: {
          _id: 1,
          firebaseId: 1,
          email: "$Preferred Email",
          type: "Speaker",
          title: "$attendeeDetail.title",
          name: "$attendeeDetail.name",
          firstName: "$attendeeDetail.firstName"
            ? "$attendeeDetail.firstName"
            : "",
          lastName: "$attendeeDetail.lastName"
            ? "$attendeeDetail.lastName"
            : "",
          company: "$attendeeDetail.company",
          profession: "$attendeeDetail.profession",
          phone: "$attendeeDetail.phone",
          facebook: "$attendeeDetail.facebook",
          linkedin: "$attendeeDetail.linkedin",
          description: "$attendeeDetail.description" ?? "",
          offer: "$attendeeDetail.offer" ?? "",
          event: eventId,
          passcode: "$passcode" ? "$passcode" : "",
          profileImg: "$profileImg" ? "$profileImg" : "",
        },
      },
    ]);
    if (speakerData.length > 0) attendeeList = attendeeList.concat(speakerData);

    const partnerData = await User.aggregate([
      {
        $match: {
          "attendeeDetail.evntData": {
            $elemMatch: { event: eventId, partner: true },
          },
        },
      },
      ...(req.query && req.query.search
        ? [
          {
            $match: {
              $or: [
                {
                  "attendeeDetail.name": {
                    $regex: ".*" + req.query.search + ".*",
                    $options: "i",
                  },
                },
                {
                  "attendeeDetail.firstName": {
                    $regex: ".*" + req.query.search + ".*",
                    $options: "i",
                  },
                },
                {
                  "attendeeDetail.lastName": {
                    $regex: ".*" + req.query.search + ".*",
                    $options: "i",
                  },
                },
                {
                  "attendeeDetail.email": {
                    $regex: ".*" + req.query.search + ".*",
                    $options: "i",
                  },
                },
              ],
            },
          },
        ]
        : []),
      { $unwind: "$attendeeDetail.evntData" },
      {
        $match: {
          "attendeeDetail.evntData.event": ObjectId(eventId),
        },
      },
      {
        $sort: { "attendeeDetail.evntData.partnerOrder": 1 },
      },
      {
        $project: {
          _id: 1,
          firebaseId: 1,
          email: "$Preferred Email",
          type: "Partner",
          title: "$attendeeDetail.title",
          name: "$attendeeDetail.name",
          firstName: "$attendeeDetail.firstName"
            ? "$attendeeDetail.firstName"
            : "",
          lastName: "$attendeeDetail.lastName"
            ? "$attendeeDetail.lastName"
            : "",
          company: "$attendeeDetail.company",
          profession: "$attendeeDetail.profession",
          phone: "$attendeeDetail.phone",
          facebook: "$attendeeDetail.facebook",
          linkedin: "$attendeeDetail.linkedin",
          description: "$attendeeDetail.description" ?? "",
          offer: "$attendeeDetail.offer" ?? "",
          event: eventId,
          passcode: "$passcode" ? "$passcode" : "",
          profileImg: "$partnerIcon" ? "$partnerIcon" : "",
        },
      },
    ]);
    if (partnerData.length > 0) attendeeList = attendeeList.concat(partnerData);

    const guestData = await User.aggregate([
      {
        $match: {
          "attendeeDetail.evntData": {
            $elemMatch: { event: eventId, guest: true },
          },
        },
      },
      ...(req.query && req.query.search
        ? [
          {
            $match: {
              $or: [
                {
                  "attendeeDetail.name": {
                    $regex: ".*" + req.query.search + ".*",
                    $options: "i",
                  },
                },
                {
                  "attendeeDetail.firstName": {
                    $regex: ".*" + req.query.search + ".*",
                    $options: "i",
                  },
                },
                {
                  "attendeeDetail.lastName": {
                    $regex: ".*" + req.query.search + ".*",
                    $options: "i",
                  },
                },
                {
                  "attendeeDetail.email": {
                    $regex: ".*" + req.query.search + ".*",
                    $options: "i",
                  },
                },
              ],
            },
          },
        ]
        : []),
      {
        $project: {
          _id: 1,
          firebaseId: 1,
          email: "$Preferred Email",
          type: "Guest",
          title: "$attendeeDetail.title",
          name: "$attendeeDetail.name",
          firstName: "$attendeeDetail.firstName"
            ? "$attendeeDetail.firstName"
            : "",
          lastName: "$attendeeDetail.lastName"
            ? "$attendeeDetail.lastName"
            : "",
          company: "$attendeeDetail.company",
          profession: "$attendeeDetail.profession",
          phone: "$attendeeDetail.phone",
          facebook: "$attendeeDetail.facebook",
          linkedin: "$attendeeDetail.linkedin",
          description: "$attendeeDetail.description" ?? "",
          offer: "$attendeeDetail.offer" ?? "",
          event: eventId,
          passcode: "$passcode" ? "$passcode" : "",
          profileImg: "$profileImg" ? "$profileImg" : "",
        },
      },
    ]);
    if (guestData.length > 0) attendeeList = attendeeList.concat(guestData);

    // Sort the attendeeList array by the specified sortField in ascending order
    if (sortField) {
      if (req.query.sortType === "Asc") {
        attendeeList.sort((a, b) => {
          const fieldA = a[sortField].toUpperCase(); // Convert to uppercase for case-insensitive sorting
          const fieldB = b[sortField].toUpperCase(); // Convert to uppercase for case-insensitive sorting

          if (fieldA < fieldB) return -1;
          if (fieldA > fieldB) return 1;
          return 0;
        });
      } else {
        attendeeList.sort((a, b) => {
          const fieldA = a[sortField].toUpperCase(); // Convert to uppercase for case-insensitive sorting
          const fieldB = b[sortField].toUpperCase(); // Convert to uppercase for case-insensitive sorting

          if (fieldA > fieldB) return -1;
          if (fieldA < fieldB) return 1;
          return 0;
        });
      }
    }

    if (attendeeList)
      return res.status(200).json({
        status: true,
        message: "Event list retrive!",
        data: attendeeList,
      });
    else
      return res.status(200).json({
        status: false,
        message: "Something went wrong while getting event list!",
      });
  } catch (error) {

    return res
      .status(500)
      .json({ status: false, message: "Internal server error!", error: error });
  }
};

// get event attendee by Id
exports.getAttendeeById = async (req, res) => {
  try {
    const attendeeId = ObjectId(req.params.id);
    const eventId = ObjectId(req.query.eventId);
    const role = req.query.role;
    let attendeeData = [];

    switch (role) {
      case "member":
        attendeeData = await User.aggregate([
          {
            $match: {
              _id: attendeeId,
              "attendeeDetail.evntData": {
                $elemMatch: { event: eventId, [`${role}`]: true },
              },
            },
          },
          {
            $project: {
              _id: 1,
              firebaseId: 1,
              email: "$Preferred Email",
              type: "Member",
              title: "$attendeeDetail.title",
              name: "$attendeeDetail.name",
              firstName: "$attendeeDetail.firstName"
                ? "$attendeeDetail.firstName"
                : "",
              lastName: "$attendeeDetail.lastName"
                ? "$attendeeDetail.lastName"
                : "",
              company: "$attendeeDetail.company",
              profession: "$attendeeDetail.profession",
              phone: "$attendeeDetail.phone",
              facebook: "$attendeeDetail.facebook",
              linkedin: "$attendeeDetail.linkedin",
              description: "$attendeeDetail.description" ?? "",
              offer: "$attendeeDetail.offer" ?? "",
              event: eventId,
              contactPartnerName: "$attendeeDetail.contactPartnerName"
                ? "$attendeeDetail.contactPartnerName"
                : "",
              passcode: "$passcode" ? "$passcode" : "",
              profileImg: "$profileImg" ? "$profileImg" : "",
              partnerIcon: "$partnerIcon" ? "$partnerIcon" : "",
            },
          },
        ]);
        break;
      case "speaker":
        attendeeData = await User.aggregate([
          {
            $match: {
              _id: attendeeId,
              "attendeeDetail.evntData": {
                $elemMatch: { event: eventId, [`${role}`]: true },
              },
            },
          },
          {
            $project: {
              _id: 1,
              firebaseId: 1,
              email: "$Preferred Email",
              type: "Speaker",
              title: "$attendeeDetail.title",
              name: "$attendeeDetail.name",
              firstName: "$attendeeDetail.firstName"
                ? "$attendeeDetail.firstName"
                : "",
              lastName: "$attendeeDetail.lastName"
                ? "$attendeeDetail.lastName"
                : "",
              company: "$attendeeDetail.company",
              profession: "$attendeeDetail.profession",
              phone: "$attendeeDetail.phone",
              facebook: "$attendeeDetail.facebook",
              linkedin: "$attendeeDetail.linkedin",
              description: "$attendeeDetail.description" ?? "",
              offer: "$attendeeDetail.offer" ?? "",
              event: eventId,
              contactPartnerName: "$attendeeDetail.contactPartnerName"
                ? "$attendeeDetail.contactPartnerName"
                : "",
              passcode: "$passcode" ? "$passcode" : "",
              profileImg: "$profileImg" ? "$profileImg" : "",
              partnerIcon: "$partnerIcon" ? "$partnerIcon" : "",
            },
          },
        ]);
        break;
      case "partner":
        attendeeData = await User.aggregate([
          {
            $match: {
              _id: attendeeId,
              "attendeeDetail.evntData": {
                $elemMatch: { event: eventId, [`${role}`]: true },
              },
            },
          },
          {
            $project: {
              _id: 1,
              firebaseId: 1,
              email: "$Preferred Email",
              type: "Partner",
              title: "$attendeeDetail.title",
              name: "$attendeeDetail.name",
              firstName: "$attendeeDetail.firstName"
                ? "$attendeeDetail.firstName"
                : "",
              lastName: "$attendeeDetail.lastName"
                ? "$attendeeDetail.lastName"
                : "",
              company: "$attendeeDetail.company",
              profession: "$attendeeDetail.profession",
              phone: "$attendeeDetail.phone",
              facebook: "$attendeeDetail.facebook",
              linkedin: "$attendeeDetail.linkedin",
              description: "$attendeeDetail.description" ?? "",
              offer: "$attendeeDetail.offer" ?? "",
              event: eventId,
              contactPartnerName: "$attendeeDetail.contactPartnerName"
                ? "$attendeeDetail.contactPartnerName"
                : "",
              passcode: "$passcode" ? "$passcode" : "",
              profileImg: "$profileImg" ? "$profileImg" : "",
              partnerIcon: "$partnerIcon" ? "$partnerIcon" : "",
            },
          },
        ]);
        break;
      case "guest":
        attendeeData = await User.aggregate([
          {
            $match: {
              _id: attendeeId,
              "attendeeDetail.evntData": {
                $elemMatch: { event: eventId, [`${role}`]: true },
              },
            },
          },
          {
            $project: {
              _id: 1,
              firebaseId: 1,
              email: "$Preferred Email",
              type: "Guest",
              title: "$attendeeDetail.title",
              name: "$attendeeDetail.name",
              firstName: "$attendeeDetail.firstName"
                ? "$attendeeDetail.firstName"
                : "",
              lastName: "$attendeeDetail.lastName"
                ? "$attendeeDetail.lastName"
                : "",
              company: "$attendeeDetail.company",
              profession: "$attendeeDetail.profession",
              phone: "$attendeeDetail.phone",
              facebook: "$attendeeDetail.facebook",
              linkedin: "$attendeeDetail.linkedin",
              description: "$attendeeDetail.description" ?? "",
              offer: "$attendeeDetail.offer" ?? "",
              event: eventId,
              contactPartnerName: "$attendeeDetail.contactPartnerName"
                ? "$attendeeDetail.contactPartnerName"
                : "",
              passcode: "$passcode" ? "$passcode" : "",
              profileImg: "$profileImg" ? "$profileImg" : "",
              partnerIcon: "$partnerIcon" ? "$partnerIcon" : "",
            },
          },
        ]);
        break;
      default:
        break;
    }

    if (attendeeData.length > 0)
      return res.status(200).json({
        status: true,
        message: "Event attendee retrive!",
        data: attendeeData[0],
      });
    else
      return res
        .status(200)
        .json({ status: false, message: "Attendee details not found!" });
  } catch (error) {
    return res
      .status(500)
      .json({ status: false, message: "Internal server error!", error: error });
  }
};

/* create event attendees from admin 
    It will check if attendee data already exists or not [using preferred email] 
    If exist then check if attendee detail exists or not and update records accordingly
    It will also add the partner order if partner type attendee is created
*/
exports.createEventAttendees = async (req, res) => {
  try {
    const getEventAttendeeEmail = await User.findOne({
      "Preferred Email": req.body.email,
      $or: [{ isDelete: false }, { isDelete: { $exists: false } }],
    }).lean();
    const io = req.app.get("socketio");
    var chatData = [];
    var getEventAttendeeAuth = null;
    if (req.body.firebaseId) {
      getEventAttendeeAuth = await User.findOne({
        firebaseId: req.body.firebaseId,
      }).lean();
    }
    let descriptionData = `<div "font-family: 'Muller';">${req.body.description}</div>`;
    let offerData = `<div "font-family: 'Muller';">${req.body.offer}</div>`;
    var partnerCount = 0;
    if (req.body.type.toLowerCase() === "partner") {
      partnerCount = await User.countDocuments({
        "attendeeDetail.evntData": {
          $elemMatch: { event: req.body.eventId, partner: true },
        },
      });
    }
    if (!getEventAttendeeEmail && !getEventAttendeeAuth) {
      const newEventAttendee = new User({
        "Preferred Email": req.body.email.toLowerCase(),
        firebaseId: req.body.firebaseId,
        passcode: req.body.passcode,
        isDelete: false,
        attendeeDetail: {
          title: req.body.title,
          name: req.body.name,
          firstName: req.body.firstName ? req.body.firstName : "",
          lastName: req.body.lastName ? req.body.lastName : "",
          email: req.body.email.toLowerCase(),
          company: req.body.company,
          phone: req.body.phone,
          facebook: req.body.facebook,
          linkedin: req.body.linkedin,
          firebaseId: req.body.firebaseId,
          description: descriptionData,
          profession: req.body.profession,
          offer: offerData,
          contactPartnerName: req.body.contactPartnerName,
          evntData: [
            {
              event: req.body.eventId,
              partnerOrder: partnerCount > 0 ? partnerCount + 1 : 0,
              [req.body.type.toLowerCase()]: true,
            },
          ],
        },
      });
      const eventAttendeeData = await newEventAttendee.save();
      const newChatChannelData = await chatChannel.find({
        eventId: ObjectId(req.body.eventId),
        $or: [
          { restrictedAccess: { $in: req.body.type.toLowerCase() } },
          { accessPermission: "public" },
          { accessPermission: "admin" },
        ],
        isDelete: false,
      });

      if (newChatChannelData.length > 0) {
        const newMembers = newChatChannelData?.map(async (newChannel) => {
          if (
            newChannel.accessPermission !== "admin" ||
            (newChannel.accessPermission === "admin" &&
              eventAttendeeData.migrate_user &&
              eventAttendeeData.migrate_user.plan_id === "Staff")
          ) {
            const checkChannelMemberExists = await chatChannelMembers.find({
              userId: eventAttendeeData._id,
              channelId: newChannel._id,
              status: 2,
            });
            if (checkChannelMemberExists.length === 0) {
              const channelMember = new chatChannelMembers({
                userId: eventAttendeeData._id,
                channelId: newChannel._id,
                status: 2,
                user_type: "airtable-syncs",
              });
              if (!channelMember) {
                return res.status(200).json({
                  status: false,
                  message:
                    "Something went wrong while adding members in channel!!",
                });
              }
              await channelMember.save();
              const getAllChannelMembers = await chatChannelMembers.find({
                channelId: newChannel._id,
                status: 2,
                user_type: "airtable-syncs",
              });
              var channelMembersChat = getAllChannelMembers
                ? getAllChannelMembers.map((ids) => {
                  return { id: ids.userId._id, readmsg: false };
                })
                : [];
              channelMembersChat = [
                ...channelMembersChat.filter((ids) => {
                  if (ids.id.toString() !== eventAttendeeData._id.toString())
                    return ids;
                }),
                { id: eventAttendeeData._id, readmsg: false },
              ];
              let allMembersIdsSocket = channelMembersChat?.map((member) => {
                return member.id;
              });
              let messageData = {
                message: "",
                recipient_type: "chatChannel",
                sender_type: "adminuser",
                recipient: newChannel._id,
                sender: req.admin_Id,
                type: "chatChannel",
                group_member: channelMembersChat,
                activity_status: true,
                activity: {
                  type: "addChannelMembers",
                  userId: [eventAttendeeData._id],
                },
                userTimeStamp: moment
                  .utc()
                  .format("YYYY-MM-DDTHH:mm:ss.SSSSSS[Z]"),
              };
              // const channelMessage = new chat({
              //   message: "",
              //   recipient_type: "chatChannel",
              //   sender_type: "adminuser",
              //   recipient: newChannel._id,
              //   sender: req.admin_Id,
              //   type: "chatChannel",
              //   group_member: channelMembersChat,
              //   activity_status: true,
              //   activity: {
              //     type: "addChannelMembers",
              //     userId: [eventAttendeeData._id],
              //   },
              //   userTimeStamp: moment
              //     .utc()
              //     .format("YYYY-MM-DDTHH:mm:ss.SSSSSS[Z]"),
              // });
              // const chatData = await saveChatData(channelMessage, newChannel);
              // if (chatData) {
              const userData = await User.findById(eventAttendeeData._id);
              if (
                userData &&
                userData.deleted_group_of_user &&
                userData.deleted_group_of_user.includes(newChannel._id)
              ) {
                await User.findByIdAndUpdate(
                  eventAttendeeData._id,
                  {
                    $pull: { deleted_group_of_user: newChannel._id },
                  },
                  { new: true }
                );
              }
              // }

              // await emitSocketChannelActivityEvent(
              //   io,
              //   allMembersIdsSocket,
              //   chatData
              // );
              chatData.push({
                messageData: messageData,
                deleteMultipleRecordFromChatList: [],
                channelData: newChannel,
                allMembersIdsSocket: allMembersIdsSocket,
                removeFromDeleteGroupOfUser: true,
              });
            }
          } else {
            return {};
          }
        });
        await Promise.all([...newMembers]);
      }
      if (eventAttendeeData)
        return res.status(200).json({
          status: true,
          message: "Event attendees created successfully.",
          data: eventAttendeeData,
          chatData: chatData,
        });
      else
        return res.status(200).json({
          status: false,
          message: "Something went wrong while updating event attendees!",
        });
    } else {
      const getEventAttendee = getEventAttendeeAuth
        ? getEventAttendeeAuth
        : getEventAttendeeEmail;
      if (typeof getEventAttendee.attendeeDetail === "object") {
        const attendeeEventDataExists =
          getEventAttendee.attendeeDetail.evntData.filter((data) => {
            if (
              data.event &&
              data.event.toString() === req.body.eventId &&
              data[req.body.type.toLowerCase()]
            ) {
              return data;
            }
          });
        if (attendeeEventDataExists.length > 0) {
          return res
            .status(200)
            .json({ status: false, message: "Event attendees already exist!" });
        } else {
          const eventDataDetail =
            getEventAttendee.attendeeDetail.evntData.filter((evnt) => {
              if (evnt.event && evnt.event.toString() === req.body.eventId)
                return evnt;
            });
          var memberEventDetails;
          if (eventDataDetail.length > 0) {
            memberEventDetails = [
              ...getEventAttendee.attendeeDetail.evntData.filter((evnt) => {
                if (evnt.event && evnt.event.toString() !== req.body.eventId)
                  return evnt;
              }),
              {
                ...eventDataDetail[0],
                [req.body.type.toLowerCase()]: true,
                partnerOrder:
                  req.body.type.toLowerCase() === "partner" &&
                    eventDataDetail[0].partner !== true
                    ? partnerCount + 1
                    : eventDataDetail[0].partnerOrder,
              },
            ];
          } else {
            memberEventDetails = [
              ...getEventAttendee.attendeeDetail.evntData.filter((evnt) => {
                if (evnt.event && evnt.event.toString() !== req.body.eventId)
                  return evnt;
              }),
              {
                event: req.body.eventId,
                [req.body.type.toLowerCase()]: true,
                partnerOrder:
                  req.body.type.toLowerCase() === "partner" &&
                    eventDataDetail[0].partner !== true
                    ? partnerCount + 1
                    : 0,
              },
            ];
          }

          const updatedEventAttendeeData = await User.findByIdAndUpdate(
            getEventAttendee._id,
            {
              passcode: req.body.passcode,
              isDelete: false,
              attendeeDetail: {
                title: req.body.title,
                name: req.body.name,
                firstName: req.body.firstName ? req.body.firstName : "",
                lastName: req.body.lastName ? req.body.lastName : "",
                email: req.body.email.toLowerCase(),
                company: req.body.company,
                phone: req.body.phone,
                facebook: req.body.facebook,
                linkedin: req.body.linkedin,
                firebaseId: req.body.firebaseId,
                description: descriptionData,
                profession: req.body.profession,
                offer: offerData,
                contactPartnerName: req.body.contactPartnerName,
                evntData: memberEventDetails,
              },
            },
            { new: true }
          );

          const newChatChannelData = await chatChannel.find({
            eventId: ObjectId(req.body.eventId),
            $or: [
              { restrictedAccess: { $in: req.body.type.toLowerCase() } },
              { accessPermission: "public" },
              { accessPermission: "admin" },
            ],
            isDelete: false,
          });

          if (newChatChannelData.length > 0) {
            const newMembers = newChatChannelData?.map(async (newChannel) => {
              if (
                newChannel.accessPermission !== "admin" ||
                (newChannel.accessPermission === "admin" &&
                  getEventAttendee.migrate_user &&
                  getEventAttendee.migrate_user.plan_id === "Staff")
              ) {
                const checkChannelMemberExists = await chatChannelMembers.find({
                  userId: getEventAttendee._id,
                  channelId: newChannel._id,
                  status: 2,
                });
                if (checkChannelMemberExists.length === 0) {
                  const channelMember = new chatChannelMembers({
                    userId: getEventAttendee._id,
                    channelId: newChannel._id,
                    status: 2,
                    user_type: "airtable-syncs",
                  });
                  if (!channelMember) {
                    return res.status(200).json({
                      status: false,
                      message:
                        "Something went wrong while adding members in channel!!",
                    });
                  }
                  await channelMember.save();
                  const getAllChannelMembers = await chatChannelMembers.find({
                    channelId: newChannel._id,
                    status: 2,
                    user_type: "airtable-syncs",
                  });
                  var channelMembersChat = getAllChannelMembers
                    ? getAllChannelMembers.map((ids) => {
                      return { id: ids.userId._id, readmsg: false };
                    })
                    : [];
                  channelMembersChat = [
                    ...channelMembersChat.filter((ids) => {
                      if (ids.id.toString() !== getEventAttendee._id.toString())
                        return ids;
                    }),
                    { id: getEventAttendee._id, readmsg: false },
                  ];
                  let allMembersIdsSocket = channelMembersChat?.map(
                    (member) => {
                      return member.id;
                    }
                  );
                  // const channelMessage = new chat({
                  //   message: "",
                  //   recipient_type: "chatChannel",
                  //   sender_type: "adminuser",
                  //   recipient: newChannel._id,
                  //   sender: req.admin_Id,
                  //   type: "chatChannel",
                  //   group_member: channelMembersChat,
                  //   activity_status: true,
                  //   activity: {
                  //     type: "addChannelMembers",
                  //     userId: [getEventAttendee._id],
                  //   },
                  //   userTimeStamp: moment
                  //     .utc()
                  //     .format("YYYY-MM-DDTHH:mm:ss.SSSSSS[Z]"),
                  // });
                  // const chatData = await saveChatData(
                  //   channelMessage,
                  //   newChannel
                  // );
                  // if (chatData) {
                  const userData = await User.findById(getEventAttendee._id);
                  if (
                    userData &&
                    userData.deleted_group_of_user &&
                    userData.deleted_group_of_user.includes(newChannel._id)
                  ) {
                    await User.findByIdAndUpdate(
                      getEventAttendee._id,
                      {
                        $pull: { deleted_group_of_user: newChannel._id },
                      },
                      { new: true }
                    );
                  }
                  // }
                  // await emitSocketChannelActivityEvent(
                  //   io,
                  //   allMembersIdsSocket,
                  //   chatData
                  // );

                  chatData.push({
                    messageData: messageData,
                    deleteMultipleRecordFromChatList: [],
                    channelData: newChannel,
                    allMembersIdsSocket: allMembersIdsSocket,
                    removeFromDeleteGroupOfUser: true,
                  });
                }
              } else {
                return {};
              }
            });
            await Promise.all([...newMembers]);
          }

          if (updatedEventAttendeeData)
            return res.status(200).json({
              status: true,
              message: "Event attendees created successfully.",
              data: updatedEventAttendeeData,
              chatData: chatData,
            });
          else
            return res.status(200).json({
              status: false,
              message: "Something went wrong while updating event attendees!",
            });
        }
      } else {
        const updatedEventAttendee = await User.findByIdAndUpdate(
          getEventAttendee._id,
          {
            passcode: req.body.passcode,
            isDelete: false,
            attendeeDetail: {
              title: req.body.title,
              name: req.body.name,
              firstName: req.body.firstName ? req.body.firstName : "",
              lastName: req.body.lastName ? req.body.lastName : "",
              email: req.body.email.toLowerCase(),
              company: req.body.company,
              phone: req.body.phone,
              facebook: req.body.facebook,
              linkedin: req.body.linkedin,
              firebaseId: req.body.firebaseId,
              description: descriptionData,
              profession: req.body.profession,
              offer: offerData,
              contactPartnerName: req.body.contactPartnerName,
              evntData: [
                {
                  event: req.body.eventId,
                  [req.body.type.toLowerCase()]: true,
                  partnerOrder:
                    req.body.type.toLowerCase() === "partner"
                      ? partnerCount + 1
                      : 0,
                },
              ],
            },
          },
          { new: true }
        );

        if (updatedEventAttendee)
          return res.status(200).json({
            status: true,
            message: "Event attendees created successfully.",
            data: updatedEventAttendee,
            chatData: chatData,
          });
        else
          return res.status(200).json({
            status: false,
            message: "Something went wrong while updating event attendees!",
          });
      }
    }
  } catch (error) {
    return res
      .status(500)
      .json({ status: false, message: "Internal server error!", error: error });
  }
};

// edit event attendees from admin
exports.editEventAttendees = async (req, res) => {
  try {
    const attendeeId = ObjectId(req.params.id);
    const io = req.app.get("socketio");
    let socketEventList = [];
    let memberEventDetails = [];
    const getEventAttendee = await User.findOne({
      _id: attendeeId,
    }).lean();
    var partnerCount = 0;
    if (
      req.body.newType.toLowerCase() !== req.body.oldType.toLowerCase() &&
      req.body.newType.toLowerCase() === "partner"
    ) {
      partnerCount = await User.countDocuments({
        "attendeeDetail.evntData": {
          $elemMatch: { event: req.body.eventId, partner: true },
        },
      });
    }
    if (getEventAttendee !== null) {
      let descriptionData = `<div "font-family: 'Muller';">${req.body.description ?? ""
        }</div>`;
      let offerData = `<div "font-family: 'Muller';">${req.body.offer ?? ""
        }</div>`;

      const attendeeEventDataExists =
        getEventAttendee.attendeeDetail.evntData.filter((data) => {
          if (
            data.event.toString() === req.body.eventId &&
            data[req.body.newType.toLowerCase()] &&
            data[req.body.oldType.toLowerCase()]
          ) {
            return data;
          }
        });

      if (
        req.body.newType.toLowerCase() !== req.body.oldType.toLowerCase() &&
        req.body.oldType.toLowerCase() === "speaker"
      ) {
        const alreadyAssignSession = await eventSession
          .find({
            speakerId: { $in: [attendeeId] },
            event: ObjectId(req.body.eventId),
            isDelete: false,
          })
          .lean();
        if (alreadyAssignSession && alreadyAssignSession.length > 0) {
          var sessionList = [];
          if (alreadyAssignSession.length > 0) {
            alreadyAssignSession.map((itemSession, i) => {
              sessionList.push(itemSession.title);
            });
          }

          return res.status(200).json({
            status: false,
            message:
              "You can not updates this attendees type because this attendee is assigned as a speaker to particular: ",
            data: { sessionList },
          });
        } else {
          if (attendeeEventDataExists.length > 0) {
            const eventDataDetail =
              getEventAttendee.attendeeDetail.evntData.filter((evnt) => {
                if (evnt.event.toString() === req.body.eventId) return evnt;
              });

            if (
              eventDataDetail.length > 0 &&
              req.body.oldType !== req.body.newType
            ) {
              memberEventDetails = [
                ...getEventAttendee.attendeeDetail.evntData.filter((evnt) => {
                  if (evnt.event.toString() !== req.body.eventId) return evnt;
                }),
                {
                  ...eventDataDetail[0],
                  [req.body.oldType.toLowerCase()]: false,
                  [req.body.newType.toLowerCase()]: true,
                  partnerOrder:
                    partnerCount > 0
                      ? partnerCount + 1
                      : req.body.oldType.toLowerCase() === "partner"
                        ? 0
                        : eventDataDetail[0].partnerOrder,
                },
              ];
            }
          } else {
            const existingEvent =
              getEventAttendee.attendeeDetail.evntData.filter((evnt) => {
                if (evnt.event.toString() === req.body.eventId) return evnt;
              });

            if (
              existingEvent.length > 0 &&
              req.body.oldType !== req.body.newType
            ) {
              memberEventDetails = [
                ...getEventAttendee.attendeeDetail.evntData.filter((evnt) => {
                  if (evnt.event.toString() !== req.body.eventId) return evnt;
                }),
                {
                  ...existingEvent[0],
                  [req.body.oldType.toLowerCase()]: false,
                  [req.body.newType.toLowerCase()]: true,
                  partnerOrder:
                    partnerCount > 0
                      ? partnerCount + 1
                      : req.body.oldType.toLowerCase() === "partner"
                        ? 0
                        : existingEvent[0].partnerOrder,
                },
              ];
            } else {
              memberEventDetails = [
                ...getEventAttendee.attendeeDetail.evntData.filter((evnt) => {
                  if (evnt.event.toString() !== req.body.eventId) return evnt;
                }),
                {
                  ...existingEvent[0],
                  event: req.body.eventId,
                  [req.body.newType.toLowerCase()]: true,
                  partnerOrder:
                    partnerCount > 0
                      ? partnerCount + 1
                      : req.body.oldType.toLowerCase() === "partner"
                        ? 0
                        : existingEvent[0].partnerOrder,
                },
              ];
            }
          }

          const updateAttendeeData = await User.findOneAndUpdate(
            getEventAttendee._id,
            {
              passcode: req.body.passcode ?? getEventAttendee.passcode,
              attendeeDetail: {
                email: getEventAttendee.attendeeDetail.email.toLowerCase(),
                firebaseId:
                  req.body.firebaseId ?? getEventAttendee.attendeeDetail.firebaseId,
                title: req.body.title ?? getEventAttendee.attendeeDetail.title,
                name: req.body.name ?? getEventAttendee.attendeeDetail.name,
                firstName: req.body.firstName
                  ? req.body.firstName
                  : getEventAttendee.attendeeDetail.firstName === undefined
                    ? ""
                    : getEventAttendee.attendeeDetail.firstName,
                lastName: req.body.lastName
                  ? req.body.lastName
                  : getEventAttendee.attendeeDetail.lastName === undefined
                    ? ""
                    : getEventAttendee.attendeeDetail.lastName,
                company:
                  req.body.company ?? getEventAttendee.attendeeDetail.company,
                profession:
                  req.body.profession ??
                  getEventAttendee.attendeeDetail.profession,
                phone: req.body.phone ?? getEventAttendee.attendeeDetail.phone,
                facebook:
                  req.body.facebook ?? getEventAttendee.attendeeDetail.facebook,
                linkedin:
                  req.body.linkedin ?? getEventAttendee.attendeeDetail.linkedin,
                description: req.body.description
                  ? descriptionData
                  : getEventAttendee.attendeeDetail.description !== "" &&
                    getEventAttendee.attendeeDetail.description !== null
                    ? getEventAttendee.attendeeDetail.description
                    : "",
                offer: req.body.offer
                  ? offerData
                  : getEventAttendee.attendeeDetail.offer !== "" &&
                    getEventAttendee.attendeeDetail.offer !== null
                    ? getEventAttendee.attendeeDetail.offer
                    : "",
                contactPartnerName:
                  req.body.contactPartnerName ??
                  getEventAttendee.contactPartnerName,
                evntData:
                  memberEventDetails.length > 0
                    ? memberEventDetails
                    : getEventAttendee.attendeeDetail.evntData,
              },
            },
            { new: true }
          );

          if (req.body.oldType !== req.body.newType) {
            const newChatChannelData = await chatChannel.find({
              eventId: ObjectId(req.body.eventId),
              $or: [
                { restrictedAccess: { $in: req.body.newType.toLowerCase() } },
                { accessPermission: "public" },
                { accessPermission: "admin" },
              ],
              isDelete: false,
            });
            const oldChatChannelData = await chatChannel.find({
              eventId: ObjectId(req.body.eventId),
              $or: [
                { restrictedAccess: { $in: req.body.oldType.toLowerCase() } },
                { accessPermission: "public" },
                { accessPermission: "admin" },
              ],
              isDelete: false,
            });

            if (oldChatChannelData.length > 0) {
              const oldMembers = oldChatChannelData?.map(async (oldChannel) => {
                const userDataInside = await User.findById(attendeeId).lean();
                const userEventData =
                  userDataInside.attendeeDetail.evntData.filter((eventData) => {
                    if (
                      eventData.event &&
                      eventData.event.toString() ===
                      oldChannel._id.toString() &&
                      (eventData.partner === true ||
                        eventData.speaker === true ||
                        eventData.member === true ||
                        eventData.guest === true)
                    )
                      return eventData;
                  });
                if (
                  !(
                    (oldChannel.accessPermission === "public" &&
                      userEventData &&
                      userEventData.length > 0) ||
                    (oldChannel.accessPermission === "admin" &&
                      userEventData &&
                      userEventData.length > 0 &&
                      userDataInside.migrate_user &&
                      userDataInside.migrate_user.plan_id === "Staff")
                  )
                ) {
                  const checkChannelMemberExists =
                    await chatChannelMembers.find({
                      userId: attendeeId,
                      channelId: oldChannel._id,
                      status: 2,
                    });
                  if (checkChannelMemberExists.length > 0) {
                    const removeMembersAttendee =
                      await chatChannelMembers.deleteOne(
                        {
                          userId: attendeeId,
                          channelId: oldChannel._id,
                          user_type: "airtable-syncs",
                          status: 2,
                        },
                        { new: true }
                      );
                    if (!removeMembersAttendee) {
                      return res.status(200).json({
                        status: false,
                        message:
                          "Something went wrong while adding members in channel!!",
                      });
                    }
                    const getAllChannelMembers = await chatChannelMembers.find({
                      channelId: oldChannel._id,
                      status: 2,
                      user_type: "airtable-syncs",
                    });
                    var channelMembersChat = getAllChannelMembers
                      ? getAllChannelMembers.map((ids) => {
                        return { id: ids.userId._id, readmsg: false };
                      })
                      : [];
                    // const channelMessage = new chat({
                    //   message: "",
                    //   recipient_type: "chatChannel",
                    //   sender_type: "adminuser",
                    //   recipient: oldChannel._id,
                    //   sender: req.admin_Id,
                    //   type: "chatChannel",
                    //   group_member: channelMembersChat,
                    //   activity_status: true,
                    //   activity: {
                    //     type: "removedChannelMembers",
                    //     userId: [attendeeId],
                    //   },
                    //   userTimeStamp: moment
                    //     .utc()
                    //     .format("YYYY-MM-DDTHH:mm:ss.SSSSSS[Z]"),
                    // });
                    // const chatData = await saveChatData(
                    //   channelMessage,
                    //   oldChannel
                    // );
                    let messageData = {
                      message: "",
                      recipient_type: "chatChannel",
                      sender_type: "adminuser",
                      recipient: oldChannel._id,
                      sender: req.admin_Id,
                      type: "chatChannel",
                      group_member: channelMembersChat,
                      activity_status: true,
                      activity: {
                        type: "removedChannelMembers",
                        userId: [attendeeId],
                      },
                      userTimeStamp: moment
                        .utc()
                        .format("YYYY-MM-DDTHH:mm:ss.SSSSSS[Z]"),
                    };
                    let allMembersIdsSocket = channelMembersChat?.map(
                      (member) => {
                        return member.id;
                      }
                    );
                    socketEventList.push({
                      messageData: messageData,
                      deleteMultipleRecordFromChatList: [attendeeId],
                      channelData: oldChannel,
                      allMembersIdsSocket: allMembersIdsSocket,
                      removeFromDeleteGroupOfUser: true,
                    });
                    // deleteMultipleRecordFromChatList(
                    //   [attendeeId],
                    //   oldChannel._id
                    // );
                    // if (chatData) {
                    const userData = await User.findById(attendeeId);
                    if (
                      !(
                        userData &&
                        userData.deleted_group_of_user &&
                        userData.deleted_group_of_user.includes(oldChannel._id)
                      )
                    ) {
                      await User.findByIdAndUpdate(attendeeId, {
                        $push: { deleted_group_of_user: oldChannel._id },
                      });
                    }
                    // }

                    // await emitSocketChannelActivityEvent(
                    //   io,
                    //   allMembersIdsSocket,
                    //   chatData
                    // );
                  }
                } else {
                  return {};
                }
              });
              await Promise.all([...oldMembers]);
            }

            if (newChatChannelData.length > 0) {
              const newMembers = newChatChannelData?.map(async (newChannel) => {
                const userDataInnerSide = await User.findById(
                  attendeeId
                ).lean();
                if (
                  newChannel.accessPermission !== "admin" ||
                  (newChannel.accessPermission === "admin" &&
                    userDataInnerSide.migrate_user &&
                    userDataInnerSide.migrate_user.plan_id === "Staff")
                ) {
                  const checkChannelMemberExists =
                    await chatChannelMembers.find({
                      userId: attendeeId,
                      channelId: newChannel._id,
                      status: 2,
                    });
                  if (checkChannelMemberExists.length === 0) {
                    const channelMember = new chatChannelMembers({
                      userId: attendeeId,
                      channelId: newChannel._id,
                      status: 2,
                      user_type: "airtable-syncs",
                    });
                    if (!channelMember) {
                      return res.status(200).json({
                        status: false,
                        message:
                          "Something went wrong while adding members in channel!!",
                      });
                    }
                    await channelMember.save();
                    const getAllChannelMembers = await chatChannelMembers.find({
                      channelId: newChannel._id,
                      status: 2,
                      user_type: "airtable-syncs",
                    });
                    var channelMembersChat = getAllChannelMembers
                      ? getAllChannelMembers.map((ids) => {
                        return { id: ids.userId._id, readmsg: false };
                      })
                      : [];
                    channelMembersChat = [
                      ...channelMembersChat.filter((ids) => {
                        if (ids.id.toString() !== attendeeId.toString())
                          return ids;
                      }),
                      { id: attendeeId, readmsg: false },
                    ];
                    let allMembersIdsSocket = channelMembersChat?.map(
                      (member) => {
                        return member.id;
                      }
                    );
                    let messageData = {
                      message: "",
                      recipient_type: "chatChannel",
                      sender_type: "adminuser",
                      recipient: newChannel._id,
                      sender: req.admin_Id,
                      type: "chatChannel",
                      group_member: channelMembersChat,
                      activity_status: true,
                      activity: {
                        type: "addChannelMembers",
                        userId: [attendeeId],
                      },
                      userTimeStamp: moment
                        .utc()
                        .format("YYYY-MM-DDTHH:mm:ss.SSSSSS[Z]"),
                    };
                    socketEventList.push({
                      messageData: messageData,
                      deleteMultipleRecordFromChatList: [],
                      channelData: newChannel,
                      allMembersIdsSocket: allMembersIdsSocket,
                      removeFromDeleteGroupOfUser: true,
                    });
                    // const channelMessage = new chat({
                    //   message: "",
                    //   recipient_type: "chatChannel",
                    //   sender_type: "adminuser",
                    //   recipient: newChannel._id,
                    //   sender: req.admin_Id,
                    //   type: "chatChannel",
                    //   group_member: channelMembersChat,
                    //   activity_status: true,
                    //   activity: {
                    //     type: "addChannelMembers",
                    //     userId: [attendeeId],
                    //   },
                    //   userTimeStamp: moment
                    //     .utc()
                    //     .format("YYYY-MM-DDTHH:mm:ss.SSSSSS[Z]"),
                    // });
                    // const chatData = await saveChatData(
                    //   channelMessage,
                    //   newChannel
                    // );
                    // if (chatData) {
                    if (
                      userDataInnerSide &&
                      userDataInnerSide.deleted_group_of_user &&
                      userDataInnerSide.deleted_group_of_user.includes(
                        newChannel._id
                      )
                    ) {
                      await User.findByIdAndUpdate(
                        attendeeId,
                        {
                          $pull: { deleted_group_of_user: newChannel._id },
                        },
                        { new: true }
                      );
                    }
                    // }

                    // await emitSocketChannelActivityEvent(
                    //   io,
                    //   allMembersIdsSocket,
                    //   chatData
                    // );
                  }
                } else {
                  return {};
                }
              });
              await Promise.all([...newMembers]);
            }
          }
          if (
            req.body.newType.toLowerCase() !== req.body.oldType.toLowerCase() &&
            req.body.oldType.toLowerCase() === "partner"
          ) {
            const rearragedData = await rearrangePatneer.rearrangeAttendee(
              req.body.eventId,
              true
            );
          }
          if (updateAttendeeData)
            return res.status(200).json({
              status: true,
              message: "Event attendee updated successfully.",
              data: updateAttendeeData,
              socketEventList: socketEventList,
            });
          else
            return res.status(200).json({
              status: false,
              message: "Error while updateing event attendee!",
            });
        }
      } else {
        if (attendeeEventDataExists.length > 0) {
          const eventDataDetail =
            getEventAttendee.attendeeDetail.evntData.filter((evnt) => {
              if (evnt.event.toString() === req.body.eventId) return evnt;
            });

          if (
            eventDataDetail.length > 0 &&
            req.body.oldType !== req.body.newType
          ) {
            memberEventDetails = [
              ...getEventAttendee.attendeeDetail.evntData.filter((evnt) => {
                if (evnt.event.toString() !== req.body.eventId) return evnt;
              }),
              {
                ...eventDataDetail[0],
                [req.body.oldType.toLowerCase()]: false,
                [req.body.newType.toLowerCase()]: true,
                partnerOrder:
                  partnerCount > 0
                    ? partnerCount + 1
                    : req.body.oldType.toLowerCase() === "partner"
                      ? 0
                      : eventDataDetail[0].partnerOrder,
              },
            ];
          }
        } else {
          const existingEvent = getEventAttendee.attendeeDetail.evntData.filter(
            (evnt) => {
              if (evnt.event.toString() === req.body.eventId) return evnt;
            }
          );

          if (
            existingEvent.length > 0 &&
            req.body.oldType !== req.body.newType
          ) {
            memberEventDetails = [
              ...getEventAttendee.attendeeDetail.evntData.filter((evnt) => {
                if (evnt.event.toString() !== req.body.eventId) return evnt;
              }),
              {
                ...existingEvent[0],
                [req.body.oldType.toLowerCase()]: false,
                [req.body.newType.toLowerCase()]: true,
                partnerOrder:
                  partnerCount > 0
                    ? partnerCount + 1
                    : req.body.oldType.toLowerCase() === "partner"
                      ? 0
                      : existingEvent[0].partnerOrder,
              },
            ];
          } else {
            memberEventDetails = [
              ...getEventAttendee.attendeeDetail.evntData.filter((evnt) => {
                if (evnt.event.toString() !== req.body.eventId) return evnt;
              }),
              {
                ...existingEvent[0],
                event: req.body.eventId,
                [req.body.newType.toLowerCase()]: true,
                partnerOrder:
                  partnerCount > 0
                    ? partnerCount + 1
                    : req.body.oldType.toLowerCase() === "partner"
                      ? 0
                      : existingEvent[0].partnerOrder,
              },
            ];
          }
        }

        const updateAttendeeData = await User.findOneAndUpdate(
          getEventAttendee._id,
          {
            passcode: req.body.passcode ?? getEventAttendee.passcode,
            attendeeDetail: {
              email: getEventAttendee.attendeeDetail.email,
              firebaseId: getEventAttendee.attendeeDetail.firebaseId,
              title: req.body.title ?? getEventAttendee.attendeeDetail.title,
              name: req.body.name ?? getEventAttendee.attendeeDetail.name,
              firstName: req.body.firstName
                ? req.body.firstName
                : getEventAttendee.attendeeDetail.firstName === undefined
                  ? ""
                  : getEventAttendee.attendeeDetail.firstName,
              lastName: req.body.lastName
                ? req.body.lastName
                : getEventAttendee.attendeeDetail.lastName === undefined
                  ? ""
                  : getEventAttendee.attendeeDetail.lastName,
              company:
                req.body.company ?? getEventAttendee.attendeeDetail.company,
              profession:
                req.body.profession ??
                getEventAttendee.attendeeDetail.profession,
              phone: req.body.phone ?? getEventAttendee.attendeeDetail.phone,
              facebook:
                req.body.facebook ?? getEventAttendee.attendeeDetail.facebook,
              linkedin:
                req.body.linkedin ?? getEventAttendee.attendeeDetail.linkedin,
              description: req.body.description
                ? descriptionData
                : getEventAttendee.attendeeDetail.description !== "" &&
                  getEventAttendee.attendeeDetail.description !== null
                  ? getEventAttendee.attendeeDetail.description
                  : "",
              offer: req.body.offer
                ? offerData
                : getEventAttendee.attendeeDetail.offer !== "" &&
                  getEventAttendee.attendeeDetail.offer !== null
                  ? getEventAttendee.attendeeDetail.offer
                  : "",
              contactPartnerName:
                req.body.contactPartnerName ??
                getEventAttendee.contactPartnerName,
              evntData:
                memberEventDetails.length > 0
                  ? memberEventDetails
                  : getEventAttendee.attendeeDetail.evntData,
            },
          },
          { new: true }
        );

        if (req.body.oldType !== req.body.newType) {
          const newChatChannelData = await chatChannel.find({
            eventId: ObjectId(req.body.eventId),
            $or: [
              { restrictedAccess: { $in: req.body.newType.toLowerCase() } },
              { accessPermission: "public" },
              { accessPermission: "admin" },
            ],
            isDelete: false,
          });
          const oldChatChannelData = await chatChannel.find({
            eventId: ObjectId(req.body.eventId),
            $or: [
              { restrictedAccess: { $in: req.body.oldType.toLowerCase() } },
              { accessPermission: "public" },
              { accessPermission: "admin" },
            ],
            isDelete: false,
          });

          if (oldChatChannelData.length > 0) {
            const oldMembers = oldChatChannelData?.map(async (oldChannel) => {
              const userDataInside = await User.findById(attendeeId).lean();
              const userEventData =
                userDataInside.attendeeDetail.evntData.filter((eventData) => {
                  if (
                    eventData.event &&
                    eventData.event.toString() === oldChannel._id.toString() &&
                    (eventData.partner === true ||
                      eventData.speaker === true ||
                      eventData.member === true ||
                      eventData.guest === true)
                  )
                    return eventData;
                });
              if (
                !(
                  (oldChannel.accessPermission === "public" &&
                    userEventData &&
                    userEventData.length > 0) ||
                  (oldChannel.accessPermission === "admin" &&
                    userEventData &&
                    userEventData.length > 0 &&
                    userDataInside.migrate_user &&
                    userDataInside.migrate_user.plan_id === "Staff")
                )
              ) {
                const checkChannelMemberExists = await chatChannelMembers.find({
                  userId: attendeeId,
                  channelId: oldChannel._id,
                  status: 2,
                });
                if (checkChannelMemberExists.length > 0) {
                  const removeMembersAttendee =
                    await chatChannelMembers.deleteOne(
                      {
                        userId: attendeeId,
                        channelId: oldChannel._id,
                        user_type: "airtable-syncs",
                        status: 2,
                      },
                      { new: true }
                    );
                  if (!removeMembersAttendee) {
                    return res.status(200).json({
                      status: false,
                      message:
                        "Something went wrong while adding members in channel!!",
                    });
                  }
                  const getAllChannelMembers = await chatChannelMembers.find({
                    channelId: oldChannel._id,
                    status: 2,
                    user_type: "airtable-syncs",
                  });
                  var channelMembersChat = getAllChannelMembers
                    ? getAllChannelMembers.map((ids) => {
                      return { id: ids.userId._id, readmsg: false };
                    })
                    : [];
                  let messageData = {
                    message: "",
                    recipient_type: "chatChannel",
                    sender_type: "adminuser",
                    recipient: oldChannel._id,
                    sender: req.admin_Id,
                    type: "chatChannel",
                    group_member: channelMembersChat,
                    activity_status: true,
                    activity: {
                      type: "removedChannelMembers",
                      userId: [attendeeId],
                    },
                    userTimeStamp: moment
                      .utc()
                      .format("YYYY-MM-DDTHH:mm:ss.SSSSSS[Z]"),
                  };
                  let allMembersIdsSocket = channelMembersChat?.map(
                    (member) => {
                      return member.id;
                    }
                  );
                  socketEventList.push({
                    messageData: messageData,
                    deleteMultipleRecordFromChatList: [attendeeId],
                    channelData: oldChannel,
                    eventType: "remove-channel-member-receive",
                    allMembersIdsSocket: [attendeeId],
                    removeFromDeleteGroupOfUser: true,
                  });

                  const userData = await User.findById(attendeeId);
                  if (
                    !(
                      userData &&
                      userData.deleted_group_of_user &&
                      userData.deleted_group_of_user.includes(oldChannel._id)
                    )
                  ) {
                    await User.findByIdAndUpdate(attendeeId, {
                      $push: { deleted_group_of_user: oldChannel._id },
                    });
                  }

                }
              } else {
                return {};
              }
            });
            await Promise.all([...oldMembers]);
          }

          if (newChatChannelData.length > 0) {
            const newMembers = newChatChannelData?.map(async (newChannel) => {
              const userDataInnerSide = await User.findById(attendeeId).lean();
              if (
                (
                  userDataInnerSide.migrate_user &&
                  userDataInnerSide.migrate_user.plan_id === "Staff")
              ) {
                const checkChannelMemberExists = await chatChannelMembers.find({
                  userId: attendeeId,
                  channelId: newChannel._id,
                  status: 2,
                });
                if (checkChannelMemberExists.length === 0) {
                  const channelMember = new chatChannelMembers({
                    userId: attendeeId,
                    channelId: newChannel._id,
                    status: 2,
                    user_type: "airtable-syncs",
                  });
                  if (!channelMember) {
                    return res.status(200).json({
                      status: false,
                      message:
                        "Something went wrong while adding members in channel!!",
                    });
                  }
                  await channelMember.save();
                  const getAllChannelMembers = await chatChannelMembers.find({
                    channelId: newChannel._id,
                    status: 2,
                    user_type: "airtable-syncs",
                  });
                  var channelMembersChat = getAllChannelMembers
                    ? getAllChannelMembers.map((ids) => {
                      return { id: ids.userId._id, readmsg: false };
                    })
                    : [];
                  channelMembersChat = [
                    ...channelMembersChat,
                    { id: attendeeId, readmsg: false },
                  ];
                  let messageData = {
                    message: "",
                    recipient_type: "chatChannel",
                    sender_type: "adminuser",
                    recipient: newChannel._id,
                    sender: req.admin_Id,
                    type: "chatChannel",
                    group_member: channelMembersChat,
                    activity_status: true,
                    activity: {
                      type: "addChannelMembers",
                      userId: [attendeeId],
                    },
                    userTimeStamp: moment
                      .utc()
                      .format("YYYY-MM-DDTHH:mm:ss.SSSSSS[Z]"),
                  };
                  let allMembersIdsSocket = channelMembersChat?.map(
                    (member) => {
                      return member.id;
                    }
                  );
                  socketEventList.push({
                    messageData: messageData,
                    deleteMultipleRecordFromChatList: [],
                    channelData: newChannel,
                    allMembersIdsSocket: allMembersIdsSocket,
                    removeFromDeleteGroupOfUser: true,
                  });


                  const userData = await User.findById(attendeeId);
                  if (
                    userData &&
                    userData.deleted_group_of_user &&
                    userData.deleted_group_of_user.includes(newChannel._id)
                  ) {
                    await User.findByIdAndUpdate(
                      attendeeId,
                      {
                        $pull: { deleted_group_of_user: newChannel._id },
                      },
                      { new: true }
                    );
                  }

                }
              } else {
                return {};
              }
            });
            await Promise.all([...newMembers]);
          }
        }
        if (
          req.body.newType.toLowerCase() !== req.body.oldType.toLowerCase() &&
          req.body.oldType.toLowerCase() === "partner"
        ) {
          const rearragedData = await rearrangePatneer.rearrangeAttendee(req.body.eventId, true);
        }
        if (updateAttendeeData)
          return res.status(200).json({
            status: true,
            message: "Event attendee updated successfully.",
            data: updateAttendeeData,
            listOfSocketEvents: socketEventList,
          });
        else
          return res.status(200).json({
            status: false,
            message: "Error while updateing event attendee!",
          });
      }
    } else {
      return res
        .status(200)
        .json({ status: false, message: "Event attendee not found!" });
    }
  } catch (error) {
    return res
      .status(500)
      .json({ status: false, message: "Internal server error!", error: error });
  }
};

// delete event attendees from admin
exports.deleteEventAttendees = async (req, res) => {
  try {
    const deleteDataArray = req.body.deleteData;
    const eventId = ObjectId(req.query.eventId);
    var attendeeIds = (updateAttendeeDataArray = []);
    var speakerIds = [];
    let socketEventList = [];

    var deleteData = deleteDataArray.map(async (item, i) => {
      if (item.role !== "Speaker") attendeeIds.push(ObjectId(item.attendeeId));
      else speakerIds.push(ObjectId(item.attendeeId));
    });

    const alreadyAssignSession = await eventSession
      .find({
        speakerId: { $in: speakerIds },
        event: eventId,
        isDelete: false,
      })
      .lean();
    if (alreadyAssignSession && alreadyAssignSession.length > 0) {
      var sessionList = [];
      if (alreadyAssignSession.length > 0) {
        alreadyAssignSession.map((itemSession, i) => {
          sessionList.push(itemSession.title);
        });
      }
      return res.status(200).json({
        status: false,
        message:
          "You cannot delete this attendee because it is assigned to following sessions: ",
        data: { sessionList },
      });
    } else {
      attendeeIds = [...attendeeIds, speakerIds];
    }

    var deleteData = deleteDataArray.map(async (item, i) => {
      const attendeeId = ObjectId(item.attendeeId);
      const role = item.role;
      const io = req.app.get("socketio");
      let memberEventDetails = [];
      const getEventAttendee = await User.findOne({
        _id: attendeeId,
        $or: [{ isDelete: false }, { isDelete: { $exists: false } }],
      }).lean();
      if (getEventAttendee !== null) {
        const attendeeEventDataExists =
          getEventAttendee.attendeeDetail.evntData.filter((data) => {
            if (
              data.event &&
              data.event.toString() === eventId.toString() &&
              data[role.toLowerCase()]
            ) {
              return data;
            }
          });
        if (attendeeEventDataExists.length > 0) {
          const eventDataDetail =
            getEventAttendee.attendeeDetail.evntData.filter((evnt) => {
              if (evnt.event && evnt.event.toString() === eventId.toString())
                return evnt;
            });
          if (eventDataDetail.length > 0) {
            memberEventDetails = [
              ...getEventAttendee.attendeeDetail.evntData.filter((evnt) => {
                if (evnt.event && evnt.event.toString() !== eventId.toString())
                  return evnt;
              }),
              {
                ...eventDataDetail[0],
                [role.toLowerCase()]: false,
                partnerOrder:
                  role.toLowerCase() === "partner"
                    ? 0
                    : eventDataDetail[0].partnerOrder,
              },
            ];
          }
        }

        let updateAttendee = await User.findOneAndUpdate(
          { _id: getEventAttendee._id },
          {
            attendeeDetail: {
              title: getEventAttendee.attendeeDetail.title,
              name: getEventAttendee.attendeeDetail.name,
              firstName: getEventAttendee.attendeeDetail.firstName
                ? getEventAttendee.attendeeDetail.firstName
                : "",
              lastName: getEventAttendee.attendeeDetail.lastName
                ? getEventAttendee.attendeeDetail.lastName
                : "",
              company: getEventAttendee.attendeeDetail.company,
              profession: getEventAttendee.attendeeDetail.profession,
              phone: getEventAttendee.attendeeDetail.phone,
              facebook: getEventAttendee.attendeeDetail.facebook,
              linkedin: getEventAttendee.attendeeDetail.linkedin,
              firebaseId: getEventAttendee.firebaseId,
              description:
                getEventAttendee.attendeeDetail.description !== "" &&
                  getEventAttendee.attendeeDetail.description !== null
                  ? getEventAttendee.attendeeDetail.description
                  : "",
              offer:
                getEventAttendee.attendeeDetail.offer !== "" &&
                  getEventAttendee.attendeeDetail.offer !== null
                  ? getEventAttendee.attendeeDetail.offer
                  : "",
              evntData:
                memberEventDetails.length > 0
                  ? memberEventDetails
                  : getEventAttendee.attendeeDetail.evntData,
            },
          },
          { new: true }
        );
        updateAttendeeDataArray.push(updateAttendee);
        const oldChatChannelData = await chatChannel.find({
          eventId: eventId,
          $or: [
            { restrictedAccess: { $in: role.toLowerCase() } },
            { accessPermission: "public" },
            { accessPermission: "admin" },
          ],
          isDelete: false,
        });

        if (oldChatChannelData.length > 0) {
          const oldMembers = oldChatChannelData?.map(async (oldChannel) => {
            const userDataInside = await User.findById(attendeeId).lean();
            const userEventData = userDataInside.attendeeDetail.evntData.filter(
              (eventData) => {
                if (
                  eventData.event &&
                  eventData.event.toString() === oldChannel._id.toString() &&
                  (eventData.partner === true ||
                    eventData.speaker === true ||
                    eventData.member === true ||
                    eventData.guest === true)
                )
                  return eventData;
              }
            );
            if (
              !(
                (oldChannel.accessPermission === "public" &&
                  userEventData &&
                  userEventData.length > 0) ||
                (oldChannel.accessPermission === "admin" &&
                  userEventData &&
                  userEventData.length > 0 &&
                  userDataInside.migrate_user &&
                  userDataInside.migrate_user.plan_id === "Staff")
              )
            ) {
              const checkChannelMemberExists = await chatChannelMembers.find({
                userId: attendeeId,
                channelId: oldChannel._id,
                status: 2,
                user_type: "airtable-syncs",
              });
              if (checkChannelMemberExists.length > 0) {
                const removeMembersAttendee =
                  await chatChannelMembers.deleteOne(
                    {
                      userId: attendeeId,
                      channelId: oldChannel._id,
                      user_type: "airtable-syncs",
                      status: 2,
                    },
                    { new: true }
                  );
                if (!removeMembersAttendee) {
                  return res.status(200).json({
                    status: false,
                    message:
                      "Something went wrong while deleting members in channel!!",
                  });
                }
                const getAllChannelMembers = await chatChannelMembers.find({
                  channelId: oldChannel._id,
                  status: 2,
                  user_type: "airtable-syncs",
                });
                var channelMembersChat = getAllChannelMembers
                  ? getAllChannelMembers.map((ids) => {
                    return { id: ids.userId._id, readmsg: false };
                  })
                  : [];
                let memberData = {
                  message: "",
                  recipient_type: "chatChannel",
                  sender_type: "adminuser",
                  recipient: oldChannel._id,
                  sender: req.admin_Id,
                  type: "chatChannel",
                  group_member: channelMembersChat,
                  activity_status: true,
                  activity: {
                    type: "removedChannelMembers",
                    userId: [attendeeId],
                  },
                  userTimeStamp: moment
                    .utc()
                    .format("YYYY-MM-DDTHH:mm:ss.SSSSSS[Z]"),
                };
                let allMembersIdsSocket = channelMembersChat?.map((member) => {
                  return member.id;
                });
                socketEventList.push({
                  messageData: messageData,
                  deleteMultipleRecordFromChatList: [attendeeId],
                  channelData: oldChannel,
                  allMembersIdsSocket: allMembersIdsSocket,
                  removeFromDeleteGroupOfUser: false,
                });
                // const channelMessage = new chat({
                //   message: "",
                //   recipient_type: "chatChannel",
                //   sender_type: "adminuser",
                //   recipient: oldChannel._id,
                //   sender: req.admin_Id,
                //   type: "chatChannel",
                //   group_member: channelMembersChat,
                //   activity_status: true,
                //   activity: {
                //     type: "removedChannelMembers",
                //     userId: [attendeeId],
                //   },
                //   userTimeStamp: moment
                //     .utc()
                //     .format("YYYY-MM-DDTHH:mm:ss.SSSSSS[Z]"),
                // });
                // const chatData = await saveChatData(channelMessage, oldChannel);
                // deleteMultipleRecordFromChatList([attendeeId], oldChannel._id);
                // if (chatData) {
                const userData = await User.findById(attendeeId);
                if (
                  !(
                    userData &&
                    userData.deleted_group_of_user &&
                    userData.deleted_group_of_user.includes(oldChannel._id)
                  )
                ) {
                  await User.findByIdAndUpdate(attendeeId, {
                    $push: { deleted_group_of_user: oldChannel._id },
                  });
                }
                // }

                // await emitSocketChannelActivityEvent(
                //   io,
                //   allMembersIdsSocket,
                //   chatData
                // );
              }
            } else {
              return {};
            }
          });
          await Promise.all([...oldMembers]);
        }
        if (role.toLowerCase() === "partner") {
          const rearragedData = await rearrangePatneer.rearrangeAttendee(req.body.eventId, true);
        }
      }
    });
    await Promise.all([...deleteData]);
    if (updateAttendeeDataArray)
      return res.status(200).json({
        status: true,
        message: "Event attendee deleted successfully.",
        data: updateAttendeeDataArray,
        listOfSocketEvents: socketEventList,
      });
    else
      return res
        .status(200)
        .json({ status: false, message: "Event attendee not found!" });
  } catch (error) {
    return res
      .status(500)
      .json({ status: false, message: "Internal server error!", error: error });
  }
};

//reorder partner attendees from admin side
exports.rearrangeAttendees = async (req, res) => {
  try {
    const rearrangedData = await rearrangePatneer.rearrangeAttendee(
      req.body.eventId,
      false,
      req.body.Ids
    );
    res.status(200).json(rearrangedData);
  } catch (e) {
    res.status(200).json({ status: false, message: e });
  }
};

//reorder partner attendees from admin side
exports.rearrangeAttendeesListV2 = async (req, res) => {
  try {
    const rearrangedData = await rearrangePatneer.rearrangeAttendeeListV2(
      req.body.eventId,
      req.body.Ids
    );
    res.status(200).json(rearrangedData);
  } catch (e) {
    res.status(200).json({ status: false, message: e });
  }
};

// get event attendee by Id
exports.getAttendeeByIdV2 = async (req, res) => {
  try {
    const attendeeId = ObjectId(req.params.id);
    const eventId = ObjectId(req.query.eventId);
    const role = ObjectId(req.query.role);
    let attendeeData = [];

    let pipeline = [
      {
        '$match': {
          'event': eventId,
          'role': role,
          'user': attendeeId,
          'isDelete': false
        }
      }, {
        '$lookup': {
          'from': 'event_wise_participant_types',
          'localField': 'role',
          'foreignField': '_id',
          'as': 'event_wise_participant_types_result'
        }
      }, {
        '$unwind': {
          'path': '$event_wise_participant_types_result',
          'preserveNullAndEmptyArrays': false
        }
      }, {
        '$lookup': {
          'from': 'airtable-syncs',
          'localField': 'user',
          'foreignField': '_id',
          'as': 'airtable-syncs_result'
        }
      }, {
        '$unwind': {
          'path': '$airtable-syncs_result',
          'preserveNullAndEmptyArrays': false
        }
      },{
        '$lookup': {
          'from': 'event_questions', 
          'localField': 'user', 
          'foreignField': 'userId', 
          'pipeline': [
              {
                '$match': {
                    'eventId': eventId,
                    isDelete: false
                }
              }
          ], 
          'as': 'event_questions'
        }
      },
       {
        '$unwind': {
          'path': '$event_questions', 
          'preserveNullAndEmptyArrays': true
        }
      },
       {
        '$project': {
          '_id': {
            '$ifNull': [
              '$airtable-syncs_result._id', ''
            ]
          },
          'email': {
            '$ifNull': [
              '$airtable-syncs_result.Preferred Email', ''
            ]
          },
          'firebaseId': {
            '$ifNull': [
              '$airtable-syncs_result.firebaseId', ''
            ]
          },
          'passcode': {
            '$ifNull': [
              '$airtable-syncs_result.passcode', ''
            ]
          },
          'title': {
            '$ifNull': [
              '$airtable-syncs_result.attendeeDetail.title', ''
            ]
          },
          'name': {
            '$ifNull': [
              '$airtable-syncs_result.attendeeDetail.name', ''
            ]
          },
          'firstName': {
            '$ifNull': [
              '$airtable-syncs_result.first_name', ''
            ]
          },
          'lastName': {
            '$ifNull': [
              '$airtable-syncs_result.last_name', ''
            ]
          },
          'display_name': {
            '$ifNull': [
              '$airtable-syncs_result.display_name', ''
            ]
          },
          'company': {
            '$ifNull': [
              '$airtable-syncs_result.attendeeDetail.company', ''
            ]
          },
          'profession': {
            '$ifNull': [
              '$airtable-syncs_result.attendeeDetail.profession', ''
            ]
          },
          'phone': {
            '$ifNull': [
              '$airtable-syncs_result.attendeeDetail.phone', ''
            ]
          },
          'facebook': {
            '$ifNull': [
              '$airtable-syncs_result.attendeeDetail.facebook', ''
            ]
          },
          'linkedin': {
            '$ifNull': [
              '$airtable-syncs_result.attendeeDetail.linkedin', ''
            ]
          },
          'description': {
            '$ifNull': [
              '$airtable-syncs_result.attendeeDetail.description', ''
            ]
          },
          'offer': {
            '$ifNull': [
              '$airtable-syncs_result.attendeeDetail.offer', ''
            ]
          },
          'contact_name': 1,
          'partner_order': 1,
          // 'description': 1,
          // 'offer': 1,
          'type_icon': {
            '$cond': {
              'if': { '$eq': ['$event_wise_participant_types_result.role', 'Partner'] },
              'then': '$airtable-syncs_result.partnerIcon',
              'else': '$airtable-syncs_result.profileImg'
            }
          },
          'contactPartnerName': '$contact_name',
          'profileImg': {
            '$cond': {
              'if': { '$eq': ['$event_wise_participant_types_result.role', 'Partner'] },
              'then': { '$ifNull': ['$airtable-syncs_result.partnerIcon', ''] },
              'else': { '$ifNull': ['$airtable-syncs_result.profileImg', ''] },
            }
          },
          'partnerIcon': '$airtable-syncs_result.profileImg',
          'event': '$event',
          'event': '$event',
          'role': '$event_wise_participant_types_result.role',
          'role_type': '$event_wise_participant_types_result._id',
          'isManuallyAdded': '$isManuallyAdded',
          event_questions: "$event_questions"
        }
      }
    ]

    // let iconPipeline = [
    //   {
    //     '$match': {
    //       'event': eventId, 
    //       'user': attendeeId,
    //       'isDelete': false
    //     }
    //   }, {
    //     '$lookup': {
    //       'from': 'event_wise_participant_types', 
    //       'localField': 'role', 
    //       'foreignField': '_id', 
    //       'as': 'event_wise_participant_types_result'
    //     }
    //   }, {
    //     '$unwind': {
    //       'path': '$event_wise_participant_types_result', 
    //       'preserveNullAndEmptyArrays': false
    //     }
    //   }, {
    //     '$group': {
    //       '_id': '$event_wise_participant_types_result._id', 
    //       'role': {
    //         '$first': '$event_wise_participant_types_result.role'
    //       }, 
    //       'type_icon': {
    //         '$first': '$type_icon'
    //       }
    //     }
    //   }
    // ]
    attendeeData = await EventParticipantAttendees.aggregate(pipeline);
    // let iconList = await EventParticipantAttendees.aggregate(iconPipeline);

    if (attendeeData.length > 0)
      return res.status(200).json({
        status: true,
        message: "Event attendee retrive!",
        data: attendeeData[0],
        // iconList : iconList,
      });
    else
      return res
        .status(200)
        .json({ status: false, message: "Attendee details not found!", data: {}, iconList: [] });
  } catch (error) {
    return res
      .status(500)
      .json({ status: false, message: "Internal server error!", error: error });
  }
};


// import event attendees data version 2
exports.importAttendeesIMPV2 = async (req, res) => {
  try {
    const communityId = req.currentEdge.relation_id._id
    const body = req.body;
    const allAttendees = body.allAttendees;
    let listOfSocketEvents = [];
    let resultArray = [];
    let errorArray = [];
    let allRole = [];
    let allEvent = [];
    let emailPattern =
      /^(([^<>()[\]\\.,;:\s@"]+(\.[^<>()[\]\\.,;:\s@"]+)*)|.(".+"))@((\[[0-9]{1,3}\.[0-9]{1,3}\.[0-9]{1,3}\.[0-9]{1,3}\])|(([a-zA-Z\-0-9]+\.)+[a-zA-Z]{2,}))$/;
    let isGuest = true;
    for (let i = 0; i < allAttendees.length; i++) {
      let name = allAttendees[i].name ? allAttendees[i].name.trim() : "";
      let email = allAttendees[i].email ? allAttendees[i].email.trim() : "";
      let type = allAttendees[i].type ? allAttendees[i].type.trim() : "";
      let eventId = allAttendees[i].eventId
        ? allAttendees[i].eventId.trim()
        : "";

      if (type) {
        if (!allRole.includes(type)) {
          allRole.push(type);
        }
      } else {
        if (!errorArray.includes("Type can not be empty")) {
          errorArray.push("Type can not be empty");
        }
      }

      if (eventId && ObjectId.isValid(eventId)) {
        if (!allEvent.includes(eventId)) {
          allEvent.push(eventId);
        }
      } else {
        if (!errorArray.includes("Event id can not be empty or invalid")) {
          errorArray.push("Event id can not be empty or invalid");
        }
      }

      if (!name || name.trim() == "") {
        if (!errorArray.includes("Attendees name can not be empty")) {
          errorArray.push("Attendees name can not be empty");
        }
      }
      if (!email || email.trim() == "" || !emailPattern.test(email)) {
        if (!errorArray.includes("Attendees email can not be empty")) {
          errorArray.push("Attendees email can not be empty");
        }
      }
    }

    for (let i = 0; i < allRole.length; i++) {
      let roleDetail = await eventWiseParticipantTypes.findOne({
        role: allRole[i],
        event: new ObjectId(allEvent[0]),
        isDelete: false,
      });
      if (
        !roleDetail &&
        !errorArray.includes(
          `${allRole[i]} role is not created as attendee participant types`
        )
      ) {
        errorArray.push(
          `${allRole[i]} role is not created as attendee participant types`
        );
      }
    }

    if (errorArray.length == 0) {
      for (let i = 0; i < allAttendees.length; i++) {
        if (
          allAttendees[i].name &&
          allAttendees[i].email &&
          allAttendees[i].type &&
          allAttendees[i].eventId
        ) {
          let name = allAttendees[i].name.trim();
          let email = allAttendees[i].email.toLowerCase().trim();
          let type = allAttendees[i].type.trim();
          let eventId = ObjectId(allAttendees[i].eventId);
          let passcode = allAttendees[i].passcode;
          let title = allAttendees[i].title;
          let firstName = name.split(" ")[0];
          let lastName = name.split(" ")[1];
          let company = allAttendees[i].company;
          let profession = allAttendees[i].profession;
          let phone = allAttendees[i].phone;
          let facebook = allAttendees[i].facebook;
          let linkedin = allAttendees[i].linkedin;
          let firebaseId = allAttendees[i].firebaseId;
          let userData = {};
          userData = await User.findOne({
            "Preferred Email": email,
            $or: [{ isDelete: false }, { isDelete: { $exists: false } }],
          })
            .select("attendeeDetail first_name last_name display_name")
            .lean();
          if (!userData) {
            userData = await User.create({
              "Preferred Email": email,
              email: email,
              passcode: passcode,
              isDelete: false,
              first_name: firstName,
              last_name: lastName,
              attendeeDetail: {
                title: title,
                name: name,
                firstName: firstName,
                lastName: lastName,
                email: email,
                company: company,
                profession: profession,
                phone: phone,
                facebook: facebook,
                linkedin: linkedin,
                firebaseId: firebaseId,
              },
            });
          } else {
            if (!userData.attendeeDetail) {
              userData.attendeeDetail = {};
            }
            let obj = {
              passcode: passcode ? passcode : userData.passcode,
              first_name: firstName
                ? firstName
                : userData.first_name,
              last_name: lastName
                ? lastName
                : userData.last_name,
              attendeeDetail: {
                title: title ? title : userData.attendeeDetail.title,
                name: name ? name : userData.attendeeDetail.name,
                firstName: firstName
                  ? firstName
                  : userData.attendeeDetail.firstName,
                lastName: lastName
                  ? lastName
                  : userData.attendeeDetail.lastName,
                email: email ? email : userData.attendeeDetail.email,
                company: company ? company : userData.attendeeDetail.company,
                profession: profession
                  ? profession
                  : userData.attendeeDetail.profession,
                phone: phone ? phone : userData.attendeeDetail.phone,
                facebook: facebook
                  ? facebook
                  : userData.attendeeDetail.facebook,
                linkedin: linkedin
                  ? linkedin
                  : userData.attendeeDetail.linkedin,
                firebaseId: firebaseId ? firebaseId : userData.attendeeDetail.firebaseId,
              },
            };
            await User.findByIdAndUpdate(userData._id, { $set: obj });
          }
          userData.roleDetail = {};
          userData.participantAttendees = {};

          let roleDetail = await eventWiseParticipantTypes.findOne({
            role: type,
            event: eventId,
            isDelete: false,
          });
          if (roleDetail) {
            let partnerCount = 0;
            let private_profile = false;
            if (type === "Partner") {
              partnerCount = await EventParticipantAttendees.countDocuments({
                event: eventId,
                role: ObjectId(roleDetail._id),
                isDelete: false,
              });
              private_profile = true;
            }
            let attendeesData = await EventParticipantAttendees.findOne({
              event: eventId,
              role: ObjectId(roleDetail._id),
              user: ObjectId(userData._id),
              isDelete: false,
              relation_id: ObjectId(communityId),
            });
            if (!attendeesData) {
              //add isManuallyAdded=true flag
              let createAttendeesData = await EventParticipantAttendees.create({
                event: eventId,
                role: ObjectId(roleDetail._id),
                user: ObjectId(userData._id),
                isDelete: false,
                partner_order: ++partnerCount,
                isManuallyAdded: true,
                private_profile: private_profile,
                relation_id: ObjectId(communityId)
              });
              userData.participantAttendees = createAttendeesData;
            } else {
              userData.participantAttendees = attendeesData;
              // update
            }
            userData.roleDetail = roleDetail;
          }

          let updatingData = await addAttendeeInChannelAtImport(
            eventId,
            type,
            userData,
            req.admin_Id,
          );
          if(updatingData){
            listOfSocketEvents.push(updatingData[0]);
          }
          resultArray.push(userData);

           // create guest user
            let response = await this.createGuestUser({
              email: email,
              community: req.currentEdge.relation_id,
            });
            if (!response.status) {
              isGuest = false
            }
        }
      }
      if(!isGuest){
        return res.status(200).json({
          status: false,
          message: "Something went wrong while creating the guest user!",
          errorReasons: [],
        });
      }

      // Push RabbitMQ event for chat microservice
      if(listOfSocketEvents && listOfSocketEvents.length > 0){
        const payload = {
          event: "UPDATE_CHANNEL",
          data: {
            listSocketEvents: listOfSocketEvents,
            relation_id:communityId,
          },
        };
  
        await publishMessage(JSON.stringify(payload), "CHAT_CHANNEL");
      }
      

      return res.status(200).json({
        status: true,
        message: "Import successfully done!",
        resultArray: resultArray,
        listOfSocketEvents: listOfSocketEvents,
      });
    } else {
      return res.status(200).json({
        status: false,
        message: "Import data is not validated!",
        errorReasons: errorArray,
      });
    }
  } catch (e) {
    console.log("error ", e)
    res.status(200).json({ status: false, message: "Something went wrong!" });
  }
};

// export event attendees data
exports.exportAttendeesV2 = async (req, res) => {
  try {
    const eventId = new ObjectId(req.params.id);
    let pipeline = [
      {
        '$match': {
          'isDelete': false,
          'event': eventId
        }
      }, {
        '$lookup': {
          'from': 'airtable-syncs',
          'localField': 'user',
          'foreignField': '_id',
          'as': 'user_result',
          'pipeline': [
            {
              '$project': {
                'Preferred Email': 1,
                'attendeeDetail': 1
              }
            }
          ]
        }
      }, {
        '$unwind': {
          'path': '$user_result',
          'preserveNullAndEmptyArrays': false
        }
      }, {
        '$lookup': {
          'from': 'events',
          'localField': 'event',
          'foreignField': '_id',
          'as': 'events_result'
        }
      }, {
        '$unwind': {
          'path': '$events_result',
          'preserveNullAndEmptyArrays': false
        }
      }, {
        '$lookup': {
          'from': 'event_wise_participant_types',
          'localField': 'role',
          'foreignField': '_id',
          'as': 'event_wise_participant_types_result'
        }
      }, {
        '$unwind': {
          'path': '$event_wise_participant_types_result',
          'preserveNullAndEmptyArrays': false
        }
      }, {
        '$project': {
          '_id': 0,
          'title ': '$events_result.title',
          'name': '$user_result.attendeeDetail.name',
          'email': '$user_result.Preferred Email',
          'company': '$user_result.attendeeDetail.company',
          'phone': '$user_result.attendeeDetail.phone',
          'facebook': '$user_result.attendeeDetail.facebook',
          'linkedin': '$user_result.attendeeDetail.linkedin',
          'firebaseId': '$user_result.attendeeDetail.firebaseId',
          'profession': '$user_result.attendeeDetail.profession',
          'type': '$event_wise_participant_types_result.role'
        }
      }, {
        '$sort': {
          'name': 1
        }
      }
    ]
    let data = await EventParticipantAttendees.aggregate(pipeline);
    if (data)
      return res.status(200).json({
        status: true,
        message: "All Attendees list retrive!",
        data: data,
      });
    else
      return res
        .status(200)
        .json({ status: true, message: "No attendees data found!", data: [] });
  } catch (error) {
    return res
      .status(500)
      .json({ status: false, message: "Internal server error!", error: error });
  }
};

/* create event attendees from admin version 2
    It will check if attendee data already exists or not [using preferred email] 
    If exist then check if attendee detail exists or not and update records accordingly
    It will also add the partner order if partner type attendee is created
*/
exports.createEventAttendeesV2 = async (req, res) => {
  try {
    const errors = validationResult(req);
    if (!errors.isEmpty()) {
      let result = validationResult(req).array({ onlyFirstError: false });
      result = result.map((err) => { return { path: err['path'], msg: err['msg'] }; });
      return res.status(403).json({ status: false, message: "Validation error!", errors: result });
    } else {
      const io = req.app.get("socketio");
      let chatData = [];
      let descriptionData = `<div "font-family: 'Muller';">${req.body.description}</div>`;
      let offerData = `<div "font-family: 'Muller';">${req.body.offer}</div>`;
      var partnerCount = 0;
      var contactName = req.body.contactPartnerName;
      // var typeIcon = "";
      // if (req && req.type_icon) {
      //   typeIcon = req.type_icon;
      // }

      let allRoleDetail = await eventWiseParticipantTypes.find({ event: ObjectId(req.body.eventId), isDelete: false });
      let roleDetail = allRoleDetail.find(x => x["_id"].toString() == req.body.type.toString());
      var profileImg = "";
      var partnerIcon = "";
      if (req && req.type_icon) {
        roleDetail && roleDetail.role.toString().toLowerCase() == "partner" ? partnerIcon = req.type_icon : profileImg = req.type_icon;
      }

      if (roleDetail) {
        if (roleDetail && roleDetail.role.toString().toLowerCase() == "partner") {
          partnerCount = await EventParticipantAttendees.countDocuments({ event: req.body.eventId, role: roleDetail["_id"], isDelete: false, }).lean();
        }

        const getEventAttendeeEmail = await User.findOne({ "Preferred Email": req.body.email, $or: [{ isDelete: false }, { isDelete: { $exists: false } }], }).lean();
        var getEventAttendeeAuth = null;
        if (req.body.firebaseId) {
          getEventAttendeeAuth = await User.findOne({ firebaseId: req.body.firebaseId, }).lean();
        }

        if (!getEventAttendeeEmail && !getEventAttendeeAuth) { //user not exist + attendeeDetail not exist
          let tempFirstName = req.body.firstName ? req.body.firstName : ""
          let tempLastName = req.body.lastName ? req.body.lastName : ""
          const newEventAttendee = new User({
            "Preferred Email": req.body.email.toLowerCase(),
            firebaseId: req.body.firebaseId,
            passcode: req.body.passcode,
            isDelete: false,
            profileImg: profileImg,
            partnerIcon: partnerIcon,
            first_name: req.body.firstName ? req.body.firstName : "",
            last_name: req.body.lastName ? req.body.lastName : "",
            display_name: req.body.display_name ? req.body.display_name : (`${tempFirstName} ${tempLastName}`),
            attendeeDetail: {
              title: req.body.title,
              name: req.body.name,
              firstName: req.body.firstName ? req.body.firstName : "",
              lastName: req.body.lastName ? req.body.lastName : "",
              email: req.body.email.toLowerCase(),
              company: req.body.company,
              phone: req.body.phone,
              facebook: req.body.facebook,
              linkedin: req.body.linkedin,
              firebaseId: req.body.firebaseId,
              description: descriptionData,
              profession: req.body.profession,
              offer: offerData,
              contactPartnerName: req.body.contactPartnerName,
            },
          });
          const eventAttendeeData = await newEventAttendee.save();
          let eventParticipantAttendeesObj = { user: eventAttendeeData._id, event: req.body.eventId, isDelete: false, relation_id: ObjectId(req.currentEdge.relation_id._id) ,role: roleDetail["_id"],};
          const eventParticipantAttendeesDetail = await EventParticipantAttendees.findOne(eventParticipantAttendeesObj).lean();
          if (!eventParticipantAttendeesDetail) {
            eventParticipantAttendeesObj["partner_order"] = ++partnerCount;
            eventParticipantAttendeesObj["contact_name"] = contactName;
            if (roleDetail.role.toString().toLowerCase() == "partner") {
              eventParticipantAttendeesObj["private_profile"] = true;
              eventParticipantAttendeesObj["description"] = descriptionData;
              eventParticipantAttendeesObj["offer"] = offerData;
            }
            // eventParticipantAttendeesObj["type_icon"] = typeIcon;
            eventParticipantAttendeesObj["isManuallyAdded"] = true;
            const eventParticipantAttendeesCreated = await EventParticipantAttendees.create(eventParticipantAttendeesObj);
            // await updateAttendeeIcon(eventAttendeeData._id, roleDetail, typeIcon);
          }

          const newChatChannelData = await chatChannel.find({
            eventId: ObjectId(req.body.eventId),
            $or: [
              { restrictedAccess: { $in: roleDetail["role"].toLowerCase() } },
              { accessPermission: "public" },
              { accessPermission: "admin" },
            ],
            isDelete: false,
          });

          if (newChatChannelData.length > 0) {
            const newMembers = newChatChannelData?.map(async (newChannel) => {
              if (
                newChannel.accessPermission !== "admin" ||
                (newChannel.accessPermission === "admin" &&
                  eventAttendeeData.migrate_user &&
                  eventAttendeeData.migrate_user.plan_id === "Staff")
              ) {
                const checkChannelMemberExists = await chatChannelMembers.find({
                  userId: eventAttendeeData._id,
                  channelId: newChannel._id,
                  status: 2,
                });
                if (checkChannelMemberExists.length === 0) {
                  const channelMember = new chatChannelMembers({
                    userId: eventAttendeeData._id,
                    channelId: newChannel._id,
                    status: 2,
                    user_type: "airtable-syncs",
                  });
                  if (!channelMember) {
                    return res.status(200).json({ status: false, message: "Something went wrong while adding members in channel!!", });
                  }
                  await channelMember.save();
                  const getAllChannelMembers = await chatChannelMembers.find({ channelId: newChannel._id, status: 2, user_type: "airtable-syncs", });
                  var channelMembersChat = getAllChannelMembers
                    ? getAllChannelMembers.map((ids) => {
                      return { id: ids.userId._id, readmsg: false };
                    })
                    : [];
                  channelMembersChat = [
                    ...channelMembersChat.filter((ids) => {
                      if (ids.id.toString() !== eventAttendeeData._id.toString())
                        return ids;
                    }),
                    { id: eventAttendeeData._id, readmsg: false },
                  ];
                  let allMembersIdsSocket = channelMembersChat?.map((member) => {
                    return member.id;
                  });
                  let messageData = {
                    message: "",
                    recipient_type: "chatChannel",
                    sender_type: "adminuser",
                    recipient: newChannel._id,
                    sender: req.admin_Id,
                    type: "chatChannel",
                    group_member: channelMembersChat,
                    activity_status: true,
                    activity: {
                      type: "addChannelMembers",
                      userId: [eventAttendeeData._id],
                    },
                    userTimeStamp: moment
                      .utc()
                      .format("YYYY-MM-DDTHH:mm:ss.SSSSSS[Z]"),
                  }

                  const userData = await User.findById(eventAttendeeData._id);
                  if (
                    userData &&
                    userData.deleted_group_of_user &&
                    userData.deleted_group_of_user.includes(newChannel._id)
                  ) {
                    await User.findByIdAndUpdate(eventAttendeeData._id, { $pull: { deleted_group_of_user: newChannel._id }, }, { new: true });
                  }

                  chatData.push({
                    messageData: messageData,
                    deleteMultipleRecordFromChatList: [],
                    channelData: newChannel,
                    allMembersIdsSocket: allMembersIdsSocket,
                    removeFromDeleteGroupOfUser: true
                  })
                }
              } else {
                return {};
              }
            });
            await Promise.all([...newMembers]);
          }
          // create guest user
          let response = await this.createGuestUser({
            email: req.body.email.toLowerCase(),
            community: req.currentEdge.relation_id,
          });
          if (!response.status) {
            return res.status(200).json({ status: false, message: response.message,});
          }
          if (eventAttendeeData) {
            return res.status(200).json({ status: true, message: "Event attendees created successfully.", data: eventAttendeeData, chatData: chatData });
          } else {
            return res.status(200).json({ status: false, message: "Something went wrong while updating event attendees!", });
          }
        } else {
          const getEventAttendee = getEventAttendeeAuth ? getEventAttendeeAuth : getEventAttendeeEmail;
          if (typeof getEventAttendee.attendeeDetail === "object") { // user exist + with attendeeDetail
            const eventParticipantAttendeesDetail = await EventParticipantAttendees.findOne({ user: getEventAttendee._id, event: req.body.eventId, isDelete: false, role: roleDetail["_id"], }).lean();
            if (eventParticipantAttendeesDetail) {
              if (
                (eventParticipantAttendeesDetail["isManuallyAdded"] ===
                  undefined ||
                  eventParticipantAttendeesDetail["isManuallyAdded"] ===
                    null)
              ) {
                const updateEventParticipantDetails =
                  await EventParticipantAttendees.findOneAndUpdate(
                    {
                      user: getEventAttendee._id,
                      event: req.body.eventId,
                      role: roleDetail["_id"],
                      isDelete: false,
                    },
                    { $set: { isManuallyAdded: true } }
                  );
              }
              return res.status(200).json({ status: false, message: "Event attendees already exist!" });
            } else {
              // let eventParticipantAttendeesCreated = await EventParticipantAttendees.create({ user: getEventAttendee._id, event: req.body.eventId, role: roleDetail["_id"], isDelete: false, partner_order: ++partnerCount, contact_name: contactName, type_icon : typeIcon });
              let eventParticipantAttendeesCreated = await EventParticipantAttendees.create({ user: getEventAttendee._id, event: req.body.eventId, role: roleDetail["_id"], isDelete: false, partner_order: ++partnerCount, contact_name: contactName, isManuallyAdded: true , relation_id: ObjectId(req.currentEdge.relation_id._id) });
              // await updateAttendeeIcon(getEventAttendee._id, roleDetail, typeIcon);
              let tempFirstName = req.body.firstName ? req.body.firstName : ""
              let tempLastName = req.body.lastName ? req.body.lastName : ""
              const updatedEventAttendeeData = await User.findByIdAndUpdate(
                getEventAttendee._id,
                {
                  passcode: req.body.passcode,
                  isDelete: false,
                  profileImg: profileImg,
                  partnerIcon: partnerIcon,
                  first_name: req.body.firstName ? req.body.firstName : "",
                  last_name: req.body.lastName ? req.body.lastName : "",
                  display_name: req.body.display_name ? req.body.display_name : (`${tempFirstName} ${tempLastName}`),
                  attendeeDetail: {
                    title: req.body.title,
                    name: req.body.name,
                    firstName: req.body.firstName ? req.body.firstName : "",
                    lastName: req.body.lastName ? req.body.lastName : "",
                    email: req.body.email.toLowerCase(),
                    company: req.body.company,
                    phone: req.body.phone,
                    facebook: req.body.facebook,
                    linkedin: req.body.linkedin,
                    firebaseId: req.body.firebaseId,
                    description: descriptionData,
                    profession: req.body.profession,
                    offer: offerData,
                    contactPartnerName: req.body.contactPartnerName,
                    // evntData: memberEventDetails,
                  },
                },
                { new: true }
              );

              const newChatChannelData = await chatChannel.find({
                eventId: ObjectId(req.body.eventId),
                $or: [
                  { restrictedAccess: { $in: roleDetail["role"].toLowerCase() } },
                  { accessPermission: "public" },
                  { accessPermission: "admin" },
                ],
                isDelete: false,
              });

              if (newChatChannelData.length > 0) {
                const newMembers = newChatChannelData?.map(async (newChannel) => {
                  if (newChannel.accessPermission !== "admin" || (newChannel.accessPermission === "admin" && getEventAttendee.migrate_user && getEventAttendee.migrate_user.plan_id === "Staff")) {
                    const checkChannelMemberExists = await chatChannelMembers.find({
                      userId: getEventAttendee._id,
                      channelId: newChannel._id,
                      status: 2,
                    });
                    if (checkChannelMemberExists.length === 0) {
                      const channelMember = new chatChannelMembers({
                        userId: getEventAttendee._id,
                        channelId: newChannel._id,
                        status: 2,
                        user_type: "airtable-syncs",
                      });
                      if (!channelMember) {
                        return res.status(200).json({
                          status: false,
                          message:
                            "Something went wrong while adding members in channel!!",
                        });
                      }
                      await channelMember.save();
                      const getAllChannelMembers = await chatChannelMembers.find({
                        channelId: newChannel._id,
                        status: 2,
                        user_type: "airtable-syncs",
                      });
                      var channelMembersChat = getAllChannelMembers
                        ? getAllChannelMembers.map((ids) => {
                          return { id: ids.userId._id, readmsg: false };
                        })
                        : [];
                      channelMembersChat = [
                        ...channelMembersChat.filter((ids) => {
                          if (ids.id.toString() !== getEventAttendee._id.toString())
                            return ids;
                        }),
                        { id: getEventAttendee._id, readmsg: false },
                      ];
                      let allMembersIdsSocket = channelMembersChat?.map(
                        (member) => {
                          return member.id;
                        }
                      );
                      const userData = await User.findById(getEventAttendee._id);
                      if (
                        userData &&
                        userData.deleted_group_of_user &&
                        userData.deleted_group_of_user.includes(newChannel._id)
                      ) {
                        await User.findByIdAndUpdate(
                          getEventAttendee._id,
                          {
                            $pull: { deleted_group_of_user: newChannel._id },
                          },
                          { new: true }
                        );
                      }


                      chatData.push({
                        messageData: messageData,
                        deleteMultipleRecordFromChatList: [],
                        channelData: newChannel,
                        allMembersIdsSocket: allMembersIdsSocket,
                        removeFromDeleteGroupOfUser: true
                      })
                    }
                  } else {
                    return {};
                  }
                });
                await Promise.all([...newMembers]);
              }
              // create guest user
              let response = await this.createGuestUser({
                email: req.body.email.toLowerCase(),
                community: req.currentEdge.relation_id,
              });
              if (!response.status) {
                return res.status(200).json({ status: false, message: response.message, });
              }
              if (updatedEventAttendeeData)
                return res.status(200).json({
                  status: true,
                  message: "Event attendees created successfully.",
                  data: updatedEventAttendeeData,
                  chatData: chatData
                });
              else
                return res.status(200).json({
                  status: false,
                  message: "Something went wrong while updating event attendees!",
                });
            }
          } else { //user exist + without attendeeDetail
            let tempFirstName = req.body.firstName ? req.body.firstName : ""
            let tempLastName = req.body.lastName ? req.body.lastName : ""
            const updatedEventAttendee = await User.findByIdAndUpdate(
              getEventAttendee._id,
              {
                passcode: req.body.passcode,
                isDelete: false,
                profileImg: profileImg,
                partnerIcon: partnerIcon,
                first_name: req.body.firstName ? req.body.firstName : "",
                last_name: req.body.lastName ? req.body.lastName : "",
                display_name: req.body.display_name ? req.body.display_name : (`${tempFirstName} ${tempLastName}`),
                attendeeDetail: {
                  title: req.body.title,
                  name: req.body.name,
                  firstName: req.body.firstName ? req.body.firstName : "",
                  lastName: req.body.lastName ? req.body.lastName : "",
                  email: req.body.email.toLowerCase(),
                  company: req.body.company,
                  phone: req.body.phone,
                  facebook: req.body.facebook,
                  linkedin: req.body.linkedin,
                  firebaseId: req.body.firebaseId,
                  description: descriptionData,
                  profession: req.body.profession,
                  offer: offerData,
                  contactPartnerName: req.body.contactPartnerName,
                },
              },
              { new: true }
            );

            const eventParticipantAttendeesDetail = await EventParticipantAttendees.findOne({ user: getEventAttendee._id, event: req.body.eventId, role: roleDetail["_id"], isDelete: false, relation_id: ObjectId(req.currentEdge.relation_id._id) }).lean();
            if (eventParticipantAttendeesDetail) {
              if ((eventParticipantAttendeesDetail["isManuallyAdded"] === undefined) || (eventParticipantAttendeesDetail["isManuallyAdded"] === null)) {
                const updateEventParticipantDetails = await EventParticipantAttendees.findOneAndUpdate({ user: getEventAttendee._id, event: req.body.eventId, role: roleDetail["_id"], isDelete: false,relation_id: ObjectId(req.currentEdge.relation_id._id) }, { $set: { isManuallyAdded: true } })
              }

              return res.status(200).json({ status: false, message: "Event attendees already exist!" });
            } else {
              // let eventParticipantAttendeesCreated = await EventParticipantAttendees.create({ user: getEventAttendee._id, event: req.body.eventId, role: roleDetail["_id"], isDelete: false, partner_order: ++partnerCount, contact_name: contactName, type_icon : typeIcon });
              let eventParticipantAttendeesCreated;
              if (roleDetail.role.toString().toLowerCase() == "partner") {
                eventParticipantAttendeesCreated = await EventParticipantAttendees.create({ user: getEventAttendee._id, event: req.body.eventId, role: roleDetail["_id"], isDelete: false, partner_order: ++partnerCount, contact_name: contactName, private_profile: true, isManuallyAdded: true, relation_id: ObjectId(req.currentEdge.relation_id._id) });
              } else {
                eventParticipantAttendeesCreated = await EventParticipantAttendees.create({ user: getEventAttendee._id, event: req.body.eventId, role: roleDetail["_id"], isDelete: false, partner_order: ++partnerCount, contact_name: contactName, isManuallyAdded: true, relation_id: ObjectId(req.currentEdge.relation_id._id) });
              }
              // await updateAttendeeIcon(getEventAttendee._id, roleDetail, typeIcon);
            }
            // create guest user
            let response = await this.createGuestUser({
              email: req.body.email.toLowerCase(),
              community: req.currentEdge.relation_id,
            });
            if (!response.status) {
              return res.status(200).json({ status: false, message: response.message, });
            }

            if (updatedEventAttendee)
              return res.status(200).json({
                status: true,
                message: "Event attendees created successfully.",
                data: updatedEventAttendee,
                chatData: chatData
              });
            else
              return res.status(200).json({
                status: false,
                message: "Something went wrong while updating event attendees!",
              });
          }
        }
      } else {
        return res.status(200).json({ status: false, message: "Role detail not found!" });
      }
    }
  } catch (error) {
    return res
      .status(500).json({ status: false, message: "Internal server error!", error: error });
  }
};

// get event attendees by event Id version 2
exports.getAttendeesByEventIdV2 = async (req, res) => {
  try {
    let page = req.query.page ? +req.query.page : 1;
    let limit = req.query.limit ? +req.query.limit : 10;
    let skip = (page - 1) * limit;
    let sortField =
      req.query.sortField === "name"
        ? "name"
        : req.query.sortField === "email"
          ? "email"
          : req.query.sortField === "company"
            ? "company"
            : req.query.sortField === "type"
              ? "type"
              : req.query.sortField === "isManuallyAdded"
                ? "isManuallyAdded"
                : "name";
    let sortTypeOrder =
      req.query && req.query.sortType && req.query.sortType == "Dec" ? -1 : 1;
    let sortTemp = {};
    let eventId = new ObjectId(req.params.id);
    let attendeeList = [];

    const communityId = req.currentEdge.relation_id._id;

    if (req.query.sortField && req.query.sortType) {
      sortTemp[sortField] = sortTypeOrder;
    } else {
      sortTemp = { name: 1 };
    }

    let match = { event: eventId, isDelete: false };
    if (req.query && req.query.role) {
      match["role"] = new ObjectId(req.query.role);
    }

    if (
      req.query?.isManuallyAdded === "true" ||
      req.query?.isManuallyAdded === "false"
    ) {
      match["isManuallyAdded"] = req.query.isManuallyAdded === "true";
    }

    let pipeline = [
      {
        $match: match,
      },
      {
        $lookup: {
          from: "airtable-syncs",
          localField: "user",
          foreignField: "_id",
          as: "airtable-syncs_result",
        },
      },
      {
        $unwind: {
          path: "$airtable-syncs_result",
          preserveNullAndEmptyArrays: false,
        },
      },
      ...(req.query && req.query.search
        ? [
          {
            $match: {
              $or: [
                {
                  "airtable-syncs_result.attendeeDetail.name": {
                    $regex: ".*" + req.query.search + ".*",
                    $options: "i",
                  },
                },
                {
                  "airtable-syncs_result.attendeeDetail.firstName": {
                    $regex: ".*" + req.query.search + ".*",
                    $options: "i",
                  },
                },
                {
                  "airtable-syncs_result.attendeeDetail.lastName": {
                    $regex: ".*" + req.query.search + ".*",
                    $options: "i",
                  },
                },
                {
                  "airtable-syncs_result.first_name": {
                    $regex: ".*" + req.query.search + ".*",
                    $options: "i",
                  },
                },
                {
                  "airtable-syncs_result.last_name": {
                    $regex: ".*" + req.query.search + ".*",
                    $options: "i",
                  },
                },
                {
                  "airtable-syncs_result.display_name": {
                    $regex: ".*" + req.query.search + ".*",
                    $options: "i",
                  },
                },
                {
                  "airtable-syncs_result.Preferred Email": {
                    $regex: ".*" + req.query.search + ".*",
                    $options: "i",
                  },
                },
              ],
            },
          },
        ]
        : []),
      {
        $lookup: {
          from: "event_wise_participant_types",
          localField: "role",
          foreignField: "_id",
          as: "event_wise_participant_types_result",
        },
      },
      {
        $unwind: {
          path: "$event_wise_participant_types_result",
          preserveNullAndEmptyArrays: false,
        },
      },
      {
        $lookup: {
          from: "ticket_purchase_v2",
          let: {
            userId: "$user",
            eventId: "$event",
          },
          as: "purchased_ticket",
          pipeline: [
            {
              $match: {
                $expr: {
                  $and: [
                    {
                      $eq: ["$userId", "$$userId"],
                    },
                    {
                      $eq: ["$eventId", "$$eventId"],
                    },
                    {
                      $eq: ["$isDelete", false],
                    },
                    {
                      $eq: ["$ticketOrderStatus", "succeeded"],
                    },
                  ],
                },
              },
            },
            {
              $count: "purchaseTicket",
            },
          ],
        },
      },
      // {
      //   $lookup: {
      //     from: "ticket_payments",
      //     let: {
      //       user: "$user",
      //       event: "$event",
      //     },
      //     as: "purchased_ticket",
      //     pipeline: [
      //       {
      //         $match: {
      //           $expr: {
      //             $and: [
      //               { $eq: ["$userId", "$$user"] },
      //               { $eq: ["$eventId", "$$event"] },
      //               { $eq: ["$isDelete", false] },
      //             ],
      //           },
      //         },
      //       },
      //       {
      //         $group: {
      //           _id: null,
      //           purchaseTicket: {
      //             $sum: "$purchaseQuantity",
      //           },
      //         },
      //       },
      //     ],
      //   },
      // },
      {
        $unwind: {
          path: "$purchased_ticket",
          preserveNullAndEmptyArrays: true,
        },
      },
      {
        $addFields: {
          purchasedTicket: "$purchased_ticket.purchaseTicket",
        },
      },
      {
        $project: {
          attendee_id: "$_id",
          _id: "$airtable-syncs_result._id",
          firebaseId: {
            $ifNull: ["$airtable-syncs_result.firebaseId", ""],
          },
          email: {
            $ifNull: ["$airtable-syncs_result.Preferred Email", ""],
          },
          type: {
            $ifNull: ["$event_wise_participant_types_result.role", ""],
          },
          first_name: {
            $ifNull: ["$airtable-syncs_result.first_name", ""],
          },
          last_name: {
            $ifNull: ["$airtable-syncs_result.last_name", ""],
          },
          display_name: {
            $ifNull: ["$airtable-syncs_result.display_name", ""],
          },
          "Preferred Email": {
            $ifNull: ["$airtable-syncs_result.Preferred Email", ""],
          },
          firstName: {
            $ifNull: ["$airtable-syncs_result.attendeeDetail.firstName", ""],
          },
          lastName: {
            $ifNull: ["$airtable-syncs_result.attendeeDetail.lastName", ""],
          },
          name: {
            $ifNull: ["$airtable-syncs_result.attendeeDetail.name", ""],
          },
          company: {
            $ifNull: ["$airtable-syncs_result.attendeeDetail.company", ""],
          },
          profession: {
            $ifNull: ["$airtable-syncs_result.attendeeDetail.profession", ""],
          },
          phone: {
            $ifNull: ["$airtable-syncs_result.attendeeDetail.phone", ""],
          },
          facebook: {
            $ifNull: ["$airtable-syncs_result.attendeeDetail.facebook", ""],
          },
          linkedin: {
            $ifNull: ["$airtable-syncs_result.attendeeDetail.linkedin", ""],
          },
          description: "$description",
          offer: "$offer",
          event: "$event",
          passcode: {
            $ifNull: ["$airtable-syncs_result.passcode", ""],
          },
          profileImg: {
            $cond: {
              if: {
                $eq: ["$event_wise_participant_types_result.role", "Partner"],
              },
              then: {
                $ifNull: ["$airtable-syncs_result.partnerIcon", ""],
              },
              else: {
                $ifNull: ["$airtable-syncs_result.profileImg", ""],
              },
            },
          },
          partnerIcon: {
            $ifNull: ["$airtable-syncs_result.partnerIcon", ""],
          },
          roleId: {
            $ifNull: ["$event_wise_participant_types_result._id", ""],
          },
          role: {
            $ifNull: ["$event_wise_participant_types_result.role", ""],
          },
          type_icon: {
            $cond: {
              if: {
                $eq: ["$event_wise_participant_types_result.role", "Partner"],
              },
              then: "$airtable-syncs_result.partnerIcon",
              else: "$airtable-syncs_result.profileImg",
            },
          },
          contact_name: "$contact_name",
          partner_order: "$partner_order",
          private_profile: "$private_profile",
          purchasedTicket: {
            $ifNull: ["$purchasedTicket", 0],
          },
          isManuallyAdded: "$isManuallyAdded",
          "Denver Check in Form": "$airtable-syncs_result.Denver Check in Form",
        },
      },
      ...(req.query && req.query["denver_check_in_form"] ? [
        {
          $match: req.query["denver_check_in_form"] === "true"
            ?
            {
              "Denver Check in Form": {$eq : "true" }
            }
            : {
                "Denver Check in Form": {$ne : "true" }
              }
        }
      ] : []),
      {
        $sort: sortTemp,
      },
    ];
    let pipelineTemp = [...pipeline];
    if (req.query.page && req.query.limit) {
      pipelineTemp = [...pipeline, { $skip: skip }, { $limit: limit }];
    }

    let [list, count] = await Promise.all([
      EventParticipantAttendees.aggregate(pipelineTemp),
      EventParticipantAttendees.aggregate([...pipeline, { $count: "total" }]),
    ]);
    let total = count && count[0] && count[0]["total"] ? count[0]["total"] : 0;
    if (attendeeList) {
      return res.status(200).json({
        status: true,
        message: "Event list retrive!",
        data: list,
        total: total,
      });
    } else {
      return res.status(200).json({
        status: false,
        message: "Something went wrong while getting event list!",
        data: [],
        total: 0,
      });
    }
  } catch (error) {
    return res
      .status(500)
      .json({ status: false, message: "Internal server error!", error: error });
  }
};

// get event attendees suggestion by event Id 
exports.getAttendeesSuggestion = async (req, res) => {
  try {
    let match = { event: new ObjectId(req.params.id), isDelete: false };
    let pipeline = [
      {
        '$match': match,

      }, {
        '$lookup': {
          'from': 'airtable-syncs',
          'localField': 'user',
          'foreignField': '_id',
          'as': 'airtable-syncs_result'
        }
      }, {
        '$unwind': {
          'path': '$airtable-syncs_result',
          'preserveNullAndEmptyArrays': false
        }
      },
      {
        $addFields: {
          full_name: {
            $concat: [
              { $ifNull: ['$airtable-syncs_result.first_name', ''] },
              ' ',
              { $ifNull: ['$airtable-syncs_result.last_name', ''] }
            ]
          },
          full_name_lower: { // Add a field with lowercase version of full_name
            $toLower: {
              $concat: [
                { $ifNull: ['$airtable-syncs_result.first_name', ''] },
                ' ',
                { $ifNull: ['$airtable-syncs_result.last_name', ''] }
              ]
            }
          }
        }
      },
      {
        '$project': {
          'first_name': { '$ifNull': ['$airtable-syncs_result.first_name', ''] },
          'last_name': { '$ifNull': ['$airtable-syncs_result.last_name', ''] },
          'display_name': { '$ifNull': ['$airtable-syncs_result.display_name', ''] },
          'Preferred Email': { '$ifNull': ['$airtable-syncs_result.Preferred Email', ''] },
          full_name: 1,
          full_name_lower: 1
        }
      },
      {
        $sort: {
          "full_name_lower": 1,
        },
      },
    ];
    let list = await EventParticipantAttendees.aggregate(pipeline);
    if (list.length) {
      return res.status(200).json({ status: true, message: "Event attendee list suggestion!", data: list, });
    } else {
      return res.status(200).json({ status: false, message: "Something went wrong while getting event attendee suggestion list!", });
    }
  } catch (error) {
    return res.status(500).json({ status: false, message: "Internal server error!", error: error });
  }
};

// edit event attendees from admin version 2
exports.editEventAttendeesV2 = async (req, res) => {
  try {
    const communityId = req.currentEdge.relation_id._id

    const errors = validationResult(req);
    if (!errors.isEmpty()) {
      let result = validationResult(req).array({ onlyFirstError: false });
      result = result.map((err) => { return { path: err['path'], msg: err['msg'] }; });
      return res.status(403).json({ status: false, message: "Validation error!", errors: result });
    } else {
      const attendeeId = ObjectId(req.params.id);
      const eventId = ObjectId(req.body.eventId);
      const newTypeId = ObjectId(req.body.newType);
      const oldTypeId = ObjectId(req.body.oldType);

      // var typeIcon = "";
      // if (req && req.type_icon) {
      // typeIcon = req.type_icon;
      // }

      let socketEventList = [];
      const getEventAttendee = await User.findOne({ _id: attendeeId, }).lean();
      if (getEventAttendee !== null) {
        let allRoleDetail = await eventWiseParticipantTypes.find({ event: eventId, isDelete: false });
        let oldRoleDetail = allRoleDetail.find(x => x._id.toString() == oldTypeId.toString());
        if (oldRoleDetail) {
          let oldRoleName = oldRoleDetail.role.toLowerCase();
          let newRoleDetail = allRoleDetail.find(x => x._id.toString() == newTypeId.toString());
          if (newRoleDetail) {
            let newRoleName = newRoleDetail.role.toLowerCase();

            var profileImg = "";
            var partnerIcon = "";
            if (req && req.type_icon) {
              newRoleName === "partner" ? partnerIcon = req.type_icon : profileImg = req.type_icon;
            }




            if (newTypeId.toString() === oldTypeId.toString()) {

              if (profileImg !== "" && getEventAttendee.profileImg && (getEventAttendee.profileImg.length > 0)) {
                deleteImage(getEventAttendee.profileImg);
              }
              if (partnerIcon !== "" && newRoleName === "partner" && getEventAttendee.partnerIcon && (getEventAttendee.partnerIcon.length > 0)) {
                deleteImage(getEventAttendee.partnerIcon);
              }

              if (profileImg == "") {
                profileImg = getEventAttendee.profileImg;
              }
              if (partnerIcon == "" && newRoleName === "partner") {
                partnerIcon = getEventAttendee.partnerIcon;
              }


              let tempFirstName = req.body.firstName ? req.body.firstName : ""
              let tempLastName = req.body.lastName ? req.body.lastName : ""
              const updateAttendeeData = await User.findOneAndUpdate(
                getEventAttendee._id,
                {
                  passcode: req.body.passcode ?? getEventAttendee.passcode,
                  first_name: req.body.firstName,
                  last_name: req.body.lastName,
                  display_name: req.body.display_name ? req.body.display_name : (`${tempFirstName} ${tempLastName}`),
                  partnerIcon: partnerIcon,
                  profileImg: profileImg,
                  attendeeDetail: {
                    email: getEventAttendee['Preferred Email'].toLowerCase(),
                    firebaseId: req.body.firebaseId,
                    title: req.body.title,
                    name: req.body.name,
                    firstName: req.body.firstName,
                    lastName: req.body.lastName,
                    company: req.body.company,
                    profession: req.body.profession,
                    phone: req.body.phone,
                    facebook: req.body.facebook,
                    linkedin: req.body.linkedin,
                    description: req.body.description,
                    offer: req.body.offer,
                    contactPartnerName: req.body.contactPartnerName,
                  },
                },
                { new: true }
              );
              let isExistType = await EventParticipantAttendees.findOne({ event: eventId, role: ObjectId(newRoleDetail["_id"]), user: attendeeId, isDelete: false, relation_id: ObjectId(communityId) });
              if (isExistType) {
                let createObjTemp = {};
                createObjTemp["description"] = `<div "font-family: 'Muller';">${req.body.description ?? ""}</div>`;;
                createObjTemp["offer"] = `<div "font-family: 'Muller';">${req.body.offer ?? ""}</div>`;
                createObjTemp["contact_name"] = req.body.contactPartnerName;
                if (newRoleName == "partner") {
                  createObjTemp["private_profile"] = true;
                }

                if ((isExistType["isManuallyAdded"] === undefined) || (isExistType["isManuallyAdded"] === null)) {
                  createObjTemp["isManuallyAdded"] = true
                }

                // createObjTemp["type_icon"] = typeIcon;
                const updated_data = await EventParticipantAttendees.findOneAndUpdate(
                  { event: eventId, role: ObjectId(newRoleDetail["_id"]), user: attendeeId, isDelete: false, relation_id: ObjectId(communityId) },
                  { $set: createObjTemp },
                  { returnOriginal: false },
                );
                // await updateAttendeeIcon(attendeeId, newRoleDetail, typeIcon);
              }
              if (updateAttendeeData) {
                return res.status(200).json({ status: true, message: "Event attendee updated successfully.", data: updateAttendeeData, socketEventList: socketEventList });
              } else {
                return res.status(200).json({ status: false, message: "Error while updateing event attendee!", });
              }
            } else {
              let alreadyAssignSession = []
              if (oldRoleName === "speaker") {
                alreadyAssignSession = await eventSession.find({ speakerId: { $in: [attendeeId] }, event: eventId, isDelete: false, }).lean();
                if (alreadyAssignSession && alreadyAssignSession.length > 0) {
                  let sessionList = [];
                  for (let i = 0; i < alreadyAssignSession.length; i++) {
                    sessionList.push(alreadyAssignSession[i]["title"]);
                  }
                  return res.status(200).json({ status: false, message: "You can not updates this attendees type because this attendee is assigned as a speaker to particular: ", data: { sessionList } });
                }
              }
              if (alreadyAssignSession.length == 0) {
                let eventParticipantTypeObj = { event: eventId, role: ObjectId(oldRoleDetail["_id"]), user: attendeeId, isDelete: false };
                let isExistOldType = await EventParticipantAttendees.findOne({ event: eventId, role: ObjectId(oldRoleDetail["_id"]), user: attendeeId, isDelete: false, relation_id: ObjectId(communityId) });
                if (isExistOldType) {
                  const deleteAttendeeData = await EventParticipantAttendees.updateOne({ _id: ObjectId(isExistOldType["_id"]) }, { $set: { isDelete: true } });
                  // re-aggange patner data  - 
                } else {
                  return res.status(200).json({ status: false, message: "Old role detail not found!" });
                }

                let isExistNewType = await EventParticipantAttendees.findOne({ event: eventId, role: ObjectId(newRoleDetail["_id"]), user: attendeeId, isDelete: false, relation_id: ObjectId(communityId) });
                if (!isExistNewType) {
                  let createObjTemp = { event: eventId, role: ObjectId(newRoleDetail["_id"]), user: attendeeId, isDelete: false, relation_id: ObjectId(communityId) };
                  if (newRoleName == "partner") {
                    let partnerCount = await EventParticipantAttendees.countDocuments({ event: eventId, role: ObjectId(newRoleDetail["_id"]), isDelete: false }).lean();
                    createObjTemp["partner_order"] = ++partnerCount;
                    createObjTemp["description"] = `<div "font-family: 'Muller';">${req.body.description ?? ""}</div>`;;
                    createObjTemp["offer"] = `<div "font-family: 'Muller';">${req.body.offer ?? ""}</div>`;
                    createObjTemp["contact_name"] = req.body.contactPartnerName;
                    createObjTemp["private_profile"] = true;
                    // createObjTemp["type_icon"] = typeIcon;
                  }
                  createObjTemp["isManuallyAdded"] = true;
                  let isExistNewTypeTemp = await EventParticipantAttendees.create(createObjTemp);
                  // await updateAttendeeIcon(attendeeId, newRoleDetail, typeIcon);
                }

                if (profileImg !== "" && getEventAttendee.profileImg && (getEventAttendee.profileImg.length > 0)) {
                  deleteImage(getEventAttendee.profileImg);
                }
                if (partnerIcon !== "" && newRoleName === "partner" && getEventAttendee.partnerIcon && (getEventAttendee.partnerIcon.length > 0)) {
                  deleteImage(getEventAttendee.partnerIcon);
                }

                if (profileImg == "") {
                  profileImg = getEventAttendee.profileImg;
                }
                if (partnerIcon == "") {
                  partnerIcon = getEventAttendee.partnerIcon;
                }

                let tempFirstName = req.body.firstName ? req.body.firstName : ""
                let tempLastName = req.body.lastName ? req.body.lastName : ""
                const updateAttendeeData = await User.findOneAndUpdate(
                  getEventAttendee._id,
                  {
                    passcode: req.body.passcode,
                    first_name: req.body.firstName,
                    last_name: req.body.lastName,
                    display_name: req.body.display_name ? req.body.display_name : (`${tempFirstName} ${tempLastName}`),
                    partnerIcon: partnerIcon,
                    profileImg: profileImg,
                    attendeeDetail: {
                      email: getEventAttendee['Preferred Email'].toLowerCase(),
                      firebaseId: req.body.firebaseId,
                      title: req.body.title,
                      name: req.body.name,
                      firstName: req.body.firstName,
                      lastName: req.body.lastName,
                      company: req.body.company,
                      profession: req.body.profession,
                      phone: req.body.phone,
                      facebook: req.body.facebook,
                      linkedin: req.body.linkedin,
                      description: req.body.description,
                      offer: req.body.offer,
                      contactPartnerName: req.body.contactPartnerName,
                    },
                  },
                  { new: true }
                );


                // update chanle here start 
                if (req.body.oldType !== req.body.newType) {
                  const [oldChatChannelData, newChatChannelData] = await Promise.all([
                    chatChannel.find({
                      eventId: new ObjectId(eventId),
                      restrictedAccess: { $in: [new ObjectId(req.body.oldType)] },
                      isDelete: false,
                    }),
                    chatChannel.find({
                      eventId: new ObjectId(eventId),
                      restrictedAccess: { $in: [new ObjectId(req.body.newType)] },
                      isDelete: false,
                    }),
                  ]);

                  if (oldChatChannelData.length > 0) {
                    const oldMembers = oldChatChannelData?.map(async (oldChannel) => {
                      const checkChannelMemberExists = await chatChannelMembers.find({ userId: new ObjectId(attendeeId), user_type: 'airtable-syncs', channelId: oldChannel._id, status: 2, });
                      if (checkChannelMemberExists.length > 0) {
                        const removeMembersAttendee =
                          await chatChannelMembers.deleteOne({ userId: attendeeId, channelId: oldChannel._id, user_type: "airtable-syncs", status: 2, }, { new: true });
                        if (!removeMembersAttendee) {
                          return res.status(200).json({ status: false, message: "Something went wrong while adding members in channel!!", });
                        }
                        const getAllChannelMembers = await chatChannelMembers.find({
                          channelId: oldChannel._id, status: 2, user_type: "airtable-syncs",
                        });
                        var channelMembersChat = getAllChannelMembers ? getAllChannelMembers.map((ids) => { return { id: ids.userId._id, readmsg: false }; }) : [];
                        let messageData = {
                          message: "",
                          recipient_type: "chatChannel",
                          sender_type: "adminuser",
                          recipient: oldChannel._id,
                          sender: req.admin_Id,
                          type: "chatChannel",
                          group_member: channelMembersChat,
                          activity_status: true,
                          activity: {
                            type: "removedChannelMembers",
                            userId: [attendeeId],
                          },
                          userTimeStamp: moment
                            .utc()
                            .format("YYYY-MM-DDTHH:mm:ss.SSSSSS[Z]"),
                        }
                        let allMembersIdsSocket = channelMembersChat?.map(
                          (member) => {
                            return member.id;
                          }
                        );
                        socketEventList.push({
                          messageData: messageData,
                          eventType: "remove-channel-member-receive",
                          deleteMultipleRecordFromChatList: [attendeeId],
                          addMemberIds: [],
                          channelData: oldChannel,
                          allMembersIdsSocket: allMembersIdsSocket,
                          allMembersIds: [...allMembersIdsSocket,attendeeId],
                        })

                      }
                    });
                    await Promise.all([...oldMembers]);
                  }

                  if (newChatChannelData.length > 0) {
                    const newMembers = newChatChannelData?.map(async (newChannel) => {
                      const userDataInnerSide = await User.findById(attendeeId).lean();
                      if (
                        userDataInnerSide
                      ) {
                        const checkChannelMemberExists =
                          await chatChannelMembers.find({
                            userId: new ObjectId(attendeeId),
                            channelId: new ObjectId(newChannel._id),
                            status: 2,
                          });
                        if (checkChannelMemberExists.length === 0) {
                          const channelMember = new chatChannelMembers({
                            userId: attendeeId,
                            channelId: newChannel._id,
                            status: 2,
                            user_type: "airtable-syncs",
                          });
                          if (!channelMember) {
                            return res.status(200).json({ status: false, message: "Something went wrong while adding members in channel!!", });
                          }
                          await channelMember.save();
                          const getAllChannelMembers = await chatChannelMembers.find({
                            channelId: newChannel._id,
                            status: 2,
                            user_type: "airtable-syncs",
                          });
                          var channelMembersChat = getAllChannelMembers
                            ? getAllChannelMembers.map((ids) => {
                              return { id: ids.userId._id, readmsg: false };
                            })
                            : [];
                          channelMembersChat = [
                            ...channelMembersChat.filter((ids) => {
                              if (ids.id.toString() !== attendeeId.toString())
                                return ids;
                            }),
                            { id: attendeeId, readmsg: false },
                          ];
                          let allMembersIdsSocket = channelMembersChat?.map(
                            (member) => {
                              return member.id;
                            }
                          );
                          let messageData = {
                            message: "",
                            recipient_type: "chatChannel",
                            sender_type: "adminuser",
                            recipient: newChannel._id,
                            sender: req.admin_Id,
                            type: "chatChannel",
                            group_member: channelMembersChat,
                            activity_status: true,
                            activity: {
                              type: "addChannelMembers",
                              userId: [attendeeId],
                            },
                            userTimeStamp: moment
                              .utc()
                              .format("YYYY-MM-DDTHH:mm:ss.SSSSSS[Z]"),
                          }
                          socketEventList.push({
                            eventType: "add-channel-member-receive",
                            deleteMultipleRecordFromChatList: [],
                            addMemberIds: [attendeeId],
                            allMembersIdsSocket: [...allMembersIdsSocket,attendeeId],
                            messageData: messageData,
                            channelData: newChannel,
                            removeFromDeleteGroupOfUser: true
                          })

                        }
                      } else {
                        return {};
                      }
                    });
                    await Promise.all([...newMembers]);
                  }
                }
                // update changle end

                if (updateAttendeeData) {
                  const payload = {
                    event: "UPDATE_CHANNEL",
                    data: {
                      listSocketEvents: socketEventList,
                      relation_id: communityId
                    }
                  }


                  publishMessage(JSON.stringify(payload), "CHAT_CHANNEL")
                  return res.status(200).json({ status: true, message: "Event attendee updated successfully.", data: updateAttendeeData, socketEventList: socketEventList });
                } else {
                  return res.status(200).json({ status: false, message: "Error while updateing event attendee!", });
                }
              }
            }
          } else {
            return res.status(200).json({ status: false, message: "New role detail not found!", });
          }
        } else {
          return res.status(200).json({ status: false, message: "Old role detail not found!", });
        }
      } else {
        return res.status(200).json({ status: false, message: "Event attendee not found!" });
      }
    }
  } catch (error) {
    console.error(error, "Error  while updating")
    return res.status(500).json({ status: false, message: "Internal server error!", error: error });
  }
};

// function to update attendee detail version 2
async function updateAttendeeV2(attendeeId, emailExist, data, adminId, io, allRoleDetail) {
  if (
    emailExist &&
    emailExist.attendeeDetail &&
    emailExist.attendeeDetail.evntData &&
    emailExist.attendeeDetail.evntData.filter((eventData) => {
      if (
        eventData.event &&
        data.eventId &&
        eventData.event.toString() === data.eventId.toString()
      )
        return eventData;
    }).length
  ) {
    let attendeeEventData = {
      passcode:
        data.passcode && data.passcode.length
          ? data.passcode
          : emailExist.attendeeDetail?.passcode,
      isDelete: false,
      attendeeDetail: {
        title:
          data.title && data.title.length
            ? data.title
            : emailExist.attendeeDetail?.title,
        email:
          data.email.toLowerCase().trim() &&
            data.email.toLowerCase().trim().length
            ? data.email.toLowerCase().trim()
            : emailExist.attendeeDetail?.email,
        name:
          data.name && data.name.length
            ? data.name
            : emailExist.attendeeDetail?.name,
        firstName:
          data.firstName && data.firstName.length
            ? data.firstName
            : emailExist.attendeeDetail?.firstName,
        lastName:
          data.lastName && data.lastName.length
            ? data.lastName
            : emailExist.attendeeDetail?.lastName,
        company:
          data.company && data.company.length
            ? data.company
            : emailExist.attendeeDetail?.company,
        profession:
          data.profession && data.profession.length
            ? data.profession
            : emailExist.attendeeDetail?.profession,
        phone:
          data.phone && data.phone.length
            ? data.phone
            : emailExist.attendeeDetail?.phone,
        facebook:
          data.facebook && data.facebook.length
            ? data.facebook
            : emailExist.attendeeDetail?.facebook,
        linkedin:
          data.linkedin && data.linkedin.length
            ? data.linkedin
            : emailExist.attendeeDetail?.linkedin,
        firebaseId:
          data.firebaseId && data.firebaseId.length
            ? data.firebaseId
            : emailExist.attendeeDetail?.firebaseId,

      },
    };

    for (let i = 0; i < data.role.length; i++) {
      let roleName = data.role[i].toLowerCase();
      let roleDetail = allRoleDetail.find(x => x.role.toLowerCase() == roleName);
      if (roleDetail) {
        let eventParticipantAttendeesObj = { user: attendeeId, event: data.eventId, role: roleDetail["_id"], isDelete: false, };
        const eventParticipantAttendeesDetail = await EventParticipantAttendees.findOne(eventParticipantAttendeesObj).lean();
        if (!eventParticipantAttendeesDetail) {
          let partnerCount = 0;
          if (roleDetail.role.toString().toLowerCase() == "partner") {
            let eventParticipantAttendeesObjTemp = { ...eventParticipantAttendeesObj };
            delete eventParticipantAttendeesObjTemp.user;
            partnerCount = await EventParticipantAttendees.countDocuments(eventParticipantAttendeesObjTemp).lean();
            eventParticipantAttendeesObj["partner_order"] = ++partnerCount;
          }
          const eventParticipantAttendeesCreated = await EventParticipantAttendees.create(eventParticipantAttendeesObj);
        }
      }
    }


    const updateAttendeeDetail = await User.findByIdAndUpdate(
      attendeeId,
      attendeeEventData,
      { new: true }
    );
    let socketEventList = await addAttendeeInChannelAtImport(
      data.eventId,
      data.role,
      updateAttendeeDetail,
      adminId,
      io
    );
    return socketEventList;
  } else if (
    emailExist &&
    emailExist.attendeeDetail &&
    emailExist.attendeeDetail.evntData &&
    emailExist.attendeeDetail.evntData.filter((eventData) => {
      if (
        eventData.event &&
        data.eventId &&
        eventData.event.toString() === data.eventId.toString()
      )
        return eventData;
    }).length === 0
  ) {
    let roleWise = {};
    for (let roleIndex = 0; roleIndex < data.role.length; roleIndex++) {
      roleWise = { ...roleWise, [data.role[roleIndex]]: true };
    }
    let attendeeEventData = {
      passcode:
        data.passcode && data.passcode.length
          ? data.passcode
          : emailExist.attendeeDetail?.passcode,
      isDelete: false,
      attendeeDetail: {
        title:
          data.title && data.title.length
            ? data.title
            : emailExist.attendeeDetail?.title,
        email:
          data.email.toLowerCase().trim() &&
            data.email.toLowerCase().trim().length
            ? data.email.toLowerCase().trim()
            : emailExist.attendeeDetail?.email,
        name:
          data.name && data.name.length
            ? data.name
            : emailExist.attendeeDetail?.name,
        firstName:
          data.firstName && data.firstName.length
            ? data.firstName
            : emailExist.attendeeDetail?.firstName,
        lastName:
          data.lastName && data.lastName.length
            ? data.lastName
            : emailExist.attendeeDetail?.lastName,
        company:
          data.company && data.company.length
            ? data.company
            : emailExist.attendeeDetail?.company,
        profession:
          data.profession && data.profession.length
            ? data.profession
            : emailExist.attendeeDetail?.profession,
        phone:
          data.phone && data.phone.length
            ? data.phone
            : emailExist.attendeeDetail?.phone,
        facebook:
          data.facebook && data.facebook.length
            ? data.facebook
            : emailExist.attendeeDetail?.facebook,
        linkedin:
          data.linkedin && data.linkedin.length
            ? data.linkedin
            : emailExist.attendeeDetail?.linkedin,
        firebaseId:
          data.firebaseId && data.firebaseId.length
            ? data.firebaseId
            : emailExist.attendeeDetail?.firebaseId,

      },
    };
    for (let i = 0; i < data.role.length; i++) {
      let roleName = data.role[i].toLowerCase();
      let roleDetail = allRoleDetail.find(x => x.role.toLowerCase() == roleName);
      if (roleDetail) {
        let eventParticipantAttendeesObj = { user: attendeeId, event: data.eventId, role: roleDetail["_id"], isDelete: false, };
        const eventParticipantAttendeesDetail = await EventParticipantAttendees.findOne(eventParticipantAttendeesObj).lean();
        if (!eventParticipantAttendeesDetail) {
          let partnerCount = 0;
          if (roleDetail.role.toString().toLowerCase() == "partner") {
            let eventParticipantAttendeesObjTemp = { ...eventParticipantAttendeesObj };
            delete eventParticipantAttendeesObjTemp.user;
            partnerCount = await EventParticipantAttendees.countDocuments(eventParticipantAttendeesObjTemp).lean();
            eventParticipantAttendeesObj["partner_order"] = ++partnerCount;
          }
          const eventParticipantAttendeesCreated = await EventParticipantAttendees.create(eventParticipantAttendeesObj);
        }
      }
    }
    const updateAttendeeDetail = await User.findByIdAndUpdate(
      attendeeId,
      attendeeEventData,
      { new: true }
    );
    let socketEventList = await addAttendeeInChannelAtImport(
      data.eventId,
      data.role,
      updateAttendeeDetail,
      adminId,
      io
    );
    return socketEventList;
  } else if (emailExist && !emailExist.attendeeDetail) {
    let roleWise = {};
    for (let roleIndex = 0; roleIndex < data.role.length; roleIndex++) {
      roleWise = { ...roleWise, [data.role[roleIndex]]: true };
    }
    let attendeeEventData = {
      passcode: data.passcode,
      isDelete: false,
      attendeeDetail: {
        title: data.title,
        email: data.email.toLowerCase(),
        name: data.name,
        firstName: data.firstName,
        lastName: data.lastName,
        company: data.company,
        profession: data.profession,
        phone: data.phone,
        facebook: data.facebook,
        linkedin: data.linkedin,
        firebaseId: data.firebaseId,
      },
    };
    for (let i = 0; i < data.role.length; i++) {
      let roleName = data.role[i].toLowerCase();
      let roleDetail = allRoleDetail.find(x => x.role.toLowerCase() == roleName);
      if (roleDetail) {
        let eventParticipantAttendeesObj = { user: attendeeId, event: data.eventId, role: roleDetail["_id"], isDelete: false, };
        const eventParticipantAttendeesDetail = await EventParticipantAttendees.findOne(eventParticipantAttendeesObj).lean();
        if (!eventParticipantAttendeesDetail) {
          let partnerCount = 0;
          if (roleDetail.role.toString().toLowerCase() == "partner") {
            let eventParticipantAttendeesObjTemp = { ...eventParticipantAttendeesObj };
            delete eventParticipantAttendeesObjTemp.user;
            partnerCount = await EventParticipantAttendees.countDocuments(eventParticipantAttendeesObjTemp).lean();
            eventParticipantAttendeesObj["partner_order"] = ++partnerCount;
          }
          const eventParticipantAttendeesCreated = await EventParticipantAttendees.create(eventParticipantAttendeesObj);
        }
      }
    }
    const updateAttendeeDetail = await User.findByIdAndUpdate(
      attendeeId,
      attendeeEventData,
      { new: true }
    );
    let socketEventList = await addAttendeeInChannelAtImport(
      data.eventId,
      data.role,
      updateAttendeeDetail,
      adminId,
      io
    );
    return socketEventList;
  }
}

// delete event attendees from admin version 2
exports.deleteEventAttendeesV2 = async (req, res) => {
  try {
    const communityId = req.currentEdge.relation_id._id
    let token = req.headers["authorization"];

    const deleteDataArray = req.body.deleteData;
    const eventId = ObjectId(req.query.eventId);
    let updateAttendeeDataArray = [];
    let socketEventList = [];
    let isValidOpration = true;
    let messageOfOprationFail = "";
    let isPartnerRearrange = true;
    let isGuest=true

    let allAttendeeIds = [];
    for (let i = 0; i < deleteDataArray.length; i++) {
      let pipeline = [
        {
          '$match': {
            'user': ObjectId(deleteDataArray[i]["attendeeId"]),
            'event': eventId,
            'role': ObjectId(deleteDataArray[i]["role"]),
            'isDelete': false
          }
        }, {
          '$lookup': {
            'from': 'event_wise_participant_types',
            'localField': 'role',
            'foreignField': '_id',
            'as': 'event_wise_participant_types_result',
            'pipeline': [
              {
                '$match': {
                  '$expr': {
                    '$eq': [
                      '$role', 'Speaker'
                    ]
                  }
                }
              }
            ]
          }
        }, {
          '$unwind': {
            'path': '$event_wise_participant_types_result',
            'preserveNullAndEmptyArrays': false
          }
        }
      ]

      let speakerAttendees = await EventParticipantAttendees.aggregate(pipeline);
      if (speakerAttendees && speakerAttendees.length != 0) {
        allAttendeeIds.push(ObjectId(deleteDataArray[i]["attendeeId"]));
      }
    }

    const alreadyAssignSession = await eventSession
      .find({
        speakerId: { $in: allAttendeeIds },
        event: eventId,
        isDelete: false,
      })
      .lean();
    if (alreadyAssignSession && alreadyAssignSession.length > 0) {
      var sessionList = [];
      if (alreadyAssignSession.length > 0) {
        alreadyAssignSession.map((itemSession, i) => {
          sessionList.push(itemSession.title);
        });
      }
      return res.status(200).json({
        status: false,
        message:
          "You cannot delete this attendee because it is assigned to following sessions: ",
        data: { sessionList },
      });
    } else {
      for (let i = 0; i < deleteDataArray.length; i++) {
        let role = new ObjectId(deleteDataArray[i]["role"]);
        let attendeeId = new ObjectId(deleteDataArray[i]["attendeeId"]);

        let pipeline = [
          {
            $match: {
              event: eventId,
              role: role,
              user: attendeeId,
              isDelete: false,
            },
          },
          {
            $lookup: {
              from: "event_wise_participant_types",
              localField: "role",
              foreignField: "_id",
              as: "event_wise_participant_types_result",
            },
          },
          {
            $unwind: {
              path: "$event_wise_participant_types_result",
              preserveNullAndEmptyArrays: false,
            },
          },
        ];

        let attendeesData = await EventParticipantAttendees.aggregate(pipeline);
        if (attendeesData && attendeesData.length &&
          attendeesData[0]["event_wise_participant_types_result"][
            "role"
          ].toLowerCase() == "partner"
        ) {
          isPartnerRearrange = true;
        }
        if (attendeesData && attendeesData.length != 0) {
          const oldChatChannelData = await chatChannel.find({
            eventId: eventId,
            isDelete: false,
          });

          for (let j = 0; j < oldChatChannelData.length; j++) {
            let oldChannel = oldChatChannelData[j];
            const checkChannelMemberExists = await chatChannelMembers.find({
              userId: attendeeId,
              channelId: new ObjectId(oldChannel["_id"]),
              status: 2,
              user_type: "airtable-syncs",
            });
            if (checkChannelMemberExists.length > 0) {
              const removeMembersAttendee = await chatChannelMembers.deleteOne(
                {
                  userId: attendeeId,
                  channelId: new ObjectId(oldChannel["_id"]),
                  user_type: "airtable-syncs",
                  status: 2,
                },
                { new: true }
              );

              const getAllChannelMembers = await chatChannelMembers.find({
                channelId: new ObjectId(oldChannel["_id"]),
                status: 2,
                user_type: "airtable-syncs",
              });
              var channelMembersChat = getAllChannelMembers
                ? getAllChannelMembers.map((ids) => {
                  return { id: ids.userId._id, readmsg: false };
                })
                : [];
              let memberData = {
                message: "",
                recipient_type: "chatChannel",
                sender_type: "adminuser",
                recipient: new ObjectId(oldChannel["_id"]),
                sender: req.admin_Id,
                type: "chatChannel",
                group_member: channelMembersChat,
                activity_status: true,
                activity: {
                  type: "removedChannelMembers",
                  userId: [attendeeId],
                },
                userTimeStamp: moment
                  .utc()
                  .format("YYYY-MM-DDTHH:mm:ss.SSSSSS[Z]"),
              };
              let allMembersIdsSocket = channelMembersChat?.map((member) => {
                return member.id;
              });
              socketEventList.push({
                messageData: memberData,
                deleteMultipleRecordFromChatList: [attendeeId],
                addMemberIds: [],
                eventType: "remove-channel-member-receive",
                channelData: oldChannel,
                allMembersIds: [attendeeId],
                removeFromDeleteGroupOfUser: false,
              });

              const userData = await User.findById(attendeeId);
              if (
                !(
                  userData &&
                  userData.deleted_group_of_user &&
                  userData.deleted_group_of_user.includes(oldChannel._id)
                )
              ) {
                await User.findByIdAndUpdate(attendeeId, {
                  $push: { deleted_group_of_user: oldChannel._id },
                });
              }
            }
          }
        } else {
          isValidOpration = false;
          messageOfOprationFail = "Attendees detail not found";
        }
      }
      if (isValidOpration === true) {
        for (let i = 0; i < deleteDataArray.length; i++) {
          let role = ObjectId(deleteDataArray[i]["role"]);
          let attendeeId = ObjectId(deleteDataArray[i]["attendeeId"]);
          let checkEventParticipantAttendees = await EventParticipantAttendees.find({
            user: attendeeId,
            isDelete: false,
          });
          // delete guest user
          if (checkEventParticipantAttendees.length == 1) {
            let response = await deleteGuestUser({
              userId: attendeeId,
              community: req.currentEdge.relation_id,
              token,
            });
            if (!response.status) {
              isGuest = false
            }
          }

          let deleted = await EventParticipantAttendees.findOneAndUpdate(
            {
              role: role,
              user: attendeeId,
              event: eventId,
              isDelete: false,
              relation_id: ObjectId(communityId)
            },
            {
              isDelete: true
            },
            { new: true }
          );
          updateAttendeeDataArray.push(deleted);
        }

        if (isPartnerRearrange == true) {
        await rearrangePatneer.rearrangeAttendeeV2(eventId);
        }

        const payload = {
          event: "UPDATE_CHANNEL",
          data: {
            listSocketEvents: socketEventList,
            relation_id: communityId
          }
        }

        publishMessage(JSON.stringify(payload), "CHAT_CHANNEL")

        return res.status(200).json({
          status: true,
          message: "Event attendee deleted successfully.",
          data: updateAttendeeDataArray,
        });
      } else {
        return res
          .status(200)
          .json({ status: false, message: messageOfOprationFail, data: {} });
      }
    }
  } catch (error) {
    return res
      .status(500)
      .json({ status: false, message: "Internal server error!", error: error });
  }
};

// function to update attendee icon
async function updateAttendeeIcon(attendeeId, role, typeIcon) {
  if (attendeeId && role && typeIcon) {
    let pipeline = [
      {
        '$match': {
          'user': ObjectId(attendeeId)
        }
      }, {
        '$lookup': {
          'from': 'event_wise_participant_types',
          'localField': 'role',
          'foreignField': '_id',
          'as': 'event_wise_participant_types'
        }
      }, {
        '$unwind': {
          'path': '$event_wise_participant_types',
          'preserveNullAndEmptyArrays': false
        }
      }, {
        '$match': {
          'event_wise_participant_types.role': role.role
        }
      }
    ];
    const list = await EventParticipantAttendees.aggregate(pipeline);
    let ids = list.map((data) => ObjectId(data["_id"]));
    if (ids && ids.length != 0) {
      const eventParticipantAttendeesCreated = await EventParticipantAttendees.updateMany(
        { _id: { $in: ids } },
        {
          $set: { "type_icon": typeIcon }
        });
    }
  };
  return;
}

exports.getAttendeePurchaseTicket = async (req, res) => {
  try {
    const id = ObjectId(req.params.id);
    const eventId = ObjectId(req.query.eventId);
    let attendeeData = [];

    const pipeline = [{
        $match: {
          event: eventId,
          user: id,
          isDelete: false,
        },
      },
      {
        $lookup: {
          from: "ticket_purchase_v2",
          localField: "user",
          foreignField: "userId",
          pipeline: [{
              $match: {
                eventId: ObjectId(eventId),
                isDelete: false,
                userId: ObjectId(id),
                ticketOrderStatus: "succeeded"
              },
            },
            {
              $lookup: {
                from: "ticket_submissions",
                localField: "_id",
                foreignField: "ticketPurchaseId",
                pipeline: [{
                    $lookup: {
                      from: "application_forms",
                      localField: "applicationFormId",
                      foreignField: "_id",
                      pipeline: [{
                        $project: {
                          _id: 1,
                          name: 1,
                        }
                      }],
                      as: "applicationFormId",
                    },
                  },
                  {
                    $unwind: {
                      path: "$applicationFormId",
                      preserveNullAndEmptyArrays: false
                    },
                  },
                ],
                as: "ticket_submissions",
              }
            },
            {
              $lookup: {
                from: "event_tickets",
                localField: "ticketId",
                foreignField: "_id",
                pipeline: [{
                  $project: {
                    _id: 1,
                    name: 1,
                  }
                }],
                as: "ticketId",
              },
            },
            {
              $unwind: {
                path: "$ticketId",
                preserveNullAndEmptyArrays: false
              },
            },
          ],
          as: "event_tickets",
        },
      }

    ]

    attendeeData = await EventParticipantAttendees.aggregate(pipeline);
    if (attendeeData.length > 0)
      return res.status(200).json({
        status: true,
        message: "Event attendee purchase ticket retrive!",
        data: attendeeData[0],
      });
    else
      return res
        .status(200)
        .json({
          status: false,
          message: "Attendee purchase ticket details not found!",
          data: {},
          iconList: []
        });
  } catch (error) {
    console.log(error);
    return res.status(500).json({
      status: false,
      message: "Internal server error!"
    })
  }
}
exports.createGuestUser= async (payload)=>{
  try {
    const { email, community } = payload
    let getEdgeDetails = null
    let userExists = await User.findOne({
      "Preferred Email": email,
      isDelete: false,
    });
    let getMEdgeDetails = await userEdges.findOne({ user_id: userExists._id, relation_id: ObjectId(community._id), type: { $in: ["M", "CU"] }, isDelete: false });
    if (!getMEdgeDetails) {
      // Find the edgeIds that do not already exist in user_edges
      const edgeIds = await assignGuestRoleToUser(userExists._id, community._id);
      const newEdgeIds = edgeIds.filter(edgeId => !userExists.user_edges.includes(edgeId));

      if (newEdgeIds.length > 0) {
        userExists.status = "ACTIVE"
        userExists.user_edges.push(...newEdgeIds);
        await userExists.save();
      } else if (userExists.status == "INACTIVE") {
        userExists.status = "ACTIVE"
        await userExists.save();
      }

      const lastEdgeId = edgeIds[edgeIds.length - 1];
      getEdgeDetails = await userEdges
        .findOne({ _id: lastEdgeId, isDelete: false })
        .lean();

      if (!getEdgeDetails)
        return {
          status: false,
          message: "Edge details not found for the user.",
        }

      if (!getEdgeDetails.subscription_id) {
        try {
          const eventGUSubscriptionPayload = {
            method: "post",
            url: `https://${community.nickname}.${BASE_URL}/api/billings/api/v1/subscriptions/guestUser`,
            data: {
              edge: getEdgeDetails,
              user_id: userExists._id.toString(),
              relation_id: community._id.toString(),
            },
          };
          const response = await axios.request(eventGUSubscriptionPayload);
          if (response.status === 200) {
            await userEdges.findByIdAndUpdate(getEdgeDetails._id, {
              subscription_id: response.data._id,
            });
            return { status: true, message: "Subscription created successfully." };
          }
        } catch (error) {
          console.error("Error during subscription request:", error.message);
          return { status: false, message: "Subscription service error." };
        }
      } else {
        return { status: true, message: "Subscription already created." };
      }
    } else {
      return { status: true, message: "Already have members or collaborator user edge" };
    }
  } catch (error) {
    console.error("Error during OTP verification:", error);
    return {
      status: false,
      message: error.message || "An unexpected error occurred.",
      data: {},
    };
  }
};
async function deleteGuestUser(payload) {
  try {
    const { userId, community, token } = payload
    let userExists = await User.findOne({ _id: ObjectId(userId), isDelete: false })

    let getEdgeDetails = await userEdges.findOne({ user_id: userExists._id, relation_id: ObjectId(community._id), type: "GU", isDelete: false });

    if (getEdgeDetails && getEdgeDetails.subscription_id) {
      try {
        const eventGUSubscriptionPayload = {
          method: "delete",
          url: `https://${community.nickname}.${BASE_URL}/api/billings/api/v1/subscriptions/${getEdgeDetails.subscription_id}`,
          headers: {
              'Authorization': token 
          },
          data: {},
        };

        const response = await axios(eventGUSubscriptionPayload);
        if (response.status === 200) {
          getEdgeDetails.isDelete = true;
          await getEdgeDetails.save()
          userExists.user_edges = userExists.user_edges.filter((edge) => edge.toString() != getEdgeDetails._id.toString())
          await userExists.save()
          return { status: true, message: "Subscription created successfully." };
        }
      } catch (error) {
        console.error("Error during subscription request:", error);
        return { status: false, message: "Subscription service error." };
      }
    } else {
      return { status: true, message: "Subscription is not created." };
    }
  } catch (error) {
    console.error("Error during OTP verification:", error.message);
    return {
      status: false,
      message: error.message || "An unexpected error occurred.",
      data: {},
    };
  }
};
async function assignGuestRoleToUser(userId, communityId) {
  // Assign default role
  const getDefault = await assignRoleToUser(userId, "DEFAULT", userId, "DEFAULT", true, true);

  // Assign guest role
  const getGU = await assignRoleToUser(userId, "GUEST_USER", communityId, "GU", false, false);

  return [getDefault._id.toString(), getGU._id.toString()];
};
async function assignRoleToUser(userId, roleName, relationId, type, isDefault, isOwner) {
  // Retrieve the role once to avoid multiple calls
  const role = await userRole.findOne({ role_name: roleName });

  // Build the query based on role type
  const query = type === "DEFAULT"
    ? { user_id: userId, role_id: role._id, type, isDelete: false }
    : { user_id: userId, relation_id: relationId, role_id: role._id, type, isDelete: false };

  // Check if the user already has this role assigned
  const existingEdge = await userEdges.findOne(query);

  // If the role assignment doesn't exist, create a new one
  if (!existingEdge) {
    const userEdge = new userEdges({
      default: isDefault,
      owner: isOwner,
      user_id: userId,
      role_id: role._id,
      ...(type !== "DEFAULT" && { relation_id: relationId }), // Add relation_id only for non-DEFAULT
      type,
    });
    await userEdge.save();
    return userEdge;  // Return the newly created edge
  }

  return existingEdge; // Return the existing edge if the role already exists
};