const {
  StartTranscriptionJobCommand,
  DeleteTranscriptionJobCommand,
  GetTranscriptionJobCommand,
  TranscribeClient,
} = require("@aws-sdk/client-transcribe");
const fs = require("fs");
const AWS = require("aws-sdk");
const ffmpeg = require("fluent-ffmpeg");
const mongoose = require("mongoose");
const { ObjectId } = require("mongodb");
var axios = require("axios").default;
const { deleteImage } = require("../../utils/mediaUpload");
const ContentCategory = require("../../database/models/contentArchive_category");
const contentSubCategory = require("../../database/models/contentArchive_subcategory");
const ContentSpeaker = require("../../database/models/contentArchive_speaker");
const ContentTag = require("../../database/models/contentArchive_tag");
const ContentEvent = require("../../database/models/contentArchive_event");
const ContentArchiveVideo = require("../../database/models/contentArchive_video");
const videoErrorLogger = require("../../database/models/contentArchive_error_logger");
const ContentSearch = require("../../database/models/contentArchive_search");
const Group = require("../../database/models/group");
const Event = require("../../database/models/event");
const eventLocation = require("../../database/models/eventLocation");
const Dummy = require("../../database/models/dummy");
const User = require("../../database/models/airTableSync");
const adminNews = require("../../database/models/adminNews");
const chat = require("../../database/models/chat");
const chatChannel = require("../../database/models/chatChannel/chatChannel.js");
const chatChannelMembers = require("../../database/models/chatChannel/chatChannelMembers.js");
const { AdminUser } = require("../../database/models/adminuser");
const moment = require("moment");
const Partner = require("../../database/models/partner/partner");
const userEdge = require("../../microservices/user/components/user-edges/database/models/user-edges.js");
const {userAccessRulesCommonCondition, checkValidIds} = require("../../controller/userAccessRules/restrictionAccess");
const eventParticipantAttendees = require("../../database/models/eventParticipantAttendees");
const UserEdgesService = require("../../microservices/user/components/user-edges/services/user-edges-service");
const RolesService = require("../../microservices/user/components/roles/services/roles-service");
const event = require("../../database/models/event.js");
const {
  communities,
} = require("../../microservices/user/components/communities/database/models/index.js");
const path = require("path");
var s3 = new AWS.S3({
  accessKeyId: process.env.AWS_ID,
  secretAccessKey: process.env.AWS_SECRET,
  Bucket: process.env.AWS_BUCKET,
  signatureVersion: 'v4',
  region: process.env.AWS_REGION
});
const region = "us-east-2";
const credentials = {
  accessKeyId: process.env.AWS_ID,
  secretAccessKey: process.env.AWS_SECRET,
};
const nodeApiDocumentsApiUrl = process.env.NODE_API_DOCUMENTS_API_URL;
const userEdgesService = new UserEdgesService();
const rolesService = new RolesService();
const { getAllTiersfromBilling } = require("../userAccessRules/tiers.js");
const {
  getTaggedUser,
  getTaggedUserForDelete,
  getAlluserInTag,
} = require("../chatChannelController.js");
const publishMessage = require("../../microservices/user/components/users/entry-points/message-queue/publisher");
const { BASE_URL } = require("../../config/config.js");
const { convert } = require('html-to-text');
const metascraperYoutube = require('metascraper-youtube')
const metascraper = require("metascraper")([
  metascraperYoutube(),
  require("metascraper-title")(),
  require("metascraper-description")(),
  require("metascraper-image")(),
  require("metascraper-url")(),
  require("metascraper-author")(),
  require("metascraper-date")(),
  require("metascraper-publisher")(),
  require("metascraper-logo")(),
  require("metascraper-lang")(),
  require("metascraper-readability")(),
]);

let ProcessStates = 0;
function getVideoDuration(videoPath) {
  return new Promise((resolve, reject) => {
    ffmpeg.ffprobe(videoPath, (err, metadata) => {
      if (err) {
        reject(err);
      } else {
        const duration = metadata.format.duration;
        resolve(duration);
      }
    });
  });
}

// check Alreday Exist SubCategory
exports.checkAlredayExistSubCategory = async (req, res) => {
  try {
    if (req.body.name) {
      const subcategory = await contentSubCategory.findOne({
        name: new RegExp('^' + req.body.name + '$', 'i'),
        relation_id: ObjectId(req.relation_id),
        isDelete: false,
      });
      if (subcategory) {
        const isAssignToCategory = await ContentCategory.find({subcategory:subcategory._id,isDelete:false});
        const names = isAssignToCategory.map(category => category.name);
        return res.status(200).json({ status: false, message: `You can not add this subcategory because it is assigned to '${names}' category:`, data: names });
      } else {
        return res.status(200).json({ status: true, message: `This subcategory does not exist, you can add subcategory` });
      }
    } else {
      return res.status(200).json({ status: false, message: `Input parameter name is missing` });
    }
  } catch (error) {
    return res.status(200).json({ status: false, message: `${error.message}` });
  }
};

// edit SubCategory
exports.editSubCategory = async (req, res) => {
  try {
    const { id } = req.params;
    const isSubCategoryExist = await contentSubCategory.findOne({
        _id:{$nin:id},
        name: new RegExp('^' + req.body.name + '$', 'i'),
        relation_id: ObjectId(req.relation_id),
        isDelete: false,
    });
    if (!isSubCategoryExist) {
      const updateSubCategory = await contentSubCategory.findByIdAndUpdate(
        id,
        { name: req.body.name },
        { new: true }
      );
      if (updateSubCategory) {
        return res
          .status(200)
          .json({
            status: true,
            message: `SubCategory updated successfully!`,
            data: updateSubCategory,
          });
      } else {
        return res
          .status(200)
          .json({ status: false, message: `somthing went wrong` });
      }
    } else {
      const isAssignToCategory = await ContentCategory.find({subcategory:isSubCategoryExist._id,isDelete:false});
      const names = isAssignToCategory.map(category => category.name);
      return res
        .status(200)
        .json({ status: false, message: `You cannot edit this subcategory because it is assigned to '${names}' category`,data:names });
    }
  } catch (error) {
    return res.status(200).json({ status: false, message: `${error.message}` });
  }
};

exports.createCategoty = async (req, res) => {
  try {
    const subcatArr =
      req.body.subcategory !== undefined &&
        req.body.subcategory !== null &&
        req.body.subcategory !== ""
        ? req.body.subcategory.split(",")
        : [];
    const checkname = await ContentCategory.find({
      name: new RegExp('^' + req.body.name + '$', 'i'),
      relation_id: ObjectId(req.relation_id),
      isDelete: false,
    });
    if (checkname && checkname.length > 0) {
      return res
        .status(200)
        .json({ status: false, message: `Category name must be unique.` });
    }

    let subcategory = [];
    var subcategory_data = subcatArr.map(async (item, index) => {
      if (await contentSubCategory.findOne({ name: item,relation_id: ObjectId(req.relation_id), isDelete: false }))
        return res.status(200).json({
          status: false,
          message: `Sub Category name must be unique.`,
        });

      const newSubEntry = new contentSubCategory({ name: item, relation_id: ObjectId(req.relation_id), });
      const subResult = await newSubEntry.save();
      subcategory.push(subResult._id);
    });
    await Promise.all([...subcategory_data]);

    const newentry = new ContentCategory({
      name: req.body.name,
      categoryImage: req.categoryImage,
      subcategory: subcategory,
      relation_id: ObjectId(req.relation_id),
    });

    const result = await newentry.save();
    return res
      .status(200)
      .json({ status: true, message: `Category created.`, data: result });
  } catch (error) {
    if (error.name === "MongoServerError" && error.code === 11000) {
      return res
        .status(200)
        .json({ status: false, message: `Category name must be unique.` });
    } else {
      return res
        .status(200)
        .json({ status: false, message: `Something went wrong. ${error}` });
    }
  }
};

exports.getCategoriesList = async (req, res) => {
  try {
    const authUser = req.authUserId;
    const currentEdgeId = req.currentEdge._id;
    let ruleCondition = await userAccessRulesCommonCondition({ userId: authUser, relation_id: req.relation_id });
    const data = await ContentCategory.aggregate([
      {
        $match: {
          isDelete: false,
          relation_id: ObjectId(req.relation_id),
        },
      },
      {
        $lookup: {
          from: "contentarchive_videos",
          localField: "_id",
          foreignField: "categories",
          pipeline: [
            {
              $match: {
                isDelete: false,
                ...ruleCondition
              },
            },
          ],
          as: "totalcount",
        },
      },
      {
        $lookup: {
          from: "contentarchive_subcategories",
          localField: "subcategory",
          foreignField: "_id",
          pipeline: [
            {
              $match: {
                $expr: {
                  $eq: ["$isDelete", false],
                },
              },
            },
            {
              $project: {
                _id: 1,
                name: 1,
              },
            },
          ],
          as: "subcategory",
        },
      },
      {
        $project: {
          _id: "$_id",
          name: 1,
          counts: {
            $size: "$totalcount",
          },
          subcategory: 1,
        },
      },
      {
        $sort: { createdAt: -1 },
      },
      {
        $match: {
          counts: {
            $gt: 0,
          },
        },
      },
    ]);
    return res
      .status(200)
      .json({ status: true, message: `List of categories.`, data: data });
  } catch (error) {
    return res.status(200).json({ status: false, message: `${error.message}` });
  }
};

exports.getCategoriesList_as = async (req, res) => {
  try {
    const sortType = req.query.sortType === "Asc" ? 1 : -1;

    let search = req.query.search;
    const pipeline = [
      {
        $match: {
          isDelete: false,
          relation_id: ObjectId(req.relation_id),
        },
      },
      ...(search && search != "" ? [
        {
          $match: {
            $or: [
              { name: { $regex: ".*" + search + ".*", $options: "i" }, },
            ]
          },
        },
      ] : []),
      {
        $lookup: {
          from: "contentarchive_videos",
          localField: "_id",
          foreignField: "categories",
          pipeline: [
            {
              $match: {
                isDelete: false,
              },
            },
          ],
          as: "totalcount",
        },
      },
      {
        $lookup: {
          from: "contentarchive_subcategories",
          localField: "subcategory",
          foreignField: "_id",
          pipeline: [
            {
              $match: {
                isDelete: false,
              },
            },
          ],
          as: "subcategory",
        },
      },
      {
        $project: {
          _id: "$_id",
          name: 1,
          subcategory: "$subcategory",
          counts: {
            $size: "$totalcount",
          },
        },
      },
      {
        $addFields: {
          sortFieldLower:
            req.query.sortField === "name"
              ? { $toLower: "$name" }
              : req.query.sortField === "counts"
                ? { $toInt: "$counts" }
                : "$createdAt",
        },
      },
      { $sort: { sortFieldLower: sortType } },
      {
        $project: {
          sortFieldLower: 0,
        },
      },
    ]

    const [data, countAllData] = await Promise.all([
      ContentCategory.aggregate(pipeline),
      ContentCategory.countDocuments({ relation_id: ObjectId(req.relation_id), isDelete: false }),
    ]);

    return res
      .status(200)
      .json({ status: true, message: `List of categories.`, data: data, countAllData });
  } catch (error) {
    return res.status(200).json({ status: false, message: `${error.message}` });
  }
};

// get Categories Suggestion List
exports.getCategoriesSuggestionList = async (req, res) => {
  try {
    const data = await ContentCategory.find(
      { isDelete: false, relation_id: ObjectId(req.relation_id), },
      { _id: 0, name: 1, subcategory: 0 }
    ).sort({ name: 1 }).lean();
    return res
      .status(200)
      .json({ status: true, message: `List of categories.`, data: data });
  } catch (error) {
    return res.status(200).json({ status: false, message: `${error.message}` });
  }
};

/** code by SJ start **/

exports.getVideoByCategoriesAndFilter = async (req, res) => {
  try {
    const page = parseInt(req.query.page);
    const limit = parseInt(req.query.limit);
    const skip = (page - 1) * limit;
    const authUser = req.authUserId;
    const currentEdgeId = req.currentEdge._id;

    var categorie_id = [];
    if (
      req.query.categorie_id &&
      req.query.categorie_id !== "null" &&
      req.query.categorie_id !== "undefined"
    ) {
      categorie_id = [ObjectId(req.query.categorie_id)];
    }

    var subcategorie_id = [];
    if (
      req.query.subcategorie_id &&
      req.query.subcategorie_id !== "null" &&
      req.query.subcategorie_id !== "undefined"
    ) {
      subcategorie_id = [ObjectId(req.query.subcategorie_id)];
    }

    var search = "";
    if (req.query.search) {
      search = req.query.search;
    }

    var event = [];
    let attendeeAsMember;
    if (
      req.query.event &&
      req.query.event !== "null" &&
      req.query.event !== "undefined"
    ) {
      event = [ObjectId(req.query.event)];
      let pipeline = [
        {
          '$match': {
            'user': ObjectId(authUser),
            'event': ObjectId(req.query.event),
            'isDelete': false,
            'relation_id': ObjectId(req.relation_id),
          }
        }, {
          '$lookup': {
            'from': 'event_wise_participant_types',
            'localField': 'role',
            'foreignField': '_id',
            'as': 'event_wise_participant_types',
            'pipeline': [
              {
                '$match': {
                  '$expr': {
                    '$eq': [
                      '$role', 'Member'
                    ]
                  }
                }
              }
            ]
          }
        }, {
          '$unwind': {
            'path': '$event_wise_participant_types',
            'preserveNullAndEmptyArrays': false
          }
        }
      ];
      let attendeeAsMemberTemp = await eventParticipantAttendees.aggregate(pipeline);
      if (attendeeAsMemberTemp && attendeeAsMemberTemp.length && attendeeAsMemberTemp[0]) {
        attendeeAsMember = attendeeAsMemberTemp[0];
      }
    }

    var speaker = [];
    if (
      req.query.speaker &&
      req.query.speaker !== "null" &&
      req.query.speaker !== "undefined"
    ) {
      speaker = [ObjectId(req.query.speaker)];
    }

    var tagId = [];
    if (
      req.query.tagId &&
      req.query.tagId !== "null" &&
      req.query.tagId !== "undefined"
    ) {
      tagId = [ObjectId(req.query.tagId)];
    }

    const filter = req.query.filter;
    var sort = { createdAt: -1 };


    let ruleCondition = await userAccessRulesCommonCondition({ userId: authUser, relation_id: req.relation_id });
    var match = {
      isDelete: false,
      uploadstatus: { $ne: "inprocess" },
      ...ruleCondition,
      relation_id: ObjectId(req.relation_id),
    };

    if (categorie_id.length > 0) {
      match = { ...match, categories: { $in: categorie_id } };
    }

    if (search) {
      match = {
        ...match,
        title: { $regex: ".*" + search + ".*", $options: "i" },
      };
    }

    if (subcategorie_id.length > 0) {
      match = { ...match, subcategory: { $in: subcategorie_id } };
    }

    if (event.length > 0) {
      match = { ...match, eventIds: { $in: event } };
    }

    if (speaker.length > 0) {
      match = { ...match, speaker: { $in: speaker } };
    }

    if (tagId.length > 0) {
      match = { ...match, tag: { $in: tagId } };
    }

    if (filter === "recent") {
      sort = { createdAt: -1, updatedAt: -1 };
    } else if (filter === "popular") {
      sort = { viewsCount: -1 };
    } else if (filter === "comment") {
      sort = { commentsCount: -1 };
    }

    var data = await ContentArchiveVideo.aggregate([
      {
        $match: match,
      },
      {
        $lookup: {
          from: "contentarchive_categories",
          let: { contentarchive_categories_id: "$categories" },
          pipeline: [
            {
              $match: {
                $expr: {
                  $in: ["$_id", "$$contentarchive_categories_id"],
                },
              },
            },
            { $project: { name: 1 } },
          ],
          as: "categories",
        },
      },
      {
        $lookup: {
          from: "contentarchive_subcategories",
          localField: "subcategory",
          foreignField: "_id",
          pipeline: [
            {
              $match: {
                isDelete: false,
              },
            },
          ],
          as: "subcategory",
        },
      },
      {
        $lookup: {
          from: "groups",
          let: { suggestion_id: "$restrictedAccessGroupId" },
          pipeline: [
            {
              $match: {
                $expr: {
                  $cond: {
                    if: { $isArray: "$$suggestion_id" },
                    then: { $in: ["$_id", "$$suggestion_id"] },
                    else: false 
                  }
                }
              }
            },
            { $project: { groupTitle: 1 } },
          ],
          as: "group_ids",
        },
      },
      {
        $addFields: {
          viewsCount: {
            $cond: {
              if: { $isArray: "$views" },
              then: { $add: [{ $size: "$views" }, "$starting_view_cnt"] },
              else: "$starting_view_cnt",
            },
          },
        },
      },
      {
        $addFields: {
          commentsCount: {
            $cond: {
              if: { $isArray: "$comments" },
              then: { $size: "$comments" },
              else: 0,
            },
          },
        },
      },
      { $sort: sort },
      { $skip: skip },
      { $limit: limit },
      {
        $project: {
          _id: 1,
          title: 1,
          video: 1,
          description: 1,
          thumbnail: 1,
          createdAt: 1,
          viewsCount: 1,
          commentsCount: 1,
          duration: 1,
          categories: 1,
          views: 1,
          likes: 1,
          restrictedAccessGroupId: 1,
          user_video_pause: 1,
        },
      },
    ]);

    var count;
    count = await ContentArchiveVideo.countDocuments({
      ...match,
    });
    var arr = [];
    for (var i = 0; i < data.length; i++) {
      var url = s3.getSignedUrl("getObject", {
        Bucket: "arn:aws:s3:us-east-2:496737174815:accesspoint/accessforapp",
        Key: data[i].video,
        Expires: 100000,
      });
      arr.push({ ...data[i], video: url });
    }
    data = arr;

    var mobile_desc = data?.map(async (item, index) => {
      let mobile_description = "";
      if (item.description !== undefined) {
        let without_html_description = item.description.replace(/&amp;/g, "&");
        without_html_description = item.description.replace(/(<([^>]+)>)/g, "");
        without_html_description = without_html_description.replace(
          /(\r\n|\n|\r)/gm,
          ""
        );
        mobile_description = without_html_description.substring(0, 600);
      }
      item.mobile_description = mobile_description.trim();
    });
    await Promise.all([...mobile_desc]);

    if (
      req.query.event &&
      req.query.event !== "null" &&
      req.query.event !== "undefined"
    ) {
      if (
        attendeeAsMember
      ) {
        return res.status(200).json({
          status: true,
          message: `List of Content Archive Video.`,
          data: [
            {
              videos: data,
              currentPage: req.query.page,
              totalPages: Math.ceil(count / limit),
              totalVideos: count,
            },
          ],
        });
      } else {
        return res.status(200).json({
          status: true,
          message: `Content Archive Video list not found`,
          data: [
            {
              videos: [],
              currentPage: req.query.page,
              totalPages: 0,
              totalVideos: 0,
            },
          ],
        });
      }
    } else if (!req.query.event) {
      return res.status(200).json({
        status: true,
        message: `List of Content Archive Video.`,
        data: [
          {
            videos: data,
            currentPage: req.query.page,
            totalPages: Math.ceil(count / limit),
            totalVideos: count,
          },
        ],
      });
    } else {
      return res.status(200).json({
        status: true,
        message: `Content Archive Video list not found`,
        data: [
          {
            videos: [],
            currentPage: req.query.page,
            totalPages: 0,
            totalVideos: 0,
          },
        ],
      });
    }
  } catch (error) {
    return res.status(200).json({ status: false, message: `${error.message}` });
  }
};

exports.getVideoByCateFilterSort = async (req, res) => {
  try {
    const page = parseInt(req.query.page);
    const limit = parseInt(req.query.limit);
    const authUser = req.authUserId;
    const currentEdgeId = req.currentEdge._id;

    const skip = (page - 1) * limit;
    var categorie_id = [];
    if (
      req.query.categorie_id &&
      req.query.categorie_id !== "null" &&
      req.query.categorie_id !== "undefined"
    ) {
      categorie_id = [ObjectId(req.query.categorie_id)];
    }
    var subcategorie_id = [];
    if (
      req.query.subcategorie_id &&
      req.query.subcategorie_id !== "null" &&
      req.query.subcategorie_id !== "undefined"
    ) {
      subcategorie_id = [ObjectId(req.query.subcategorie_id)];
    }
    var search = "";
    if (req.query.search) {
      search = req.query.search;
    }
    var sorting = "h2l";
    if (req.query.sort) {
      sorting = req.query.sort;
    }

    const filter = req.query.filter;
    var sort = { createdAt: -1 };
    let ruleCondition = await userAccessRulesCommonCondition({ userId: authUser, relation_id: req.relation_id });
    var match = {
      isDelete: false,
      uploadstatus: { $ne: "inprocess" },
      ...ruleCondition,
      relation_id: ObjectId(req.relation_id),
    };

    if (categorie_id.length > 0) {
      match = { ...match, categories: { $in: categorie_id } };
    }
    if (search) {
      match = {
        ...match,
        $or: [
          { title: { $regex: ".*" + search + ".*", $options: "i" } },
          { description: { $regex: ".*" + search + ".*", $options: "i" } },
          { 'tagData.name': { $regex: ".*" + search + ".*", $options: "i" } },
        ]
      };
    }
    if (subcategorie_id.length > 0) {
      match = { ...match, subcategory: { $in: subcategorie_id } };
    }

    if (filter === "popular" && sorting === "h2l") {
      sort = { viewsCount: -1 };
    } else if (filter === "comment" && sorting === "h2l") {
      sort = { commentsCount: -1 };
    } else if (filter === "popular" && sorting === "l2h") {
      sort = { viewsCount: 1 };
    } else if (filter === "comment" && sorting === "l2h") {
      sort = { commentsCount: 1 };
    }

    const aggregatePipeline = [
      {
        $lookup: {
          from: "contentarchive_tags",
          localField: "tag",
          foreignField: "_id",
          pipeline: [
            {
              $match: {
                isDelete: false,
                name: { $regex: ".*" + search + ".*", $options: "i" } ,
              },
            },
          ],
          as: "tagData",
        },
      },
      {
        $match: match,
      },
      {
        $match: {...ruleCondition},
      },
      {
        $lookup: {
          from: "contentarchive_categories",
          let: { contentarchive_categories_id: "$categories" },
          pipeline: [
            {
              $match: {
                $expr: {
                  $in: ["$_id", "$$contentarchive_categories_id"],
                },
              },
            },
            { $project: { name: 1 } },
          ],
          as: "categories",
        },
      },
      {
        $lookup: {
          from: "contentarchive_subcategories",
          localField: "subcategory",
          foreignField: "_id",
          pipeline: [
            {
              $match: {
                isDelete: false,
              },
            },
          ],
          as: "subcategory",
        },
      },
      {
        $lookup: {
          from: "groups",
          let: { suggestion_id: "$restrictedAccessGroupId" },
          pipeline: [
            {
              $match: {
                $expr: {
                  $cond: {
                    if: { $isArray: "$$suggestion_id" },
                    then: { $in: ["$_id", "$$suggestion_id"] },
                    else: false 
                  }
                }
              }
            },
            { $project: { groupTitle: 1 } },
          ],
          as: "group_ids",
        },
      },
      {
        $addFields: {
          viewsCount: {
            $cond: {
              if: { $isArray: "$views" },
              then: { $add: [{ $size: "$views" }, "$starting_view_cnt"] },
              else: "$starting_view_cnt",
            },
          },
        },
      },
      {
        $addFields: {
          commentsCount: {
            $cond: {
              if: { $isArray: "$comments" },
              then: { $size: "$comments" },
              else: 0,
            },
          },
        },
      },
      {
        $addFields: {
          priority: {
            $switch: {
              branches: [
                {
                  case: { $regexMatch: { input: "$title", regex: "^" + search, options: "i" } },
                  then: 1,
                },
                {
                  case: {
                    $and: [
                      { $isArray: "$tagData" },
                      { $gt: [{ $size: "$tagData" }, 0] }, // Ensure the array has elements
                      { $regexMatch: { input: { $arrayElemAt: ["$tagData.name", 0] }, regex: "^" + search, options: "i" } }
                    ]
                  },
                  then: 2,
                },
                {
                  case: { $regexMatch: { input: "$title", regex: ".*" + search + ".*", options: "i" } },
                  then: 3,
                },
                {
                  case: { $regexMatch: { input: "$description", regex: ".*" + search + ".*", options: "i" } },
                  then: 4,
                },
              ],
              default: 5, // Default value if no match
            },
          },
        },
      },
      {
          $sort: {
            ...(search ? { priority: 1 } : sort ),
          },
      },
      {
        $project: {
          _id: 1,
          title: 1,
          video: 1,
          description: 1,
          thumbnail: 1,
          createdAt: 1,
          viewsCount: 1,
          commentsCount: 1,
          duration: 1,
          categories: 1,
          views: 1,
          likes: 1,
          user_video_pause: 1,
        },
      },
    ]
    var data = await ContentArchiveVideo.aggregate([
      ...aggregatePipeline,
      { $skip: skip },
      { $limit: limit },
    ]);

    const getAllData = await ContentArchiveVideo.aggregate([...aggregatePipeline]);
    var count = getAllData.length;

    var arr = [];
    for (var i = 0; i < data.length; i++) {
      var url = s3.getSignedUrl("getObject", {
        Bucket: "arn:aws:s3:us-east-2:496737174815:accesspoint/accessforapp",
        Key: data[i].video,
        Expires: 100000,
      });
      arr.push({ ...data[i], video: url });
    }
    data = arr;

    var mobile_desc = data?.map(async (item, index) => {
      let mobile_description = "";
      if (item.description !== undefined) {
        let without_html_description = item.description.replace(/&amp;/g, "&");
        without_html_description = item.description.replace(/(<([^>]+)>)/g, "");
        without_html_description = without_html_description.replace(
          /(\r\n|\n|\r)/gm,
          ""
        );
        mobile_description = without_html_description.substring(0, 600);
      }
      item.mobile_description = mobile_description.trim();
    });
    await Promise.all([...mobile_desc]);

    return res.status(200).json({
      status: true,
      message: `List of Content Archive Video.`,
      data: [
        {
          videos: data,
          currentPage: req.query.page,
          totalPages: Math.ceil(count / limit),
          totalVideos: count,
        },
      ],
    });
  } catch (error) {
    return res.status(200).json({ status: false, message: `${error.message}` });
  }
};

// get video listing refactor saved and history wise filter added
exports.getVideoByCateFilterSortV2 = async (req, res) => {
  try {
    let page = req.query.page ? +req.query.page : 1;
    let limit = req.query.limit ? +req.query.limit : 20;
    let skip = (page - 1) * limit;
    const authUser = req.authUserId;
    const currentEdgeId = req.currentEdge._id;
    let userData = await User.findById(authUser);
    let { filter, search, filterType, reqFromDate, reqToDate, status, } = req.query;
    
    let ruleCondition = await userAccessRulesCommonCondition({ userId: authUser, relation_id: req.relation_id });
    let match = {
      ...ruleCondition,
      uploadstatus: { $ne: "inprocess" },
      relation_id: ObjectId(req.relation_id),
      isDelete: false,
    };

    let categorie_id = [];
    if (
      req.query.categorie_id &&
      req.query.categorie_id !== "null" &&
      req.query.categorie_id !== "undefined"
    ) {
      categorie_id = [ObjectId(req.query.categorie_id)];
    };

    let subcategorie_id = [];
    if (
      req.query.subcategorie_id &&
      req.query.subcategorie_id !== "null" &&
      req.query.subcategorie_id !== "undefined"
    ) {
      subcategorie_id = [ObjectId(req.query.subcategorie_id)];
    };

    let speaker_id = [];
    if (
      req.query.speaker_id &&
      req.query.speaker_id !== "null" &&
      req.query.speaker_id !== "undefined"
    ) {
      speaker_id = [ObjectId(req.query.speaker_id)];
    };

    let tag_id = [];
    if (
      req.query.tag_id &&
      req.query.tag_id !== "null" &&
      req.query.tag_id !== "undefined"
    ) {
      tag_id = [ObjectId(req.query.tag_id)];
    };

    let event_id = [];
    if (
      req.query.event_id &&
      req.query.event_id !== "null" &&
      req.query.event_id !== "undefined"
    ) {
      event_id = [ObjectId(req.query.event_id)];
    };

    if (categorie_id.length > 0) {
      match = { ...match, categories: { $in: categorie_id } };
    }

    if (subcategorie_id.length > 0) {
      match = { ...match, subcategory: { $in: subcategorie_id } };
    }

    if (speaker_id.length > 0) {
      match = { ...match, speaker: { $in: speaker_id } };
    }

    if (tag_id.length > 0) {
      match = { ...match, tag: { $in: tag_id } };
    }

    if (event_id.length > 0) {
      match = { ...match, eventIds: { $in: event_id } };
    }

    if (filterType && filterType != "lifetime") {
      let toDay = moment.utc();
      switch (filterType) {
          case "first24hours":
              match["createdAt"] = { $gte: moment(toDay).subtract(1, "days").toDate(), $lt: toDay.toDate() };
              break;
          case "past7days":
              match["createdAt"] = { $gte: moment(toDay).startOf('day').subtract(7, "days").toDate(), $lt: toDay.endOf('day').toDate()};
              break;
          case "past30days":
              match["createdAt"] = { $gte: moment(toDay).startOf('day').subtract(30, "days").toDate(), $lt: toDay.endOf('day').toDate()};
              break;
          case "thisYear":
              match["createdAt"] = { $gte: moment().startOf('year').toDate(), $lt: moment().endOf('year').toDate() };
              break;
          case "lastYear":
              match["createdAt"] = { $gte: moment().startOf('year').subtract(1, 'year').toDate(), $lt: moment().endOf('year').subtract(1, 'year').toDate() };
              break;
          case "custom":
              if (reqFromDate && moment(reqFromDate).isValid()) {
                  match["createdAt"] = { $gte: moment.utc(reqFromDate).startOf('day').toDate() };
              }
              if (reqToDate && moment(reqToDate).isValid()) {
                  match["createdAt"] = { ...match["createdAt"], $lt: moment.utc(reqToDate).toDate() };
              }
              break;
          default:
              // Handle default case
              // Handle unsupported filter types or invalid values
              console.error("Unsupported date filter type:", filterType);
              break;
            }
    }
  
    if (search) {
      match = {
        $and: [
          {...match},
          {
            $or: [
              { title: { $regex: ".*" + search.replace(/[.*+?^${}()|[\]\\]/g, '\\$&') + ".*", $options: "i" } },
              { description: { $regex: ".*" + search.replace(/[.*+?^${}()|[\]\\]/g, '\\$&') + ".*", $options: "i" } },
              { 'tagDataTemp.name': { $regex: ".*" + search.replace(/[.*+?^${}()|[\]\\]/g, '\\$&') + ".*", $options: "i" } },
            ]
          }
        ]
      };
    };

    if (status == "history") {
      userData.video_history_data = userData.video_history_data.sort((a, b) => b.history_date - a.history_date);
     };
  
    let totalSavedVideo = await ContentArchiveVideo.countDocuments({ $and: [match, {_id: { $in: userData.saveVideos },}]});
    
    let historyVideosIds = userData.video_history_data.map((data) => new ObjectId(data.video_id));
    let totalHistoryVideo = await ContentArchiveVideo.countDocuments({ $and: [match,{_id: { $in: historyVideosIds },}]});

    let totalVideoCount = await ContentArchiveVideo.countDocuments(match)

    const aggregatePipeline = [
      {
        '$lookup': {
          'from': 'contentarchive_tags', 
          'localField': 'tag', 
          'foreignField': '_id',
          'pipeline': [
            {
              '$match': {
                'isDelete': false,
              },
            },
          ], 
          'as': 'tagDataTemp'
        }
      },
      {
        $match: match,
      },
      ...(status == "saved" ? [
        {
          $match: { _id: { $in: userData.saveVideos },},
        }
      ] : []),
      ...(status == "history" ? [
        {
          $match:{_id: { $in: userData.video_history_data.map((data)=>new ObjectId(data.video_id)) }},
        }
      ] : []),
      {
        $lookup: {
          from: "contentarchive_categories",
          let: { contentarchive_categories_id: "$categories" },
          pipeline: [
            {
              $match: {
                $expr: {
                  $in: ["$_id", "$$contentarchive_categories_id"],
                },
              },
            },
            { $project: { name: 1 } },
          ],
          as: "categories",
        },
      },
      {
        $addFields: {
          viewsCount: {
            $cond: {
              if: { $isArray: "$views" },
              then: { $add: [{ $size: "$views" }, "$starting_view_cnt"] },
              else: "$starting_view_cnt",
            },
          },
        },
      },
      {
        $addFields: {
          commentsCount: {
            $cond: {
              if: { $isArray: "$comments" },
              then: { $size: "$comments" },
              else: 0,
            },
          },
        },
      },
      {
        $addFields: {
          priority: {
            $switch: {
              branches: [
                {
                  case: { $regexMatch: { input: "$title", regex: "^" + search, options: "i" } },
                  then: 1,
                },
                {
                  case: { $regexMatch: { input: "$title", regex: ".*" + search + ".*", options: "i" } },
                  then: 2,
                },
                {
                  case: { $regexMatch: { input: "$description", regex: ".*" + search + ".*", options: "i" } },
                  then: 3,
                },
                {
                  case: {
                    $and: [
                      { $isArray: "$tagDataTemp" },
                      { $gt: [{ $size: "$tagDataTemp" }, 0] }, // Ensure the array has elements
                      { $regexMatch: { input: { $arrayElemAt: ["$tagDataTemp.name", 0] }, regex: "^" + search, options: "i" } }
                    ]
                  },
                  then: 4,
                },
              ],
              default: 5, // Default value if no match
            },
          },
        },
      },
      {
        $lookup: {
          from: "airtable-syncs",
          let: {
            videoId: "$_id",
            userId: new ObjectId(authUser),
          },
          pipeline: [
            {
              $match: {
                $expr: {
                  $and: [
                    {
                      $eq: ["$_id", "$$userId"],
                    },
                    {
                      $in: [
                        "$$videoId",
                        { $ifNull:["$video_history_data.video_id", []]},
                      ],
                    },
                  ],
                },
              },
            },
            {
              $project: {
                _id: 0,
                videoData: {
                  $arrayElemAt: [
                    {
                      $filter: {
                        input: "$video_history_data",
                        as: "history",
                        cond: {
                          $eq: [
                            "$$history.video_id",
                            "$$videoId",
                          ],
                        },
                      },
                    },
                    0,
                  ],
                },
              },
            },
          ],
          as: "videoId",
        },
      },
      {
        $unwind: 
        {
          path: "$videoId",
          preserveNullAndEmptyArrays: true
        }
      },
      {
        $sort: search 
        ? { createdAt: -1, priority: 1 } 
        : filter === "recent" || !filter || filter === "" 
          ? { createdAt: -1 } 
          : filter === "popular" 
            ? { createdAt: -1, viewsCount: -1 } 
            : filter === "comment" 
              ? { createdAt: -1, commentsCount: -1 } 
              : { createdAt: -1 }
      },
      {
        $project: {
          _id: 1,
          title: 1,
          video: 1,
          description: 1,
          thumbnail: 1,
          createdAt: 1,
          viewsCount: 1,
          commentsCount: 1,
          duration: 1,
          categories: 1,
          views: 1,
          likes: 1,
          user_video_pause: 1,
          video_history_id: {
            $cond: {
              if: { $eq: ["$videoId.videoData._id", null] },
              then: null,
              else: "$videoId.videoData._id"
            }
          },
        },
      },
    ];
    let data = await ContentArchiveVideo.aggregate([
      ...aggregatePipeline,
      { $skip: skip },
      { $limit: limit },
    ]);
    const getAllData = await ContentArchiveVideo.aggregate([...aggregatePipeline]);
    let count = getAllData.length;

    let arr = [];
    for (let i = 0; i < data.length; i++) {
      let url = s3.getSignedUrl("getObject", {
        Bucket: "arn:aws:s3:us-east-2:496737174815:accesspoint/accessforapp",
        Key: data[i].video,
        Expires: 100000,
      });
      arr.push({ ...data[i], video: url });
    }
    data = arr;

    let mobile_desc = data?.map(async (item, index) => {
      let mobile_description = "";
      if (item.description !== undefined) {
        let without_html_description = item.description.replace(/&amp;/g, "&");
        without_html_description = item.description.replace(/(<([^>]+)>)/g, "");
        without_html_description = without_html_description.replace(
          /(\r\n|\n|\r)/gm,
          ""
        );
        mobile_description = without_html_description.substring(0, 600);
      }
      item.mobile_description = mobile_description.trim();
    });
    await Promise.all([...mobile_desc]);

    if(data.length == 0){
      const errorLogs = await videoErrorLogger.create({
        user_id:new ObjectId(req.authUserId),
        token:req.headers.authorization?req.headers.authorization : "",
        pipeline:JSON.stringify(...aggregatePipeline,{ $skip: skip },{ $limit: limit }),
        function:"get-video-by-filter-sort"})
    }

    return res.status(200).json({
      status: true,
      message: `List of Content Archive Video.`,
      data:
        {
          videos: data,
          currentPage: page,
          totalPages: Math.ceil(count / limit),
          totalVideos: count,
          all: totalVideoCount,
          saveVideo: totalSavedVideo,
          historyVideo: totalHistoryVideo
        }
    });
  }
  catch (error) {
    return res.status(200).json({ status: false, message: `${error.message}` });
  }
};

// get video listing refactor saved and history wise filter added
exports.getVideoByCateFilterSortCountV2 = async (req, res) => {
  try {
    const authUser = req.authUserId;
    const currentEdgeId = req.currentEdge._id;
    let userData = await User.findById(authUser);
    let total = 0;

    let { filter, search, filterType, reqFromDate, reqToDate, status, } = req.query;

    let categorie_id = [];
    if (
      req.query.categorie_id &&
      req.query.categorie_id !== "null" &&
      req.query.categorie_id !== "undefined"
    ) {
      categorie_id = [ObjectId(req.query.categorie_id)];
    }

    let subcategorie_id = [];
    if (
      req.query.subcategorie_id &&
      req.query.subcategorie_id !== "null" &&
      req.query.subcategorie_id !== "undefined"
    ) {
      subcategorie_id = [ObjectId(req.query.subcategorie_id)];
    }

    let speaker_id = [];
    if (
      req.query.speaker_id &&
      req.query.speaker_id !== "null" &&
      req.query.speaker_id !== "undefined"
    ) {
      speaker_id = [ObjectId(req.query.speaker_id)];
    };

    let sort = { createdAt: -1 };
    let ruleCondition = await userAccessRulesCommonCondition({ userId: authUser, relation_id: req.relation_id });
    let match = {
      ...ruleCondition,
      relation_id: ObjectId(req.relation_id),
      uploadstatus: { $ne: "inprocess" },
      isDelete: false,
    };

    if (status == "history") {
     userData.video_history_data = userData.video_history_data.sort((a, b) => b.history_date - a.history_date);
    };

    if (status == "saved") {
      match = {
        ...match,
        _id: { $in: userData.saveVideos },
      };
    } else if (status == "history") {
      match = {
        ...match,
        _id: { $in: userData.video_history_data.map((data)=>new ObjectId(data.video_id)) }
      };
    } else {
      match = {...match}
    }
  
    if (categorie_id.length > 0) {
      match = { ...match, categories: { $in: categorie_id } };
    }

    if (subcategorie_id.length > 0) {
      match = { ...match, subcategory: { $in: subcategorie_id } };
    }

    if (speaker_id.length > 0) {
      match = { ...match, speaker: { $in: speaker_id } };
    }

    if (filterType && filterType != "lifetime") {
      let toDay = moment.utc();
      switch (filterType) {
          case "first24hours":
              match["createdAt"] = { $gte: moment(toDay).subtract(1, "days").toDate(), $lt: toDay.toDate() };
              break;
          case "past7days":
              match["createdAt"] = { $gte: moment(toDay).startOf('day').subtract(7, "days").toDate(), $lt: toDay.endOf('day').toDate()};
              break;
          case "past30days":
              match["createdAt"] = { $gte: moment(toDay).startOf('day').subtract(30, "days").toDate(), $lt: toDay.endOf('day').toDate()};
              break;
          case "thisYear":
              match["createdAt"] = { $gte: moment().startOf('year').toDate(), $lt: moment().endOf('year').toDate() };
              break;
          case "lastYear":
              match["createdAt"] = { $gte: moment().startOf('year').subtract(1, 'year').toDate(), $lt: moment().endOf('year').subtract(1, 'year').toDate() };
              break;
          case "custom":
              if (reqFromDate && moment(reqFromDate).isValid()) {
                  match["createdAt"] = { $gte: moment.utc(reqFromDate).startOf('day').toDate() };
              }
              if (reqToDate && moment(reqToDate).isValid()) {
                  match["createdAt"] = { ...match["createdAt"], $lt: moment.utc(reqToDate).toDate() };
              }
              break;
          default:
              // Handle default case
              // Handle unsupported filter types or invalid values
              console.error("Unsupported date filter type:", filterType);
              break;
            }
    }
  
    if (search) {
      match = {
        ...match,
        $or: [
          { title: { $regex: ".*" + search + ".*", $options: "i" } },
          { description: { $regex: ".*" + search + ".*", $options: "i" } },
          { 'tagData.name': { $regex: ".*" + search + ".*", $options: "i" } },
        ]
      };
    };

    if (filter === "popular") {
      sort = { viewsCount: -1 };
    } else if (filter === "comment" ) {
      sort = { commentsCount: -1 };
    };

    const aggregatePipeline = [
      {
        $match: match,
      },
      { $count: 'total' }
    ]
    let data = await ContentArchiveVideo.aggregate(aggregatePipeline);

    if (data && data.length && data[0] && data[0]["total"]) {
      total = data[0]["total"];
    }
    return res.status(200).json({ status: true, message: `Content Archive Video count retrive successfully.`, total: total })
  }
  catch (error) {
    return res.status(200).json({ status: false, message: `${error.message}` });
  }
};
exports.getVideoBySubCategoriesAndFilter = async (req, res) => {
  try {
    const page = parseInt(req.query.page);
    const limit = parseInt(req.query.limit);
    const skip = (page - 1) * limit;
    const authUser = req.authUserId;

    var sub_category_id = [];
    if (req.query.sub_category_id) {
      sub_category_id = [ObjectId(req.query.sub_category_id)];
    }

    var search = "";
    if (req.query.search) {
      search = req.query.search;
    }

    const filter = req.query.filter;
    const userdata = await User.findById(authUser);
    var sort = { createdAt: -1 };

    var match = {
      isDelete: false,
      uploadstatus: { $ne: "inprocess" },
    };

    if (sub_category_id.length > 0 && search) {
      match = {
        title: { $regex: ".*" + search + ".*", $options: "i" },
        subcategory: { $in: sub_category_id },
        isDelete: false,
        uploadstatus: { $ne: "inprocess" },
      };
    } else if (sub_category_id.length > 0) {
      match = {
        subcategory: { $in: sub_category_id },
        isDelete: false,
        uploadstatus: { $ne: "inprocess" },
      };
    } else if (search) {
      match = {
        title: { $regex: ".*" + search + ".*", $options: "i" },
        isDelete: false,
        uploadstatus: { $ne: "inprocess" },
      };
    } else if (sub_category_id.length > 0 && search) {
      match = {
        title: { $regex: ".*" + search + ".*", $options: "i" },
        subcategory: { $in: sub_category_id },
        isDelete: false,
        uploadstatus: { $ne: "inprocess" },
      };
    } else if (sub_category_id.length > 0) {
      match = {
        subcategory: { $in: sub_category_id },
        isDelete: false,
        uploadstatus: { $ne: "inprocess" },
      };
    } else if (search) {
      match = {
        title: { $regex: ".*" + search + ".*", $options: "i" },
        isDelete: false,
        uploadstatus: { $ne: "inprocess" },
      };
    }

    if (filter === "recent") {
      sort = { createdAt: -1, updatedAt: -1 };
    } else if (filter === "popular") {
      sort = { viewsCount: -1 };
    } else if (filter === "comment") {
      sort = { commentsCount: -1 };
    }

    var data = await ContentArchiveVideo.aggregate([
      {
        $match: match,
      },
      {
        $lookup: {
          from: "contentarchive_categories",
          let: { contentarchive_categories_id: "$categories" },
          pipeline: [
            {
              $match: {
                $expr: {
                  $in: ["$_id", "$$contentarchive_categories_id"],
                },
              },
            },
            { $project: { name: 1 } },
          ],
          as: "categories",
        },
      },
      {
        $lookup: {
          from: "contentarchive_subcategories",
          let: { contentarchive_subcategory_id: "$subcategory" },
          pipeline: [
            {
              $match: {
                $expr: {
                  $in: ["$_id", "$$contentarchive_subcategory_id"],
                },
              },
            },
            { $project: { name: 1 } },
          ],
          as: "subcategory",
        },
      },
      {
        $lookup: {
          from: "groups",
          let: { suggestion_id: "$restrictedAccessGroupId" },
          pipeline: [
            {
              $match: {
                $expr: {
                  $in: ["$_id", "$$suggestion_id"],
                },
              },
            },
            { $project: { groupTitle: 1 } },
          ],
          as: "group_ids",
        },
      },
      {
        $addFields: {
          viewsCount: {
            $cond: {
              if: { $isArray: "$views" },
              then: { $add: [{ $size: "$views" }, "$starting_view_cnt"] },
              else: "$starting_view_cnt",
            },
          },
        },
      },
      {
        $addFields: {
          commentsCount: {
            $cond: {
              if: { $isArray: "$comments" },
              then: { $size: "$comments" },
              else: 0,
            },
          },
        },
      },
      { $sort: sort },
      { $skip: skip },
      { $limit: limit },
      {
        $project: {
          _id: 1,
          title: 1,
          video: 1,
          description: 1,
          thumbnail: 1,
          createdAt: 1,
          viewsCount: 1,
          commentsCount: 1,
          duration: 1,
          categories: 1,
          views: 1,
          likes: 1,
          user_video_pause: 1,
        },
      },
    ]);

    var count;
    if (sub_category_id.length > 0 && search) {
      count = await ContentArchiveVideo.countDocuments({
        title: { $regex: ".*" + search + ".*", $options: "i" },
        subcategory: { $in: sub_category_id },
        isDelete: false,
        uploadstatus: { $ne: "inprocess" },
      });
    } else if (sub_category_id.length > 0) {
      count = await ContentArchiveVideo.countDocuments({
        subcategory: { $in: sub_category_id },
        isDelete: false,
        uploadstatus: { $ne: "inprocess" },
      });
    } else if (search) {
      count = await ContentArchiveVideo.countDocuments({
        title: { $regex: ".*" + search + ".*", $options: "i" },
        isDelete: false,
        uploadstatus: { $ne: "inprocess" },
      });
    } else if (sub_category_id.length > 0 && search) {
      count = await ContentArchiveVideo.countDocuments({
        title: { $regex: ".*" + search + ".*", $options: "i" },
        subcategory: { $in: sub_category_id },
        isDelete: false,
        uploadstatus: { $ne: "inprocess" },
      });
    } else if (sub_category_id.length > 0) {
      count = await ContentArchiveVideo.countDocuments({
        subcategory: { $in: sub_category_id },
        isDelete: false,
        uploadstatus: { $ne: "inprocess" },
      });
    } else if (search) {
      count = await ContentArchiveVideo.countDocuments({
        title: { $regex: ".*" + search + ".*", $options: "i" },
        isDelete: false,
        uploadstatus: { $ne: "inprocess" },
      });
    } else {
      count = await ContentArchiveVideo.countDocuments({
        isDelete: false,
        uploadstatus: { $ne: "inprocess" },
      });
    }

    var arr = [];
    for (var i = 0; i < data.length; i++) {
      var url = s3.getSignedUrl("getObject", {
        Bucket: "arn:aws:s3:us-east-2:496737174815:accesspoint/accessforapp",
        Key: data[i].video,
        Expires: 100000,
      });
      arr.push({ ...data[i], video: url });
    }
    data = arr;

    var mobile_desc = data?.map(async (item, index) => {
      let mobile_description = "";
      if (item.description !== undefined) {
        let without_html_description = item.description.replace(/&amp;/g, "&");
        without_html_description = item.description.replace(/(<([^>]+)>)/g, "");
        without_html_description = without_html_description.replace(
          /(\r\n|\n|\r)/gm,
          ""
        );
        mobile_description = without_html_description.substring(0, 600);
      }
      item.mobile_description = mobile_description.trim();
    });
    await Promise.all([...mobile_desc]);

    return res.status(200).json({
      status: true,
      message: `List of Content Archive Video.`,
      data: [
        {
          videos: data,
          currentPage: req.query.page,
          totalPages: Math.ceil(count / limit),
          totalVideos: count,
        },
      ],
    });
  } catch (error) {
    return res.status(200).json({ status: false, message: `${error.message}` });
  }
};

exports.getVideoBySubCateFilterSort = async (req, res) => {
  try {
    const page = parseInt(req.query.page);
    const limit = parseInt(req.query.limit);
    const skip = (page - 1) * limit;

    var sub_category_id = [];
    if (req.query.sub_category_id) {
      sub_category_id = [ObjectId(req.query.sub_category_id)];
    }

    var search = "";
    if (req.query.search) {
      search = req.query.search;
    }

    var sorting = "h2l";
    if (req.query.sort) {
      sorting = req.query.sort;
    }

    const filter = req.query.filter;
    const authUser = req.authUserId;
    const userdata = await User.findById(authUser);
    var sort = { createdAt: -1 };
    var match = {
      isDelete: false,
      uploadstatus: { $ne: "inprocess" },
    };

    if (sub_category_id.length > 0 && search) {
      match = {
        title: { $regex: ".*" + search + ".*", $options: "i" },
        subcategory: { $in: sub_category_id },
        isDelete: false,
        uploadstatus: { $ne: "inprocess" },
      };
    } else if (sub_category_id.length > 0) {
      match = {
        subcategory: { $in: sub_category_id },
        isDelete: false,
        uploadstatus: { $ne: "inprocess" },
      };
    } else if (search) {
      match = {
        title: { $regex: ".*" + search + ".*", $options: "i" },
        isDelete: false,
        uploadstatus: { $ne: "inprocess" },
      };
    } else if (sub_category_id.length > 0) {
      match = {
        subcategory: { $in: sub_category_id },
        isDelete: false,
        uploadstatus: { $ne: "inprocess" },
      };
    } else if (search) {
      match = {
        title: { $regex: ".*" + search + ".*", $options: "i" },
        isDelete: false,
        uploadstatus: { $ne: "inprocess" },
      };
    }

    if (filter === "popular" && sorting === "h2l") {
      sort = { viewsCount: -1 };
    } else if (filter === "comment" && sorting === "h2l") {
      sort = { commentsCount: -1 };
    } else if (filter === "popular" && sorting === "l2h") {
      sort = { viewsCount: 1 };
    } else if (filter === "comment" && sorting === "l2h") {
      sort = { commentsCount: 1 };
    }

    var data = await ContentArchiveVideo.aggregate([
      {
        $match: match,
      },
      {
        $lookup: {
          from: "contentarchive_categories",
          let: { contentarchive_categories_id: "$categories" },
          pipeline: [
            {
              $match: {
                $expr: {
                  $in: ["$_id", "$$contentarchive_categories_id"],
                },
              },
            },
            { $project: { name: 1 } },
          ],
          as: "categories",
        },
      },
      {
        $lookup: {
          from: "contentarchive_subcategories",
          let: { contentarchive_subcategory_id: "$subcategory" },
          pipeline: [
            {
              $match: {
                $expr: {
                  $in: ["$_id", "$$contentarchive_subcategory_id"],
                },
              },
            },
            { $project: { name: 1 } },
          ],
          as: "subcategory",
        },
      },
      {
        $lookup: {
          from: "groups",
          let: { suggestion_id: "$restrictedAccessGroupId" },
          pipeline: [
            {
              $match: {
                $expr: {
                  $in: ["$_id", "$$suggestion_id"],
                },
              },
            },
            { $project: { groupTitle: 1 } },
          ],
          as: "group_ids",
        },
      },
      {
        $addFields: {
          viewsCount: {
            $cond: {
              if: { $isArray: "$views" },
              then: { $add: [{ $size: "$views" }, "$starting_view_cnt"] },
              else: "$starting_view_cnt",
            },
          },
        },
      },
      {
        $addFields: {
          commentsCount: {
            $cond: {
              if: { $isArray: "$comments" },
              then: { $size: "$comments" },
              else: 0,
            },
          },
        },
      },
      { $sort: sort },
      { $skip: skip },
      { $limit: limit },
      {
        $project: {
          _id: 1,
          title: 1,
          video: 1,
          description: 1,
          thumbnail: 1,
          createdAt: 1,
          viewsCount: 1,
          commentsCount: 1,
          duration: 1,
          categories: 1,
          views: 1,
          likes: 1,
          user_video_pause: 1,
        },
      },
    ]);

    var count;
    if (sub_category_id.length > 0 && search) {
      count = await ContentArchiveVideo.countDocuments({
        title: { $regex: ".*" + search + ".*", $options: "i" },
        subcategory: { $in: sub_category_id },
        isDelete: false,
        uploadstatus: { $ne: "inprocess" },
      });
    } else if (sub_category_id.length > 0) {
      count = await ContentArchiveVideo.countDocuments({
        subcategory: { $in: sub_category_id },
        isDelete: false,
        uploadstatus: { $ne: "inprocess" },
      });
    } else if (search) {
      count = await ContentArchiveVideo.countDocuments({
        title: { $regex: ".*" + search + ".*", $options: "i" },
        isDelete: false,
        uploadstatus: { $ne: "inprocess" },
      });
    } else if (sub_category_id.length > 0 && search) {
      count = await ContentArchiveVideo.countDocuments({
        title: { $regex: ".*" + search + ".*", $options: "i" },
        subcategory: { $in: sub_category_id },
        isDelete: false,
        uploadstatus: { $ne: "inprocess" },
      });
    } else if (sub_category_id.length > 0) {
      count = await ContentArchiveVideo.countDocuments({
        subcategory: { $in: sub_category_id },
        isDelete: false,
        uploadstatus: { $ne: "inprocess" },
      });
    } else if (search) {
      count = await ContentArchiveVideo.countDocuments({
        title: { $regex: ".*" + search + ".*", $options: "i" },
        isDelete: false,
        uploadstatus: { $ne: "inprocess" },
      });
    } else {
      count = await ContentArchiveVideo.countDocuments({
        isDelete: false,
        uploadstatus: { $ne: "inprocess" },
      });
    }

    var arr = [];
    for (var i = 0; i < data.length; i++) {
      var url = s3.getSignedUrl("getObject", {
        Bucket: "arn:aws:s3:us-east-2:496737174815:accesspoint/accessforapp",
        Key: data[i].video,
        Expires: 100000,
      });
      arr.push({ ...data[i], video: url });
    }
    data = arr;

    var mobile_desc = data?.map(async (item, index) => {
      let mobile_description = "";
      if (item.description !== undefined) {
        let without_html_description = item.description.replace(/&amp;/g, "&");
        without_html_description = item.description.replace(/(<([^>]+)>)/g, "");
        without_html_description = without_html_description.replace(
          /(\r\n|\n|\r)/gm,
          ""
        );
        mobile_description = without_html_description.substring(0, 600);
      }
      item.mobile_description = mobile_description.trim();
    });
    await Promise.all([...mobile_desc]);

    return res.status(200).json({
      status: true,
      message: `List of Content Archive Video.`,
      data: [
        {
          videos: data,
          currentPage: req.query.page,
          totalPages: Math.ceil(count / limit),
          totalVideos: count,
        },
      ],
    });
  } catch (error) {
    return res.status(200).json({ status: false, message: `${error.message}` });
  }
};

exports.getVideoBySearchFilter = async (req, res) => {
  try {
    const page = parseInt(req.query.page);
    const limit = parseInt(req.query.limit);
    const skip = (page - 1) * limit;
    var search = "";
    if (req.query.search) {
      search = req.query.search;
    }
    const filter = req.query.filter;
    const authUser = req.authUserId;
    const currentEdgeId = req.currentEdge._id;

    const userdata = await User.findById(authUser);
    var sort = { createdAt: -1 };
    let ruleCondition = await userAccessRulesCommonCondition({ userId: authUser, relation_id: req.relation_id });
  
    var match = {
      isDelete: false,
      uploadstatus: { $ne: "inprocess" },
      ...ruleCondition,
      relation_id: ObjectId(req.relation_id),
    };
    if (search) {
      match = {
        ...match,
        $or: [
          { title: { $regex: ".*" + search + ".*", $options: "i" } },
          { description: { $regex: ".*" + search + ".*", $options: "i" } },
          { 'tagData.name': { $regex: ".*" + search + ".*", $options: "i" } },
        ]
      };
    }

    if (filter === "recent") {
      sort = { createdAt: -1, updatedAt: -1 };
    } else if (filter === "popular") {
      sort = { viewsCount: -1 };
    } else if (filter === "comment") {
      sort = { commentsCount: -1 };
    }

    const aggregatePipeline = [
      {
        $lookup: {
          from: "contentarchive_tags",
          localField: "tag",
          foreignField: "_id",
          pipeline: [
            {
              $match: {
                isDelete: false,
                name: { $regex: ".*" + search + ".*", $options: "i" } ,
              },
            },
          ],
          as: "tagData",
        },
      },
      {
        $match: match,
      },
      {
        $match: {...ruleCondition},
      },
      {
        $lookup: {
          from: "contentarchive_categories",
          let: { contentarchive_categories_id: "$categories" },
          pipeline: [
            {
              $match: {
                $expr: {
                  $in: ["$_id", "$$contentarchive_categories_id"],
                },
              },
            },
            { $project: { name: 1 } },
          ],
          as: "categories",
        },
      },
      {
        $lookup: {
          from: "contentarchive_subcategories",
          localField: "subcategory",
          foreignField: "_id",
          pipeline: [
            {
              $match: {
                isDelete: false,
              },
            },
          ],
          as: "subcategory",
        },
      },
      {
        $lookup: {
          from: "groups",
          let: { suggestion_id: "$restrictedAccessGroupId" },
          pipeline: [
            {
              $match: {
                $expr: {
                  $in: ["$_id", "$$suggestion_id"],
                },
              },
            },
            { $project: { groupTitle: 1 } },
          ],
          as: "group_ids",
        },
      },
      {
        $addFields: {
          viewsCount: {
            $cond: {
              if: { $isArray: "$views" },
              then: { $add: [{ $size: "$views" }, "$starting_view_cnt"] },
              else: "$starting_view_cnt",
            },
          },
        },
      },
      {
        $addFields: {
          commentsCount: {
            $cond: {
              if: { $isArray: "$comments" },
              then: { $size: "$comments" },
              else: 0,
            },
          },
        },
      },
      {
        $addFields: {
          priority: {
            $switch: {
              branches: [
                {
                  case: { $regexMatch: { input: "$title", regex: "^" + search, options: "i" } },
                  then: 1,
                },
                {
                  case: {
                    $and: [
                      { $isArray: "$tagData" },
                      { $gt: [{ $size: "$tagData" }, 0] }, // Ensure the array has elements
                      { $regexMatch: { input: { $arrayElemAt: ["$tagData.name", 0] }, regex: "^" + search, options: "i" } }
                    ]
                  },
                  then: 2,
                },
                {
                  case: { $regexMatch: { input: "$title", regex: ".*" + search + ".*", options: "i" } },
                  then: 3,
                },
                {
                  case: { $regexMatch: { input: "$description", regex: ".*" + search + ".*", options: "i" } },
                  then: 4,
                },
              ],
              default: 5, // Default value if no match
            },
          },
        },
      },
      {
          $sort: {
            ...(search ? { priority: 1 } : sort ),
          },
      },
      // { $sort: sort },
      {
        $project: {
          _id: 1,
          title: 1,
          video: 1,
          description: 1,
          thumbnail: 1,
          createdAt: 1,
          viewsCount: 1,
          commentsCount: 1,
          duration: 1,
          categories: 1,
          views: 1,
          likes: 1,
          user_video_pause: 1,
        },
      },
    ]
    var data = await ContentArchiveVideo.aggregate([
      ...aggregatePipeline,
      { $skip: skip },
      { $limit: limit },
    ]);
    var allData = await ContentArchiveVideo.aggregate([
      ...aggregatePipeline,
    ]);

    var count = allData.length;

    var arr = [];
    for (var i = 0; i < data.length; i++) {
      var url = await s3.getSignedUrl("getObject", {
        Bucket: "arn:aws:s3:us-east-2:496737174815:accesspoint/accessforapp",
        Key: data[i].video,
        Expires: 100000,
      });
      arr.push({ ...data[i], video: url });
    }
    data = arr;

    var mobile_desc = data?.map(async (item, index) => {
      let mobile_description = "";
      if (item.description !== undefined) {
        let without_html_description = item.description.replace(/&amp;/g, "&");
        without_html_description = item.description.replace(/(<([^>]+)>)/g, "");
        without_html_description = without_html_description.replace(
          /(\r\n|\n|\r)/gm,
          ""
        );
        mobile_description = without_html_description.substring(0, 600);
      }
      item.mobile_description = mobile_description.trim();
    });
    await Promise.all([...mobile_desc]);

    return res.status(200).json({
      status: true,
      message: `List of Content Archive Video.`,
      data: [
        {
          videos: data,
          totalPages: Math.ceil(count / limit),
          currentPage: req.query.page,
          totalVideos: count,
        },
      ],
    });
  } catch (error) {
    return res.status(200).json({ status: false, message: `${error.message}` });
  }
};

exports.addAndUpdateVideoHistoryById = async (req, res) => {
  try {
    const userData = await User.findById(req.authUserId);
    const body = req.body;
    const history_date = new Date();

    var video_history = [];

    const videoHistoryData = {
      video_id: new ObjectId(body.videoId),
      history_date: history_date,
    };

    const alreadyAdded = await User.findOne(
      {
        _id: userData._id,
        video_history_data: {
          $elemMatch: { video_id: new ObjectId(body.videoId) },
        },
      },
      { video_history_data: 1 }
    );

    if (alreadyAdded !== null) {
      video_history = await User.findOneAndUpdate(
        {
          _id: userData._id,
          video_history_data: {
            $elemMatch: { video_id: new ObjectId(body.videoId) },
          },
        },
        { $set: { "video_history_data.$.history_date": history_date } },
        { new: true }
      );
    } else {
      video_history = await User.findOneAndUpdate(
        { _id: userData._id },
        { $push: { video_history_data: videoHistoryData } },
        { new: true }
      );
    }

    if (video_history) {
      res.status(200).json({
        status: true,
        message: "User view history updated successfully!",
        data: video_history,
      });
    }
  } catch (error) {
    return res.status(200).json({ status: false, message: `${error.message}` });
  }
};

exports.getVideoHistoryByUser = async (req, res) => {
  try {
    const page = parseInt(req.query.page);
    const limit = parseInt(req.query.limit);
    const skip = (page - 1) * limit;
    var count = 0;

    var userData = await User.aggregate([
      {
        $match: {
          _id: ObjectId(req.authUserId),
        },
      },
      { $unwind: "$video_history_data" },
      { $sort: { "video_history_data.history_date": -1 } },
      {
        $lookup: {
          from: "contentarchive_videos",
          let: {
            contentarchive_id: "$video_history_data.video_id",
          },
          pipeline: [
            {
              $match: {
                $expr: {
                  $eq: ["$_id", "$$contentarchive_id"],
                },
                relation_id: ObjectId(req.relation_id),
              },
            },
            ...(true ? [
              {
                $match: await userAccessRulesCommonCondition({ userId: req.authUserId, relation_id: req.relation_id }),
              }
            ] : []),
            {
              $lookup: {
                from: "contentarchive_categories",
                let: { contentarchive_categories_id: "$categories" },
                pipeline: [
                  {
                    $match: {
                      $expr: {
                        $in: ["$_id", "$$contentarchive_categories_id"],
                      },
                    },
                  },
                  { $project: { name: 1 } },
                ],
                as: "categories",
              },
            },
            {
              $lookup: {
                from: "contentarchive_subcategories",
                localField: "subcategory",
                foreignField: "_id",
                pipeline: [
                  {
                    $match: {
                      isDelete: false,
                    },
                  },
                ],
                as: "subcategory",
              },
            },
            {
              $lookup: {
                from: "groups",
                let: { suggestion_id: "$restrictedAccessGroupId" },
                pipeline: [
                  {
                    $match: {
                      $expr: {
                        $in: ["$_id", "$$suggestion_id"],
                      },
                    },
                  },
                  { $project: { groupTitle: 1 } },
                ],
                as: "group_ids",
              },
            },
            {
              $addFields: {
                viewsCount: {
                  $cond: {
                    if: { $isArray: "$views" },
                    then: { $add: [{ $size: "$views" }, "$starting_view_cnt"] },
                    else: "$starting_view_cnt",
                  },
                },
              },
            },
            {
              $addFields: {
                commentsCount: {
                  $cond: {
                    if: { $isArray: "$comments" },
                    then: { $size: "$comments" },
                    else: 0,
                  },
                },
              },
            },
            {
              $project: {
                _id: 1,
                title: 1,
                video: 1,
                description: 1,
                thumbnail: 1,
                createdAt: 1,
                viewsCount: 1,
                commentsCount: 1,
                duration: 1,
                categories: 1,
                views: 1,
                likes: 1,
                user_video_pause: 1,
              },
            },
          ],
          as: "archive_video",
        },
      },
      { $unwind: "$archive_video" },
      { $skip: skip },
      { $limit: limit },
      {
        $set: {
          "video_history_data.archive_video": "$archive_video",
        },
      },
      {
        $group: {
          _id: "$_id",
          video_history_data: { $push: "$video_history_data" },
        },
      },
    ]);

    const video_history_data = await User.aggregate([
      {
        $match: {
          _id: ObjectId(req.authUserId),
        },
      },
      { $unwind: "$video_history_data" },
      { $sort: { "video_history_data.history_date": -1 } },
      {
        $lookup: {
          from: "contentarchive_videos",
          let: {
            contentarchive_id: "$video_history_data.video_id",
          },
          pipeline: [
            {
              $match: {
                $expr: {
                  $eq: ["$_id", "$$contentarchive_id"],
                },
                relation_id: ObjectId(req.relation_id),
              },
            },
            ...(true ? [
              {
                $match: await userAccessRulesCommonCondition({ userId: req.authUserId, relation_id: req.relation_id }),
              }
            ] : []),
          ],
          as: "archive_video",
        },
      },
      {
        $set: {
          "video_history_data.archive_video": "$archive_video",
        },
      },
      { $unwind: "$archive_video" },
      {
        $group: {
          _id: "$_id",
          video_history_data: { $push: "$video_history_data" },
        },
      },
    ]);

    if (userData.length !== 0) {
      count = video_history_data[0].video_history_data.length;
    }

    if (userData.length !== 0) {
      var arr = [];
      for (var i = 0; i < userData[0].video_history_data.length; i++) {
        var url = s3.getSignedUrl("getObject", {
          Bucket: "arn:aws:s3:us-east-2:496737174815:accesspoint/accessforapp",
          Key: userData[0].video_history_data[i].archive_video.video,
          Expires: 100000,
        });
        arr.push({
          ...userData[0].video_history_data[i],
          archive_video: {
            ...userData[0].video_history_data[i].archive_video,
            video: url,
          },
        });
      }
      userData = arr;

      var mobile_desc = userData?.map(async (item, index) => {
        let mobile_description = "";
        if (item.archive_video.description !== undefined) {
          let without_html_description = item.archive_video.description.replace(
            /&amp;/g,
            "&"
          );
          without_html_description = item.archive_video.description.replace(
            /(<([^>]+)>)/g,
            ""
          );
          without_html_description = without_html_description.replace(
            /(\r\n|\n|\r)/gm,
            ""
          );
          mobile_description = without_html_description.substring(0, 600);
        }
        item.archive_video.mobile_description = mobile_description.trim();
      });
      await Promise.all([...mobile_desc]);
    }

    if (userData.length !== 0) {
      return res.status(200).json({
        status: true,
        message: `History List of Content Archive Video retrive.`,
        data: [
          {
            video_history_data: userData,
            totalPages: Math.ceil(count / limit),
            currentPage: req.query.page,
            totalVideos: count,
          },
        ],
      });
    } else {
      return res.status(200).json({
        status: true,
        message: `History List of Content Archive Video not found!`,
        data: [
          {
            video_history_data: [],
            totalPages: Math.ceil(count / limit),
            currentPage: req.query.page,
            totalVideos: count,
          },
        ],
      });
    }
  } catch (error) {
    return res.status(200).json({ status: false, message: `${error.message}` });
  }
};

exports.removeVideoHistoryById = async (req, res) => {
  try {
    const userData = await User.findById(req.authUserId);
    const body = req.body;

    const alreadyAdded = await User.findOne(
      {
        _id: userData._id,
        video_history_data: { $elemMatch: { _id: new ObjectId(body._id) } },
      },
      { video_history_data: 1 }
    );
    if (alreadyAdded !== null) {
      const new_video_history = alreadyAdded?.video_history_data?.filter(
        (item) => {
          return item._id.toString() !== body._id;
        }
      );

      const video_history = await User.findOneAndUpdate(
        { _id: userData._id },
        { video_history_data: new_video_history },
        { new: true }
      );

      if (video_history) {
        return res.status(200).json({
          status: true,
          message: "User view history updated successfully!",
          data: video_history,
        });
      }
    } else {
      return res.status(404).json({
        status: false,
        message: "User view history not found!",
        data: [],
      });
    }
  } catch (error) {
    return res.status(200).json({ status: false, message: `${error.message}` });
  }
};

exports.removeAllVideoHistory = async (req, res) => {
  try {
    const userData = await User.findById(req.authUserId);
    const new_video_history = [];

    const video_history = await User.findOneAndUpdate(
      { _id: userData._id },
      { video_history_data: new_video_history },
      { new: true }
    );
    res.status(200).json({
      status: true,
      message: "User view history updated successfully!",
      data: video_history,
    });
  } catch (error) {
    return res.status(200).json({ status: false, message: `${error.message}` });
  }
};

/** code by SJ end **/

exports.getCategorybyId = async (req, res) => {
  try {
    const { id } = req.params;
    if (ObjectId.isValid(id)) {
      const data = await ContentCategory.findOne({
        _id: id,
        isDelete: false,
        relation_id: ObjectId(req.relation_id),
      }).select("-__v -createdAt -updatedAt");

      return res
        .status(200)
        .json({ status: true, message: `Category data.`, data: data });
    } else {
      return res
        .status(200)
        .json({ status: false, message: `Id is invalid`, data: [] });
    }
  } catch (error) {
    return res.status(200).json({ status: false, message: `${error.message}` });
  }
};

exports.deleteCategory = async (req, res) => {
  try {
    const { id } = req.params;
    const updated_data = await ContentCategory.findByIdAndUpdate(
      id,
      { isDelete: true },
      { new: true }
    ).select("-__v -createdAt -updatedAt");
    if (updated_data && updated_data.subcategory) {
      await contentSubCategory.deleteMany({ _id: { $in: updated_data.subcategory } });
      contentSubCategory.remove({
        _id: { $in: [...updated_data.subcategory] },
      });
    }
    return res
      .status(200)
      .json({ status: true, message: `Category deleted.`, data: updated_data });
  } catch (error) {
    return res.status(200).json({ status: false, message: `${error.message}` });
  }
};

// delete Video Category and assign another category api
exports.deleteCategoryV2 = async (req, res) => {
  try {
    //category is soft delete and subcategory hard delete
    const { deleteVideoCategoryId, reassignVideoCategoryId } = req.body;

    if (deleteVideoCategoryId) {
      const existCategory = await ContentCategory.findById(deleteVideoCategoryId);
      if (!existCategory)
        return res
          .status(200)
          .send({ status: false, message: `Category not found!` });

      const alreadyAssign = await ContentArchiveVideo
        .find(
          {
            categories: { $in: [ObjectId(deleteVideoCategoryId)] },
            relation_id: ObjectId(req.relation_id),
            isDelete: false,
          },
          { _id: 1 }
        )
        .lean();

      // delete category
      const deleteCategory = await ContentCategory
        .findByIdAndUpdate(
          deleteVideoCategoryId,
          { isDelete: true },
          { new: true }
        )
        .select("-__v -createdAt -updatedAt");

      if (deleteCategory) {
        // delete subcategory
        if (deleteCategory.subcategory) {
          await contentSubCategory.deleteMany({
            _id: { $in: [...deleteCategory.subcategory] },
          });
          contentSubCategory.remove({
            _id: { $in: [...deleteCategory.subcategory] },
          });
        }

        if (alreadyAssign.length > 0) {
          let reassignVideoCategory = alreadyAssign.map(
            async (item, i) => {
              await ContentArchiveVideo
                .findByIdAndUpdate(
                  ObjectId(item._id),
                  { $pull: { categories: ObjectId(deleteVideoCategoryId) } },
                  { new: true }
                )
                .select("_id");

                // reassign Category
              if (reassignVideoCategoryId) {
                await ContentArchiveVideo
                  .findOneAndUpdate(
                    {
                      _id: item._id,
                      categories: { $nin: reassignVideoCategoryId },
                    },
                    { $push: { categories: ObjectId(reassignVideoCategoryId) } },
                    { new: true }
                  )
                  .select("_id");
              }
            }
          );
          await Promise.all([...reassignVideoCategory]);
        }
        return res
          .status(200)
          .send({
            status: true,
            message: `Category delete successfully.`,
            data: deleteCategory,
          });
      } else {
        return res.status(200).json({
          status: false,
          message: `Something went wrong while deleting video Category!`,
        });
      }
    } else {
      return res
        .status(200)
        .json({ status: false, message: `Input parameters are missings!` });
    }
  } catch (error) {
    return res.status(500).json({ status: false, message: `${error.message}` });
  }
};

exports.reassignCategories = async (req, res) => {
  try {

    const categoryId = req.params.id;
    var match = {
      isDelete: false,
      relation_id: ObjectId(req.relation_id),
      _id: { $ne: ObjectId(categoryId) },
    };

    //list category data of event
    const eventCategoryData = await ContentCategory.aggregate([
      {
        $match: match,
      },
      {
        $project: {
          _id: 1,
          name: 1,
        },
      },
    ]);

    return res.status(200).send({
      status: true,
      message: `List of video categories.`,
      data: eventCategoryData
    });
  } catch (error) {
    return res.status(500).json({ status: false, message: `${error.message}` });
  }
};

exports.editCategory = async (req, res) => {
  try {
    const { id } = req.params;
    const checkname = await ContentCategory.find({
      _id: { $ne: ObjectId(id) },
      relation_id: ObjectId(req.relation_id),
      name: req.body.name,
      isDelete: false,
    });

    if (checkname && checkname.length > 0) {
      return res
        .status(200)
        .json({ status: false, message: `Category name must be unique.` });
    }

    const addSubCat =
      req.body.subcategory !== undefined &&
        req.body.subcategory !== null &&
        req.body.subcategory.length > 0
        ? req.body.subcategory.trim().split(",")
        : [];

    var subcategory_data =
      req.body.subcategory !== undefined &&
      req.body.subcategory !== null &&
      req.body.subcategory.length > 0 &&
      addSubCat.map(async (item, index) => {
        if (await contentSubCategory.findOne({ name: item, relation_id: ObjectId(req.relation_id), isDelete: false }))
          return res.status(200).json({
            status: false,
            message: `Sub Category name must be unique.`,
          });

        const newSubEntry = new contentSubCategory({ name: item, relation_id: ObjectId(req.relation_id),});
        const subResult = await newSubEntry.save();
        await ContentCategory.findByIdAndUpdate(
          id,
          {
            $push: { subcategory: subResult._id },
          },
          { new: true }
        );
      });

    if (subcategory_data) await Promise.all([...subcategory_data]);

    const deltSubCat =
      req.body.deleteSubCategory !== undefined &&
        req.body.deleteSubCategory !== null &&
        req.body.deleteSubCategory.length > 0
        ? req.body.deleteSubCategory.trim().split(",")
        : [];

    var deleteSubCategory =
      req.body.deleteSubCategory !== undefined &&
      req.body.deleteSubCategory !== null &&
      req.body.deleteSubCategory.length > 0 &&
      deltSubCat.map(async (ditem, index) => {
        await ContentCategory.findByIdAndUpdate(
          id,
          {
            $pull: { subcategory: new ObjectId(ditem) },
          },
          { new: true }
        );

        await contentSubCategory.findByIdAndUpdate(
          { _id: new ObjectId(ditem) },
          { isDelete: true },
          { new: true }
        );
      });
    if (deleteSubCategory) await Promise.all([...deleteSubCategory]);

    const catExists = await ContentCategory.findById(ObjectId(id));

    if(req.body.categoryImage){
      if (
        catExists &&
        catExists.categoryImage !== undefined
      )
        deleteImage(catExists.categoryImage);
    }

    const updated_data = await ContentCategory.findByIdAndUpdate(
      id,
      {
        name: req.body.name,
        categoryImage: req.body.categoryImage
          ? req.categoryImage
          : catExists.categoryImage,
      },
      { new: true }
    );

    return res.status(200).json({
      status: true,
      message: `Category updated successfully.`,
      data: updated_data,
    });
  } catch (error) {
    return res.status(200).json({ status: false, message: `${error.message}` });
  }
};

exports.createVideo = async (req, res) => {
  try {
    const { video_v, thumbnail, files_v, partners_v } = req;
    ProcessStates = 5;
    var url = s3.getSignedUrl("getObject", {
      Bucket: "arn:aws:s3:us-east-2:496737174815:accesspoint/accessforapp",
      Key: video_v,
      Expires: 100000,
    });
    const total_duration = await getVideoDuration(url);
    var dateObj = new Date(total_duration * 1000);
    var hours = dateObj.getUTCHours();
    var minutes = dateObj.getUTCMinutes();
    var seconds = dateObj.getSeconds();

    var duration =
      (hours.toString().padStart(2, "0") !== "00"
        ? hours.toString() + ":"
        : "") +
      (hours.toString().padStart(2, "0") !== "00"
        ? minutes.toString().padStart(2, "0") + ":"
        : minutes.toString() + ":") +
      seconds.toString().padStart(2, "0");
    const new_partners = [];
    var prtn_index = 0;

    req.body.partners?.map((partner, i) => {
      const obj = {};
      obj.name = partner.name;
      if (partner.havelogo === "true") {
        obj.logo = partners_v[prtn_index];
        prtn_index++;
      } else {
        obj.logo = "";
      }
      obj.url = partner.url;
      new_partners.push(obj);
    });

    const new_files = [];
    req.body.c_files?.map((file, i) => {
      const obj = {};
      obj.name = file.name;
      obj.url = files_v[i];
      new_files.push(obj);
    });
    const body = req.body;

    // check valid or not
    let checkIds =  await checkValidIds({
    restrictedAccessGroupId:req.body.restrictedAccessGroupId,
    restrictedAccessMembershipPlanId:req.body.restrictedAccessMembershipPlanId,
    restrictedAccessUserId:req.body.restrictedAccessUserId,
    restrictedAccessTagId:req.body.restrictedAccessTagId,
    restrictedAccessEventId:req.body.restrictedAccessEventId,
    restrictedAccessTierId: req.body.restrictedAccessTierId,
    relation_id: req.relation_id
    });

    if(checkIds.status === false)
    { return res.status(200).json({ status: false, message: checkIds.msg }); }

    if (body.categories?.length > 0) {
      const cat_data = await ContentCategory.countDocuments({
        _id: { $in: body.categories },
        isDelete: false,
      });
      if (cat_data !== body.categories.length)
        return res.status(200).json({
          status: false,
          message: `Something wrong, Invalid category.`,
        });
    }
    if (body.subcategories?.length > 0) {
      const cat_data = await contentSubCategory.countDocuments({
        _id: { $in: body.subcategories },
        isDelete: false,
      });
      if (cat_data !== body.subcategories.length)
        return res.status(200).json({
          status: false,
          message: `Something wrong, Invalid sub category.`,
        });
    }
    let description = `<div "font-family: 'Muller';">${body.description}</div>`;
    const newentry = new ContentArchiveVideo({
      video: video_v,
      thumbnail: thumbnail ?? "",
      title: body.title,
      description: description,
      categories: body.categories,
      subcategory: body.subcategories,
      speaker: body.speaker,
      tag: body.tag,
      clif_notes_title: body.clif_notes_title,
      clif_notes: body.clif_notes,
      files: new_files,
      relevant_partners: new_partners,
      eventIds: body.eventIds,
      starting_view_cnt: body.starting_view_cnt,
      duration: duration,
      uploadstatus: "inprocess",
      createdAt: body.upload_date, 
      restrictionAccess: body.restrictionAccess,
      restrictedAccessGroupId: body.restrictedAccessGroupId,
      restrictedAccessMembershipPlanId: body.restrictedAccessMembershipPlanId,
      restrictedAccessUserId: body.restrictedAccessUserId,
      restrictedAccessEventId: body.restrictedAccessEventId,
      restrictedAccessTagId: body.restrictedAccessTagId,
      restrictedAccessTierId: body.restrictedAccessTierId,
      relation_id: ObjectId(req.relation_id),
    });

    const data = await newentry.save();
    if (data) {
      let newVideoId = [data._id];

      if (req.body.tag) {
        const tags =
          typeof req.body.tag === "string"
            ? [ObjectId(req.body.tag)]
            : req.body.tag.map((tagId) => ObjectId(tagId));
        const partnerList = await Partner.find(
          { isDelete: false, tag: { $in: tags } },
          { videoIds: 1 }
        );
        if (partnerList.length > 0) {
          let resPartnerList = partnerList.map(async (partner) => {
            let videosWithOrder = [];
            let existingVideoIds = partner.videoIds;
            const tagVideosArr = [
              ...partner.videoIds.map((video) => video.id),
              ...newVideoId,
            ];

            let sortingOption = partner.relatedVideoSortOption
              ? partner.relatedVideoSortOption
              : "default";

            switch (sortingOption) {
              case "custom":
                relatedVideos = tagVideosArr;
                break;
              case "views":
                relatedVideos = await ContentArchiveVideo.aggregate([
                  {
                    $match: {
                      _id: { $in: tagVideosArr },
                    },
                  },
                  {
                    $project: {
                      _id: 1,
                      views: {
                        $sum: [
                          {
                            $cond: [
                              { $ifNull: ["$starting_view_cnt", false] },
                              "$starting_view_cnt",
                              0,
                            ],
                          },
                          {
                            $cond: [
                              { $not: ["views.0"] },
                              0,
                              { $size: "$views" },
                            ],
                          },
                        ],
                      },
                    },
                  },
                  {
                    $sort: { views: -1 },
                  },
                ]);
                break;
              case "likes":
                relatedVideos = await ContentArchiveVideo.aggregate([
                  {
                    $match: {
                      _id: { $in: tagVideosArr },
                    },
                  },
                  {
                    $project: {
                      _id: 1,
                      likes: {
                        $cond: [{ $not: ["likes.0"] }, 0, { $size: "$likes" }],
                      },
                    },
                  },
                  {
                    $sort: { likes: -1 },
                  },
                ]);
                break;

              case "comments":
                relatedVideos = await ContentArchiveVideo.aggregate([
                  {
                    $match: {
                      _id: { $in: tagVideosArr },
                    },
                  },
                  {
                    $project: {
                      _id: 1,
                      comments: {
                        $cond: [
                          { $not: ["comments.0"] },
                          0,
                          { $size: "$comments" },
                        ],
                      },
                    },
                  },
                  {
                    $sort: { comments: -1 },
                  },
                ]);
                break;

              case "default":
                relatedVideos = await ContentArchiveVideo.aggregate([
                  {
                    $match: {
                      _id: { $in: tagVideosArr },
                    },
                  },
                  {
                    $project: {
                      _id: 1,
                      createdAt: 1,
                    },
                  },
                  {
                    $sort: { createdAt: -1 },
                  },
                ]);
                break;
              case "latest":
                relatedVideos = await ContentArchiveVideo.aggregate([
                  {
                    $match: {
                      _id: { $in: tagVideosArr },
                    },
                  },
                  {
                    $project: {
                      _id: 1,
                      createdAt: 1,
                    },
                  },
                  {
                    $sort: { createdAt: -1 },
                  },
                ]);
                break;
            }
            videosWithOrder = relatedVideos.map((videoId, index) => {
              if (videoId && typeof videoId === "object") {
                const singleExistsVideo = existingVideoIds.filter(
                  (existsVideo) => {
                    if (existsVideo !== null) {
                      return (
                        existsVideo.id.toString() === videoId._id.toString()
                      );
                    }
                  }
                );
                if (singleExistsVideo.length > 0) {
                  const obj = {
                    id: videoId,
                    order: index + 1,
                    status: singleExistsVideo[0].status,
                  };
                  return obj;
                } else {
                  const obj = { id: videoId, order: index + 1 };
                  return obj;
                }
              } else {
                const singleExistsVideo = existingVideoIds.filter(
                  (existsVideo) =>
                    existsVideo.id.toString() === videoId.toString()
                );
                if (singleExistsVideo.length > 0) {
                  const obj = {
                    id: videoId,
                    order: index + 1,
                    status: singleExistsVideo[0].status,
                  };
                  return obj;
                } else {
                  const obj = { id: videoId, order: index + 1 };
                  return obj;
                }
              }
            });

            const updatedPartnerDetails = await Partner.findByIdAndUpdate(
              partner._id,
              {
                videoIds: videosWithOrder,
              },
              { new: true }
            );
          });

          await Promise.all([...resPartnerList]);
        }
      }

      if (
        body.makeFeaturedCheckbox &&
        body.makeFeaturedCheckbox.toString() === "true"
      ) {
        const newNewsData = new adminNews({
          date: new Date(),
          publishDate: new Date(),
          publishOrHide: "publish",
          makeFeaturedCheckbox: false,
          newsType: "video",
          videoReferenceId: data._id,
          relation_id: ObjectId(req.relation_id), 
        });
        await newNewsData.save();
      }
    }
    return res.status(200).json({
      status: true,
      message: `Video added in Content archive library.`,
      data,
    });
  } catch (error) {
    console.log("🚀 ~ exports.createVideo= ~ error:", error);
    return res.status(200).json({ status: false, message: `${error.message}` });
  }
};

exports.CompressVideo = async (req, res) => {
  try {
    const resolutions_480 = await generate_video_resolution_480(req.body.video_v);
    const query = await ContentArchiveVideo.findByIdAndUpdate(
      req.body.id,
      { video: resolutions_480 && resolutions_480.length ? resolutions_480 : req.body.video_v, uploadstatus: "completed" },
      { runValidators: true, new: true, timestamps: false }
    );
    if (query)
      return res.status(200).json({
        status: true,
        message: `Video added in Content archive library.`,
        data: query,
      });
    else {
      if (resolutions_480) deleteImage(resolutions_480);
      return res.status(200).json({
        status: true,
        message: `Video deleted.`,
      });
    }
  } catch (error) {
    return res.status(200).json({ status: false, message: `${error.message}` });
  }
};

exports.editVideo = async (req, res) => {
  try {
    const {
      video_v,
      thumbnail,
      files_v,
      partners_v,
      upt_partners_v,
      upt_files_v,
    } = req;
    const body = req.body;

    const old_v_info = await ContentArchiveVideo.findOne({
      _id: body.id,
      isDelete: false,
    });

    if (!old_v_info)
      return res
        .status(200)
        .json({ status: false, message: "Can not found video with this id." });

    // check valid or not
    let checkIds =  await checkValidIds({
      restrictedAccessGroupId:req.body.restrictedAccessGroupId,
      restrictedAccessMembershipPlanId:req.body.restrictedAccessMembershipPlanId,
      restrictedAccessUserId:req.body.restrictedAccessUserId,
      restrictedAccessTagId:req.body.restrictedAccessTagId,
      restrictedAccessEventId:req.body.restrictedAccessEventId,
      restrictedAccessTierId: req.body.restrictedAccessTierId,
      relation_id: req.relation_id
      });
  
      if(checkIds.status === false)
      { return res.status(200).json({ status: false, message: checkIds.msg }); }
    if (body.categories?.length > 0) {
      const cat_data = await ContentCategory.countDocuments({
        _id: { $in: body.categories },
        isDelete: false,
      });

      if (cat_data !== body.categories?.length)
        return res.status(200).json({
          status: false,
          message: `Something wrong, Invalid category.`,
        });
    }
    if (body.subcategories?.length > 0) {
      const cat_data = await contentSubCategory.countDocuments({
        _id: { $in: body.subcategories },
        isDelete: false,
      });
      if (cat_data !== body.subcategories.length)
        return res.status(200).json({
          status: false,
          message: `Something wrong, Invalid sub category.`,
        });
    }
    var duration = old_v_info.duration;
    if (video_v) {
      var url = s3.getSignedUrl("getObject", {
        Bucket: "arn:aws:s3:us-east-2:496737174815:accesspoint/accessforapp",
        Key: video_v,
        Expires: 100000,
      });
      const total_duration = await getVideoDuration(url);
      var dateObj = new Date(total_duration * 1000);
      var hours = dateObj.getUTCHours();
      var minutes = dateObj.getUTCMinutes();
      var seconds = dateObj.getSeconds();
      duration =
        (hours.toString().padStart(2, "0") !== "00"
          ? hours.toString() + ":"
          : "") +
        (hours.toString().padStart(2, "0") !== "00"
          ? minutes.toString().padStart(2, "0") + ":"
          : minutes.toString() + ":") +
        seconds.toString().padStart(2, "0");
    }

    if (video_v) {
      await deleteImage(old_v_info.video);
      old_v_info.video_240 !== undefined && old_v_info.video_240.length > 0
        ? await deleteImage(old_v_info.video_240)
        : "";
      old_v_info.video_360 !== undefined && old_v_info.video_360.length > 0
        ? await deleteImage(old_v_info.video_360)
        : "";
      old_v_info.video_480 !== undefined && old_v_info.video_480.length > 0
        ? await deleteImage(old_v_info.video_480)
        : "";
      old_v_info.subtitle_file !== undefined &&
        old_v_info.subtitle_file.length > 0
        ? await deleteImage(old_v_info.subtitle_file)
        : "";
    }

    if (thumbnail && old_v_info.thumbnail)
      await deleteImage(old_v_info.thumbnail);

    var new_partners_arr = old_v_info.relevant_partners;

    if (body.partners?.length > 0) {
      var fI = 0;
      body.partners.map((item3, j) => {
        new_partners_arr.map((partner2, p) => {
          if (item3.id === partner2._id.toString()) {
            new_partners_arr[p]["name"] = item3.name;
            new_partners_arr[p]["url"] = item3.url;
            if (item3.alterlogo === "ok") {
              new_partners_arr[p]["logo"] = upt_partners_v[fI];
              fI++;
            }
          }
        });
      });
    }

    if (body.remove_partner?.length > 0) {
      var arr_ex = [];
      for (var i = 0; i < new_partners_arr.length; i++) {
        if (!body.remove_partner.includes(new_partners_arr[i]._id.toString()))
          arr_ex.push(new_partners_arr[i]);
      }
      new_partners_arr = arr_ex;
    }
    var prtn_index = 0;
    body.partners?.map((partner, i) => {
      const obj = {};
      obj.name = partner.name;
      if (partner.havelogo === "true") {
        obj.logo = partners_v[prtn_index];
        prtn_index++;
      } else {
        obj.logo = "";
      }
      obj.url = partner.url;
      new_partners_arr.push(obj);
    });

    var new_files_arr = old_v_info.files;
    if (body.update_file?.length > 0) {
      var fI = 0;
      body.update_file.map((item, j) => {
        new_files_arr.map((file2, f) => {
          if (item.id === file2._id.toString()) {
            if (item.name) {
              new_files_arr[f]["name"] = item.name;
            }
            if (item.alterurl === "ok") {
              new_files_arr[f]["url"] = upt_files_v[fI];
              fI++;
            }
          }
        });
      });
    }

    if (body.remove_files?.length > 0) {
      var arr_file = [];
      for (var i = 0; i < new_files_arr.length; i++) {
        if (!body.remove_files.includes(new_files_arr[i]._id.toString()))
          arr_file.push(new_files_arr[i]);
      }
      new_files_arr = arr_file;
    }

    body.c_files?.map((file, i) => {
      const obj = {};
      obj.name = file.name;
      obj.url = files_v[i];
      new_files_arr.push(obj);
    });

    let description = `<div "font-family: 'Muller';">${body.description}</div>`;

    const updated = {
      video: video_v ?? old_v_info.video,
      thumbnail:
        thumbnail !== undefined && thumbnail.length > 0
          ? thumbnail
          : old_v_info.thumbnail,
      title: body.title ?? old_v_info.title,
      description: description ?? old_v_info.description,
      categories: body.categories
        ? body.categories
        : body.empty_categories
          ? []
          : old_v_info.categories,
      subcategory: body.subcategories
        ? body.subcategories
        : body.empty_subcategories
          ? []
          : old_v_info.subcategory,
      speaker: body.speaker
        ? body.speaker
        : body.empty_speakers
          ? []
          : old_v_info.speaker,
      tag: body.tag ? body.tag : [],
      clif_notes_title: body.clif_notes_title ?? old_v_info.clif_notes_title,
      clif_notes: body.clif_notes ?? old_v_info.clif_notes,
      files: new_files_arr,
      relevant_partners: new_partners_arr,
      eventIds: body.eventIds
        ? body.eventIds
        : body.empty_event_ids
        ? []
        : old_v_info.eventIds,
      restrictionAccess: body.restrictionAccess ? body.restrictionAccess : "public",
      restrictedAccessGroupId: body.restrictedAccessGroupId 
      ? body.restrictedAccessGroupId 
      : body.empty_restrictedAccessGroupId 
      ? [] 
      : old_v_info.restrictedAccessGroupId,
      restrictedAccessMembershipPlanId: body.restrictedAccessMembershipPlanId ?  body.restrictedAccessMembershipPlanId : [],
      restrictedAccessUserId: body.restrictedAccessUserId ? body.restrictedAccessUserId : [],
      restrictedAccessEventId: body.restrictedAccessEventId ? body.restrictedAccessEventId : [],
      restrictedAccessTagId: body.restrictedAccessTagId ? body.restrictedAccessTagId : [],    
      restrictedAccessTierId: body.restrictedAccessTierId ? body.restrictedAccessTierId : [],
      starting_view_cnt: body.starting_view_cnt,
      duration: duration,
      uploadstatus: video_v ? "inprocess" : "completed",
      createdAt: new Date(body.upload_date),
    };
    const updatedRecord = await ContentArchiveVideo.findByIdAndUpdate(
      body.id,
      updated,
      { runValidators: true, new: true, timestamps: false }
    );
    if (updatedRecord) {
      const videoExistInNews = await adminNews.findOne({
        videoReferenceId: new ObjectId(body.id),
      });
      if (body.makeFeaturedCheckbox.toString() === "true") {
        if (!videoExistInNews) {
          const addVideoInNews = new adminNews({
            date: new Date(),
            publishDate: new Date(),
            publishOrHide: "publish",
            makeFeaturedCheckbox: false,
            newsType: "video",
            videoReferenceId: body.id,
            relation_id: ObjectId(req.relation_id),
          });
          const saveNews = await addVideoInNews.save();
        }
      }

      if(body.makeFeaturedCheckbox.toString() === "false") {
        if (videoExistInNews) {
          await adminNews.findByIdAndDelete(videoExistInNews?._id)
        }
      }

    }

    const newTags = req.body.tag
      ? typeof req.body.tag === "string"
        ? [ObjectId(req.body.tag)]
        : req.body.tag
      : [];
    const existingTags =
      old_v_info.tag && old_v_info.tag.length > 0
        ? old_v_info.tag.map((tag) => tag._id.toString())
        : [];

    let getExistingDiff =
      newTags.length > 0
        ? existingTags.filter((x) => !newTags.includes(x))
        : existingTags;
    let getExistingTagsArr = getExistingDiff.map((x) => ObjectId(x));

    if (getExistingTagsArr.length > 0) {
      const oldTagPartnerList = await Partner.find(
        { isDelete: false, tag: { $in: getExistingTagsArr } },
        { videoIds: 1 }
      );
      if (oldTagPartnerList.length > 0) {
        let resPartnerList = oldTagPartnerList.map(async (partner) => {
          const updateVideoRecord = await Partner.findByIdAndUpdate(
            partner._id,
            { $pull: { videoIds: { id: ObjectId(body.id) } } }
          );
        });
        await Promise.all([...resPartnerList]);
      }
    }

    if (newTags.length > 0) {
      let getNewTagsDiff =
        existingTags.length > 0
          ? newTags.filter((x) => !existingTags.includes(x))
          : newTags;
      let getNewTagsArr = getNewTagsDiff.map((x) => ObjectId(x));

      const newTagPartnerList = await Partner.find(
        { isDelete: false, tag: { $in: getNewTagsArr } },
        { videoIds: 1 }
      );
      if (newTagPartnerList.length > 0) {
        let resPartnerList = newTagPartnerList.map(async (partner) => {
          let videosWithOrder = [];
          let relatedVideos = [];

          let existsVideoIds = partner.videoIds.map((x) => x.id.toString());
          let existingVideoIds = partner.videoIds;

          if (!existsVideoIds.includes(old_v_info._id.toString())) {
            existsVideoIds = [...existsVideoIds, old_v_info._id.toString()];
            const mergedObjVideoIds = existsVideoIds.map((x) => ObjectId(x));

            let sortingOption = partner.relatedVideoSortOption
              ? partner.relatedVideoSortOption
              : "default";

            switch (sortingOption) {
              case "custom":
                relatedVideos = mergedObjVideoIds;
                break;
              case "views":
                relatedVideos = await ContentArchiveVideo.aggregate([
                  {
                    $match: {
                      _id: { $in: mergedObjVideoIds },
                    },
                  },
                  {
                    $project: {
                      _id: 1,
                      views: {
                        $sum: [
                          {
                            $cond: [
                              { $ifNull: ["$starting_view_cnt", false] },
                              "$starting_view_cnt",
                              0,
                            ],
                          },
                          {
                            $cond: [
                              { $not: ["views.0"] },
                              0,
                              { $size: "$views" },
                            ],
                          },
                        ],
                      },
                    },
                  },
                  {
                    $sort: { views: -1 },
                  },
                ]);
                break;
              case "likes":
                relatedVideos = await ContentArchiveVideo.aggregate([
                  {
                    $match: {
                      _id: { $in: mergedObjVideoIds },
                    },
                  },
                  {
                    $project: {
                      _id: 1,
                      likes: {
                        $cond: [{ $not: ["likes.0"] }, 0, { $size: "$likes" }],
                      },
                    },
                  },
                  {
                    $sort: { likes: -1 },
                  },
                ]);
                break;

              case "comments":
                relatedVideos = await ContentArchiveVideo.aggregate([
                  {
                    $match: {
                      _id: { $in: mergedObjVideoIds },
                    },
                  },
                  {
                    $project: {
                      _id: 1,
                      comments: {
                        $cond: [
                          { $not: ["comments.0"] },
                          0,
                          { $size: "$comments" },
                        ],
                      },
                    },
                  },
                  {
                    $sort: { comments: -1 },
                  },
                ]);
                break;

              case "default":
                relatedVideos = await ContentArchiveVideo.aggregate([
                  {
                    $match: {
                      _id: { $in: mergedObjVideoIds },
                    },
                  },
                  {
                    $project: {
                      _id: 1,
                      createdAt: 1,
                    },
                  },
                  {
                    $sort: { createdAt: -1 },
                  },
                ]);
                break;
              case "latest":
                relatedVideos = await ContentArchiveVideo.aggregate([
                  {
                    $match: {
                      _id: { $in: mergedObjVideoIds },
                    },
                  },
                  {
                    $project: {
                      _id: 1,
                      createdAt: 1,
                    },
                  },
                  {
                    $sort: { createdAt: -1 },
                  },
                ]);
                break;
            }
            videosWithOrder = relatedVideos.map((videoId, index) => {
              if (videoId && typeof videoId === "object") {
                const singleExistsVideo = existingVideoIds.filter(
                  (existsVideo) => {
                    if (existsVideo !== null) {
                      return (
                        existsVideo.id.toString() === videoId._id.toString()
                      );
                    }
                  }
                );
                if (singleExistsVideo.length > 0) {
                  const obj = {
                    id: videoId,
                    order: index + 1,
                    status: singleExistsVideo[0].status,
                  };
                  return obj;
                } else {
                  const obj = { id: videoId, order: index + 1 };
                  return obj;
                }
              } else {
                const singleExistsVideo = existingVideoIds.filter(
                  (existsVideo) =>
                    existsVideo.id.toString() === videoId.toString()
                );
                if (singleExistsVideo.length > 0) {
                  const obj = {
                    id: videoId,
                    order: index + 1,
                    status: singleExistsVideo[0].status,
                  };
                  return obj;
                } else {
                  const obj = { id: videoId, order: index + 1 };
                  return obj;
                }
              }
            });

            const updatedPartnerDetails = await Partner.findByIdAndUpdate(
              partner._id,
              {
                videoIds: videosWithOrder,
              },
              { new: true }
            );
          }
        });
        await Promise.all([...resPartnerList]);
      }
    }

    return res.status(200).json({
      status: true,
      message: `Video details updated successfully .`,
      data: updatedRecord,
    });
  } catch (error) {
    return res.status(200).json({ status: false, message: `${error.message}` });
  }
};

// get Users we want to add rules
exports.getUserCount = async (req, res) => {
    try {
      let body = req.body;
      let match;

      //get event Attendee users
      let matchAttendees, uniqueEventUserIds;
      if (body.restrictedAccessEventId) {
          const eventIds = body.restrictedAccessEventId.map((id) => new ObjectId(id));

          matchAttendees = {
            event: { $in: eventIds },
            relation_id: ObjectId(req.relation_id),
            isDelete: false,
          };

        const list = await eventParticipantAttendees.aggregate([
          {
            $match: matchAttendees
          },
          {
              $lookup: {
                from: "event_wise_participant_types",
                localField: "role",
                foreignField: "_id",
                pipeline: [
                {
                $match: {
                  isDelete: false,
                },
                },
              ],
                as: "roleData",
              },
            },
            {
              $match: {"roleData.role":"Member"}
            },
          ]); 

        const eventUserIds = list.map((item) => item.user);
        const uniqueUserIds = [...new Set(eventUserIds)];
        uniqueEventUserIds = Array.from(new Set(uniqueUserIds));
      }

      match = { isDelete: false ,$or: [{ blocked: false }, { blocked: { $exists: false } }],firebaseId: { $nin: ["", null] },};
      if (body.restrictionAccess === "restricted") {
          const uniqueAllUserIds = [...new Set([...body.restrictedAccessUserId, ...(uniqueEventUserIds || [])]),];
    
          match = {
          ...match,
          $or: [
            { _id: { $in: uniqueAllUserIds } },
            { purchased_plan: { $in: body.restrictedAccessMembershipPlanId } },
            { accessible_groups: { $in: body.restrictedAccessGroupId } },
            { tagId: { $in: body.restrictedAccessTagId } },
          ],
        };
      } else {
        match = {
          ...match,
          isCollaborator: false
        };
      }

      const userData = await User.find(match, {
        _id: 1,
        first_name: { '$ifNull': ['$first_name', ''] },
        last_name: { '$ifNull': ['$last_name', ''] },
        display_name: { '$ifNull': ['$display_name', ''] },
        'Preferred Email': 1,
      })
        .populate({ path: "accessible_groups", select: "groupTitle groupInfo " })
        .populate({
          path: "purchased_plan",
          select: "plan_name -accessResources",
        })
        .populate({
          path: "attendeeDetail",
          populate: {
            path: "evntData",
            populate: {
              path: "event",
              select: { _id: 1, title: 1 },
            },
          },
        });

      if (userData.length !== 0) {
        return res.status(200).json({
          status: true,
          message: "Get user list successfully for video!",
          data: userData,
        });
      } else {
        return res.status(200).json({
          status: false,
          message: "Data not found!",
        });
      }
    } catch (error) {
      return res.status(500).json({ status: false, message: error.message });
    }
  };

exports.getContentVideolist = async (req, res) => {
  try {
    const page = req.query.page ? +req.query.page : 1;
    const limit =req.query.limit ? +req.query.limit : 20;
    const tag = req.query.tag ? req.query.tag : "";
    const search = req.query.search ? req.query.search : "";

    const category = req.query.category ? req.query.category : "";

    let match = {
      isDelete: false,
      relation_id: ObjectId(req.relation_id),
    };

    if (tag !== "") {
      match = { ...match, tag: { $in: [ObjectId(tag)] } };
    }

    if (category !== "") {
      match = { ...match, categories: { $in: [ObjectId(category)] } };
    }

    if (search && search !== "") {
      match = {
        ...match,
        title: { $regex: ".*" + search.replace(/[.*+?^${}()|[\]\\]/g, '\\$&') + ".*", $options: "i" },
      };
    }

    const data = await ContentArchiveVideo.find(match)
      .select("-__v")
      .sort({ createdAt: -1, updatedAt: -1 })
      .limit(limit * 1)
      .skip((page - 1) * limit);
    const count = await ContentArchiveVideo.countDocuments(match);

    const countAllData = await ContentArchiveVideo.countDocuments({ relation_id: ObjectId(req.relation_id), isDelete: false });

    return res.status(200).json({
      status: true,
      message: `List of videos.`,
      data: [
        {
          videos: data,
          totalPages: Math.ceil(count / limit),
          currentPage: page,
          totalVideos: count,
          countAllData: countAllData
        },
      ],
    });
  } catch (error) {
    return res.status(200).json({ status: false, message: `${error.message}` });
  }
};

// get Content Video Suggestion List
exports.getContentVideoSuggestionList = async (req, res) => {
  try {
    var match = { isDelete: false, relation_id: ObjectId(req.relation_id), };
    const data = await ContentArchiveVideo.find(match, {
      _id: 0,
      title: 1,
      categories: 0,
      subcategory: 0,
      speaker: 0,
      tag: 0,
      eventIds: 0,
      restrictedAccessGroupId: 0,
    }).sort({ title: 1 }).lean();
    return res
      .status(200)
      .json({ status: true, message: `List of videos.`, data: data });
  } catch (error) {
    return res.status(200).json({ status: false, message: `${error.message}` });
  }
};

exports.getContentVideo_UserWiselist = async (req, res) => {
  try {
    const { page, limit } = req.query;
    const authUser = req.authUserId;
    const userdata = await User.findById(authUser);

    const data = await ContentArchiveVideo.find({
      isDelete: false,
      relation_id: ObjectId(req.relation_id),
    })
      .select("-v")
      .sort({ createdAt: -1 })
      .limit(limit * 1)
      .skip((page - 1) * limit);

    const count = await ContentArchiveVideo.countDocuments({
      isDelete: false,
      relation_id: ObjectId(req.relation_id),
    });

    let resOrder = data.map(async (item, i) => {
      const without_html_description = item.description.replace(
        /(<([^>]+)>)/gi,
        ""
      );
      const mobile_description = without_html_description.substring(0, 600);

      item.mobile_description = mobile_description;
      var url = s3.getSignedUrl("getObject", {
        Bucket: "arn:aws:s3:us-east-2:496737174815:accesspoint/accessforapp",
        Key: item.video,
        Expires: 100000,
      });
      item = { ...item, video_url: url };
    });
    await Promise.all([...resOrder]);

    return res.status(200).json({
      status: true,
      message: `List of videos.`,
      data: [
        {
          videos: data,
          totalPages: Math.ceil(count / limit),
          currentPage: page,
          totalVideos: count,
        },
      ],
    });
  } catch (error) {
    return res.status(200).json({ status: false, message: `${error.message}` });
  }
};

exports.getSearchContentVideo = async (req, res) => {
  try {
    const { search } = req.query;
    const authUser = req.authUserId;
    const userdata = await User.findById(authUser);

    const data = await ContentArchiveVideo.find({
      isDelete: false,
      title: { $regex: ".*" + search + ".*", $options: "i" },
      relation_id: ObjectId(req.relation_id),
    }).select("-__v");

    return res
      .status(200)
      .json({ status: true, message: `List of videos.`, data: data });
  } catch (error) {
    return res.status(200).json({ status: false, message: `${error.message}` });
  }
};

exports.getContentVideo_byId = async (req, res) => {
  try {
    var localDate = new Date(req.query.localDate);
    localDate = moment(localDate, "YYYY-MM-DD").toDate();
    const obj = { relation_id: req.relation_id }
    if (ObjectId.isValid(req.params.id)) {
      var data = await ContentArchiveVideo.findOne({
        _id: req.params.id,
        isDelete: false,
        relation_id: ObjectId(req.relation_id),
      })
        .select("-__v")
        .lean();
      if (data !== null) {
        let mobile_description = "";
        if (data.description !== undefined) {
          let without_html_description = data.description.replace(
            /&amp;/g,
            "&"
          );
          without_html_description = without_html_description.replace(
            /(<([^>]+)>)/g,
            ""
          );
          without_html_description = without_html_description.replace(
            /(\r\n|\n|\r)/gm,
            ""
          );
          mobile_description = without_html_description.substring(0, 600);
        }

        data.mobile_description = mobile_description;
        var url = s3.getSignedUrl("getObject", {
          Bucket: "arn:aws:s3:us-east-2:496737174815:accesspoint/accessforapp",
          Key: data.video,
          Expires: 100000,
        });
        data = { ...data, video_url: url };
        const tiers = await getAllTiersfromBilling(obj, expand = true);
        const restrictedTierIds = Array.isArray(data.restrictedAccessTierId) ?
          data.restrictedAccessTierId.map(id => id.toString()) :
          [];
        const filteredTiers = tiers.filter(tier =>
          restrictedTierIds.includes(tier._id.toString())
        ).map(tier => ({
          _id: tier._id,
          name: tier.name
        }));
        data.restrictedAccessTierId = filteredTiers;
        var new_likes = await getLikes_detail(data.likes);
        const featuredInNews = await adminNews.findOne({
          videoReferenceId: new ObjectId(req.params.id),
        });
        const makeFeaturedCheckbox = featuredInNews ? true : false;
        let eventDataResults = await Promise.all(
          data.eventIds.map((event) =>
            getEventType(event, req.authUserId, req.relation_id, localDate)
          )
        );
        data.eventIds = data.eventIds.map((event, idx) => ({
          ...event,
          ...eventDataResults[idx]["0"],
        }));
        return res.status(200).json({
          status: true,
          message: `Video by id.`,
          data: {
            ...data,
            likes: new_likes,
            makeFeaturedCheckbox: makeFeaturedCheckbox,
          },
        });
      } else {
        return res
          .status(404)
          .json({ status: false, message: `Video data not found!`, data: [] });
      }
    } else {
      return res
        .status(200)
        .json({ status: false, message: `Id is invalid!`, data: [] });
    }
  } catch (error) {
    return res.status(200).json({ status: false, message: `${error.message}` });
  }
};

async function getEventType(data, authUserId, relation_id, localDate) {
  try{
    const eventIdsList = Array.isArray(data._id) ? data._id.map(id => ObjectId(id.toString())) : [ObjectId(data._id.toString())];
  var localDate = localDate;
  localDate = moment(localDate, "YYYY-MM-DD").toDate();
  const authUser = authUserId;
  let ruleCondition = await userAccessRulesCommonCondition({
    userId: authUser,
    relation_id: relation_id,
  });

  const userData = await User.findById(authUser).select("firebaseId accessible_groups purchased_plan attendeeDetail.evntData");

  const aggregate = [
    {
      $addFields: {
        Date: {
          $let: {
            vars: {
              year: { $substr: ["$endDate", 6, 10] },
              month: { $substr: ["$endDate", 0, 2] },
              dayOfMonth: { $substr: ["$endDate", 3, 2] },
              startMinute: { $substr: ["$endTime", 3, 2] },
              startHours: {
                $toString: {
                  $cond: {
                    if: { $eq: [{ $substr: ["$endTime", 6, 2] }, "am"] },
                    then: {
                      $cond: {
                        if: { $eq: [{ $substr: ["$endTime", 0, 2] }, "12"] },
                        then: "00", // Midnight (12 AM) should be converted to "00"
                        else: { $substr: ["$endTime", 0, 2] }, // No change needed for AM times other than midnight
                      },
                    },
                    else: {
                      $cond: {
                        if: { $eq: [{ $substr: ["$endTime", 0, 2] }, "12"] },
                        then: "12", // Noon (12 PM) should remain "12"
                        else: {
                          $add: [
                            {
                              $toInt: { $substr: ["$endTime", 0, 2] },
                            },
                            12, // Adding 12 to convert PM times to 24-hour format
                          ],
                        },
                      },
                    },
                  },
                },
              },
              timeZoneTemp: {
                $cond: {
                  if: {
                    $or: [
                      { $eq: ["$timeZone", ""] },
                      {
                        $eq: [
                          "$timeZone",
                          "(UTC) Dublin, Edinburgh, Lisbon, London"
                        ]
                      }
                    ]
                  },
                  then: "-00:00",
                  else: {
                    $substr: ["$timeZone", 4, 6]
                  }
                }
              }
            },
            in: {
              $toDate: {
                $concat: [
                  "$$year",
                  "-",
                  "$$month",
                  "-",
                  "$$dayOfMonth",
                  "T",
                  "$$startHours",
                  ":",
                  "$$startMinute",
                  ":",
                  "00.000",
                  "$$timeZoneTemp"
                ],
              },
            },
          },
        },
      },
    },
    {
      $match: {
        ...ruleCondition,
        isDelete: false,
        _id: { $in: eventIdsList } 
      },
    },
    {
      $lookup: {
        from: "eventactivities",
        let: { eventIdsList },
        pipeline: [
          {
            $match: {
              $expr: {
                $in: ["$event", "$$eventIdsList"], 
              },
            },
          },
          {
            $match: {
              isDelete: false,
            },
          },
        ],
        as: "activities",
      },
    },
    {
      $addFields: {
      activityCount:  { $size: '$activities' }
      }
    },
    {
      $lookup: {
        from: "event_participant_attendees",
        let: { eventIdsList }, // Pass eventIdsList through `let`
        pipeline: [
          {
            $match: {
              $expr: {
                $and: [
                  { $in: ["$event", "$$eventIdsList"] }, // Match event IDs in the list
                  { $eq: [ObjectId(userData._id), "$user"] },
                  { $eq: ["$isDelete", false] }
                ],
              },
            },
          },
        ],
        as: "event_participant_attendees_result",
      },
    },
    {
      $project: {
          _id: 0,
          activityCount:"$activityCount",
          registationFlag: {
              $let: {
                  vars: { }, 
                  in: {
                      $gt: [{ $size: "$event_participant_attendees_result" }, 0]
                  }
              }
          },
          eventType: {
            $cond: {
              if: {
                $gt: [{ $size: "$event_participant_attendees_result" }, 0], // Check if there are attendees
              },
              then: {
                $cond: {
                  if: { $lt: ["$Date", moment(localDate).toDate()] }, // Check if localDate is less than event date
                  then: "pastEvent",
                  else: "myEvent",
                }
              },
              else: {
                $cond: {
                  if: { $gte: ["$Date", moment(localDate).toDate()] },
                  then: "upcomingEvent",
                  else: "pastEvent",
                }
              }
            }
          }
      },

    },

  ]

  let eventListData = await event.aggregate(aggregate);
  return eventListData.length ? eventListData.map(item => item) : [];
  } catch(error) {
    return res.status(200).json({ status: false, message: `${error.message}` });
  }
}

exports.getContentVideo_byId_byUser = async (req, res) => {
  try {
    var localDate = new Date(req.query.localDate);
    localDate = moment(localDate, "YYYY-MM-DD").toDate();
    const obj = { relation_id: req.relation_id }
    const authUser = req.authUserId;
    const userdata = await User.findById(authUser);
    const currentEdgeId = req.currentEdge._id;
    let ruleCondition = await userAccessRulesCommonCondition({ userId: authUser, relation_id: req.relation_id });

    if (ObjectId.isValid(req.params.id)) {
      var data = await ContentArchiveVideo.findOne({
        _id: req.params.id,
        isDelete: false,
      })
        .select("-__v")
        .lean();

      let mainSubcategoryIds = data.subcategory.map(sub => sub._id.toString());

      data.categories.forEach(category => {
        category.subcategory = category.subcategory?.filter(sub => {
          return mainSubcategoryIds.includes(sub._id.toString());
        });
      });

      if (data !== null) {
        let dataWithRule = await ContentArchiveVideo.findOne({ _id: req.params.id, isDelete: false,...(req.owner ? {}: {...ruleCondition}),});
        if (dataWithRule !== null) {
          const eventAttendeesData = data.eventIds;
          if (
            data.eventIds !== undefined &&
            userdata !== undefined &&
            userdata !== null &&
            eventAttendeesData !== null
          ) {
            var eventList = [],
              location = {};
            let attendeesDetails = eventAttendeesData?.map(
              async (attendee, i) => {
                let eventData = await Event.findOne(
                  { _id: attendee._id, isDelete: false },
                  {
                    _id: 1,
                    title: 1,
                    thumbnail: 1,
                    eventUrl: 1,
                    startDate: 1,
                    startTime: 1,
                    endDate: 1,
                    endTime: 1,
                    timeZone: 1,
                    location: 1,
                    activities: 1,
                    locationType: 1,
                  }
                ).lean();

                if (
                  eventData !== null &&
                  eventData.location !== undefined &&
                  eventData.location !== "" &&
                  eventData.location !== null
                ) {
                  eventData = {
                    ...eventData,
                    city: eventData.location ? eventData.location.city : null,
                    country: eventData.location
                      ? eventData.location.country
                      : null,
                  };
                  delete eventData.location;
                  eventList.push(eventData);
                } else {
                  location = await eventLocation
                    .findOne({
                      event: attendee.event,
                      locationVisible: true,
                      isDelete: false,
                    })
                    .lean();
                  delete eventData.location;
                  if (location !== null) {
                    eventData = {
                      ...eventData,
                      city: location ? location.city : null,
                      country: location ? location.country : null,
                    };
                  } else {
                    eventData = { ...eventData, city: null, country: null };
                  }
                  eventList.push(eventData);
                }
              }
            );
            await Promise.all([...attendeesDetails]);
            data = { ...data, eventIds: eventList };
          }
          const mobile_description = convert(data.description, { wordwrap: false }).trim();
          data.mobile_description = mobile_description;
        var url = s3.getSignedUrl("getObject", {
          Bucket: "arn:aws:s3:us-east-2:496737174815:accesspoint/accessforapp",
          Key: data.video,
          Expires: 100000,
        });
        data = { ...data, video_url: url };
        const tiers = await getAllTiersfromBilling(obj, expand = true);
        const restrictedTierIds = Array.isArray(data.restrictedAccessTierId) ?
          data.restrictedAccessTierId.map(id => id.toString()) :
          [];
        const filteredTiers = tiers.filter(tier =>
          restrictedTierIds.includes(tier._id.toString())
        ).map(tier => ({
          _id: tier._id,
          name: tier.name
        }));
        data.restrictedAccessTierId = filteredTiers;
        data.viewsCount = 0;
        if (data.starting_view_cnt) {
          data.viewsCount = data.viewsCount + data.starting_view_cnt;
        }
        if (data.views?.length) {
          data.viewsCount = data.viewsCount + data.views.length;
        }
        var new_likes = await getLikes_detail(data.likes);
        var new_dislikes = data.disLikes ? await getDisLikes_detail(data.disLikes):[];
        if (data && data.eventIds) {
          let eventDataResults = await Promise.all(
            data.eventIds.map((event) =>
              getEventType(event, req.authUserId, req.relation_id, localDate)
            )
          );
          data.eventIds = data.eventIds.map((event, idx) => ({
            ...event,
            ...eventDataResults[idx]["0"],
          }));
        }
        return res.status(200).json({
          status: true,
          message: `Video by id.`,
          data: { ...data, likes: new_likes,dislikes: new_dislikes },
        });
        } else {
          return res.status(404).json({ status: false, message: `User don't have access of this video!`, data: [] });
        }
      } else {
        return res
          .status(404)
          .json({ status: false, message: `Video data not found!`, data: [] });
      }
    } else {
      return res
        .status(200)
        .json({ status: false, message: `Id is invalid!`, data: [] });
    }
  } catch (error) {
    return res.status(200).json({ status: false, message: `${error.message}` });
  }
};

exports.deleteContentVideo_byId = async (req, res) => {
  try {
    const data = await ContentArchiveVideo.findByIdAndUpdate(req.params.id, {
      isDelete: true,
    }).select("-__v");
    if (data) {
      const newsExist = await adminNews.findOne({
        videoReferenceId: new ObjectId(req.params.id),
      });
      if (newsExist && newsExist.thumbnail) deleteImage(newsExist.thumbnail);
      if (newsExist) await adminNews.findByIdAndDelete(newsExist._id);
    }
    return res
      .status(200)
      .json({ status: true, message: `Video deleted.`, data });
  } catch (error) {
    return res.status(200).json({ status: false, message: `${error.message}` });
  }
};

async function getLikes_detail(data) {
  var arr_likes = [];
  for (var i = 0; i < data.length; i++) {
    var normal_user = await User.findById(data[i].like_userid);
    if (normal_user) {
      arr_likes.push({
        id: data[i].like_userid,
        name:
          normal_user.first_name +
          " " +
          normal_user.last_name,
        profile_pic: normal_user.profileImg,
        first_name: { '$ifNull': ['$normal_user.first_name', ''] },
        last_name: { '$ifNull': ['$normal_user.last_name', ''] },
        display_name: { '$ifNull': ['$normal_user.display_name', ''] },
        user: "user",
      });
    } else {
      var admin_user = await AdminUser.findById(data[i].like_userid);
      if (admin_user) {
        arr_likes.push({
          id: data[i].like_userid,
          name: admin_user.first_name + " " + admin_user.last_name,
          first_name: { '$ifNull': ['$admin_user.first_name', ''] },
          last_name: { '$ifNull': ['$admin_user.last_name', ''] },
          display_name: { '$ifNull': ['$admin_user.display_name', ''] },
          user: "adminuser",
        });
      } else {
        arr_likes.push({ id: data[i].like_userid, name: "", user: "" });
      }
    }
  }
  return arr_likes;
}

async function getDisLikes_detail(data) {
  var arr_dislikes = [];
  for (var i = 0; i < data.length; i++) {
    var normal_user = await User.findById(data[i].disLike_userid);
    if (normal_user) {
      arr_dislikes.push({
        id: data[i].disLike_userid,
        name:
          normal_user.first_name +
          " " +
          normal_user.last_name,
        profile_pic: normal_user.profileImg,
        first_name: normal_user.first_name,
        last_name: normal_user.last_name,
        display_name: normal_user.display_name,
        user: "user",
      });
    } else {
      var admin_user = await AdminUser.findById(data[i].disLike_userid);
      if (admin_user) {
        arr_dislikes.push({
          id: data[i].disLike_userid,
          name: admin_user.first_name + " " + admin_user.last_name,
          first_name: { '$ifNull': ['$admin_user.first_name', ''] },
          last_name: { '$ifNull': ['$admin_user.last_name', ''] },
          display_name: { '$ifNull': ['$admin_user.display_name', ''] },
          user: "adminuser",
        });
      } else {
        arr_dislikes.push({ id: data[i].disLike_userid, name: "", user: "" });
      }
    }
  }
  return arr_dislikes;
}

/** for mobile development **/
exports.uploadMedia_single = async (req, res) => {
  try {
    const { dummy_file } = req;
    const newentry = new Dummy({ test_file: dummy_file });
    const data = await newentry.save();
    return res
      .status(200)
      .json({ status: true, message: `File uploaded.`, data });
  } catch (error) {
    return res.status(200).json({ status: false, message: `${error.message}` });
  }
};

exports.getDeletedContent_Video = async (req, res) => {
  try {
    const data = await ContentArchiveVideo.find({ isDelete: true, relation_id: ObjectId(req.relation_id), }).select(
      "-__v"
    );
    return res
      .status(200)
      .json({ status: true, message: `List of deleted videos.`, data, countAllData: data.length });
  } catch (error) {
    return res.status(200).json({ status: false, message: `${error.message}` });
  }
};

// get Deleted Content Video Suggestion List
exports.getDeletedContentVideoSuggestionList = async (req, res) => {
  try {
    const data = await ContentArchiveVideo.find(
      { isDelete: true, relation_id: ObjectId(req.relation_id), },
      {
        _id: 0,
        title: 1,
        categories: 0,
        subcategory: 0,
        speaker: 0,
        tag: 0,
        eventIds: 0,
        restrictedAccessGroupId: 0,
      }
    ).sort({ title: 1 }).lean();
    return res
      .status(200)
      .json({ status: true, message: `List of deleted videos.`, data });
  } catch (error) {
    return res.status(200).json({ status: false, message: `${error.message}` });
  }
};

exports.restoreContent_Video_Byid = async (req, res) => {
  try {
    const data = await ContentArchiveVideo.findByIdAndUpdate(req.params.id, {
      isDelete: false,
    }).select("-__v");
    return res
      .status(200)
      .json({ status: true, message: `Video restored.`, data });
  } catch (error) {
    return res.status(200).json({ status: false, message: `${error.message}` });
  }
};

exports.permenantDelete_ContentVideo_Byid = async (req, res) => {
  try {
    const data = await ContentArchiveVideo.findById(
      new ObjectId(req.params.id)
    );
    if (data !== null) {
      var d_video = "",
        d_video_240 = "",
        d_video_360 = "",
        d_video_480 = "",
        d_files = [],
        d_subtitle = "",
        d_partner = [];

      if (data.video.length > 0) {
        d_video = await s3
          .deleteObject({
            Bucket: process.env.AWS_BUCKET,
            Key: data.video,
          })
          .promise();
      }

      if (data.video_240 !== undefined && data.video_240.length > 0) {
        d_video_240 = await s3
          .deleteObject({
            Bucket: process.env.AWS_BUCKET,
            Key: data.video_240,
          })
          .promise();
      }

      if (data.video_360 !== undefined && data.video_360.length > 0) {
        d_video_360 = await s3
          .deleteObject({
            Bucket: process.env.AWS_BUCKET,
            Key: data.video_360,
          })
          .promise();
      }

      if (data.video_480 !== undefined && data.video_480.length > 0) {
        d_video_480 = await s3
          .deleteObject({
            Bucket: process.env.AWS_BUCKET,
            Key: data.video_480,
          })
          .promise();
      }

      if (data.files.length > 0) {
        d_files = data.files.map(async (img) => {
          await s3
            .deleteObject({
              Bucket: process.env.AWS_BUCKET,
              Key: img.url,
            })
            .promise();
        });
      }

      if (data.subtitle_file !== undefined && data.subtitle_file.length > 0) {
        d_subtitle = await s3
          .deleteObject({
            Bucket: process.env.AWS_BUCKET,
            Key: data.subtitle_file,
          })
          .promise();
      }

      if (data.relevant_partners.length > 0) {
        d_partner = data.relevant_partners.map(async (partner) => {
          await s3
            .deleteObject({
              Bucket: process.env.AWS_BUCKET,
              Key: partner.logo,
            })
            .promise();
        });
      }

      await Promise.all([
        d_video,
        d_video_240,
        d_video_360,
        d_video_480,
        ...d_files,
        d_subtitle,
        ...d_partner,
      ]);
      await data.remove();
      return res
        .status(200)
        .json({ status: true, message: "Video permenantly deleted!" });
    } else {
      return res
        .status(404)
        .json({ status: false, message: "Video not found!" });
    }
  } catch (error) {
    return res.status(200).json({ status: false, message: `${error.message}` });
  }
};

exports.adminaddpausetime = async (req, res) => {
  try {
    const { admin_Id } = req;
    const data = await ContentArchiveVideo.findById(req.params.id).select(
      "user_video_pause"
    );
    if (data.user_video_pause !== undefined) {
      const updated_data = await ContentArchiveVideo.findByIdAndUpdate(
        req.params.id,
        {
          user_video_pause: {
            ...data.user_video_pause,
            [admin_Id]: req.body.pause_time,
          },
        }
      );
      return res
        .status(200)
        .json({ status: true, message: "added pause time" });
    } else {
      const new_data = { [admin_Id]: req.body.pause_time };
      const updated_data = await ContentArchiveVideo.findByIdAndUpdate(
        { _id: req.params.id },
        { $set: { user_video_pause: new_data } }
      );
      return res
        .status(200)
        .json({ status: true, message: "added pause time" });
    }
  } catch (e) {
    return res.status(200).json({ status: false, message: `${e.message}` });
  }
};

exports.useraddpausetime = async (req, res) => {
  try {
    const { authUserId } = req;
    const data = await ContentArchiveVideo.findById(req.params.id).select(
      "user_video_pause"
    );
    if (data.user_video_pause !== undefined) {
      const updated_data = await ContentArchiveVideo.findByIdAndUpdate(
        req.params.id,
        {
          user_video_pause: {
            ...data.user_video_pause,
            [authUserId]: req.body.pause_time,
          },
        }
      );
      return res
        .status(200)
        .json({ status: true, message: "added pause time" });
    } else {
      const new_data = { [authUserId]: req.body.pause_time };
      const updated_data = await ContentArchiveVideo.findByIdAndUpdate(
        { _id: req.params.id },
        { $set: { user_video_pause: new_data } }
      );
      return res
        .status(200)
        .json({ status: true, message: "added pause time" });
    }
  } catch (e) {
    return res.status(200).json({ status: false, message: `${e.message}` });
  }
};

exports.addvideoviewadmin = async (req, res) => {
  try {
    const { admin_Id } = req;
    const platform = req.query.platform ? req.query.platform : 'web'
    const data = await ContentArchiveVideo.findById(req.params.id).select(
      "views"
    );

    if (data.views === undefined) {
      const update = await ContentArchiveVideo.findByIdAndUpdate(
        req.params.id,
        {
          $addToSet: { views: { view_userid: admin_Id, viewdate: new Date(), platform: platform } },
        }
      );
      return res
        .status(200)
        .json({ status: true, message: "View added successfully!" });
    } else {
      const update = await ContentArchiveVideo.findByIdAndUpdate(
        req.params.id,
        { $push: { views: { view_userid: admin_Id, viewdate: new Date(), platform: platform } } }
      );
      return res
        .status(200)
        .json({ status: true, message: "View added successfully!" });
    }
  } catch (e) {
    return res.status(200).json({ status: false, message: `${e.message}` });
  }
};

exports.addvideoviewuser = async (req, res) => {
  try {
    const { authUserId } = req;
    const platform = req.query.platform ? req.query.platform : 'web'
    const data = await ContentArchiveVideo.findById(req.params.id).select(
      "views"
    );

    if (data.views === undefined) {
      const update = await ContentArchiveVideo.findByIdAndUpdate(
        req.params.id,
        {
          $addToSet: {
            views: { view_userid: authUserId, viewdate: new Date(), platform: platform },
          },
        }
      );
      return res.status(200).json({
        status: true,
        message: "View added successfully!",
        update: update,
      });
    } else {
      const update = await ContentArchiveVideo.findByIdAndUpdate(
        req.params.id,
        { $push: { views: { view_userid: authUserId, viewdate: new Date(), platform: platform } } }
      );
      return res.status(200).json({
        status: true,
        message: "View added successfully!",
        update: update,
      });
    }
  } catch (e) {
    return res.status(200).json({ status: false, message: `${e.message}` });
  }
};

exports.addvideolikeadmin = async (req, res) => {
  try {
    const { admin_Id } = req;
    const data = await ContentArchiveVideo.findById(req.params.id).select(
      "likes"
    );

    if (
      data.likes.length > 0 &&
      data.likes.filter((item) => {
        item.like_userid.toString() == admin_Id.toString();
        return item;
      }).length > 0
    ) {
      const result = await ContentArchiveVideo.findByIdAndUpdate(
        req.params.id,
        { $pull: { likes: { like_userid: admin_Id } } },
        { new: true }
      );
      return res.status(200).json({ status: true, message: "Likes Updated!" });
    } else {
      const result = await ContentArchiveVideo.updateOne(
        { _id: req.params.id },
        {
          $addToSet: {
            likes: { likes: { like_userid: admin_Id, likedate: new Date() } },
          },
        }
      );
      return res.status(200).json({ status: true, message: "Likes Updated!" });
    }
  } catch (e) {
    return res.status(200).json({ status: false, message: `${e.message}` });
  }
};

async function addLike(userId, videoId) {
  await ContentArchiveVideo.updateOne(
    { _id: videoId },
    { $addToSet: { likes: { like_userid: userId, likedate: new Date() } } }
  );
}

async function removeLike(userId, videoId) {
  await ContentArchiveVideo.findByIdAndUpdate(
    videoId,
    { $pull: { likes: { like_userid: userId } } },
    { new: true }
  );
}

async function addDislike(userId, videoId) {
  await ContentArchiveVideo.updateOne(
    { _id: videoId },
    { $addToSet: { disLikes: { disLike_userid: userId, disLikedate: new Date() } } }
  );
}

async function removeDislike(userId, videoId) {
  await ContentArchiveVideo.findByIdAndUpdate(
    videoId,
    { $pull: { disLikes: { disLike_userid: userId } } },
    { new: true }
  );
}

//add like ,dislike, remove like, remove dislike api
exports.addvideolikeuser = async (req, res) => {
  try {
    const { authUserId } = req;
    const videoId = req.params.id;
    let responseMessage = "";
    
    const data = await ContentArchiveVideo.findById(req.params.id).select(
      "likes disLikes"
    );

    let isLiked , isDisLiked;
    if (data.likes.length > 0) {
      isLiked = data.likes.find((item) => item.like_userid.toString() == authUserId.toString());
    }
    if (data.disLikes.length > 0) {
      isDisLiked = data.disLikes.find((item) => item.disLike_userid.toString() == authUserId.toString());
    }
    
    if (req.body.clickOn === "like") {
      if (isLiked) {
        await removeLike(authUserId, videoId);
        responseMessage = "Like removed successfully";
      } else {
        await addLike(authUserId, videoId);
        responseMessage = "Like added successfully";
        
        if (isDisLiked) {
          await removeDislike(authUserId, videoId);
        }
      }
    } else if (req.body.clickOn === "dislike") {
      if (isDisLiked) {
        await removeDislike(authUserId, videoId);
        responseMessage = "Dislike removed successfully";
      } else {
        await addDislike(authUserId, videoId);
        responseMessage = "Dislike added successfully";

        if (isLiked) {
          await removeLike(authUserId, videoId);
        }
      }
    }
    return res.status(200).json({ status: true, message: responseMessage });
  } catch (e) {
    return res.status(200).json({ status: false, message: `${e.message}` });
  }
};

exports.allVideoList_byadmin = async (req, res) => {
  try {
    const data = await ContentArchiveVideo.aggregate([
      {
        $match: {
          isDelete: false,
          relation_id: ObjectId(req.relation_id),
        },
      },
      {
        $project: {
          _id: "$_id",
          title: 1,

          viewscount: {
            $size: "$views",
          },
          likescount: {
            $size: "$likes",
          },
          commentscount: {
            $size: "$comments",
          },
        },
      },
      {
        $sort: { createdAt: -1 },
      },
    ]);

    return res
      .status(200)
      .json({ status: true, message: `List of Videos.`, data: data });
  } catch (error) {
    return res.status(200).json({ status: false, message: `${error.message}` });
  }
};

exports.allVideoListByDateForAdmin = async (req, res) => {
  try {
    var fromdate = req.query.fromdate;
    var todate = req.query.todate;
    fromdate = moment(new Date(fromdate)).format("YYYY-MM-DD");
    todate = moment(new Date(todate)).format("YYYY-MM-DD");

    const data = await ContentArchiveVideo.aggregate([
      { $unwind: { path: "$views", preserveNullAndEmptyArrays: true } },
      { $unwind: { path: "$likes", preserveNullAndEmptyArrays: true } },
      { $unwind: { path: "$comments", preserveNullAndEmptyArrays: true } },
      {
        $lookup: {
          from: "contentarchivecomments",
          let: { comment_id: "$comments" },
          pipeline: [
            {
              $match: {
                $expr: {
                  $eq: ["$_id", "$$comment_id"],
                },
              },
            },
            { $project: { _id: 1, createdAt: 1 } },
          ],
          as: "outcomments",
        },
      },
      { $unwind: { path: "$outcomments", preserveNullAndEmptyArrays: true } },
      {
        $project: {
          _id: "$_id",
          title: "$title",
          view_userid: "$views.view_userid",
          views: { $cond: [{ $ifNull: ["$views", false] }, 0, 1] },
          likes: { $cond: [{ $ifNull: ["$likes", false] }, 0, 1] },
          comments: { $cond: [{ $ifNull: ["$outcomments", false] }, 0, 1] },
          like_userid: "$likes.like_userid",
          comments_id: "$outcomments._id",
          viewdate: {
            $toDate: {
              $dateToString: { format: "%Y-%m-%d", date: "$views.viewdate" },
            },
          },
          likedate: {
            $toDate: {
              $dateToString: { format: "%Y-%m-%d", date: "$likes.likedate" },
            },
          },
          commentdate: {
            $toDate: {
              $dateToString: {
                format: "%Y-%m-%d",
                date: "$outcomments.createdAt",
              },
            },
          },
        },
      },
      {
        $match: {
          relation_id: ObjectId(req.relation_id),
          $or: [
            {
              $and: [
                { viewdate: { $ne: null } },
                {
                  viewdate: {
                    $gte: new Date(fromdate),
                    $lte: new Date(todate),
                  },
                },
              ],
            },
            {
              $and: [
                { likedate: { $ne: null } },
                {
                  likedate: {
                    $gte: new Date(fromdate),
                    $lte: new Date(todate),
                  },
                },
              ],
            },
            {
              $and: [
                { commentdate: { $ne: null } },
                {
                  commentdate: {
                    $gte: new Date(fromdate),
                    $lte: new Date(todate),
                  },
                },
              ],
            },
          ],
        },
      },
      {
        $project: {
          _id: "$_id",
          title: 1,
          createdAt: 1,
          viewscount: {
            $sum: "$views",
          },
          likescount: {
            $sum: "$likes",
          },
          commentscount: {
            $sum: "$comments",
          },
        },
      },
      {
        $sort: { createdAt: -1 },
      },
    ]);

    return res
      .status(200)
      .json({ status: true, message: `List of Videos.`, data: data });
  } catch (error) {
    return res.status(200).json({ status: false, message: `${error.message}` });
  }
};

exports.getvideobycategory = async (req, res) => {
  try {
    const { page, limit } = req.query;
    const authUser = req.authUserId;
    const userdata = await User.findById(authUser);

    const data = await ContentArchiveVideo.find({
      categories: { $in: req.params.cateId },
      isDelete: false,
      relation_id: ObjectId(req.relation_id),
    })
      .select("-__v")
      .sort({ updatedAt: -1 })
      .limit(limit * 1)
      .skip((page - 1) * limit);
    const count = await ContentArchiveVideo.countDocuments({
      categories: { $in: req.params.cateId },
      isDelete: false,
      relation_id: ObjectId(req.relation_id),
    });
    return res.status(200).json({
      status: true,
      message: `List of videos.`,
      data: [
        {
          videos: data,
          totalPages: Math.ceil(count / limit),
          currentPage: page,
          totalVideos: count,
        },
      ],
    });
  } catch (error) {
    return res.status(200).json({ status: false, message: `${error.message}` });
  }
};

exports.getvideobycategory_byadmin = async (req, res) => {
  try {
    const { page, limit } = req.query;
    const tag = req.query.tag;
    const { search } = req.query;

    var match = {
      isDelete: false,
      relation_id: ObjectId(req.relation_id),
    };

    if (req.params.cateId) {
      match = {
        ...match,
        categories: { $in: ObjectId(req.params.cateId) },
      };
    }

    if (tag) {
      match = { ...match, tag: { $in: ObjectId(tag) } };
    }

    if (search && search !== "") {
      match = {
        ...match,
        title: { $regex: ".*" + search + ".*", $options: "i" },
      };
    }

    const data = await ContentArchiveVideo.find(match)
      .select("-__v")
      .sort({ updatedAt: -1 })
      .limit(limit * 1)
      .skip((page - 1) * limit);
    const count = await ContentArchiveVideo.countDocuments(match);

    // const data = await ContentArchiveVideo.find({
    //   categories: { $in: req.params.cateId },
    //   isDelete: false,
    // })
    //   .select("-__v")
    //   .sort({ updatedAt: -1 })
    //   .limit(limit * 1)
    //   .skip((page - 1) * limit);
    // const count = await ContentArchiveVideo.countDocuments({
    //   categories: { $in: req.params.cateId },
    //   isDelete: false,
    // });

    return res.status(200).json({
      status: true,
      message: `List of videos.`,
      data: [
        {
          videos: data,
          totalPages: Math.ceil(count / limit),
          currentPage: page,
          totalVideos: count,
        },
      ],
    });
  } catch (error) {
    return res.status(200).json({ status: false, message: `${error.message}` });
  }
};

exports.getmostpopularvideos = async (req, res) => {
  try {
    const page = parseInt(req.query.page);
    const limit = parseInt(req.query.limit);
    const skip = (page - 1) * limit;
    const authUser = req.authUserId;
    const currentEdgeId = req.currentEdge._id;
    let ruleCondition = await userAccessRulesCommonCondition({ userId: authUser, relation_id: req.relation_id });

    const count = await ContentArchiveVideo.countDocuments({
      isDelete: false,
      uploadstatus: { $ne: "inprocess" },
      ...ruleCondition,
      relation_id: ObjectId(req.relation_id),
    });

    const data = await ContentArchiveVideo.aggregate([
      {
        $match: {
          isDelete: false,
          uploadstatus: { $ne: "inprocess" },
          ...ruleCondition,
          relation_id: ObjectId(req.relation_id),
        },
      },
      {
        $addFields: {
          count: {
            $cond: {
              if: { $isArray: "$views" },
              then: { $size: "$views" },
              else: 0,
            },
          },
        },
      },
      { $sort: { count: -1 } },
      { $skip: skip },
      { $limit: limit },
      {
        $lookup: {
          from: "contentarchive_categories",
          let: { suggestion_id: "$categories" },
          pipeline: [
            {
              $match: {
                $expr: {
                  $in: ["$_id", "$$suggestion_id"],
                },
              },
            },
            { $project: { name: 1 } },
          ],
          as: "categories",
        },
      },
      {
        $lookup: {
          from: "contentarchive_subcategories",
          localField: "subcategory",
          foreignField: "_id",
          pipeline: [
            {
              $match: {
                isDelete: false,
              },
            },
          ],
          as: "subcategory",
        },
      },
      {
        $lookup: {
          from: "groups",
          let: { suggestion_id: "$restrictedAccessGroupId" },
          pipeline: [
            {
              $match: {
                $expr: {
                  $in: ["$_id", "$$suggestion_id"],
                },
              },
            },
            { $project: { groupTitle: 1 } },
          ],
          as: "group_ids",
        },
      },
      {
        $lookup: {
          from: "contentarchive_tags",
          localField: "tag",
          foreignField: "_id",
          pipeline: [
            {
              $match: {
                isDelete: false,
              },
            },
          ],
          as: "tag",
        },
      },
      {
        $project: {
          _id: 1,
          title: 1,
          video: 1,
          description: 1,
          thumbnail: 1,
          createdAt: 1,
          viewsCount: 1,
          commentsCount: 1,
          duration: 1,
          categories: 1,
          views: 1,
          likes: 1,
          user_video_pause: 1,
          tag: 1,
        },
      },
    ]);

    return res.status(200).json({
      status: true,
      message: `List of videos.`,
      data: [
        {
          videos: data,
          totalPages: Math.ceil(count / limit),
          currentPage: req.query.page,
          totalVideos: count,
        },
      ],
    });
  } catch (error) {
    return res.status(200).json({ status: false, message: `${error.message}` });
  }
};

exports.getrecentlyaddedvideos = async (req, res) => {
  try {
    const page = parseInt(req.query.page);
    const limit = parseInt(req.query.limit);
    const skip = (page - 1) * limit;
    const authUser = req.authUserId;
    const currentEdgeId = req.currentEdge._id;
    let ruleCondition = await userAccessRulesCommonCondition({ userId: authUser, relation_id: req.relation_id });
    const data = await ContentArchiveVideo.find({
      isDelete: false,
      ...ruleCondition,
      relation_id: ObjectId(req.relation_id),
    })
      .select("-__v")
      .sort({ createdAt: -1 })
      .limit(limit)
      .skip(skip);

    const count = await ContentArchiveVideo.countDocuments({
      isDelete: false,
      ...ruleCondition,
      relation_id: ObjectId(req.relation_id),
    });

    return res.status(200).json({
      status: true,
      message: `List of videos.`,
      data: [
        {
          videos: data,
          totalPages: Math.ceil(count / limit),
          currentPage: req.query.page,
          totalVideos: count,
        },
      ],
    });
  } catch (error) {
    return res.status(200).json({ status: false, message: `${error.message}` });
  }
};

exports.getrelatedvideos = async (req, res) => {
  try {
    const authUser = req.authUserId;
    const currentEdgeId = req.currentEdge._id;
    const videodata = await ContentArchiveVideo.findById(req.params.videoId);
    let ruleCondition = await userAccessRulesCommonCondition({ userId: authUser, relation_id: req.relation_id });
    let page = req.query.page ? +req.query.page : 1;
    let limit = req.query.limit ? +req.query.limit : 20;
    let skip = (page - 1) * limit;
    let count = 0;
    let eventIds = [];
    let speaker = [];
    let tag = [];
    let categories = [];
    if (videodata && videodata.categories && videodata.categories.length) {
      for (let i = 0; i < videodata.categories.length; i++) {
        categories.push(ObjectId(videodata.categories[i]));
      }
    };
    if (videodata && videodata.eventIds && videodata.eventIds.length) {
      for (let i = 0; i < videodata.eventIds.length; i++) {
        eventIds.push(ObjectId(videodata.eventIds[i]));
      }
    };
    if (videodata && videodata.speaker && videodata.speaker.length) {
      for (let i = 0; i < videodata.speaker.length; i++) {
        speaker.push(ObjectId(videodata.speaker[i]));
      }
    };
    if (videodata && videodata.tag && videodata.tag.length) {
      for (let i = 0; i < videodata.tag.length; i++) {
        tag.push(ObjectId(videodata.tag[i]));
      }
    };

    let eventData = [{
      $match: {
        _id: { $ne: new ObjectId(req.params.videoId) },
        eventIds: { $nin: eventIds },
        isDelete: false,
        uploadstatus: { $ne: "inprocess" },
        relation_id: ObjectId(req.relation_id),
        ...ruleCondition
      },
    }];

    let speakerData = [{
      $match: {
        _id: { $ne: new ObjectId(req.params.videoId) },
        speaker: { $in: speaker },
        isDelete: false,
        uploadstatus: { $ne: "inprocess" },
        relation_id: ObjectId(req.relation_id),
        ...ruleCondition
      },
    }];

    let tagData = [{
      $match: {
        _id: { $ne: new ObjectId(req.params.videoId) },
        tag: { $in: tag },
        isDelete: false,
        uploadstatus: { $ne: "inprocess" },
        relation_id: ObjectId(req.relation_id),
        ...ruleCondition
      },
    }];

    let categotyData = [{
      $match: {
        _id: { $ne: new ObjectId(req.params.videoId) },
        categories: { $in: categories },
        isDelete: false,
        uploadstatus: { $ne: "inprocess" },
        relation_id: ObjectId(req.relation_id),
        ...ruleCondition
      },
    }];

    let pipeline = [
      {
        $lookup: {
          from: "contentarchive_categories",
          let: { contentarchive_categories_id: "$categories" },
          pipeline: [
            {
              $match: {
                $expr: {
                  $in: ["$_id", "$$contentarchive_categories_id"],
                },
              },
            },
            { $project: { name: 1 } },
          ],
          as: "categories",
        },
      },
      {
        $lookup: {
          from: "contentarchive_subcategories",
          localField: "subcategory",
          foreignField: "_id",
          pipeline: [
            {
              $match: {
                isDelete: false,
              },
            },
          ],
          as: "subcategory",
        },
      },
      {
        $lookup: {
          from: "groups",
          let: { suggestion_id: "$restrictedAccessGroupId" },
          pipeline: [
            {
              $match: {
                $expr: {
                  $cond: {
                    if: { $isArray: "$$suggestion_id" },
                    then: { $in: ["$_id", "$$suggestion_id"] },
                    else: false
                  }
                }
              }
            },
            { $project: { groupTitle: 1 } },
          ],
          as: "group_ids",
        },
      },
      {
        $lookup: {
          from: "contentarchive_tags",
          localField: "tag",
          foreignField: "_id",
          pipeline: [
            {
              $match: {
                isDelete: false,
              },
            },
          ],
          as: "tag",
        },
      },
      {
        $addFields: {
          viewsCount: {
            $cond: {
              if: { $isArray: "$views" },
              then: { $add: [{ $size: "$views" }, "$starting_view_cnt"] },
              else: "$starting_view_cnt",
            },
          },
        },
      },
      {
        $addFields: {
          commentsCount: {
            $cond: {
              if: { $isArray: "$comments" },
              then: { $size: "$comments" },
              else: 0,
            },
          },
        },
      },
      {
        $project: {
          _id: 1,
          title: 1,
          video: 1,
          description: 1,
          thumbnail: 1,
          createdAt: 1,
          viewsCount: 1,
          commentsCount: 1,
          duration: 1,
          categories: 1,
          views: 1,
          likes: 1,
          user_video_pause: 1,
          tag: 1,
        },
      },
    ];

    const [eventDatas, speakerDatas, tagDatas, categoryDatas,] = await Promise.all([
      ContentArchiveVideo.aggregate([...eventData, ...pipeline, { $sort: { createdAt: -1 } },]),
      ContentArchiveVideo.aggregate([...speakerData, ...pipeline, { $sort: { createdAt: -1 } },]),
      ContentArchiveVideo.aggregate([...tagData, ...pipeline, { $sort: { createdAt: -1 } },]),
      ContentArchiveVideo.aggregate([...categotyData, ...pipeline, { $sort: { createdAt: -1 } },]),
    ]);

    const uniqueObjects = [...eventDatas, ...speakerDatas, ...tagDatas, ...categoryDatas,];

    // Function to filter unique objects based on _id
    function filterUniqueByProperty(arr, prop) {
      let seen = {};
      return arr.filter(item => {
        let key = item[prop];
        return seen.hasOwnProperty(key) ? false : (seen[key] = true);
      });
    }

    let sortData = filterUniqueByProperty(uniqueObjects, '_id');
    let data = sortData.slice(skip, skip + limit);
    if (sortData.length !== 0) { count = sortData.length };

    var mobile_desc = data?.map(async (item, index) => {
      let mobile_description = "";
      if (item.description !== undefined) {
        let without_html_description = item.description.replace(/&amp;/g, "&");
        without_html_description = item.description.replace(/(<([^>]+)>)/g, "");
        without_html_description = without_html_description.replace(
          /(\r\n|\n|\r)/gm,
          ""
        );
        mobile_description = without_html_description.substring(0, 600);
      }
      var url = s3.getSignedUrl("getObject", {
        Bucket: "arn:aws:s3:us-east-2:496737174815:accesspoint/accessforapp",
        Key: item.video,
        Expires: 100000,
      });
      item.video = url;
      item.mobile_description = mobile_description.trim();
    });
    await Promise.all([...mobile_desc]);

    return res.status(200).json({
      status: true,
      message: `List of videos.`,
      data: [
        {
          videos: data,
          totalPages: Math.ceil(count / limit),
          currentPage: page.toString(),
          totalVideos: count,
        },
      ],
    });
  } catch (error) {
    return res.status(200).json({ status: false, message: `${error.message}` });
  }
};

// get related video based on event ids
exports.getrelatedvideosByEventIds = async (req, res) => {
  try {
    const authUser = req.authUserId;
    let ruleCondition = await userAccessRulesCommonCondition({ userId: authUser, relation_id: req.relation_id });
    let ids = [];
    for (let i = 0; i < req.body.eventIds.length; i++) {
      ids.push(ObjectId(req.body.eventIds[i]));
    }

    let match = {
      _id: { $ne: new ObjectId(req.params.videoId) },
      eventIds: { $in: ids },
      isDelete: false,
      uploadstatus: { $ne: "inprocess" },
      ...ruleCondition,
      relation_id: ObjectId(req.relation_id),
    }

    if (ids.length) {
      let pipeline = [
        {
          $match: match
        },
        {
          $addFields: {
            viewsCount: {
              $cond: {
                if: { $isArray: "$views" },
                then: { $add: [{ $size: "$views" }, "$starting_view_cnt"] },
                else: "$starting_view_cnt",
              },
            },
          },
        },
        { $sort: { createdAt: -1 } },
        {
          $project: {
            _id: 1,
            title: 1,
            video: 1,
            thumbnail: 1,
            createdAt: 1,
            viewsCount: 1,
            duration: 1,
          },
        },
      ]

      let [all, count] = await Promise.all([
        ContentArchiveVideo.aggregate([...pipeline,]),
        ContentArchiveVideo.countDocuments(match)
      ]);

     return res.status(200).send({ 
      status: true, 
      message: "List of videos.!", 
      videos: all,  
      totalVideos: count })
    
    } else {
      return res.status(200).send({ status: false, message: `Please enter event id!`, })
    }
  } catch (error) {
    return res.status(200).json({ status: false, message: `${error.message}` });
  }
};
exports.getnewestcommentsvideo = async (req, res) => {
  try {
    const page = parseInt(req.query.page);
    const limit = parseInt(req.query.limit);
    const skip = (page - 1) * limit;
    const authUser = req.authUserId;
    const currentEdgeId = req.currentEdge._id;
    let ruleCondition = await userAccessRulesCommonCondition({ userId: authUser, relation_id: req.relation_id });

    const data = await ContentArchiveVideo.aggregate([
      {
        $match: {
          isDelete: false,
          ...ruleCondition,
          relation_id: ObjectId(req.relation_id),
        },
      },
      {
        $lookup: {
          from: "contentarchivecomments",
          localField: "comments",
          foreignField: "_id",
          as: "outcomments",
        },
      },
      { $sort: { "outcomments.createdAt": -1 } },
      { $skip: skip },
      { $limit: limit },
      {
        $lookup: {
          from: "contentarchive_categories",
          let: { suggestion_id: "$categories" },
          pipeline: [
            {
              $match: {
                $expr: {
                  $in: ["$_id", "$$suggestion_id"],
                },
              },
            },
            { $project: { name: 1 } },
          ],
          as: "categories",
        },
      },
      {
        $lookup: {
          from: "contentarchive_subcategories",
          localField: "subcategory",
          foreignField: "_id",
          pipeline: [
            {
              $match: {
                isDelete: false,
              },
            },
          ],
          as: "subcategory",
        },
      },
      {
        $lookup: {
          from: "groups",
          let: { suggestion_id: "$restrictedAccessGroupId" },
          pipeline: [
            {
              $match: {
                $expr: {
                  $in: ["$_id", "$$suggestion_id"],
                },
              },
            },
            { $project: { groupTitle: 1 } },
          ],
          as: "group_ids",
        },
      },
      {
        $lookup: {
          from: "contentarchive_tags",
          localField: "tag",
          foreignField: "_id",
          pipeline: [
            {
              $match: {
                isDelete: false,
              },
            },
          ],
          as: "tag",
        },
      },
    ]);

    const count = await ContentArchiveVideo.countDocuments({
      isDelete: false,
      ...ruleCondition,
      relation_id: ObjectId(req.relation_id),
    });

    return res.status(200).json({
      status: true,
      message: `List of videos.`,
      data: [
        {
          videos: data,
          totalPages: Math.ceil(count / limit),
          currentPage: req.query.page,
          totalVideos: count,
        },
      ],
    });
  } catch (error) {
    return res.status(200).json({ status: false, message: `${error.message}` });
  }
};

async function generate_video_resolution_240(video_v, total_duration) {
  return new Promise((resolve, reject) => {
    var video_name = video_v.split("/").pop();
    var loc_file_name = Date.now() + "-240p-" + video_name;
    var splittedprog;
    var seconds;
    var url = s3.getSignedUrl("getObject", {
      Bucket: "arn:aws:s3:us-east-2:496737174815:accesspoint/accessforapp",
      Key: video_v,
      Expires: 100000,
    });
    // Perform transcoding, save new video to new file name
    ffmpeg(url)
      .videoCodec("libx264")
      .videoBitrate(350)
      .output(loc_file_name)
      .on("error", function (err) {
        resolve("");
      })
      .on("progress", function (progress) {
        splittedprog = progress.timemark.split(":");
        seconds = 0;
        if (typeof splittedprog == "undefined") {
          seconds = progress.timemark;
        } else {
          if (typeof splittedprog[3] != "undefined") {
            seconds =
              parseInt(splittedprog[0]) * 24 * 60 * 60 +
              parseInt(splittedprog[1]) * 60 * 60 +
              parseInt(splittedprog[2]) * 60 +
              parseInt(splittedprog[3]);
          } else if (typeof splittedprog[2] != "undefined") {
            seconds =
              parseInt(splittedprog[0]) * 60 * 60 +
              parseInt(splittedprog[1]) * 60 +
              parseInt(splittedprog[2]);
          } else if (typeof splittedprog[1] != "undefined") {
            seconds =
              parseInt(splittedprog[0]) * 60 + parseInt(splittedprog[1]);
          } else if (typeof splittedprog[0] != "undefined") {
            seconds = parseInt(splittedprog[0]);
          }
        }
        ProcessStates = 25 + Math.floor((seconds * 25) / total_duration);
      })
      .on("end", async function () {
        var upload_s3 = await s3
          .upload({
            Bucket: process.env.AWS_BUCKET,
            Key:
              "uploads/content-archive/videos/" +
              Date.now() +
              "-240p-" +
              video_name,
            Body: fs.createReadStream(loc_file_name),
            ACL: "public-read",
          })
          .promise();
        fs.unlinkSync(loc_file_name);
        resolve(upload_s3.Key);
      })
      .run();
  });
}

async function generate_video_resolution_360(video_v, total_duration) {
  return new Promise((resolve, reject) => {
    var video_name = video_v.split("/").pop();
    var loc_file_name = Date.now() + "-360p-" + video_name;
    var splittedprog;
    var seconds;
    var url = s3.getSignedUrl("getObject", {
      Bucket: "arn:aws:s3:us-east-2:496737174815:accesspoint/accessforapp",
      Key: video_v,
      Expires: 100000,
    });
    ffmpeg(url)
      .videoCodec("libx264")
      .videoBitrate(700)
      .output(loc_file_name)
      .on("error", function (err) {
        resolve("");
      })
      .on("progress", function (progress) {
        splittedprog = progress.timemark.split(":");
        seconds = 0;
        if (typeof splittedprog == "undefined") {
          seconds = progress.timemark;
        } else {
          if (typeof splittedprog[3] != "undefined") {
            seconds =
              parseInt(splittedprog[0]) * 24 * 60 * 60 +
              parseInt(splittedprog[1]) * 60 * 60 +
              parseInt(splittedprog[2]) * 60 +
              parseInt(splittedprog[3]);
          } else if (typeof splittedprog[2] != "undefined") {
            seconds =
              parseInt(splittedprog[0]) * 60 * 60 +
              parseInt(splittedprog[1]) * 60 +
              parseInt(splittedprog[2]);
          } else if (typeof splittedprog[1] != "undefined") {
            seconds =
              parseInt(splittedprog[0]) * 60 + parseInt(splittedprog[1]);
          } else if (typeof splittedprog[0] != "undefined") {
            seconds = parseInt(splittedprog[0]);
          }
        }
        ProcessStates = 50 + Math.floor((seconds * 25) / total_duration);
      })
      .on("end", async function () {
        var upload_s3 = await s3
          .upload({
            Bucket: process.env.AWS_BUCKET,
            Key:
              "uploads/content-archive/videos/" +
              Date.now() +
              "-360p-" +
              video_name,
            Body: fs.createReadStream(loc_file_name),
            ACL: "public-read",
          })
          .promise();
        fs.unlinkSync(loc_file_name);
        resolve(upload_s3.Key);
      })
      .run();
  });
}

async function generate_video_resolution_480(video_v) {
  return new Promise((resolve, reject) => {
    var video_name = video_v.split("/").pop();
    var loc_file_name = video_name;
    var url = s3.getSignedUrl("getObject", {
      Bucket: "arn:aws:s3:us-east-2:496737174815:accesspoint/accessforapp",
      Key: video_v,
      Expires: 100000,
    });
    ffmpeg(url)
      .videoCodec("libx264")
      .videoBitrate(1200)
      .output(loc_file_name)
      .on("error", function (err) {
        resolve("");
      })
      .on("progress", function (progress) {
      })
      .on("end", async function () {
        var upload_s3 = await s3
          .upload({
            Bucket: process.env.AWS_BUCKET,
            Key:
              "uploads/content-archive/videos/" +
              Date.now() +
              "-480p-" +
              video_name,
            Body: fs.createReadStream(loc_file_name),
            ACL: "public-read",
          })
          .promise();
        fs.unlinkSync(loc_file_name);
        resolve(upload_s3.Key);
      })
      .run();
  });
}

async function getsubtitlefile(video_v) {
  try {
    var video_name = video_v.split("/").pop();
    var subtitle_URL = "";
    ProcessStates = 10;
    const params = {
      TranscriptionJobName: "speechrecognize_CurrentJob" + Math.random(),
      LanguageCode: "en-US",
      Media: {
        MediaFileUri: "s3://mds-community/" + video_v,
      },
      OutputBucketName: process.env.AWS_BUCKET,
      OutputKey: "videos-subtitles/",
      Subtitles: {
        Formats: ["vtt"],
        OutputStartIndex: 1,
      },
      ACL: "public-read",
    };
    const transcribeConfig = {
      region,
      credentials,
    };
    const transcribeClient = new TranscribeClient(transcribeConfig);
    const data_transcibe = await transcribeClient.send(
      new StartTranscriptionJobCommand(params)
    );
    ProcessStates = 15;
    const data_onsucess = await getTranscriptionDetails(params);
    if (data_onsucess.TranscriptionJob.TranscriptionJobStatus === "COMPLETED") {
      var splited_url =
        data_onsucess.TranscriptionJob.Subtitles.SubtitleFileUris[0].split("/");
      subtitle_URL =
        splited_url[splited_url.length - 2] +
        "/" +
        splited_url[splited_url.length - 1];
    } else {
      subtitle_URL = "";
    }
    ProcessStates = 25;
    return subtitle_URL;
  } catch (e) {
    console.log(e);
    return "";
  }
}

const getTranscriptionDetails = async (params) => {
  try {
    ProcessStates = 20;
    while (true) {
      const transcribeConfig = {
        region,
        credentials,
      };
      const transcribeClient = new TranscribeClient(transcribeConfig);
      const data = await transcribeClient.send(
        new GetTranscriptionJobCommand(params)
      );
      const status = data.TranscriptionJob.TranscriptionJobStatus;
      if (status === "COMPLETED") {
        ProcessStates = 25;
        const data_delete = await transcribeClient.send(
          new DeleteTranscriptionJobCommand(params)
        );
        return data;
      } else if (status === "FAILED") {
        return data.TranscriptionJob.FailureReason;
      } else {
      }
    }
  } catch (err) {
    console.log("Error", err);
  }
};

exports.getProcessStatus = async (req, res) => {
  try {
    return res.status(200).json({ data: ProcessStates });
  } catch (e) {
    console.log(e);
  }
};

exports.get_allvideos_from_s3bucket = async (req, res) => {
  try {
    let params = {
      Bucket: process.env.AWS_BUCKET,
      Prefix: "testing/",
    };
    const allKeys = [];
    var done = function (err, data) {
      if (err)
        return res
          .status(200)
          .json({ status: false, data: err, message: "err" });
      else
        return res
          .status(200)
          .json({ status: false, data: data, message: "successfull" });
    };
    var num = 1;
    const getallgroup = await Group.find({ isDelete: false });
    var allgrps = [];
    for (var i = 0; i < getallgroup.length; i++) {
      allgrps[i] = getallgroup[i]._id;
    }
    listAllKeys();

    function listAllKeys() {
      s3.listObjects(
        { Bucket: process.env.AWS_BUCKET, Prefix: "testing/" },
        function (err, data) {
          if (data.Contents.length) {
            data.Contents.forEach(async (file, index) => {
              var params_copy = {
                Bucket: process.env.AWS_BUCKET,
                CopySource: process.env.AWS_BUCKET + "/" + file.Key,
                Key: file.Key.replace(
                  "testing/",
                  "uploads/content-archive/videos/"
                ),
                ACL: "public-read",
              };
              s3.copyObject(params_copy, async function (copyErr, copyData) {
                if (copyErr) {
                  console.log(copyErr);
                } else {
                  if (params_copy.Key !== "uploads/content-archive/videos/") {
                    const metadata = await s3
                      .headObject({
                        Bucket: process.env.AWS_BUCKET,
                        Key: params_copy.Key,
                      })
                      .promise();
                    const newentry = new ContentArchiveVideo({
                      video: params_copy.Key,
                      title: metadata.Metadata.title,
                      restrictedAccessGroupId: allgrps,
                      description: metadata.Metadata.description,
                    });
                    const added_data = await newentry.save();
                    allKeys.push(added_data);
                    await s3
                      .deleteObject({
                        Bucket: process.env.AWS_BUCKET,
                        Key: file.Key,
                      })
                      .promise();
                  }
                  num++;
                  if (data.Contents.length === num) {
                    res.status(200).json({
                      status: false,
                      data: allKeys,
                      message: "successfull",
                    });
                  }
                }
              });
            });
          }
        }
      );
    }
  } catch (e) {
    console.log(e);
  }
};

/** Code By SJ Speaker CURD Start test Specket 1**/
exports.createSpeakerInUser = async (req, res) => {
  try {
    const getEventAttendeeEmail = await User.findOne({
      "Preferred Email": req.body.email.toLowerCase(),
      $or: [{ isDelete: false }, { isDelete: { $exists: false } }],
    }).lean();

    if (!req?.relation_id)
      return res.status(404).json({
        success: false,
        message: "Relation ID not found!",
      });

    const community = await communities.findOne({
      _id: req?.relation_id,
      isDelete: false,
    });

    if (!community)
      return res.status(404).json({
        success: false,
        message: "Community is not found!",
        data: {},
      });

    if (getEventAttendeeEmail) {
      if (!getEventAttendeeEmail.attendeeDetail) {
        const updateEventAttendeeDetail = await User.findOneAndUpdate(
          {
            "Preferred Email": req.body.email.toLowerCase(),
            $or: [{ isDelete: false }, { isDelete: { $exists: false } }],
          },
          {
            first_name: getEventAttendeeEmail["First Name"]
              ? getEventAttendeeEmail["First Name"]
              : "",
            last_name: getEventAttendeeEmail["Last Name"]
              ? getEventAttendeeEmail["Last Name"]
              : "",
            attendeeDetail: {
              title: "",
              name: (getEventAttendeeEmail["Full Name"]
                ? getEventAttendeeEmail["Full Name"]
                : (getEventAttendeeEmail["Last Name"]
                  ? getEventAttendeeEmail["Last Name"]
                  : "") +
                " " +
                (getEventAttendeeEmail["First Name"]
                  ? getEventAttendeeEmail["First Name"]
                  : "")
              ).trim(),
              firstName: getEventAttendeeEmail["First Name"]
                ? getEventAttendeeEmail["First Name"]
                : "",
              lastName: getEventAttendeeEmail["Last Name"]
                ? getEventAttendeeEmail["Last Name"]
                : "",
              email: getEventAttendeeEmail["Preferred Email"]
                ? getEventAttendeeEmail["Preferred Email"]
                : "",
              company: "",
              phone: "",
              facebook: "",
              linkedin: "",
              firebaseId: "",
              profession: "",
              description: "",
              offer: "",
              contactPartnerName: "",
              evntData: [],
            },
          }
        );
      }

      const fetchUserEdge = await userEdgesService.repository.findUserEdges({
        filter: {
          user_id: getEventAttendeeEmail?._id,
          relation_id: community?._id,
          type: { $nin: ["DEFAULT", "CU"] },
          isDelete: false,
        },
      });

      if (!fetchUserEdge.length) {
        const { data: role } = await rolesService.findRole({
          filter: {
            role_name: "GUEST_USER",
          },
        });

        const { data: userEdgeForCommunity } =
          await userEdgesService.createUserEdge({
            userEdgeData: {
              relation_id: req?.relation_id,
              user_id: getEventAttendeeEmail?._id,
              role_id: role?._id,
              platform: false,
              type: "GU",
            },
          });

        if (userEdgeForCommunity) {
          await User.findOneAndUpdate(
            {
              _id: getEventAttendeeEmail?._id,
            },
            {
              $push: {
                user_edges: userEdgeForCommunity?._id,
              },
            },
            { new: true }
          );
        }

        if (userEdgeForCommunity) {
          try {
            const eventGUSubscriptionPayload = {
              method: "post",
              url: `https://${community.nickname}.${BASE_URL}/api/billings/api/v1/subscriptions/guestUser`,
              data: {
                edge: userEdgeForCommunity,
                user_id: getEventAttendeeEmail?._id.toString(),
                relation_id: community?._id.toString(),
              },
            };
            const response = await axios.request(eventGUSubscriptionPayload);

            if (response.status === 200)
              await userEdgesService.repository.findAndUpdateUserEdge({
                filter: {
                  _id: userEdgeForCommunity?._id,
                  isDelete: false,
                },
                userEdgeData: {
                  subscription_id: response?.data?._id,
                },
              });
          } catch (error) {
            console.error("Error during subscription request:", error);
            return res
              .status(500)
              .json({ status: false, message: "Subscription service error." });
          }
        }

        return res
          .status(200)
          .json({ status: true, message: "Speaker created successfully." });
      } else {
        return res
          .status(200)
          .json({ status: false, message: `Speaker email must be unique.` });
      }
    }
    const io = req.app.get("socketio");
    var getEventAttendeeAuth = null;
    if (req.body.firebaseId) {
      getEventAttendeeAuth = await User.findOne({
        firebaseId: req.body.firebaseId,
      }).lean();
    }

    if (!getEventAttendeeEmail && !getEventAttendeeAuth) {
      const newEventAttendee = new User({
        "Preferred Email": req.body.email.toLowerCase(),
        firebaseId: req.body.firebaseId,
        passcode: req.body.passcode,
        isDelete: false,
        first_name: req.body.firstName ? req.body.firstName : "",
        last_name: req.body.lastName ? req.body.lastName : "",
        display_name: req.body.display_name ? req.body.display_name : "",
        profileImg: req?.origi_profile,
        status: "ACTIVE",
        attendeeDetail: {
          title: "",
          name: req.body.name,
          firstName: req.body.firstName ? req.body.firstName : "",
          lastName: req.body.lastName ? req.body.lastName : "",
          email: req.body.email.toLowerCase(),
          company: req.body.company,
          phone: req.body.phone,
          facebook: req.body.facebook,
          linkedin: req.body.linkedin,
          firebaseId: req.body.firebaseId,
          profession: req.body.profession,
          description: "",
          offer: "",
          contactPartnerName: "",
          evntData: [],
        },
      });

      const eventAttendeeData = await newEventAttendee.save();
      if (!eventAttendeeData)
        return res.status(200).json({
          status: false,
          message: "Something went wrong while creating speaker!",
        });

      if (eventAttendeeData) {
        const user_edges_id = [];

        const { data: defaultRole } = await rolesService.findRole({
          filter: {
            role_name: "DEFAULT",
          },
        });

        const { data: userEdge } = await userEdgesService.createUserEdge({
          userEdgeData: {
            default: true,
            owner: true,
            user_id: eventAttendeeData?._id,
            role_id: defaultRole?._id,
            type: "DEFAULT",
          },
        });

        if (userEdge) user_edges_id.push(userEdge?._id);

        const { data: role } = await rolesService.findRole({
          filter: {
            role_name: "GUEST_USER",
          },
        });

        const { data: userEdgeForCommunity } =
          await userEdgesService.createUserEdge({
            userEdgeData: {
              relation_id: req?.relation_id,
              user_id: eventAttendeeData?._id,
              role_id: role?._id,
              platform: false,
              type: "GU",
            },
          });

        if (userEdgeForCommunity) user_edges_id.push(userEdgeForCommunity?._id);

        if (user_edges_id.length) {
          await User.findOneAndUpdate(
            {
              _id: eventAttendeeData?._id,
            },
            {
              $set: {
                user_edges: user_edges_id,
              },
            },
            { new: true }
          );
        }

        if (userEdgeForCommunity) {
          try {
            const eventGUSubscriptionPayload = {
              method: "post",
              url: `https://${community.nickname}.${BASE_URL}/api/billings/api/v1/subscriptions/guestUser`,
              data: {
                edge: userEdgeForCommunity,
                user_id: eventAttendeeData?._id.toString(),
                relation_id: community?._id.toString(),
              },
            };
            const response = await axios.request(eventGUSubscriptionPayload);

            if (response.status === 200)
              await userEdgesService.repository.findAndUpdateUserEdge({
                filter: {
                  _id: userEdgeForCommunity?._id,
                  isDelete: false,
                },
                userEdgeData: {
                  subscription_id: response?.data?._id,
                },
              });
          } catch (error) {
            console.error("Error during subscription request:", error);
            return res
              .status(500)
              .json({ status: false, message: "Subscription service error." });
          }
        }
      }

      return res
        .status(200)
        .json({ status: true, message: "Speaker created successfully." });
    }
  } catch (error) {
    if (error.name === "MongoServerError" && error.code === 11000) {
      return res
        .status(200)
        .json({ status: false, message: `Speaker email must be unique.` });
    } else {
      return res
        .status(200)
        .json({ status: false, message: `Something went wrong. ${error}` });
    }
  }
};

exports.createSpeaker = async (req, res) => {
  try {
    const checkname = await ContentSpeaker.find({
      email: req.body.email.toLowerCase(),
      isDelete: false,
    });
    if (checkname && checkname.length > 0) {
      return res
        .status(200)
        .json({ status: false, message: `Speaker email must be unique.` });
    }

    const newentry = new ContentSpeaker({
      name: req.body.name,
      photo: req.speaker_pic,
      company: req.body.company,
      title: req.body.title,
      email: req.body.email.toLowerCase(),
      phone: req.body.phone,
      facebook: req.body.facebook,
      linkedin: req.body.linkedin,
      custom: req.body.custom,
      designation: req.body.designation,
      relation_id: ObjectId(req.relation_id),
    });
    const result = await newentry.save();

    return res
      .status(200)
      .json({ status: true, message: `Speaker created.`, data: result });
  } catch (error) {
    if (error.name === "MongoServerError" && error.code === 11000) {
      return res
        .status(200)
        .json({ status: false, message: `Speaker email must be unique.` });
    } else {
      return res
        .status(200)
        .json({ status: false, message: `Something went wrong. ${error}` });
    }
  }
};

exports.getAllSpeakerList = async (req, res) => {
  try {
    const data = await ContentSpeaker.find({ isDelete: false,relation_id: ObjectId(req.relation_id), });
    return res
      .status(200)
      .json({ status: true, message: `List of speakers.`, data: data });
  } catch (error) {
    return res.status(200).json({ status: false, message: `${error.message}` });
  }
};

exports.getAllSpeakerListSuggestion = async (req, res) => {
  try {
    const data = await ContentSpeaker.find(
      { isDelete: false, relation_id: ObjectId(req.relation_id) },
      { name: 1, relation_id: 0 }
    );
    return res
      .status(200)
      .json({ status: true, message: `List of speakers suggestions.`, data: data });
  } catch (error) {
    return res.status(200).json({ status: false, message: `${error.message}` });
  }
};

//get speakers listing based on video assign 
exports.getSpeakerListBasedOnVideo = async (req, res) => {
  try {
    const authUser = req.authUserId;
    const currentEdgeId = req.currentEdge._id;
    let ruleCondition = await userAccessRulesCommonCondition({ userId: authUser, relation_id: req.relation_id });
   
    let match = {
      isDelete: false,
      uploadstatus: { $ne: "inprocess" },
      relation_id: ObjectId(req.relation_id),
      ...ruleCondition};

    let pipeline =[
      {
        '$match': match,
      },
      {
        '$lookup': {
          'from': 'airtable-syncs', 
          'localField': 'speaker', 
          'foreignField': '_id', 
          'pipeline': [
            {
              '$match': {
                '$or': [
                  {
                    'isDelete': false
                  }, 
                  {
                    'isDelete': {
                      '$exists': false
                    }
                  }
                ], 
                '$and': [
                  {
                    'Preferred Email': {
                      '$ne': null
                    }
                  }, 
                  {
                    'Preferred Email': {
                      '$ne': ''
                    }
                  }
                ]
              }
            }, 
            {
              '$project': {
                'PreferredEmail': '$Preferred Email', 
                'first_name': {
                  '$ifNull': [
                    '$first_name', ''
                  ]
                }, 
                'last_name': {
                  '$ifNull': [
                    '$last_name', ''
                  ]
                },
                display_name: {
                  $cond: {
                      if: {
                          $or: [
                              { $eq: ["$display_name", null] },
                              { $eq:[ "$display_name", "" ]}
                          ]
                      },
                      then: { $concat: ["$first_name", " ", "$last_name"] },
                      else: "$display_name"
                  }
                }, 
              }
            }
          ], 
          'as': 'speakers'
        }
      },
      {
        '$unwind': {
          'path': '$speakers', 
          'preserveNullAndEmptyArrays': false
        }
      }, 
      {
        '$group': {
          '_id': null, 
          'speakers': {
            '$addToSet': '$speakers'
          }
        }
      }, 
      {
        '$unwind': {
          'path': '$speakers', 
          'preserveNullAndEmptyArrays': false
        }
      }, 
      {
        '$replaceRoot': {
          'newRoot': '$speakers'
        }
      },
    ];

    const data = await ContentArchiveVideo.aggregate([...pipeline]);
    return res
      .status(200)
      .json({ status: true, message: `List of speakers.`, data: data });
  } catch (error) {
    return res.status(200).json({ status: false, message: `${error.message}` });
  }
};
exports.adminSpeakerList = async (req, res) => {
  try {
    const data = await ContentSpeaker.aggregate([
      {
        $match: {
          isDelete: false,
          relation_id: ObjectId(req.relation_id),
        },
      },
      {
        $lookup: {
          from: "contentarchive_videos",
          localField: "_id",
          foreignField: "speaker",
          pipeline: [
            {
              $match: {
                isDelete: false,
              },
            },
          ],
          as: "totalcount",
        },
      },
      {
        $project: {
          _id: "$_id",
          name: 1,
          counts: {
            $size: "$totalcount",
          },
        },
      },
      {
        $sort: { createdAt: -1 },
      },
    ]);

    return res
      .status(200)
      .json({ status: true, message: `List of speakers.`, data: data });
  } catch (error) {
    return res.status(200).json({ status: false, message: `${error.message}` });
  }
};

exports.speakerById = async (req, res) => {
  try {
    const speakerData = await ContentSpeaker.findById(
      new ObjectId(req.params.id)
    );
    if (speakerData) {
      return res.status(200).json({
        status: true,
        message: `Speaker data retrive.`,
        data: speakerData,
      });
    } else {
      return res
        .status(404)
        .json({ status: false, message: `Speaker data not found.`, data: [] });
    }
  } catch (error) {
    return res
      .status(200)
      .json({ status: false, message: `Something went wrong. ${error}` });
  }
};

exports.updateSpeaker = async (req, res) => {
  try {
    const checkname = await ContentSpeaker.find({
      $and: [
        { email: req.body.email },
        { _id: { $ne: new ObjectId(req.params.id) } },
      ],
      isDelete: false,
    });
    if (checkname && checkname.length > 0) {
      return res
        .status(200)
        .json({ status: false, message: `Speaker email must be unique.` });
    }

    const speakerData = await ContentSpeaker.findById(
      new ObjectId(req.params.id)
    ).lean();
    if (speakerData) {
      if (req.speaker_pic) {
        deleteImage(speakerData.photo);
      }
      const result = await ContentSpeaker.findOneAndUpdate(
        { _id: new ObjectId(req.params.id) },
        {
          name: req.body.name ?? speakerData.name,
          photo: req.speaker_pic ?? speakerData.photo,
          company: req.body.company ?? speakerData.company,
          title: req.body.title ?? speakerData.title,
          email: req.body.email ?? speakerData.email,
          phone: req.body.phone ?? speakerData.phone,
          facebook: req.body.facebook ?? speakerData.facebook,
          linkedin: req.body.linkedin ?? speakerData.linkedin,
          custom: req.body.custom ?? speakerData.custom,
          designation: req.body.designation ?? speakerData.designation,
        },
        { new: true }
      );
      return res
        .status(200)
        .json({ status: true, message: `Speaker data updated.`, data: result });
    } else {
      return res
        .status(404)
        .json({ status: false, message: `Speaker data not found.`, data: [] });
    }
  } catch (error) {
    if (error.name === "MongoServerError" && error.code === 11000) {
      return res
        .status(200)
        .json({ status: false, message: `Speaker email must be unique.` });
    } else {
      return res
        .status(200)
        .json({ status: false, message: `Something went wrong. ${error}` });
    }
  }
};

exports.updateVideoSpeaker = async (req, res) => {
  try {
    const { id } = req.params;
    if (!id || !mongoose.Types.ObjectId.isValid(id)) {
      return res.status(400).json({
        success: false,
        message: "ID is not found or not valid!",
        data: {},
      });
    }

    const fetchSpeaker = await User.findOne({
      "Preferred Email": req.body.email.toLowerCase(),
      _id: id,
      isDelete: false,
    }).lean();

    if (!fetchSpeaker) {
      return res
        .status(200)
        .json({ status: false, message: `Speaker is not found!` });
    }

    if (req?.origi_profile) {
      deleteImage(fetchSpeaker.profileImg);
    }
    const result = await User.findOneAndUpdate(
      { _id: new ObjectId(req.params.id) },
      {
        $set: {
          first_name: req?.body?.firstName ?? fetchSpeaker?.firstName,
          last_name: req?.body?.lastName ?? fetchSpeaker?.lastName,
          display_name: req.body.display_name ?? fetchSpeaker?.display_name,
          profileImg: req?.origi_profile ?? fetchSpeaker?.profileImg,
          status: "ACTIVE",
          attendeeDetail: {
            name: req?.body?.name ?? fetchSpeaker?.attendeeDetail?.name,
            firstName:
              req?.body?.firstName ?? fetchSpeaker?.attendeeDetail?.firstName,
            lastName: req?.body?.lastName ?? fetchSpeaker?.attendeeDetail?.lastName,
            email:
              req?.body?.email.toLowerCase() ?? fetchSpeaker?.attendeeDetail?.email,
            company: req?.body?.company ?? fetchSpeaker?.attendeeDetail?.company,
            phone: req?.body?.phone ?? fetchSpeaker?.attendeeDetail?.phone,
            facebook: req?.body?.facebook ?? fetchSpeaker?.attendeeDetail?.facebook,
            linkedin: req?.body?.linkedin ?? fetchSpeaker?.attendeeDetail?.linkedin,
            profession:
              req?.body?.profession ?? fetchSpeaker?.attendeeDetail?.profession,
          },
        },
      },
      { new: true }
    );

    if (!result) {
      return res.status(200).json({
        status: true,
        message: "Something went wrong in the update the speaker",
        data: {},
      });
    }

    return res
      .status(200)
      .json({ status: true, message: `Speaker data updated.`, data: result });
  } catch (error) {
    if (error.name === "MongoServerError" && error.code === 11000) {
      return res
        .status(200)
        .json({ status: false, message: `Speaker email must be unique.` });
    } else {
      return res
        .status(200)
        .json({ status: false, message: `Something went wrong. ${error}` });
    }
  }
};

exports.deleteSpeaker = async (req, res) => {
  try {
    const speakerData = await ContentSpeaker.findById(
      new ObjectId(req.params.id),
    );
    if (speakerData) {
      const result = await ContentSpeaker.findOneAndUpdate(
        { _id: new ObjectId(req.params.id) },
        { isDelete: true },
        { new: true }
      );
      return res
        .status(200)
        .json({ status: true, message: `Speaker data deleted.`, data: result });
    } else {
      return res
        .status(404)
        .json({ status: false, message: `Speaker data not found.`, data: [] });
    }
  } catch (error) {
    return res
      .status(200)
      .json({ status: false, message: `Something went wrong. ${error}` });
  }
};

// export speaker data
exports.exportSpeakers = async (req, res) => {
  try {
    const data = await ContentSpeaker.find({ isDelete: false ,relation_id: ObjectId(req.relation_id),})
      .select(
        "title name email company phone facebook linkedin type event"
      )
      .sort({ createdAt: -1 });
    if (data)
      return res
        .status(200)
        .json({ status: true, message: "All speakers list!", data: data });
    else
      return res
        .status(200)
        .json({ status: true, message: "No data found!", data: [] });
  } catch (error) {
    return res
      .status(500)
      .json({ status: false, message: "Internal server error!", error: error });
  }
};

// import speaker data
exports.importSpeakers = async (req, res) => {
  try {
    const body = req.body;
    const allSpeaker = body.allSpeakers;
    allSpeaker?.forEach(async (speakerData, i) => {
      const speaker = await ContentSpeaker.find({
        email: speakerData.email,
        isDelete: false,
        relation_id: ObjectId(req.relation_id),
      });

      if (speakerData.email && speaker && speaker.length) {
        await ContentSpeaker.findOneAndUpdate(
          { email: speakerData.email, event: { $nin: speakerData.eventId } },
          {
            title: speakerData.title,
            name: speakerData.name,
            company: speakerData.company,
            phone: speakerData.phone,
            facebook: speakerData.facebook,
            linkedin: speakerData.linkedin,
            type: speakerData.type,
            $push: { event: speakerData.eventId },
          },
          { new: true }
        );
        await ContentSpeaker.find({
          email: speakerData.email,
          event: { $in: speakerData.eventId },
        });
      } else {
        const newData = new ContentSpeaker({
          title: speakerData.title,
          name: speakerData.name,
          email: speakerData.email,
          company: speakerData.company,
          phone: speakerData.phone,
          facebook: speakerData.facebook,
          linkedin: speakerData.linkedin,
          type: speakerData.type,
          relation_id: ObjectId(req.relation_id),
          $push: { event: speakerData.eventId },
        });
        await newData.save();
      }

      if (i === allSpeaker.length - 1) {
        return res
          .status(200)
          .json({ status: true, message: "Import Done successfully." });
      }
    });
  } catch (e) {
    return res.status(200).json({ status: false, message: "Something wrong!" });
  }
};

/** Code By SJ Speaker CURD Ends **/

/** Code By SJ Tag CURD Start **/
exports.createTag = async (req, res) => {
  try {
    const checkname = await ContentTag.find({
      name: req.body.name,
      isDelete: false,
      relation_id: ObjectId(req.relation_id)
    });
    if (checkname && checkname.length > 0) {
      return res
        .status(200)
        .json({ status: false, message: `Tag name must be unique.` });
    }

    const newentry = new ContentTag({ name: req.body.name,relation_id: ObjectId(req.relation_id), });
    const result = await newentry.save();

    return res
      .status(200)
      .json({ status: true, message: `Tag created.`, data: result });
  } catch (error) {
    if (error.name === "MongoServerError" && error.code === 11000) {
      return res
        .status(200)
        .json({ status: false, message: `Tag name must be unique.` });
    } else {
      return res
        .status(200)
        .json({ status: false, message: `Something went wrong. ${error}` });
    }
  }
};

exports.adminTagList = async (req, res) => {
  try {
    const sortType = req.query.sortType === "Asc" ? 1 : -1;
    let search = req.query.search;

    const data = await ContentTag.aggregate([
      {
        $match: {
          isDelete: false,
          relation_id: ObjectId(req.relation_id),
        },
      },
      ...(search && search != "" ? [
        {
          $match: {
            $or: [
              { name: { $regex: ".*" + search + ".*", $options: "i" }, },
            ]
          },
        },
      ] : []),
      {
        $lookup: {
          from: "contentarchive_videos",
          localField: "_id",
          foreignField: "tag",
          pipeline: [
            {
              $match: {
                isDelete: false,
              },
            },
          ],
          as: "totalcount",
        },
      },
      {
        $lookup: {
          from: "partners",
          localField: "_id",
          foreignField: "tag",
          pipeline: [
            {
              $match: {
                isDelete: false,
              },
            },
          ],
          as: "totalpartners",
        },
      },
      {
        $lookup: {
          from: "events",
          localField: "_id",
          foreignField: "tag",
          pipeline: [
            {
              $match: {
                isDelete: false,
              },
            },
          ],
          as: "totalevents",
        },
      },
      {
        $project: {
          _id: "$_id",
          name: 1,
          createdAt: 1,
          counts: {
            $size: "$totalcount",
          },
          totalpartners: {
            $size: { $ifNull: ["$totalpartners", []] },
          },
          totalevents: {
            $size: { $ifNull: ["$totalevents", []] },
          },
        },
      },
      {
        $addFields: {
          sortFieldLower:
            req.query.sortField === "name"
              ? { $toLower: "$name" }
              : req.query.sortField === "counts"
              ? { $toInt: "$counts" }
              : req.query.sortField === "totalpartners"
              ? { $toInt: "$totalpartners" }
              : req.query.sortField === "totalevents"
              ? { $toInt: "$totalevents" }
              : { $toLower: "$createdAt" },
        },
      },
      { $sort: { sortFieldLower: sortType } },
      {
        $project: {
          sortFieldLower: 0,
        },
      },
    ]);
    let docTagList = []
    try {
      const res = await axios({
        method: 'post',
        url: nodeApiDocumentsApiUrl + "/v1/documents/get-tag-counts",
        headers: { Authorization: req.headers.authorization }
      });
      docTagList = res.data.data.data;
    } catch (error) {
      docTagList = []
    }
    for (let i = 0; i < data.length; i++) {
      data[i]["totalDocument"] = 0;
      let obj = docTagList.find(o => o._id.toString() == data[i]["_id"].toString());
      if (obj) {
        data[i]["totalDocument"] = obj.count;
      }
    }

    const countAllData = await ContentTag.countDocuments({ relation_id: ObjectId(req.relation_id), isDelete: false });

    return res
      .status(200)
      .json({ status: true, message: `List of tags.`, data: data, countAllData });
  } catch (error) {
    return res.status(200).json({ status: false, message: `${error.message}` });
  }
};

// adminTagSuggestionList
exports.adminTagSuggestionList = async (req, res) => {
  try {
    const data = await ContentTag.find(
      { isDelete: false,relation_id: ObjectId(req.relation_id), },
      { _id: 0, name: 1 },
    ).sort({ name: 1 }).lean();
    return res
      .status(200)
      .json({ status: true, message: `List of tags.`, data: data });
  } catch (error) {
    return res.status(200).json({ status: false, message: `${error.message}` });
  }
};

exports.tagById = async (req, res) => {
  try {
    const tagData = await ContentTag.findById(new ObjectId(req.params.id));
    if (tagData) {
      return res
        .status(200)
        .json({ status: true, message: `Tag data retrive.`, data: tagData });
    } else {
      return res
        .status(404)
        .json({ status: false, message: `Tag data not found.`, data: [] });
    }
  } catch (error) {
    return res
      .status(200)
      .json({ status: false, message: `Something went wrong. ${error}` });
  }
};

exports.tagByIds = async (req, res) => {
  try {
    let ids = req.body.ids ? req.body.ids : [];
    let idsTemp = [];
    let list = [];

    for (let i = 0; i < ids.length; i++) {
      idsTemp.push(new ObjectId(ids[i]));
    }
    if (idsTemp.length) {
      list = await ContentTag.find({ _id: { $in: idsTemp },relation_id: ObjectId(req.relation_id), });
    }
    return res.status(200).json({ status: true, message: `Tag data retrive.`, data: list });
  } catch (error) {
    return res
      .status(200)
      .json({ status: false, message: `Something went wrong. ${error}` });
  }
};

exports.updateTag = async (req, res) => {
  try {
    const checkname = await ContentTag.find({
      _id: { $nin: req.params.id },
      name: req.body.name,
      isDelete: false,
      relation_id: ObjectId(req.relation_id),
    });
    if (checkname && checkname.length > 0) {
      return res
        .status(200)
        .json({ status: false, message: `Tag name must be unique.` });
    }

    const tagData = await ContentTag.findById(new ObjectId(req.params.id));
    if (tagData) {
      const result = await ContentTag.findOneAndUpdate(
        { _id: new ObjectId(req.params.id) },
        { name: req.body.name },
        { new: true }
      );
      return res
        .status(200)
        .json({ status: true, message: `Tag updated successfully.`, data: result });
    } else {
      return res
        .status(404)
        .json({ status: false, message: `Tag data not found.`, data: [] });
    }
  } catch (error) {
    if (error.name === "MongoServerError" && error.code === 11000) {
      return res
        .status(200)
        .json({ status: false, message: `Tag name must be unique.` });
    } else {
      return res
        .status(200)
        .json({ status: false, message: `Something went wrong. ${error}` });
    }
  }
};

exports.deleteTag = async (req, res) => {
  try {
    const communityId = req.currentEdge.relation_id._id;
    const tagId = req.params.id;

    // Fetch the tag data
    const tagData = await ContentTag.findOne({_id:tagId,isDelete:false});
    if (!tagData) {
      return res
        .status(404)
        .json({ status: false, message: `Tag data not found.`, data: [] });
    }

    // Fetch chat channels associated with the tag
    const chatChannelData = await chatChannel.find({
      tagIds: { $in: [tagId] },
      relation_id:ObjectId(communityId),
      isDelete: false,
    });


    const listOfSocketEvents = [];

    if (chatChannelData && chatChannelData.length) {
      for (const channel of chatChannelData) {
        const channelId = channel._id;
        const deleteMemberIds = [];
        
        // Fetch members existing in the tag
        const membersInTag = await getAlluserInTag([tagId],communityId);

        // Ensure all member operations complete before proceeding
        await Promise.all(
          membersInTag.map(async (member) => {
            const existingInChannel = await chatChannelMembers.findOne({
              userId: member._id,
              channelId: channelId,
              user_type: "airtable-syncs",
              withUser: false,
            });

            if (existingInChannel) {
              await chatChannelMembers.deleteOne({ _id: existingInChannel._id });
              deleteMemberIds.push(member._id.toString());
            }
          })
        );
     
        // Prepare socket event data
        const messageData = {
          message: "",
          recipient_type: "chatChannel",
          sender_type: "adminuser",
          recipient: channelId,
          sender: req.admin_Id,
          type: "chatChannel",
          
          activity_status: true,
          activity: {
            type: "removedChannelMembers",
            userId: deleteMemberIds,
          },
          userTimeStamp: new Date().toISOString(),
        };

        listOfSocketEvents.push({
          messageData,
          deleteMultipleRecordFromChatList: deleteMemberIds,
          channelData: channel,
          addMemberIds: [], // Add specific members if needed
          eventType: "remove-channel-member-receive",
          relation_id: communityId,
          allMembersIds: deleteMemberIds,
        });
      }
    }

    console.log('listOfSocketEvents listOfSocketEvents ',listOfSocketEvents)

    if(listOfSocketEvents && listOfSocketEvents.length){
      const payload = {
        event:  "UPDATE_CHANNEL",
        data:{
          listSocketEvents: listOfSocketEvents,
          relation_id:communityId
        }
      }
      publishMessage(JSON.stringify(payload), "CHAT_CHANNEL")
    }


    // Mark the tag as deleted
    const result = await ContentTag.findByIdAndUpdate(
      tagId,
      { isDelete: true },
      { new: true }
    );

    return res
      .status(200)
      .json({ status: true, message: `Tag data deleted.`, data: result });
  } catch (error) {
    return res
      .status(500)
      .json({ status: false, message: `Something went wrong. ${error}` });
  }
};

/** Code By SJ Tag CURD Ends **/

exports.deleteCommentFromVideo = async (req, res) => {
  try {
    const result = await ContentArchiveVideo.updateMany(
      {},
      { comments: [] },
      { new: true }
    );
    return res.status(200).json({
      status: true,
      message: `Deleted comments from all videos.`,
      data: result,
    });
  } catch (error) {
    return res
      .status(200)
      .json({ status: false, message: `Something went wrong. ${error}` });
  }
};

exports.allVideoList = async (req, res) => {
  try {
    const authUser = req.authUserId;
    const currentEdgeId = req.currentEdge._id;
    let ruleCondition = await userAccessRulesCommonCondition({ userId: authUser, relation_id: req.relation_id });

    const result = await Promise.all(
      await ContentArchiveVideo.aggregate([
        {
          $match: {
            isDelete: false,
            relation_id: ObjectId(req.relation_id),
            ...ruleCondition,
          },
        },
        {
          $lookup: {
            from: "contentarchive_tags",
            localField: "tag",
            foreignField: "_id",
            pipeline: [
              {
                $match: {
                  isDelete: false,
                },
              },
            ],
            as: "tagData",
          },
        },
        {
          $project: {
            title: 1,
            description: 1,
            tagData:{_id: 1, name: 1}
          },
        },
      ])
    );

    return res.status(200).json({
      status: true,
      message: `Users All videos retrive.`,
      data: result,
    });
  } catch (error) {
    return res
      .status(200)
      .json({ status: false, message: `Something went wrong. ${error}` });
  }
};

exports.addSearchHistory = async (req, res) => {
  try {
    const { search } = req.body;
    const authUser = req.authUserId;
    const userData = await User.findById(authUser).select("_id").lean();

    var result = [];
    const checkname = await ContentSearch.find({
      name: search,
      userId: userData._id,
      relation_id: ObjectId(req.relation_id),
    });
    if (checkname && checkname.length > 0) {
      result = await ContentSearch.findOneAndUpdate(
        { name: search, userId: userData._id },
        { name: search, userId: userData._id },
        { new: true }
      );
    } else {
      const newentry = new ContentSearch({
        name: search,
        userId: userData._id,
        relation_id: ObjectId(req.relation_id),
      });
      result = await newentry.save();
    }

    return res
      .status(200)
      .json({ status: true, message: `Search history added.`, data: result });
  } catch (error) {
    return res
      .status(500)
      .json({ status: false, message: `Something went wrong. ${error}` });
  }
};

exports.removeSearchHistory = async (req, res) => {
  try {
    const Data = await ContentSearch.findById(new ObjectId(req.params.id));
    if (Data) {
      const result = await ContentSearch.findOneAndDelete(
        { _id: new ObjectId(req.params.id) },
        { new: true }
      );
      return res.status(200).json({
        status: true,
        message: `Search history removed.`,
        data: result,
      });
    } else {
      return res.status(404).json({
        status: false,
        message: `Search history not found.`,
        data: [],
      });
    }
  } catch (error) {
    return res
      .status(500)
      .json({ status: false, message: `Something went wrong. ${error}` });
  }
};

exports.topSearchHistory = async (req, res) => {
  try {
    const authUser = req.authUserId;
    const userData = await User.findById(authUser).select("_id").lean();

    const result = await ContentSearch.find({ userId: userData._id,relation_id: ObjectId(req.relation_id)})
      .sort({ updatedAt: -1 })
      .limit(10)
      .select("-__v -createdAt");

    return res
      .status(200)
      .json({ status: true, message: `Search history retrive.`, data: result });
  } catch (error) {
    return res
      .status(500)
      .json({ status: false, message: `Something went wrong. ${error}` });
  }
};

exports.getMetaData = async (req, res) => {
  try {
    const url = req.query.urlData;

    if (!url) {
      return res.status(400).json({ status: false, message: "URL is required." });
    }

    const response = await axios.get(url, { responseType: "text" });

    const metadata = await metascraper({ html: response.data, url });
  
    return res.status(200).json({ 
      status: true,
      message: "URL metadata retrieved successfully.",
      data: metadata,
    });
  } catch (error) {
    console.error("Error fetching metadata:", error);
    return res.status(500).json({
      status: false,
      message: `Something went wrong. ${error.message}`,
    });
  }
};


/** Code By SJ Event CURD Start **/
exports.createEvent = async (req, res) => {
  try {
    const eventName = req.body.name.toLowerCase();
    const checkname = await ContentEvent.find({
      name: eventName,
      isDelete: false,
      relation_id: ObjectId(req.relation_id),
    });
    if (checkname && checkname.length > 0) {
      return res
        .status(200)
        .json({ status: false, message: `Event name must be unique.` });
    }

    const newentry = new ContentEvent({ name: eventName,relation_id: ObjectId(req.relation_id), });
    const result = await newentry.save();

    const AllUsers = await User.find({ isDelete: false });
    AllUsers.forEach(async (userData, i) => {
      const userEventsAlreadyAdded = await User.findOne({
        _id: userData._id,
        userEvents: { $exists: true },
      });

      if (userEventsAlreadyAdded !== null) {
        const alreadyAdded = await User.findOne(
          { _id: userData._id, [`userEvents.${eventName}`]: true },
          { [`userEvents.${eventName}`]: 1 }
        );

        if (alreadyAdded !== null) {
          if (alreadyAdded.userEvents !== null) {
            await User.findOneAndUpdate(
              { _id: userData._id },
              { $set: { [`userEvents.${eventName}`]: true } }
            );
          }
        } else {
          const notAdded = await User.findOne(
            { _id: userData._id, [`userEvents.${eventName}`]: false },
            { [`userEvents.${eventName}`]: 1 }
          );
          if (notAdded !== null && notAdded.userEvents !== null) {
            await User.findOneAndUpdate(
              { _id: userData._id },
              { $set: { [`userEvents.${eventName}`]: false } }
            );
          } else {
            await User.findOneAndUpdate(
              { _id: userData._id },
              { $set: { [`userEvents.${eventName}`]: false } }
            );
          }
        }
      } else {
        await User.findOneAndUpdate(
          { _id: userData._id },
          { $set: { [`userEvents.${eventName}`]: false } }
        );
      }
    });

    return res
      .status(200)
      .json({ status: true, message: `Event created.`, data: result });
  } catch (error) {
    if (error.name === "MongoServerError" && error.code === 11000) {
      return res
        .status(200)
        .json({ status: false, message: `Event name must be unique.` });
    } else {
      return res
        .status(200)
        .json({ status: false, message: `Something went wrong. ${error}` });
    }
  }
};

exports.adminEventList = async (req, res) => {
  try {
    const sortField =
      req.query.sortField === "name"
        ? "name"
        : req.query.sortField === "counts"
          ? "counts"
          : "createdAt";
    const sortType = req.query.sortType === "Asc" ? 1 : -1;

    const data = await ContentEvent.aggregate([
      {
        $match: {
          isDelete: false,
          relation_id: ObjectId(req.relation_id),
        },
      },
      {
        $lookup: {
          from: "contentarchive_videos",
          localField: "name",
          foreignField: "eventFor",
          pipeline: [
            {
              $match: {
                isDelete: false,
              },
            },
          ],
          as: "totalcount",
        },
      },
      {
        $project: {
          _id: "$_id",
          name: 1,
          counts: {
            $size: "$totalcount",
          },
        },
      },
      {
        $addFields: {
          sortFieldLower: { $toLower: `$${sortField}` },
        },
      },
      { $sort: { sortFieldLower: sortType } },
      {
        $project: {
          sortFieldLower: 0,
        },
      },
    ]);

    return res
      .status(200)
      .json({ status: true, message: `List of events.`, data: data });
  } catch (error) {
    return res.status(200).json({ status: false, message: `${error.message}` });
  }
};

// event Suggestion List
exports.eventSuggestionList = async (req, res) => {
  try {
    const data = await ContentEvent.find(
      { isDelete: false,relation_id: ObjectId(req.relation_id) },
      { _id: 0, name: 1 }
    );
    return res
      .status(200)
      .json({ status: true, message: `List of events.`, data: data });
  } catch (error) {
    return res.status(200).json({ status: false, message: `${error.message}` });
  }
};

exports.eventById = async (req, res) => {
  try {
    const eventData = await ContentEvent.findById(new ObjectId(req.params.id));
    if (eventData) {
      return res.status(200).json({
        status: true,
        message: `Event data retrive.`,
        data: eventData,
      });
    } else {
      return res
        .status(404)
        .json({ status: false, message: `Event data not found.`, data: [] });
    }
  } catch (error) {
    return res
      .status(200)
      .json({ status: false, message: `Something went wrong. ${error}` });
  }
};

exports.updateEvent = async (req, res) => {
  try {
    const eventName = req.body.name.toLowerCase();
    const eventData = await ContentEvent.findById(new ObjectId(req.params.id));
    const checkname = await ContentEvent.find({
      _id: { $ne: ObjectId(eventData._id) },
      name: eventName,
      isDelete: false,
    });

    if (checkname && checkname.length > 0) {
      return res
        .status(200)
        .json({ status: false, message: `Event name must be unique.` });
    }

    if (eventData) {
      const result = await ContentEvent.findOneAndUpdate(
        { _id: new ObjectId(req.params.id) },
        { name: eventName },
        { new: true }
      );

      const eventVideos = await ContentArchiveVideo.find({
        isDelete: false,
        eventFor: eventData.name,

      });

      var temp = eventVideos?.map(async (video, index) => {
        await ContentArchiveVideo.findOneAndUpdate(
          { _id: ObjectId(video._id) },
          { eventFor: eventName }
        );
      });

      const AllUsers = await User.find({ isDelete: false });
      AllUsers.forEach(async (userData, i) => {
        const alreadyEventsAdded = await User.findOne({
          _id: userData._id,
          userEvents: { $exists: true },
        });

        if (alreadyEventsAdded !== null) {
          const alreadyExistsEvent = await User.findOne({
            _id: userData._id,
            [`userEvents.${eventData.name}`]: { $exists: true },
          });

          if (alreadyExistsEvent !== null) {
            if (alreadyExistsEvent.userEvents[`${eventData.name}`] == true) {
              if (eventData.name !== eventName) {
                const removeAndUpdateEvent = await User.findOneAndUpdate(
                  { _id: userData._id },
                  {
                    $unset: { [`userEvents.${eventData.name}`]: true },
                    $set: { [`userEvents.${eventName}`]: true },
                  },
                  { new: true }
                );
              } else {
                const UpdateEventData = await User.findOneAndUpdate(
                  { _id: userData._id },
                  { $set: { [`userEvents.${eventName}`]: true } },
                  { new: true }
                );
              }
            } else {
              if (eventData.name !== eventName) {
                const removeAndUpdateEvent = await User.findOneAndUpdate(
                  { _id: userData._id },
                  {
                    $unset: { [`userEvents.${eventData.name}`]: false },
                    $set: { [`userEvents.${eventName}`]: false },
                  },
                  { new: true }
                );
              } else {
                const UpdateEventData = await User.findOneAndUpdate(
                  { _id: userData._id },
                  { $set: { [`userEvents.${eventName}`]: false } },
                  { new: true }
                );
              }
            }
          } else {
            const updateEvent = await User.findOneAndUpdate(
              { _id: userData._id },
              { $set: { [`userEvents.${eventData.name}`]: false } },
              { new: true }
            );
          }
        } else {
          const updateEvent = await User.findOneAndUpdate(
            { _id: userData._id },
            {
              $set: {
                [`userEvents.${eventName}`]: false,
              },
            }
          );
        }
      });
      await Promise.all([...temp]);

      return res
        .status(200)
        .json({ status: true, message: `Event data updated.`, data: result });
    } else {
      return res
        .status(404)
        .json({ status: false, message: `Event data not found.`, data: [] });
    }
  } catch (error) {
    if (error.name === "MongoServerError" && error.code === 11000) {
      return res
        .status(200)
        .json({ status: false, message: `Event name must be unique.` });
    } else {
      return res
        .status(200)
        .json({ status: false, message: `Something went wrong. ${error}` });
    }
  }
};

exports.deleteEvent = async (req, res) => {
  try {
    const body = req.body;
    const eventData = await ContentEvent.findById(
      new ObjectId(body.deleted_event_id)
    );
    const changeEventData = await ContentEvent.findById(
      new ObjectId(body.reassign_event_id)
    );

    const eventVideos = await ContentArchiveVideo.find({
      isDelete: false,
      eventFor: eventData.name,
    });

    var temp = eventVideos?.map(async (video, index) => {
      await ContentArchiveVideo.findOneAndUpdate(
        { _id: ObjectId(video._id) },
        { eventFor: changeEventData.name }
      );
    });

    const eventName = eventData.name;
    const AllUsers = await User.find({ isDelete: false });
    AllUsers.forEach(async (userData, i) => {
      const alreadyEventsAdded = await User.findOne({
        _id: userData._id,
        userEvents: { $exists: true },
      });

      if (alreadyEventsAdded !== null) {
        const alreadyExistsEvent = await User.findOne({
          _id: userData._id,
          [`userEvents.${eventData.name}`]: { $exists: true },
        });

        if (alreadyExistsEvent !== null) {
          if (alreadyExistsEvent.userEvents[`${eventData.name}`] == true) {
            if (eventData.name !== changeEventData.name) {
              const removeAndUpdateEvent = await User.findOneAndUpdate(
                { _id: userData._id },
                {
                  $unset: { [`userEvents.${eventData.name}`]: true },
                  $set: { [`userEvents.${changeEventData.name}`]: true },
                },
                { new: true }
              );
            } else {
              const UpdateEventData = await User.findOneAndUpdate(
                { _id: userData._id },
                { $set: { [`userEvents.${changeEventData.name}`]: true } },
                { new: true }
              );
            }
          } else {
            if (eventData.name !== changeEventData.name) {
              const removeAndUpdateEvent = await User.findOneAndUpdate(
                { _id: userData._id },
                {
                  $unset: { [`userEvents.${eventData.name}`]: false },
                  $set: { [`userEvents.${changeEventData.name}`]: false },
                },
                { new: true }
              );
            } else {
              const UpdateEventData = await User.findOneAndUpdate(
                { _id: userData._id },
                { $set: { [`userEvents.${changeEventData.name}`]: false } },
                { new: true }
              );
            }
          }
        } else {
          const updateEvent = await User.findOneAndUpdate(
            { _id: userData._id },
            { $set: { [`userEvents.${eventData.name}`]: false } },
            { new: true }
          );
        }
      } else {
        const updateEvent = await User.findOneAndUpdate(
          { _id: userData._id },
          {
            $set: {
              [`userEvents.${changeEventData.name}`]: false,
            },
          }
        );
      }
    });
    await ContentEvent.findByIdAndUpdate(new ObjectId(body.deleted_event_id), {
      isDelete: true,
    });
    await Promise.all([...temp]);

    return res
      .status(200)
      .json({ status: true, message: "Event list updated!" });
  } catch (error) {
    return res
      .status(200)
      .json({ status: false, message: `Something went wrong. ${error}` });
  }
};

//for temporary use-delete userevents properties from postman-api
exports.deleteUserEvents = async (req, res) => {
  try {
    const eventName = req.body.name;
    const AllUsers = await User.find({ isDelete: false });
    AllUsers.forEach(async (userData, i) => {
      const alreadyEventsAdded = await User.findOne({
        _id: userData._id,
        userEvents: { $exists: true },
      });

      if (alreadyEventsAdded !== null) {
        const alreadyExistsEvent = await User.findOne({
          _id: userData._id,
          [`userEvents.${eventName}`]: { $exists: true },
        });
        if (alreadyExistsEvent !== null) {
          const removeAndUpdateEvent = await User.findOneAndUpdate(
            { _id: userData._id },
            { $unset: { [`userEvents.${eventName}`]: true } },
            { new: true }
          );
        }
      }
    });

    //await Promise.all([...temp]);

    return res
      .status(200)
      .json({ status: true, message: "Event list updated!" });
  } catch (error) {
    return res
      .status(200)
      .json({ status: false, message: `Something went wrong. ${error}` });
  }
};

exports.restEvents = async (req, res) => {
  const eventid = req.params.id;
  try {
    const data = await ContentEvent.aggregate([
      {
        $match: {
          isDelete: false,
          _id: { $ne: ObjectId(eventid) },
          relation_id: ObjectId(req.relation_id),
        },
      },
      {
        $project: {
          _id: "$_id",
          name: 1,
        },
      },
      {
        $sort: { createdAt: -1 },
      },
    ]);

    return res
      .status(200)
      .json({ status: true, message: `List of rest events.`, data: data });
  } catch (error) {
    return res.status(200).json({ status: false, message: `${error.message}` });
  }
};
/** Code By SJ Event CURD Ends **/

/*search video feature in content video library on admin side */
exports.getAllOverContentVideoByAdmin = async (req, res) => {
  try {
    let { search, id } = req.query;
    const tag = req.query.tag ? req.query.tag : "";
    const category = req.query.category ? req.query.category : "";

    if(search){
      search = search.replace(/[.*+?^${}()|[\]\\]/g, '\\$&')
    }

    var match = {
      isDelete: false,
      relation_id: ObjectId(req.relation_id),
      categories: { $in: [ObjectId(id)] },
      title: { $regex: ".*" + search + ".*", $options: "i" },
      uploadstatus: { $ne: "inprocess" },
      "restrictedAccessGroupId.0": { $exists: true },
    };
    if (tag !== "") {
      match = { ...match, tag: { $in: ObjectId[tag] } };
    }
    if (category !== "") {
      match = { ...match, categories: { $in: ObjectId[category] } };
    }

    if (id !== undefined && id !== null && id !== "") {
      const data = await ContentArchiveVideo.find(match)
        .sort({ createdAt: -1, updatedAt: -1 })
        .select("-__v");

      const count = await ContentArchiveVideo.countDocuments(match);

      return res.status(200).json({
        status: true,
        message: `List of videos.`,
        data: [
          {
            videos: data,
            totalPages: Math.ceil(count / 20),
            currentPage: 1,
            totalVideos: count,
          },
        ],
      });
    } else {
      if (search !== "") {
        const data = await ContentArchiveVideo.find({
          isDelete: false,
          relation_id: ObjectId(req.relation_id),
          title:
            search !== ""
              ? { $regex: ".*" + search + ".*", $options: "i" }
              : { $ne: "" },
          uploadstatus: { $ne: "inprocess" },
          "restrictedAccessGroupId.0": { $exists: true },
        })
          .sort({ createdAt: -1, updatedAt: -1 })
          .select("-__v");

        const count = await ContentArchiveVideo.countDocuments({
          isDelete: false,
          relation_id: ObjectId(req.relation_id),
          title:
            search !== ""
              ? { $regex: ".*" + search + ".*", $options: "i" }
              : { $ne: "" },
          uploadstatus: { $ne: "inprocess" },
          "restrictedAccessGroupId.0": { $exists: true },
        });

        return res.status(200).json({
          status: true,
          message: `List of videos.`,
          data: [
            {
              videos: data,
              totalPages: Math.ceil(count / 20),
              currentPage: 1,
              totalVideos: count,
            },
          ],
        });
      } else {
        const data = await ContentArchiveVideo.find({
          isDelete: false,
          relation_id: ObjectId(req.relation_id),
          title:
            search !== ""
              ? { $regex: ".*" + search + ".*", $options: "i" }
              : { $ne: "" },
          uploadstatus: { $ne: "inprocess" },
          "restrictedAccessGroupId.0": { $exists: true },
        })
          .sort({ createdAt: -1, updatedAt: -1 })
          .select("-__v")
          .limit(20);

        const count = await ContentArchiveVideo.countDocuments({
          isDelete: false,
          relation_id: ObjectId(req.relation_id),
          title:
            search !== ""
              ? { $regex: ".*" + search + ".*", $options: "i" }
              : { $ne: "" },
          uploadstatus: { $ne: "inprocess" },
          "restrictedAccessGroupId.0": { $exists: true },
        });

        return res.status(200).json({
          status: true,
          message: `List of videos.`,
          data: [
            {
              videos: data,
              totalPages: Math.ceil(count / 20),
              currentPage: 1,
              totalVideos: count,
            },
          ],
        });
      }
    }
  } catch (error) {
    return res.status(200).json({ status: false, message: `${error.message}` });
  }
};

//Add userwise realwatchtime to all videos
function sumOFHoursWorked(time_1, time_2) {
  var time1 = time_1.split(":");
  var time2 = time_2.split(":");
  let secondSum = Number(time1[2]) + Number(time2[2]);
  let minSum = Number(time1[1]) + Number(time2[1]);
  let hrSum = Number(time1[0]) + Number(time2[0]);

  if (secondSum > 59) {
    secondSum = Math.abs(60 - secondSum);
    minSum += 1;
  }
  if (minSum > 59) {
    minSum = Math.abs(60 - minSum);
    hrSum += 1;
  }
  if (secondSum < 10) {
    secondSum = `0${secondSum}`;
  }
  if (minSum < 10) {
    minSum = `0${minSum}`;
  }
  if (hrSum < 10) {
    hrSum = `0${hrSum}`;
  }

  return `${hrSum}:${minSum}:${secondSum}`;
}

exports.AddRealWatchTime = async (req, res) => {
  const videoid = req.query.videoid;
  const userid = req.authUserId;
  var watchtime = req.query.watchtime;
  try {
    const result_ = await ContentArchiveVideo.findOne({
      _id: videoid,
      relation_id: ObjectId(req.relation_id),
      watched_realtime: { $exists: true, $elemMatch: { userid: userid } },
    }).select("watched_realtime");
    if (result_ !== null) {
      watchtime = sumOFHoursWorked(
        watchtime,
        result_.watched_realtime[0].watch_realduration
      );
      const result = await ContentArchiveVideo.findOneAndUpdate(
        { _id: videoid, watched_realtime: { $elemMatch: { userid: userid } } },
        { $set: { "watched_realtime.$.watch_realduration": watchtime } }
      );
      if (result) {
        return res
          .status(200)
          .json({ status: true, message: `Watch time added`, data: result });
      } else {
        return res
          .status(200)
          .json({ status: false, message: `Watch time not added`, data: [] });
      }
    } else {
      var time_ = watchtime.split(":");
      watchtime =
        (Number(time_[0]) < 10 ? `0${time_[0]}` : time_[0]) +
        ":" +
        (Number(time_[1]) < 10 ? `0${time_[1]}` : time_[1]) +
        ":" +
        (Number(time_[2]) < 10 ? `0${time_[2]}` : time_[2]);
      const result = await ContentArchiveVideo.findByIdAndUpdate(videoid, {
        $push: {
          watched_realtime: { userid: userid, watch_realduration: watchtime },
        },
      });
      if (result) {
        return res
          .status(200)
          .json({ status: true, message: `Watch time added`, data: result });
      } else {
        return res
          .status(200)
          .json({ status: false, message: `Watch time not added`, data: [] });
      }
    }
  } catch (error) {
    return res.status(200).json({ status: false, message: `${error.message}` });
  }
};

// Get Video Speaker profile details
exports.getVideoSpeakerProfile = async (req, res) => {
  try {
    const attendeeId = ObjectId(req.params.id);
    const attendeeProfile = await User.aggregate([
      {
        $match: {
          _id: attendeeId,
        },
      },
      {
        $project: {
          _id: 1,
          firebaseId: 1,
          email: "$Preferred Email",
          type: "Member",
          first_name: { '$ifNull': ['$first_name', ''] },
          last_name: { '$ifNull': ['$last_name', ''] },
          display_name: {
            $cond: {
                if: {
                    $or: [
                        { $eq: ["$display_name", null] },
                        { $eq:[ "$display_name", "" ]}
                    ]
                },
                then: { $concat: ["$first_name", " ", "$last_name"] },
                else: "$display_name"
            }
          },
          title: "$attendeeDetail.title",
          name: "$attendeeDetail.name",
          firstName: "$attendeeDetail.firstName"
            ? "$attendeeDetail.firstName"
            : "",
          lastName: "$attendeeDetail.lastName"
            ? "$attendeeDetail.lastName"
            : "",
          company: "$attendeeDetail.company",
          profession: "$attendeeDetail.profession",
          phone: "$attendeeDetail.phone",
          facebook: "$attendeeDetail.facebook",
          linkedin: "$attendeeDetail.linkedin",
          description: "$attendeeDetail.description" ?? "",
          offer: "$attendeeDetail.offer" ?? "",
          contactPartnerName: "$attendeeDetail.contactPartnerName" ?? "",
          event: "",
          profileImg: "$profileImg",
          partnerIcon: "$partnerIcon",
        },
      },
    ]);
    if (attendeeProfile.length > 0) {
      return res.status(200).json({
        status: true,
        message: "Video speaker profile details retrive.",
        data: attendeeProfile[0],
      });
    } else {
      return res.status(200).json({
        status: false,
        message: "Video speaker profile details not found!",
      });
    }
  } catch (error) {
    return res
      .status(500)
      .json({ status: false, message: "Internal server error!", error: error });
  }
};

/*** */
//event or speaker based video list fetched
exports.getAllContentVideoByEventOrSpeaker = async (req, res) => {
  try {
    const { id, filtertype, sortfilter } = req.query;
    const authUser = req.authUserId;
    const userdata = await User.findById(authUser);
    const page = parseInt(req.query.page);
    const limit = parseInt(req.query.limit);
    const skip = (page - 1) * limit;
    var sort = { createdAt: -1 };

    if (sortfilter === "recent") {
      sort = { createdAt: -1, updatedAt: -1 };
    } else if (sortfilter === "popular") {
      sort = { viewsCount: -1 };
    } else if (sortfilter === "comment") {
      sort = { commentsCount: -1 };
    }

    let videoList = [];
    if (req.query.filtertype === undefined || req.query.filtertype === null)
      return res.status(200).json({
        status: false,
        message: `Please apply filter type!`,
        data: [],
      });

    if (filtertype === "event") {
      const data = await ContentArchiveVideo.aggregate([
        {
          $match: {
            isDelete: false,
            relation_id: ObjectId(req.relation_id),
            eventIds: { $in: [ObjectId(id)] },
          },
        },
        {
          $lookup: {
            from: "contentarchive_categories",
            let: { contentarchive_categories_id: "$categories" },
            pipeline: [
              {
                $match: {
                  $expr: {
                    $in: ["$_id", "$$contentarchive_categories_id"],
                  },
                },
              },
              { $project: { name: 1 } },
            ],
            as: "categories",
          },
        },
        {
          $lookup: {
            from: "contentarchive_subcategories",
            localField: "subcategory",
            foreignField: "_id",
            pipeline: [
              {
                $match: {
                  isDelete: false,
                },
              },
              { $project: { name: 1 } },
            ],

            as: "subcategory",
          },
        },
        {
          $lookup: {
            from: "groups",
            let: { suggestion_id: "$restrictedAccessGroupId" },
            pipeline: [
              {
                $match: {
                  $expr: {
                    $in: ["$_id", "$$suggestion_id"],
                  },
                },
              },
              { $project: { groupTitle: 1 } },
            ],
            as: "group_ids",
          },
        },
        {
          $lookup: {
            from: "airtable-syncs",
            let: { speaker: "$speaker" },
            pipeline: [
              {
                $match: {
                  $expr: {
                    $in: ["$_id", "$$speaker"],
                  },
                },
              },
              {
                $project: {
                  firebaseId: 1,
                  first_name: { '$ifNull': ['$first_name', ''] },
                  last_name: { '$ifNull': ['$last_name', ''] },
                  display_name: {
                    $cond: {
                        if: {
                            $or: [
                                { $eq: ["$display_name", null] },
                                { $eq:[ "$display_name", "" ]}
                            ]
                        },
                        then: { $concat: ["$first_name", " ", "$last_name"] },
                        else: "$display_name"
                    }
                  },
                  profileImg: 1,
                  partnerIcon: 1,
                  attendeeDetail: {
                    title: 1,
                    name: 1,
                    profession: 1,
                  },
                },
              },
            ],
            as: "speaker",
          },
        },
        {
          $lookup: {
            from: "contentarchive_tags",
            let: { tag: "$tag" },
            pipeline: [
              {
                $match: {
                  $expr: {
                    $in: ["$_id", "$$tag"],
                  },
                },
              },
              { $project: { name: 1 } },
            ],
            as: "tag",
          },
        },
        {
          $lookup: {
            from: "events",
            let: { event_Ids: "$eventIds" },
            pipeline: [
              {
                $match: {
                  $expr: {
                    $in: ["$_id", "$$event_Ids"],
                  },
                },
              },
              {
                $project: {
                  title: 1,
                  thumbnail: 1,
                  timeZone: 1,
                  startDate: 1,
                  startTime: 1,
                  endDate: 1,
                  endTime: 1,
                  activities: 1,
                },
              },
            ],
            as: "eventIds",
          },
        },
        {
          $addFields: {
            viewsCount: {
              $cond: {
                if: { $isArray: "$views" },
                then: { $add: [{ $size: "$views" }, "$starting_view_cnt"] },
                else: "$starting_view_cnt",
              },
            },
          },
        },
        {
          $addFields: {
            commentsCount: {
              $cond: {
                if: { $isArray: "$comments" },
                then: { $size: "$comments" },
                else: 0,
              },
            },
          },
        },
        { $sort: sort },
        { $skip: skip },
        { $limit: limit },
      ]);
      const count = await ContentArchiveVideo.countDocuments({
        eventIds: { $in: [ObjectId(id)] },
        relation_id: ObjectId(req.relation_id),
        isDelete: false,
      });

      let resOrder = data.map((item, i) => {
        const without_html_description = item.description.replace(
          /(<([^>]+)>)/gi,
          ""
        );
        const mobile_description = without_html_description.substring(0, 600);

        item.mobile_description = mobile_description;
        var url = s3.getSignedUrl("getObject", {
          Bucket: "arn:aws:s3:us-east-2:496737174815:accesspoint/accessforapp",
          Key: item.video,
          Expires: 100000,
        });
        return { ...item, video: url };
      });

      return res.status(200).json({
        status: true,
        message: `List of videos.`,
        data: [
          {
            videos: resOrder,
            totalPages: Math.ceil(count / limit),
            currentPage: page,
            totalVideos: count,
          },
        ],
      });
    } else {
      const data = await ContentArchiveVideo.aggregate([
        {
          $match: {
            isDelete: false,
            relation_id: ObjectId(req.relation_id),
            speaker: { $in: [ObjectId(id)] },
          },
        },
        {
          $lookup: {
            from: "contentarchive_categories",
            let: { contentarchive_categories_id: "$categories" },
            pipeline: [
              {
                $match: {
                  $expr: {
                    $in: ["$_id", "$$contentarchive_categories_id"],
                  },
                },
              },
              { $project: { name: 1 } },
            ],
            as: "categories",
          },
        },
        {
          $lookup: {
            from: "contentarchive_subcategories",
            localField: "subcategory",
            foreignField: "_id",
            pipeline: [
              {
                $match: {
                  isDelete: false,
                },
              },
              { $project: { name: 1 } },
            ],

            as: "subcategory",
          },
        },
        {
          $lookup: {
            from: "groups",
            let: { suggestion_id: "$restrictedAccessGroupId" },
            pipeline: [
              {
                $match: {
                  $expr: {
                    $in: ["$_id", "$$suggestion_id"],
                  },
                },
              },
              { $project: { groupTitle: 1 } },
            ],
            as: "group_ids",
          },
        },
        {
          $lookup: {
            from: "airtable-syncs",
            let: { speaker: "$speaker" },
            pipeline: [
              {
                $match: {
                  $expr: {
                    $in: ["$_id", "$$speaker"],
                  },
                },
              },
              {
                $project: {
                  firebaseId: 1,
                  display_name: {
                    $cond: {
                        if: {
                            $or: [
                                { $eq: ["$display_name", null] },
                                { $eq:[ "$display_name", "" ]}
                            ]
                        },
                        then: { $concat: ["$first_name", " ", "$last_name"] },
                        else: "$display_name"
                    }
                  },
                  first_name: { '$ifNull': ['$first_name', ''] },
                  last_name: { '$ifNull': ['$last_name', ''] },
                  profileImg: 1,
                  partnerIcon: 1,
                  attendeeDetail: {
                    title: 1,
                    name: 1,
                    profession: 1,
                  },
                },
              },
            ],
            as: "speaker",
          },
        },
        {
          $lookup: {
            from: "contentarchive_tags",
            let: { tag: "$tag" },
            pipeline: [
              {
                $match: {
                  $expr: {
                    $in: ["$_id", "$$tag"],
                  },
                },
              },
              { $project: { name: 1 } },
            ],
            as: "tag",
          },
        },
        {
          $lookup: {
            from: "events",
            localField: "eventIds",
            foreignField: "_id",
            pipeline: [
              {
                $project: {
                  title: 1,
                  thumbnail: 1,
                  timeZone: 1,
                  startDate: 1,
                  startTime: 1,
                  endDate: 1,
                  endTime: 1,
                  activities: 1,
                },
              },
            ],
            as: "eventIds",
          },
        },
        {
          $addFields: {
            viewsCount: {
              $cond: {
                if: { $isArray: "$views" },
                then: { $add: [{ $size: "$views" }, "$starting_view_cnt"] },
                else: "$starting_view_cnt",
              },
            },
          },
        },
        {
          $addFields: {
            commentsCount: {
              $cond: {
                if: { $isArray: "$comments" },
                then: { $size: "$comments" },
                else: 0,
              },
            },
          },
        },
        { $sort: sort },
        { $skip: skip },
        { $limit: limit },
      ]);

      const count = await ContentArchiveVideo.countDocuments({
        speaker: { $in: [ObjectId(id)] },
        relation_id: ObjectId(req.relation_id),
        isDelete: false,
      });

      let resOrder = data.map((item, i) => {
        const without_html_description = item.description.replace(
          /(<([^>]+)>)/gi,
          ""
        );
        const mobile_description = without_html_description.substring(0, 600);

        item.mobile_description = mobile_description;
        var url = s3.getSignedUrl("getObject", {
          Bucket: "arn:aws:s3:us-east-2:496737174815:accesspoint/accessforapp",
          Key: item.video,
          Expires: 100000,
        });

        return { ...item, video: url };
      });

      return res.status(200).json({
        status: true,
        message: `List of videos.`,
        data: [
          {
            videos: resOrder,
            totalPages: Math.ceil(count / limit),
            currentPage: page,
            totalVideos: count,
          },
        ],
      });
    }
  } catch (error) {
    return res.status(200).json({ status: false, message: `${error.message}` });
  }
};

// sync event others into user_access all
exports.syncManualRuleSystem = async (req, res) => {
  try {
    const list = await ContentArchiveVideo.aggregate([{ '$match': {} }]);
    for (let i = 0; i < list.length; i++) {
      let video = list[i];
      let updateObj = {
        restrictionAccess: "restricted",
        user_id: [],
        membership_plan_id: [],
        event_id_for_rule: [],

      }
      if (video.user_id && video.user_id.length != 0) {
        updateObj.user_id = video.user_id;
      }
      if (video.membership_plan_id && video.membership_plan_id.length != 0) {
        updateObj.membership_plan_id = video.membership_plan_id;
      }
      if (video.event_id_for_rule && video.event_id_for_rule.length != 0) {
        updateObj.event_id_for_rule = video.event_id_for_rule;
      }
      if (video.eventFor && video.eventFor === 'others') {
        updateObj.restrictionAccess = "public";
      }
      const updatedSession = await ContentArchiveVideo.findOneAndUpdate({ _id: ObjectId(video._id) }, { $set: updateObj }, { new: true });
      console.log(i + 1, "/", list.length);
    }
    return res.status(200).json({ status: true, message: "Sync video user_access." });
  } catch (error) {
    return res.status(500).json({ status: false, message: "Internal server error!", error: error });
  }
};

// replace only restrictionAccess,restrictedAccessGroups and restrictedAccessMembership
exports.replaceRestrictionFieldForVideo = async (req, res) => {
  try {
    const videoList = await ContentArchiveVideo.find({ isDelete: false });
    let countAll = 0;
    let countSpecific = 0; 
    let updateData;

    for await (let item of videoList) {
      let updateFields;

      if (item.user_access == "all") {
        updateFields = {
          restrictionAccess: "public",
          restrictedAccessGroupId: [],
          restrictedAccessMembershipPlanId: [],
          restrictedAccessUserId: [],
          restrictedAccessEventId: [],
          restrictedAccessTierId: [],
        };
        countAll++; 
        console.log(countAll)
      } else {
        updateFields = {
          restrictionAccess: item.user_access,
          restrictedAccessGroupId: item.group_ids,
          restrictedAccessMembershipPlanId: item.membership_plan_id,
          restrictedAccessUserId: item.user_id,
          restrictedAccessEventId: item.event_id_for_rule,
          restrictedAccessTierId: item.tier_id,
        };
        countSpecific++; 
        console.log(countSpecific)
      }
      updateData = await ContentArchiveVideo.findOneAndUpdate(
        { _id: new ObjectId(item._id) },
        { $set: updateFields },
        { new: true }
      );
    }

    return res.status(200).json({
      status: true,
      message: "Replace Restriction Field For Video successfully!",
      countAll: countAll, 
      countSpecific: countSpecific, 
    });
  } catch (error) {
    return res.status(500).json({
      status: false,
      message: "Internal server error!",
      error: error.message, 
    });
  }
};

// script for user data sync to user edge
exports.userDataSyncToUserEdge = async (req, res) => {
  try {
    const userList = await User.aggregate([
      {
        '$match': {
          'user_edges': {
            '$exists': true
          }, 
          'isDelete': false
        }
      }, {
        '$lookup': {
          'from': 'user_edges', 
          'localField': '_id', 
          'foreignField': 'user_id', 
          'as': 'result', 
          'pipeline': [
            {
              '$match': {
                'type': 'M', 
                'relation_id': new ObjectId('6615282bb79a467cdae96bb5')
              }
            }
          ]
        }
      }, {
        '$unwind': {
          'path': '$result', 
          'preserveNullAndEmptyArrays': false
        }
      }, {
        '$project': {
          'result': 1, 
          'accessible_groups': 1, 
          'tagId': 1
        }
      }
    ]);
    let countSpecific = 0; 
    let updateData;

    for (let item of userList) {
        let updateFields = {
          accessible_groups: item.accessible_groups,
          tagId: item.tagId,
        };
        countSpecific++; 
        console.log(countSpecific)
      
      updateData = await userEdge.findOneAndUpdate(
        { _id: new ObjectId(item.result._id) },
        { $set: updateFields },
        { new: true }
      );
    }

    return res.status(200).json({
      status: true,
      message: "Sync data for Mds user tag and groups!",
      countSpecific: countSpecific, 
    });
  } catch (error) {
    return res.status(500).json({
      status: false,
      message: "Internal server error!",
      error: error.message, 
    });
  }
};

exports.videoMembershipPlanMigration = async (req, res) => {
  //* Need to add the tier data in the array 
  const tierData = []

  const fetchVideo = await ContentArchiveVideo.find({
    relation_id: ObjectId("67011d987a2a81b28438a3d8"),
    isDelete: false,
    restrictedAccessMembershipPlanId: {
      $exists: true,
    },
    restrictedAccessMembershipPlanId: {
      $ne: [],
    },
  })
    .select(
      "-views -user_video_pause -subcategory -eventIds -clif_notes -watched_realtime -description -categories -tag -restrictedAccessEventId -restrictedAccessUserId -restrictedAccessGroupId -user_id -speaker"
    )
    .limit(1);

  for await (let data of fetchVideo) {
    const membershipPlan = [];
    for await (let plan of data.restrictedAccessMembershipPlanId) {
      const planData = tierData.find((item) => item.membership_id == plan?._id);
      if (planData) {
        membershipPlan.push(planData);
      }
    }

    const ids = membershipPlan.map((item) => item.tier_id);

    const updatedData = await ContentArchiveVideo.findOneAndUpdate(
      { _id: ObjectId(data._id) },
      {
        $set: {
          restrictedAccessTierId: ids,
        },
      },
      { new: true }
    );
  }

  return res.status(200).json({
    status: true,
    message: "Video Membership Plan Migration!",
    data: fetchVideo.length,
  });
};
