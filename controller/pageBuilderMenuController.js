const PageBuilderMenu = require("../database/models/pageBuliderMenu");
const PageBuilderSubmenu = require("../database/models/pageBuilderSubmenu");
const {
    ObjectId
} = require("mongodb");
const {
    checkValidIds,
    userAccessRulesCommonCondition
} = require("../controller/userAccessRules/restrictionAccess");
const {
    getAllTiersfromBilling
} = require("../controller/userAccessRules/tiers");

// Get Menu listing
exports.getPageBuilderMenuList = async (req, res) => {
    try {
        const page = parseInt(req.query.page) || 1;
        const limit = parseInt(req.query.limit) || 20;
        const skip = (page - 1) * limit;
        const searchQuery = req.query.search || "";

        const matchStage = {
            relation_id: ObjectId(req.relation_id),
            isDelete: false,
        };

        if (searchQuery) {
            const regex = new RegExp(searchQuery, "i");
            matchStage.$or = [{
                    title: {
                        $regex: regex
                    }
                },
                {
                    slug: {
                        $regex: regex
                    }
                },
            ];
        }

        const aggregationPipeline = [{
                $lookup: {
                    from: "pagebuilders",
                    localField: "pagebuilder",
                    foreignField: "_id",
                    pipeline: [{
                        $project: {
                            _id: 1,
                            title: 1,
                        },
                    }, ],
                    as: "pagebuilder",
                },
            },
            {
                $unwind: {
                  path: "$pagebuilder",
                  preserveNullAndEmptyArrays: true
                }
            },
            {
                $addFields: {
                    pagebuilder: {
                        $ifNull: ["$pagebuilder", {}]
                    }
                }
            },
            {
                $lookup: {
                    from: "pagebuilder_submenus",
                    localField: "_id",
                    foreignField: "menuReferenceId",
                    pipeline: [{
                            $match: {
                                isDelete: false,
                                relation_id: ObjectId(req.relation_id),
                            }
                        },
                        {
                            $lookup: {
                                from: "pagebuilders",
                                localField: "pagebuilder",
                                foreignField: "_id",
                                pipeline: [{
                                    $project: {
                                        _id: 1,
                                        title: 1,
                                    },
                                }, ],
                                as: "pagebuilder",
                            },
                        },
                        {
                            $unwind: {
                              path: "$pagebuilder",
                              preserveNullAndEmptyArrays: true
                            }
                        },
                        {
                            $addFields: {
                                pagebuilder: {
                                    $ifNull: ["$pagebuilder", {}]
                                }
                            }
                        },
                        {
                            $project: {
                                _id: 1,
                                title: 1,
                                slug: 1,
                                menuReferenceId: 1,
                                order: 1,
                                isDraft: 1,
                                pagebuilder: 1
                            }
                        },
                    ],
                    as: "submenu",
                }
            },
            {
                $addFields: {
                    matchesInSubmenu: {
                        $filter: {
                            input: "$submenu",
                            as: "submenuItem",
                            cond: {
                                $or: [{
                                        $regexMatch: {
                                            input: "$$submenuItem.title",
                                            regex: new RegExp(searchQuery, "i")
                                        }
                                    },
                                    {
                                        $regexMatch: {
                                            input: "$$submenuItem.slug",
                                            regex: new RegExp(searchQuery, "i")
                                        }
                                    }
                                ]
                            }
                        }
                    }
                }
            },
            {
                $match: {
                    isDelete: false,
                    relation_id: ObjectId(req.relation_id),
                    $or: [{
                            title: {
                                $regex: new RegExp(searchQuery, "i")
                            }
                        },
                        {
                            slug: {
                                $regex: new RegExp(searchQuery, "i")
                            }
                        },
                        {
                            "matchesInSubmenu.0": {
                                $exists: true
                            }
                        }
                    ]
                }
            },
            {
                $project: {
                    _id: 1,
                    title: 1,
                    slug: 1,
                    isSubMenu: 1,
                    isDraft: 1,
                    createdAt: 1,
                    order: 1,
                    submenu: 1,
                    pagebuilder: 1,
                },
            },
            {
                $sort: {
                    order: 1
                }
            },
            {
                $skip: skip
            },
            {
                $limit: limit
            },
        ];

        const menus = await PageBuilderMenu.aggregate(aggregationPipeline);
        const totalRecords = menus.length;

        if ( !menus || menus.length === 0 ) {
            res.status(200).send({
                status: false,
                message: "Menus not found!",
                data: [],
                currentPage: 0,
                totalPages: Math.ceil(0 / limit),
                totalRecords: 0,
            });
        } else {
            res.status(200).send({
                status: true,
                message: "Menus retrieved successfully!",
                data: menus,
                currentPage: page,
                totalPages: Math.ceil(totalRecords / limit),
                totalRecords: totalRecords,
            });
        }
    } catch (error) {
        res.status(500).send({
            status: false,
            message: error.message,
        });
    }
};

// Get menu by id
exports.getPageBuilderMenuByID = async (req, res) => {
    try {
        const menuId = req.params.id
        const menuDetails = await PageBuilderMenu.findOne({
                _id: menuId,
                isDelete: false,
                relation_id: ObjectId(req.relation_id)
            }).populate({
                path: "restrictedAccessGroupId",
                select: "groupTitle"
            })
            .populate({
                path: "restrictedAccessUserId",
                select: {
                    first_name: 1,
                    last_name: 1,
                    display_name: 1,
                    attendeeDetail: 1,
                    'Preferred Email': 1
                }
            })
            .populate({
                path: "restrictedAccessEventId",
                select: "title -tag -category -subcategory"
            })
            .populate({
                path: "restrictedAccessTagId",
                select: "name"
            }).lean();

        let menu = {
            ...menuDetails
        }
        const obj = {
            relation_id: req.relation_id
        }
        const tiers = await getAllTiersfromBilling(obj, (expand = true));
        const restrictedTierIds = Array.isArray(menu?.restrictedAccessTierId) ?
            menu.restrictedAccessTierId.map((id) => id.toString()) : [];

        const filteredTiers = tiers
            .filter((tier) => restrictedTierIds.includes(tier._id.toString()))
            .map((tier) => ({
                _id: tier._id,
                name: tier.name,
            }));
        menu.restrictedAccessTierId = filteredTiers;

        if (!menu || menu.length === 0) {
            return res.status(400).send({
                status: false,
                message: "Menu not found!",
            });
        }

        res.status(200).send({
            status: true,
            message: "Menu retrieved successfully!",
            data: menu,
        });
    } catch (error) {
        // Handle errors
        res.status(500).send({
            status: false,
            message: error.message,
        });
    }
};

// add menu
exports.addPageBuilderMenu = async (req, res) => {
    try {
        const {
            title,
            slug,
            isSubMenu,
            pagebuilder,
            isDraft,
            restrictionAccess,
            restrictedAccessGroupId,
            restrictedAccessEventId,
            restrictedAccessUserId,
            restrictedAccessTagId,
            restrictedAccessTierId
        } = req.body;
        const ids = await PageBuilderMenu.countDocuments({
            relation_id: req.relation_id,
            isDelete: false
        }, {
            _id: 1,
            order: 1
        }).lean();
        let newOrder = ids + 1;

        let checkIds = await checkValidIds({
            restrictedAccessGroupId: restrictedAccessGroupId,
            restrictedAccessEventId: restrictedAccessEventId,
            restrictedAccessUserId: restrictedAccessUserId,
            restrictedAccessTagId: restrictedAccessTagId,
            restrictedAccessTierId: restrictedAccessTierId,
            relation_id: req.relation_id
        });

        if (checkIds.status === false) {
            return res.status(200).json({
                status: false,
                message: checkIds.msg
            });
        }

        if (!title) {
            return res.status(400).send({
                status: false,
                message: "Title are required."
            });
        }

        const existingMenu = await PageBuilderMenu.findOne({
            $or: [{
                    title,
                    relation_id: ObjectId(req.relation_id),
                    isDelete: false
                },
                {
                    slug,
                    relation_id: ObjectId(req.relation_id),
                    isDelete: false
                }
            ]
        });

        if (existingMenu) {
            const conflictField = existingMenu.title === title ? "title" : "slug";
            return res.status(400).send({
                status: false,
                message: `Menu with this ${conflictField} already exists.`
            });
        }

        const newMenu = new PageBuilderMenu({
            title,
            slug,
            order: newOrder,
            isSubMenu,
            pagebuilder,
            restrictionAccess,
            restrictedAccessGroupId,
            restrictedAccessEventId,
            restrictedAccessUserId,
            restrictedAccessTagId,
            restrictedAccessTierId,
            isDraft,
            relation_id: ObjectId(req.relation_id)
        });

        await newMenu.save();

        res.status(200).send({
            status: true,
            message: "Menu created successfully!",
            data: newMenu
        });
    } catch (error) {
        res.status(500).send({
            status: false,
            message: error.message
        });
    }
};

// Update menu
exports.updatePageBuilderMenuByID = async (req, res) => {
    try {
        const id = req.params.id;
        const {
            title,
            slug,
            isSubMenu,
            isDraft,
            pagebuilder,
            restrictionAccess,
            restrictedAccessGroupId,
            restrictedAccessEventId,
            restrictedAccessUserId,
            restrictedAccessTagId,
            restrictedAccessTierId
        } = req.body;

        let checkIds = await checkValidIds({
            restrictedAccessGroupId: restrictedAccessGroupId,
            restrictedAccessEventId: restrictedAccessEventId,
            restrictedAccessUserId: restrictedAccessUserId,
            restrictedAccessTagId: restrictedAccessTagId,
            restrictedAccessTierId: restrictedAccessTierId,
            relation_id: ObjectId(req.relation_id)
        });

        if (checkIds.status === false) {
            return res.status(200).json({
                status: false,
                message: checkIds.msg
            });
        }

        const menuToUpdate = await PageBuilderMenu.findOne({
            _id: ObjectId(id),
            relation_id: ObjectId(req.relation_id),
            isDelete: false
        });

        if (!menuToUpdate) {
            return res.status(404).send({
                status: false,
                message: "Menu not found or already deleted."
            });
        }

        if (!isSubMenu) {
            await PageBuilderSubmenu.updateMany({
                menuReferenceId: menuToUpdate._id
            }, {
                $set: {
                    isDelete: true
                }
            });
        }

        const duplicateMenu = await PageBuilderMenu.findOne({
            _id: {
                $ne: ObjectId(id)
            },
            $or: [{
                    title: title
                },
                {
                    slug: slug
                }
            ],
            relation_id: ObjectId(req.relation_id),
            isDelete: false
        });

        if (duplicateMenu) {
            const conflictField = duplicateMenu.title === title ? "title" : "slug";
            return res.status(400).send({
                status: false,
                message: `Menu with this ${conflictField} already exists.`
            });
        }

        const updatedMenu = await PageBuilderMenu.findByIdAndUpdate(
            id, {
                title: title || menuToUpdate.title,
                slug: slug || menuToUpdate.slug,
                isSubMenu: isSubMenu !== undefined ? isSubMenu : menuToUpdate.isSubMenu,
                pagebuilder: isSubMenu ? "" : pagebuilder || menuToUpdate.pagebuilder,
                restrictionAccess: restrictionAccess ?? menuToUpdate.restrictionAccess,
                restrictedAccessGroupId: restrictedAccessGroupId || [],
                restrictedAccessEventId: restrictedAccessEventId || [],
                restrictedAccessUserId: restrictedAccessUserId || [],
                restrictedAccessTagId: restrictedAccessTagId || [],
                restrictedAccessTierId: restrictedAccessTierId || [],
                isDraft: isDraft ? isDraft : menuToUpdate.isDraft,
            }, {
                new: true
            } // Returns the updated document
        );

        res.status(200).send({
            status: true,
            message: "Menu updated successfully!",
            data: updatedMenu
        });
    } catch (error) {
        res.status(500).send({
            status: false,
            message: error.message
        });
    }
};

// delete menu
exports.deletePageBuilderMenuByID = async (req, res) => {
    try {
        const menuToDelete = await PageBuilderMenu.findOne({
            _id: ObjectId(req.params.id),
            relation_id: ObjectId(req.relation_id),
            isDelete: false
        });

        if (!menuToDelete) {
            return res.status(400).send({
                status: false,
                message: "Menu not found or already deleted."
            });
        }

        menuToDelete.isDelete = true;
        await menuToDelete.save();
        await PageBuilderSubmenu.updateMany({
            menuReferenceId: menuToDelete._id,
            relation_id: ObjectId(req.relation_id),
            isDelete: false
        }, {
            $set: {
                isDelete: true
            }
        });
        res.status(200).send({
            status: true,
            message: "Menu and its related submenus deleted successfully!"
        });
    } catch (error) {
        res.status(500).send({
            status: false,
            message: error.message
        });
    }
};

// get menu list for user
exports.getPageBuilderMenuListforUser = async (req, res) => {
    try {
        const page = parseInt(req.query.page) || 1;
        const limit = parseInt(req.query.limit) || 20;
        const skip = (page - 1) * limit;
        const searchQuery = req.query.search || "";
        const authUser = req.authUserId;
        let ruleCondition = await userAccessRulesCommonCondition({
            userId: authUser,
            relation_id: ObjectId(req.relation_id)
        });


        let matchStage = {
            relation_id: ObjectId(req.relation_id),
            isDelete: false,
            isDraft: "publish"
        };

        if (searchQuery) {
            const regex = new RegExp(searchQuery, "i");
            matchStage.$or = [{
                    title: {
                        $regex: regex
                    }
                },
                {
                    slug: {
                        $regex: regex
                    }
                }
            ];
        }

        const aggregationPipeline = [{
                $match: {
                    ...matchStage,
                    ...ruleCondition
                }
            },
            {
                $lookup: {
                    from: "pagebuilders",
                    localField: "pagebuilder",
                    foreignField: "_id",
                    pipeline: [{
                        $project: {
                            _id: 1,
                            title: 1,
                        },
                    }, ],
                    as: "pagebuilder",
                },
            },
            {
                $unwind: {
                  path: "$pagebuilder",
                  preserveNullAndEmptyArrays: true
                }
            },
            {
                $addFields: {
                    pagebuilder: {
                        $ifNull: ["$pagebuilder", {}]
                    }
                }
            },
            {
                $lookup: {
                    from: "pagebuilder_submenus",
                    localField: "_id",
                    foreignField: "menuReferenceId",
                    pipeline: [{
                            $match: {
                                ...ruleCondition,
                                isDelete: false,
                                relation_id: ObjectId(req.relation_id),
                                isDraft: "publish",
                            }
                        },
                        {
                            $lookup: {
                                from: "pagebuilders",
                                localField: "pagebuilder",
                                foreignField: "_id",
                                pipeline: [{
                                    $project: {
                                        _id: 1,
                                        title: 1,
                                    },
                                }, ],
                                as: "pagebuilder",
                            },
                        },
                        {
                            $unwind: {
                              path: "$pagebuilder",
                              preserveNullAndEmptyArrays: true
                            }
                        },
                        {
                            $addFields: {
                                pagebuilder: {
                                    $ifNull: ["$pagebuilder", {}]
                                }
                            }
                        },
                        {
                            $project: {
                                _id: 1,
                                title: 1,
                                slug: 1,
                                menuReferenceId: 1,
                                order: 1,
                                isDraft: 1,
                                pagebuilder: 1
                            }
                        }
                    ],
                    as: "submenu",
                }
            },
            {
                $addFields: {
                    matchesInSubmenu: {
                        $filter: {
                            input: "$submenu",
                            as: "item",
                            cond: {
                                $or: [{
                                        $regexMatch: {
                                            input: "$$item.title",
                                            regex: new RegExp(searchQuery, "i")
                                        }
                                    },
                                    {
                                        $regexMatch: {
                                            input: "$$item.slug",
                                            regex: new RegExp(searchQuery, "i")
                                        }
                                    }
                                ]
                            }
                        }
                    }
                }
            },
            {
                $match: {
                    $or: [{
                            title: {
                                $regex: new RegExp(searchQuery, "i")
                            }
                        },
                        {
                            slug: {
                                $regex: new RegExp(searchQuery, "i")
                            }
                        },
                        {
                            "matchesInSubmenu.0": {
                                $exists: true
                            }
                        }
                    ]
                }
            },
            {
                $project: {
                    _id: 1,
                    title: 1,
                    slug: 1,
                    isSubMenu: 1,
                    isDraft: 1,
                    createdAt: 1,
                    order: 1,
                    submenu: 1,
                    pagebuilder: 1,
                },
            },
            {
                $sort: {
                    order: 1
                }
            },
            {
                $skip: skip
            },
            {
                $limit: limit
            },
        ];

        const menus = await PageBuilderMenu.aggregate(aggregationPipeline);
        const totalRecords = menus.length;

        if (!menus || menus.length === 0) {
            res.status(200).send({
                status: false,
                message: "Menus not found!",
                data: [],
                currentPage: 0,
                totalPages: Math.ceil(0 / limit),
                totalRecords: 0,
            });
        } else {
            res.status(200).send({
                status: true,
                message: "Menus retrieved successfully!",
                data: menus,
                currentPage: page,
                totalPages: Math.ceil(totalRecords / limit),
                totalRecords: totalRecords,
            });
        }
    } catch (error) {
        res.status(500).send({
            status: false,
            message: error.message,
        });
    }
};

exports.checkUniqueSlug = async (req, res) => {
    try {
      const slug = req.query.slug;
      const menuId = req.query.menuId;
      const submenuId = req.query.submenuId;
      const  relation_id = req.relation_id;
      if (!slug) {
        return res.status(400).send({
          status: false,
          message: "Slug is required!"
        });
      }
  
      if (menuId) {
        const menu = await PageBuilderMenu.findOne({
          _id: {$ne : ObjectId(menuId) },
          slug: slug,
          isDelete: false,
          relation_id: ObjectId(relation_id)
        });
        if (menu) {
          return res.status(400).send({
            status: false,
            message: "Slug is already in use in Menu!"
          });
        }
      }
  
      if (submenuId) {
        const submenu = await PageBuilderSubmenu.findOne({
          _id: {$ne : ObjectId(submenuId) },
          slug: slug,
          isDelete: false,
          relation_id: ObjectId(relation_id)
        });
        if (submenu) {
          return res.status(400).send({
            status: false,
            message: "Slug is already in use in Submenu!"
          });
        }
      }
  
      if (!menuId && !submenuId) {
        const filters = {
          slug: slug,
          relation_id: req.relation_id,
          isDelete: false
        };
        const [menuSlug, subMenuSlug] = await Promise.all([
          PageBuilderMenu.find(filters),
          PageBuilderSubmenu.find(filters)
        ]);
        if (menuSlug.length > 0) {
          return res.status(400).send({
            status: false,
            message: "Slug is already in use in Menu!"
          });
        }
      
        if (subMenuSlug.length > 0) {
          return res.status(400).send({
            status: false,
            message: "Slug is already in use in Submenu!"
          });
        }
      }
      res.status(200).send({
        status: true,
        message: "Slug is available!"
      });
    } catch (error) {
      res.status(500).send({
        status: false,
        message: error.message
      });
    }
};  

exports.reorderMenu = async (req, res) => {
    try {
        const ids = req.body.ids
        if (ids.length > 0) {
            let resOrder = ids.map(async (item, i) => {
                await PageBuilderMenu.findByIdAndUpdate(ObjectId(item), {
                    order: i + 1
                }, {
                    new: true
                })
            });
            await Promise.all([...resOrder]);
        }
        const reorderedMenu = await PageBuilderMenu.find({
            isDelete: false,
            relation_id: ObjectId(req.relation_id)
        }).sort({
            order: 1
        });
        if (reorderedMenu.length > 0)
            return res.status(200).json({
                status: true,
                message: "Reordered PageBuilderMenu retrieved!",
                data: reorderedMenu
            });
        else
            return res.status(200).json({
                status: false,
                message: "PageBuilderMenu not found!"
            });
    } catch (error) {
        return res.status(200).json({
            status: false,
            message: `${error.message}`
        });
    }
};

exports.addPageBuilderMenuAndSubmenu = async (req, res) => {
    try {
      const {
        title,
        slug,
        isSubMenu, // New parameter to check whether it's a submenu
        pagebuilder,
        isDraft,
        restrictionAccess,
        restrictedAccessGroupId,
        restrictedAccessEventId,
        restrictedAccessUserId,
        restrictedAccessTagId,
        restrictedAccessTierId,
        submenus, // Only needed for submenu
      } = req.body;
  
      if (!title) {
        return res.status(400).send({
          status: false,
          message: "Title is required.",
        });
      }
  
      let checkIds = await checkValidIds({
        restrictedAccessGroupId,
        restrictedAccessEventId,
        restrictedAccessUserId,
        restrictedAccessTagId,
        restrictedAccessTierId,
        relation_id: req.relation_id,
      });
  
      if (checkIds.status === false) {
        return res.status(200).json({
          status: false,
          message: checkIds.msg,
        });
      }
  
      const existingMenu = await PageBuilderMenu.findOne({
        $or: [
          {
            title,
            relation_id: ObjectId(req.relation_id),
            isDelete: false,
          },
          {
            slug,
            relation_id: ObjectId(req.relation_id),
            isDelete: false,
          },
        ],
      });
  
      if (existingMenu) {
        const conflictField = existingMenu.title === title ? "title" : "slug";
        return res.status(400).send({
          status: false,
          message: `Menu with this ${conflictField} already exists.`,
        });
      }
  
      // Create the main menu first
      const ids = await PageBuilderMenu.countDocuments(
        {
          relation_id: req.relation_id,
          isDelete: false,
        },
        {
          _id: 1,
          order: 1,
        }
      ).lean();
      let newOrder = ids + 1;
  
      if (isSubMenu === false && !pagebuilder) {
        return res.status(400).send({
          status: false,
          message: "Please select a page first.",
        });
      }
  
      const newMenu = new PageBuilderMenu({
        title: title ? title : "",
        slug: slug ? slug : "",
        order: newOrder,
        isSubMenu: isSubMenu ? isSubMenu : false, // Since it's not a submenu
        pagebuilder: pagebuilder ? pagebuilder : null,
        restrictionAccess: restrictionAccess ? restrictionAccess : "public",
        restrictedAccessGroupId: restrictedAccessGroupId
          ? restrictedAccessGroupId
          : [],
        restrictedAccessEventId: restrictedAccessEventId
          ? restrictedAccessEventId
          : [],
        restrictedAccessUserId: restrictedAccessUserId
          ? restrictedAccessUserId
          : [],
        restrictedAccessTagId: restrictedAccessTagId ? restrictedAccessTagId : [],
        restrictedAccessTierId: restrictedAccessTierId
          ? restrictedAccessTierId
          : [],
        isDraft: isDraft ? isDraft : "publish",
        relation_id: ObjectId(req.relation_id),
      });
  
      const menuId = newMenu._id;
      let subMenuArray = [];
      if (isSubMenu === true) {
        let createdSubmenus = [];
        if (submenus && submenus.length > 0) {
          // Loop through each submenu and create it
          for (const submenuData of submenus) {
            const submenuIds = await PageBuilderSubmenu.find(
              {
                isDelete: false,
                relation_id: ObjectId(req.relation_id),
                menuReferenceId: menuId,
              },
              {
                _id: 1,
                order: 1,
              }
            ).sort({
              order: -1,
            });
  
            let newSubOrder =
              submenuIds && submenuIds.length > 0 ? submenuIds[0].order + 1 : 1;
  
            const existingSubmenu = await PageBuilderSubmenu.findOne({
              relation_id: ObjectId(req.relation_id),
              isDelete: false,
              $or: [
                {
                  title: submenuData.title,
                },
                {
                  slug: submenuData.slug,
                },
              ],
            });
  
            if (existingSubmenu) {
              return res.status(400).send({
                status: false,
                message: `${submenuData.title} submenu already exists!`,
              });
            }
  
            subMenuArray.push({
              menuReferenceId: menuId,
              title: submenuData.title || "",
              slug: submenuData.slug || "",
              order: newSubOrder,
              pagebuilder: submenuData.pagebuilder || null,
              restrictionAccess: submenuData.restrictionAccess || "public",
              restrictedAccessGroupId: submenuData.restrictedAccessGroupId || [],
              restrictedAccessEventId: submenuData.restrictedAccessEventId || [],
              restrictedAccessUserId: submenuData.restrictedAccessUserId || [],
              restrictedAccessTagId: submenuData.restrictedAccessTagId || [],
              restrictedAccessTierId: submenuData.restrictedAccessTierId || [],
              isDraft: submenuData.isDraft || "publish",
              relation_id: ObjectId(req.relation_id),
            });
          }
        }
        if (isSubMenu === false) {
          await newMenu.save();
        } else if (isSubMenu === true) {
          await newMenu.save();
          createdSubmenus = await PageBuilderSubmenu.insertMany(subMenuArray);
        }
        return res.status(200).send({
          status: true,
          message: "Menu and Submenu created successfully!",
          data: {
            menu: newMenu,
            submenu: createdSubmenus,
          },
        });
      }
      await newMenu.save();
      return res.status(200).send({
        status: true,
        message: "Menu created successfully!",
        data: newMenu,
      });
    } catch (error) {
      console.log(error, "error");
  
      res.status(500).send({
        status: false,
        message: error.message,
      });
    }
  };

exports.getPageBuilderMenuByIDv2 = async (req, res) => {
    try {
        const menuId = req.params.id
        const aggregationPipeline = [{
                $match: {
                    _id: ObjectId(menuId),
                    relation_id: ObjectId(req.relation_id),
                    isDelete: false,
                },
            },
            {
                $lookup: {
                    from: "pagebuilders",
                    localField: "pagebuilder",
                    foreignField: "_id",
                    pipeline: [{
                        $project: {
                            _id: 1,
                            title: 1,
                        },
                    }, ],
                    as: "pagebuilder",
                },
            },
            {
                $lookup: {
                    from: "pagebuilder_submenus",
                    localField: "_id",
                    foreignField: "menuReferenceId",
                    pipeline: [{
                            $match: {
                                menuReferenceId: ObjectId(menuId),
                                isDelete: false,
                                relation_id: ObjectId(req.relation_id),
                            }
                        },
                        {
                            $lookup: {
                                from: "groups",
                                localField: "restrictedAccessGroupId",
                                foreignField: "_id",
                                pipeline: [{
                                    $project: {
                                        _id: 1,
                                        groupTitle: 1,
                                    },
                                }],
                                as: "group"
                            }
                        },
                        {
                            $lookup: {
                                from: "events",
                                localField: "restrictedAccessEventId",
                                foreignField: "_id",
                                pipeline: [{
                                    $project: {
                                        _id: 1,
                                        title: 1,
                                    },
                                }],
                                as: "event"
                            }
                        },
                        {
                            $lookup: {
                                from: "airtable-syncs",
                                localField: "restrictedAccessUserId",
                                foreignField: "_id",
                                pipeline: [{
                                    $project: {
                                        _id: 1,
                                        "Preferred Email": 1,
                                        attendeeDetail: 1,
                                        email: 1,
                                        first_name: 1,
                                        last_name: 1,
                                        firebaseId: 1,
                                        profileImg: 1
                                    },
                                }],
                                as: "airtable-syncs"
                            }
                        },
                        {
                            $lookup: {
                                from: "contentarchive_tags",
                                localField: "restrictedAccessTagId",
                                foreignField: "_id",
                                pipeline: [{
                                    $project: {
                                        _id: 1,
                                        name: 1,
                                    },
                                }],
                                as: "contentArchive_tag"
                            }
                        },
                        {
                            $lookup: {
                                from: "pagebuilders",
                                localField: "pagebuilder",
                                foreignField: "_id",
                                pipeline: [{
                                    $project: {
                                        _id: 1,
                                        title: 1,
                                    },
                                }, ],
                                as: "pagebuilder",
                            },
                        },
                        {
                            $project: {
                                _id: 1,
                                title: 1,
                                slug: 1,
                                menuReferenceId: 1,
                                order: 1,
                                isDraft: 1,
                                restrictionAccess: 1,
                                restrictedAccessGroupId: "$group",
                                restrictedAccessEventId: "$event",
                                restrictedAccessUserId: "$airtable-syncs",
                                restrictedAccessTagId: "$contentArchive_tag",
                                restrictedAccessTierId: 1,
                                pagebuilder: 1,
                            }
                        }
                    ],
                    as: "submenu",
                }
            },
            {
                $lookup: {
                    from: "groups",
                    localField: "restrictedAccessGroupId",
                    foreignField: "_id",
                    pipeline: [{
                        $project: {
                            _id: 1,
                            groupTitle: 1,
                        },
                    }],
                    as: "group"
                }
            },
            {
                $lookup: {
                    from: "events",
                    localField: "restrictedAccessEventId",
                    foreignField: "_id",
                    pipeline: [{
                        $project: {
                            _id: 1,
                            title: 1,
                        },
                    }],
                    as: "event"
                }
            },
            {
                $lookup: {
                    from: "airtable-syncs",
                    localField: "restrictedAccessUserId",
                    foreignField: "_id",
                    pipeline: [{
                        $project: {
                            _id: 1,
                            "Preferred Email": 1,
                            attendeeDetail: 1,
                            email: 1,
                            first_name: 1,
                            last_name: 1,
                            firebaseId: 1,
                            profileImg: 1,
                        },
                    }],
                    as: "airtable-syncs"
                }
            },
            {
                $lookup: {
                    from: "contentarchive_tags",
                    localField: "restrictedAccessTagId",
                    foreignField: "_id",
                    pipeline: [{
                        $project: {
                            _id: 1,
                            name: 1,
                        },
                    }],
                    as: "contentArchive_tag"
                }
            },
            {
                $project: {
                    _id: 1,
                    title: 1,
                    slug: 1,
                    isSubMenu: 1,
                    isDraft: 1,
                    createdAt: 1,
                    order: 1,
                    submenu: 1,
                    pagebuilder: 1,
                    restrictionAccess: 1,
                    restrictedAccessGroupId: "$group",
                    restrictedAccessEventId: "$event",
                    restrictedAccessUserId: "$airtable-syncs",
                    restrictedAccessTagId: "$contentArchive_tag",
                    restrictedAccessTierId: 1,

                },
            },
        ];
        const menu = await PageBuilderMenu.aggregate(aggregationPipeline);
        const obj = {
            relation_id: req.relation_id
        };
        const tiers = await getAllTiersfromBilling(obj, (expand = true));
        const restrictedTierIds = Array.isArray(menu[0]?.restrictedAccessTierId) ?
            menu[0].restrictedAccessTierId.map((id) => id.toString()) : [];

        const filteredTiers = tiers
            .filter((tier) => restrictedTierIds.includes(tier._id.toString()))
            .map((tier) => ({
                _id: tier._id,
                name: tier.name,
            }));

        if (menu.length > 0) {
            menu[0].restrictedAccessTierId = filteredTiers;

            menu[0].submenu.forEach(subMenu => {
                const restrictedTierIdsSubMenu = Array.isArray(subMenu.restrictedAccessTierId) ?
                    subMenu.restrictedAccessTierId.map((id) => id.toString()) : [];
        
                const filteredTiersSubMenu = tiers
                    .filter((tier) => restrictedTierIdsSubMenu.includes(tier._id.toString()))
                    .map((tier) => ({
                        _id: tier._id,
                        name: tier.name,
                    }));
        
                subMenu.restrictedAccessTierId = filteredTiersSubMenu;
            });
        }

        if (!menu || menu.length === 0) {
            return res.status(400).send({
                status: false,
                message: "Menu not found!",
            });
        }

        res.status(200).send({
            status: true,
            message: "Menu retrieved successfully!",
            data: menu[0],
        });
    } catch (error) {
        // Handle errors
        res.status(500).send({
            status: false,
            message: error.message,
        });
    }
};

exports.updateMenuAndSubmenu = async (req, res) => {
    try {
        const menuId = req.params.id;
        let {
            title,
            slug,
            isSubMenu,
            isDraft,
            pagebuilder,
            restrictionAccess,
            restrictedAccessGroupId,
            restrictedAccessEventId,
            restrictedAccessUserId,
            restrictedAccessTagId,
            restrictedAccessTierId,
            submenus
        } = req.body;
        let checkIds = await checkValidIds({
            restrictedAccessGroupId,
            restrictedAccessEventId,
            restrictedAccessUserId,
            restrictedAccessTagId,
            restrictedAccessTierId,
            relation_id: ObjectId(req.relation_id)
        });

        if (checkIds.status === false) {
            return res.status(200).json({
                status: false,
                message: checkIds.msg
            });
        }
        const menuToUpdate = await PageBuilderMenu.findOne({
            _id: ObjectId(menuId),
            relation_id: ObjectId(req.relation_id),
            isDelete: false
        });

        if (!menuToUpdate) {
            return res.status(404).send({
                status: false,
                message: "Menu not found or already deleted."
            });
        }

        const duplicateMenu = await PageBuilderMenu.findOne({
            _id: {
                $ne: ObjectId(menuId)
            },
            $or: [{
                title
            }, {
                slug
            }],
            relation_id: ObjectId(req.relation_id),
            isDelete: false
        });

        if (duplicateMenu) {
            const conflictField = duplicateMenu.title === title ? "title" : "slug";
            return res.status(400).send({
                status: false,
                message: `Menu with this ${conflictField} already exists.`
            });
        }

        if (isSubMenu === false && !pagebuilder) {
            return res.status(400).send({
                status: false,
                message: "Please select at least one pagebuilder when isSubMenu is false."
            });
        }

        if (isSubMenu === true) {
            pagebuilder = null;
        }

        const updatedMenu = await PageBuilderMenu.findByIdAndUpdate(
            menuId, {
                title: title || menuToUpdate.title,
                slug: slug || menuToUpdate.slug,
                isSubMenu: isSubMenu !== undefined ? isSubMenu : menuToUpdate.isSubMenu,
                pagebuilder: pagebuilder !== undefined ? pagebuilder : menuToUpdate.pagebuilder,
                restrictionAccess: restrictionAccess ?? menuToUpdate.restrictionAccess,
                restrictedAccessGroupId: restrictedAccessGroupId || [],
                restrictedAccessEventId: restrictedAccessEventId || [],
                restrictedAccessUserId: restrictedAccessUserId || [],
                restrictedAccessTagId: restrictedAccessTagId || [],
                restrictedAccessTierId: restrictedAccessTierId || [],
                isDraft: isDraft !== undefined ? isDraft : menuToUpdate.isDraft
            }, {
                new: true
            }
        );

        if (isSubMenu === false) {
            await PageBuilderSubmenu.updateMany({
                menuReferenceId: updatedMenu._id
            }, {
                $set: {
                    isDelete: true
                }
            });
        }

        const updatedSubmenus = [];
        
        if (submenus && Array.isArray(submenus) && submenus.length > 0) {
            for (const submenu of submenus) {
                const duplicateSlugSubmenu = await PageBuilderSubmenu.findOne({
                    _id: {
                        $ne: ObjectId(submenu._id)
                    },
                    slug: submenu.slug,
                    relation_id: ObjectId(req.relation_id),
                    isDelete: false
                });

                if (duplicateSlugSubmenu) {
                    return res.status(400).send({
                        status: false,
                        message: `Submenu with this slug already exists.`
                    });
                }
                submenu.menuReferenceId = updatedMenu._id;
                if (submenu._id) {
                    const updatedSubmenu = await PageBuilderSubmenu.findOneAndUpdate({
                        _id: submenu._id,
                        menuReferenceId: submenu.menuReferenceId
                    }, {
                        title: submenu.title || submenu.title,
                        slug: submenu.slug || submenu.slug,
                        pagebuilder: submenu.pagebuilder || submenu.pagebuilder,
                        restrictionAccess: submenu.restrictionAccess ?? menuToUpdate.restrictionAccess,
                        restrictedAccessGroupId: submenu.restrictedAccessGroupId || [],
                        restrictedAccessEventId: submenu.restrictedAccessEventId || [],
                        restrictedAccessUserId: submenu.restrictedAccessUserId || [],
                        restrictedAccessTagId: submenu.restrictedAccessTagId || [],
                        restrictedAccessTierId: submenu.restrictedAccessTierId || [],
                        isDraft: submenu.isDraft !== undefined ? submenu.isDraft : "publish"
                    }, {
                        new: true
                    });

                    if (updatedSubmenu) {
                        updatedSubmenus.push(updatedSubmenu);
                    }
                } else {
                    const submenuIds = await PageBuilderSubmenu.find({
                        isDelete: false,
                        relation_id: ObjectId(req.relation_id),
                        menuReferenceId: updatedMenu._id
                    }, {
                        _id: 1,
                        order: 1
                    }).sort({
                        order: -1
                    });

                    let newSubOrder = (submenuIds && submenuIds.length > 0) ? submenuIds[0].order + 1 : 1;
                    const createdSubmenu = await PageBuilderSubmenu.create([{
                        title: submenu.title,
                        slug: submenu.slug,
                        menuReferenceId: submenu.menuReferenceId,
                        pagebuilder: submenu.pagebuilder,
                        restrictionAccess: submenu.restrictionAccess ?? menuToUpdate.restrictionAccess,
                        restrictedAccessGroupId: submenu.restrictedAccessGroupId || [],
                        restrictedAccessMembershipPlanId: submenu.restrictedAccessMembershipPlanId || [],
                        restrictedAccessUserId: submenu.restrictedAccessUserId || [],
                        restrictedAccessTagId: submenu.restrictedAccessTagId || [],
                        restrictedAccessTierId: submenu.restrictedAccessTierId || [],
                        restrictedAccessEventId: submenu.restrictedAccessEventId || [],
                        order: newSubOrder,
                        isDraft: submenu.isDraft !== undefined ? submenu.isDraft : "publish",
                        relation_id: req.relation_id
                    }]);

                    updatedSubmenus.push(createdSubmenu[0]);
                }
            }
        }
        const populatedSubmenus = await PageBuilderSubmenu.find({
            menuReferenceId: updatedMenu._id,
            isDelete: false
        });

        res.status(200).send({
            status: true,
            message: "Menu and submenus updated successfully!",
            data: {
                menu: updatedMenu,
                submenus: populatedSubmenus
            }
        });

    } catch (error) {
        res.status(500).send({
            status: false,
            message: error.message
        });
    }
};

exports.getHtmlContentBySlug = async (req, res) => {
    try {
        const searchQuery = req.query.slug || "";

        const matchStage = {
            relation_id: ObjectId(req.relation_id),
            isDelete: false,
        };

        if (searchQuery) {
            const regex = new RegExp(searchQuery, "i");
            matchStage.$or = [{
                    title: {
                        $regex: regex
                    }
                },
                {
                    slug: {
                        $regex: regex
                    }
                },
            ];
        }

        const aggregationPipeline = [{
                $lookup: {
                    from: "pagebuilders",
                    localField: "pagebuilder",
                    foreignField: "_id",
                    pipeline: [{
                        $project: {
                            _id: 1,
                            title: 1,
                            HTML_Content: 1
                        },
                    }, ],
                    as: "pagebuilder",
                },
            },
            {
                $unwind: {
                  path: "$pagebuilder",
                  preserveNullAndEmptyArrays: true
                }
            },
            {
                $addFields: {
                    pagebuilder: {
                        $ifNull: ["$pagebuilder", {}]
                    }
                }
            },
            {
                $lookup: {
                    from: "pagebuilder_submenus",
                    localField: "_id",
                    foreignField: "menuReferenceId",
                    pipeline: [{
                            $match: {
                                isDelete: false,
                                relation_id: ObjectId(req.relation_id),
                            }
                        },
                        {
                            $lookup: {
                                from: "pagebuilders",
                                localField: "pagebuilder",
                                foreignField: "_id",
                                pipeline: [{
                                    $project: {
                                        _id: 1,
                                        title: 1,
                                        HTML_Content: 1,
                                    },
                                }, ],
                                as: "pagebuilder",
                            },
                        },
                        {
                            $unwind: {
                              path: "$pagebuilder",
                              preserveNullAndEmptyArrays: true
                            }
                        },
                        {
                            $addFields: {
                                pagebuilder: {
                                    $ifNull: ["$pagebuilder", {}]
                                }
                            }
                        },
                        {
                            $project: {
                                _id: 1,
                                title: 1,
                                slug: 1,
                                isSubMenu: 1,
                                pagebuilder: 1
                            }
                        },
                    ],
                    as: "submenu",
                }
            },
            {
                $addFields: {
                    submenu: {
                        $filter: {
                            input: "$submenu",
                            as: "submenuItem",
                            cond: {
                                $or: [{
                                        $regexMatch: {
                                            input: "$$submenuItem.title",
                                            regex: new RegExp(searchQuery, "i")
                                        }
                                    },
                                    {
                                        $regexMatch: {
                                            input: "$$submenuItem.slug",
                                            regex: new RegExp(searchQuery, "i")
                                        }
                                    }
                                ]
                            }
                        }
                    }
                }
            },
            {
                $match: {
                    isDelete: false,
                    relation_id: ObjectId(req.relation_id),
                    $or: [{
                            title: {
                                $regex: new RegExp(searchQuery, "i")
                            }
                        },
                        {
                            slug: {
                                $regex: new RegExp(searchQuery, "i")
                            }
                        },
                        {
                            "submenu": { 
                                $not: { $size: 0 }
                            }
                        }
                    ]
                }
            },
            {
                $project: {
                    _id: 1,
                    title: 1,
                    slug: 1,
                    isSubMenu: 1,
                    submenu: 1,
                    pagebuilderDetails: 1,
                },
            },
        ];

        const menus = await PageBuilderMenu.aggregate(aggregationPipeline);

        if ( !menus || menus.length === 0 ) {
            res.status(200).send({
                status: false,
                message: "HTML Content not found!",
                data: {},
            });
        } else {
            res.status(200).send({
                status: true,
                message: "HTML Content retrieved successfully!",
                data: menus[0],
            });
        }
    } catch (error) {
        res.status(500).send({
            status: false,
            message: error.message,
        });
    }
};

exports.getHtmlContentBySlugV2 = async (req, res) => {
try{
    const slug = req.query.slug;
    const {
        relation_id
    } = req;

    if (!slug) {
        return res.status(400).send({
            status: false,
            message: "Slug are required!"
        });
    }

    const filters = {
        slug: slug,
        relation_id: ObjectId(relation_id),
        isDelete: false,
    };

    const [menuSlug, subMenuSlug] = await Promise.all([
        PageBuilderMenu.findOne(filters),
        PageBuilderSubmenu.findOne(filters)
    ]);

    if (menuSlug) {
        return res.status(200).send({
            status: true,
            message: "Htmlcontent get Successfully!",
            data: menuSlug.pagebuilder.HTML_Content
        });
    }

    if (subMenuSlug) {
        return res.status(200).send({
            status: true,
            message: "Htmlcontent get Successfully!",
            data: subMenuSlug.pagebuilder.HTML_Content
        });
    }

    res.status(400).send({
        status: false,
        message: "Htmlcontent not Found!",
        data: ""
    });
    } catch (error) {
    res.status(500).send({
        status: false,
        message: error.message
    });
    }
}


exports.getPageBuilderMenuSuggestionList = async (req, res) => {
    try {
        const matchStage = {
            relation_id: ObjectId(req.relation_id),
            isDelete: false,
        };

        const aggregationPipeline = [
            {
                $match: matchStage,
            },
            {
                $lookup: {
                    from: "pagebuilders",
                    localField: "pagebuilder",
                    foreignField: "_id",
                    pipeline: [
                        {
                            $project: {
                                _id: 1,
                                title: 1,
                            },
                        },
                    ],
                    as: "pagebuilder",
                },
            },
            {
                $unwind: {
                    path: "$pagebuilder",
                    preserveNullAndEmptyArrays: true
                }
            },
            {
                $addFields: {
                    pagebuilder: {
                        $ifNull: ["$pagebuilder", {}]
                    }
                }
            },
            {
                $lookup: {
                    from: "pagebuilder_submenus",
                    localField: "_id",
                    foreignField: "menuReferenceId",
                    pipeline: [
                        {
                            $match: {
                                isDelete: false,
                                relation_id: ObjectId(req.relation_id),
                            }
                        },
                        {
                            $lookup: {
                                from: "pagebuilders",
                                localField: "pagebuilder",
                                foreignField: "_id",
                                pipeline: [
                                    {
                                        $project: {
                                            _id: 1,
                                            title: 1,
                                        },
                                    },
                                ],
                                as: "pagebuilder",
                            },
                        },
                        {
                            $unwind: {
                                path: "$pagebuilder",
                                preserveNullAndEmptyArrays: true
                            }
                        },
                        {
                            $addFields: {
                                pagebuilder: {
                                    $ifNull: ["$pagebuilder", {}]
                                }
                            }
                        },
                        {
                            $project: {
                                _id: 1,
                                title: 1,
                                slug: 1,
                                menuReferenceId: 1,
                                order: 1,
                                isDraft: 1,
                                pagebuilder: 1
                            }
                        },
                    ],
                    as: "submenu",
                }
            },
            {
                $addFields: {
                    matchesInSubmenu: {
                        $filter: {
                            input: "$submenu",
                            as: "submenuItem",
                            cond: {
                                $or: [
                                    {
                                        $regexMatch: {
                                            input: "$$submenuItem.title",
                                            regex: new RegExp("", "i")
                                        }
                                    },
                                    {
                                        $regexMatch: {
                                            input: "$$submenuItem.slug",
                                            regex: new RegExp("", "i")
                                        }
                                    }
                                ]
                            }
                        }
                    }
                }
            },
            {
                $match: {
                    isDelete: false,
                    relation_id: ObjectId(req.relation_id),
                }
            },
            {
                $addFields: {
                    titleLower: { $toLower: "$title" }
                }
            },
            {
                $sort: {
                    titleLower: 1
                }
            },
            {
                $project: {
                    _id: 1,
                    title: 1,
                    slug: 1,
                    isSubMenu: 1,
                    isDraft: 1,
                    createdAt: 1,
                    order: 1,
                    submenu: 1,
                    pagebuilder: 1,
                },
            }
        ];

        const menus = await PageBuilderMenu.aggregate(aggregationPipeline);
      

        if (!menus || menus.length === 0) {
            res.status(200).send({
                status: false,
                message: "Menus not found!",
                data: [],
              
            });
        } else {
            res.status(200).send({
                status: true,
                message: "Menus retrieved successfully!",
                data: menus,
              
                
            });
        }
    } catch (error) {
        res.status(500).send({
            status: false,
            message: error.message,
        });
    }
};
