require("./tracer.js");
const express = require("express");
var cors = require("cors");
const app = express();
require("dotenv").config();
const mongoose = require("mongoose");
const cookieparser = require("cookie-parser");
const http = require("http");
const cron = require("node-cron");
const compression = require("compression");
const ErrorHandler = require("./utils/error-handler");
const { apiLogger, apiErrorLogger } = require("./utils/api-logger");
const { logger } = require("./utils/logger");
const rateLimit = require('express-rate-limit');

const httpsAgent = new http.Agent({ keepAlive: true });
const server = http.createServer(app);

const limiter = rateLimit({
  // windowMs: 60 * 1000, // 1 minute (60,000 milliseconds)
  windowMs: 1000, // 1 second (60,000 milliseconds)
  max: 100, // Limit each IP to 100 requests per window (1 minute) as per frontend team 
  message: 'Too many requests from this IP!',
  headers: true, // Include rate limit info in response headers
});



const io = require("socket.io")(server, {
  path: "/socket-new",
  cors: {
    httpsAgent,
    origin: "*",
    methods: ["GET", "POST", "PUT", "DELETE"],
    credentials: true,
    maxHttpBufferSize: 5e8, // 500 MB,
  },
});
// const UserData = require("./database/models/user");
const debugErrorLogs = require("./database/models/debug_error_logs");
const user = require("./routes/userManagement/userRoute");
const customregisterform = require("./routes/userManagement/customregistrationformRoute");
const postRoute = require("./routes/postRoute");
const commentRoute = require("./routes/contentArchiveManagement/commentRoute");
const contentcommentRoute = require("./routes/contentArchiveManagement/contentCommentRoute");
const groupRoute = require("./routes/groupRoute");
const topicRoute = require("./routes/topicRoute");
const membershipPlanRoute = require("./routes/membershipPlanManagement/membershipPlanRoute");
const paymentRoute = require("./routes/paymentRoute");
const questionnaireRoute = require("./routes/userManagement/questionnaireRoute");
const userUtilRoute = require("./routes/userManagement/userUtilRoute");
const contentArchiveRoute = require("./routes/contentArchiveManagement/contentArchiveRoute");
const videoStatisticRoute = require("./routes/contentArchiveManagement/videoStatisticRoute");
const eventRoute = require("./routes/eventManagement/eventRoute");
const eventCategoryRoute = require("./routes/eventManagement/eventCategoryRoute");
const eventSubCategoryRoute = require("./routes/eventManagement/eventSubCategoryRoute");
const userDataSyncRoute = require("./routes/userManagement/userDataSyncRoute");
const eventActivityRoute = require("./routes/eventManagement/eventActivityRoute");
const roomRoute = require("./routes/eventManagement/eventRoomAndSessionRoute");
const adminBannerRoute = require("./routes/newsManagement/adminBannerRoute");
const adminPostRoute = require("./routes/newsManagement/adminPostRoute");
const adminNewsRoute = require("./routes/newsManagement/adminNewsRoute");
const chatChannelRoute = require("./routes/chatChannelRoute");
const eventAttendeeManageRoute = require("./routes/eventManagement/eventAttendeeManageRoute");
const eventParticipantTypesRoute = require("./routes/eventManagement/eventParticipantTypesRoute");
const eventWiseParticipantTypesRoute = require("./routes/eventManagement/eventWiseParticipantTypesRoute");
const eventTicketPaymentRoute = require("./routes/eventManagement/eventTicketPaymentRoute.js");
const eventTicketCancelledRoute = require("./routes/eventManagement/eventTicketCancelledRoute.js");
const userCensusRoute = require("./routes/userManagement/userCensusRoute");
const partnerRoute = require("./routes/partner/partnerRoute");
const partnerHelpfulLinkRoute = require("./routes/partner/partnerHelpfulLinkRoute");
const partnerReasonRoute = require("./routes/partner/partnerReasonsRoute");
const partnerPostRoute = require("./routes/partner/partnerPostRoute");
const filterPartnerRoute = require("./routes/partner/filterPartnerRoute");
const partnerReviewRoute = require("./routes/partner/partnerReviewRoute");
const partnerBannerRoute = require("./routes/partner/partnerBannerRoute");
const partnerStatisticsRoute = require("./routes/partner/partnerStatisticsRoute");
const partnerBadgeRoute = require("./routes/partner/partnerBadgeRoute");
const partnerCategoryRoute = require("./routes/partner/partnerCategoryRoute");
const partnerSubCategoryRoute = require("./routes/partner/partnerSubCategoryRoute");
const eventTypeRoute = require("./routes/eventManagement/eventTypeRoute");
const deepLinkRooute = require("./routes/deepLinkRooute");
const commonImageRoute = require("./routes/commonImage/commonImageRoute");
const chatListRoute = require("./routes/chatListRoute/chatList");
const accessResourceRoute = require("./routes/collaborator/accessResourceRoute");
const collaboratorRoute = require("./routes/collaborator/collaboratorRoute");
const mediaUploadRoute = require("./routes/mediaUploadRoute/mediaUploadRoute");
const restrictionAccessRoute = require("./routes/userAccessRules/restrictionAccessRoute.js");
const pageBuilderRoute = require("./routes/pageBuilderRoute.js");
const pagebuilder_submenu = require("./routes/pageBuilderSubmenuRoute.js");
const pageBuilderMenuRoute = require("./routes/pageBuilderMenuRoute.js");
const freeTierConfiguration = require("./routes/freeTierConfigurationRoute.js");
const eventRefundPolicyRoute = require("./routes/eventRefundPolicyRoute.js");
const emailRoute = require("./routes/emailRoute.js");

const { serverInit } = require("./microservices/user/utils/server-init");

const microserviceRoutes = require("./microservices/router");

const port = process.env.PORT || 8080;
const userMigrationTrackingRoute = require("./routes/userManagement/userMigrationTrackingRoute");
const visibiltyRoute = require("./routes/adminVisibilitySetting/visibiltyRoute");
const adminNotificationRoute = require("./routes/notification/adminNotificationRoute");
const userNotificationRoute = require("./routes/notification/userNotificationRoute");
const notificationStatisticsRout = require("./routes/notification/notificationStatisticsRoute");
const eventTicketRoute = require("./routes/eventManagement/eventTicketRoute");
const eventGuestTicketRoute = require("./routes/eventManagement/eventGuestTicketRoute");
const eventAddonRoute = require("./routes/eventManagement/eventAddon");
const eventAddonV2Route = require("./routes/eventManagement/eventAddonV2Route.js");
const eventAddonVariationRoute = require("./routes/eventManagement/eventAddonVariation");
const eventAddonGroupRoute = require("./routes/eventManagement/eventAddonGroupRoute.js");
const eventPromocodeRoute = require("./routes/eventManagement/eventPromocodeRoute");
const userInfoVisibilityRoute = require("./routes/userManagement/userInfoVisibilityRoute");
const userCustomFieldRoute = require("./routes/userManagement/userCustomFieldRoute.js");
const grouposCommonRoute = require("./routes/grouposCommonRoute");
const CommunitiesService = require("./microservices/user/components/communities/services/communities-service.js");
const appConfig = require("./routes/appVersionConfigRoute.js");
const emailVerify = require("./routes/emailVerifyRoute.js");
const eventGuestRoute = require("./routes/eventManagement/eventGuestRoute");
const eventBannerRoute = require("./routes/eventManagement/eventBannerRoute.js");
const eventCheckInRoute = require("./routes/eventManagement/eventCheckInRoute.js");

const communitiesService = new CommunitiesService();

const {
  getUsersMDSOnlyCensusExpiryNear,
} = require("./controller/userManagement/userCensusController");

require("dotenv").config();
const domainsFromEnv = process.env.CORS_DOMAINS || "";
const db = require("./config/config").get(process.env.NODE_ENV);

const whitelist = domainsFromEnv.split(",").map((item) => item.trim());
const corsOptions = {
  origin: function (origin, callback) {
    if (!origin || whitelist.indexOf(origin) !== -1) {
      callback(null, true);
    } else {
      callback(new Error("Not allowed by CORS"));
    }
  },
  credentials: true,
};

// TODO: enable cors options
// app.use(cors(corsOptions));
app.use(cors());

// Serve static files from the uploads directory
app.use("/uploads", express.static("uploads"));

const {
  updateAllUsersRegistrationDetailsOnCron,
  updateAllUsersDetailInMDSDocument,
} = require("./controller/userManagement/userDataSyncController");

const {
  sendScheduleNotification,
  sendScheduleNotificationV2,
} = require("./controller/notification/adminNotificationController");

const {
  airTableEventSyncUp
} = require("./controller/userManagement/userDataSyncController.js");

const {
  cronRemovePaymentIntent,
} = require("./controller/eventManagement/eventTicketPaymentControllerV2");

mongoose.Promise = global.Promise;
mongoose.connect(
  db.DATABASE,
  {
    useNewUrlParser: true,
    useUnifiedTopology: true,
    autoIndex: true,
  },
  function (err) {
    if (err) console.log(err);
    console.log("Database is connected");
    serverInit(); //run server init script when DB is connected
  }
);

// Apply the rate limiter middleware to all routes
app.use(limiter);

mongoose.connection.on("connected", () => {
  console.log("Mongoose connection open");
});

// for parsing application/json
app.use(express.json({ limit: "50mb" }));

// for parsing application/x-www-form-urlencoded
app.use(
  express.urlencoded({ limit: "50mb", extended: true, parameterLimit: 50000 })
);

app.use(cookieparser());

// compression
app.use(compression({ filter: shouldCompress }));

function shouldCompress(req, res) {
  if (req.headers["x-no-compression"]) {
    // don't compress responses with this request header
    return false;
  }

  // fallback to standard filter function
  return compression.filter(req, res);
}

// for logging all the api requests
app.use(apiLogger());

process.on("uncaughtException", (err) => {
  console.error("🔥 Uncaught Exception:", err);
});

process.on("unhandledRejection", (reason, promise) => {
  console.error("❌ Unhandled Promise Rejection:", reason);
});


app.use(async (req, res, next) => {
  // console.log("called:", req.path);
  // const subdomain = req.subdomains ? req.subdomains[0]
  const subdomain = req.subdomains[0];

  // if (subdomain === "app") {
  //   //
  // } else if (subdomain === "admin") {
  //   //
  // } else {
  //   if (!subdomain) {
  //     return res.status(400).send("Wrong request!");
  //   }
  //   const community = await communitiesService.repository.findCommunity({
  //     filter: {
  //       nickname: subdomain,
  //     },
  //   });

  //   console.log(community, ":::");
  // }

  next();
});

// abc.groupos.com

app.use("/api/user", user);
app.use("/api", freeTierConfiguration);
app.use("/api", postRoute);
app.use("/api", userNotificationRoute);
app.use("/api", adminNotificationRoute);
app.use("/api", commentRoute);
app.use("/api", groupRoute);
app.use("/api", topicRoute);
app.use("/api", membershipPlanRoute);
app.use("/api", paymentRoute);
app.use("/api", questionnaireRoute);
app.use("/api", userUtilRoute);
app.use("/api", customregisterform);
app.use("/api", contentArchiveRoute);
app.use("/api", contentcommentRoute);
app.use("/api", videoStatisticRoute);
app.use("/api", eventRoute);
app.use("/api", eventCategoryRoute);
app.use("/api", eventSubCategoryRoute);
app.use("/api", userDataSyncRoute);
app.use("/api", eventActivityRoute);
app.use("/api", roomRoute);
app.use("/api", adminBannerRoute);
app.use("/api", adminPostRoute);
app.use("/api", adminNewsRoute);
app.use("/api", chatChannelRoute);
app.use("/api", eventAttendeeManageRoute);
app.use("/api", eventParticipantTypesRoute);
app.use("/api", eventWiseParticipantTypesRoute);
app.use("/api", eventTicketPaymentRoute);
app.use("/api", eventTicketCancelledRoute);
app.use("/api", userCensusRoute);
app.use("/api", partnerRoute);
app.use("/api", partnerCategoryRoute);
app.use("/api", partnerSubCategoryRoute);
app.use("/api", partnerHelpfulLinkRoute);
app.use("/api", partnerPostRoute);
app.use("/api", partnerReasonRoute);
app.use("/api", filterPartnerRoute);
app.use("/api", partnerReviewRoute);
app.use("/api", partnerBannerRoute);
app.use("/api", partnerStatisticsRoute);
app.use("/api", partnerBadgeRoute);
app.use("/api", eventTypeRoute);
app.use("/api", deepLinkRooute);
app.use("/api", commonImageRoute);
app.use("/api", chatListRoute);
app.use("/api", accessResourceRoute);
app.use("/api", collaboratorRoute);
app.use("/api", mediaUploadRoute);
app.use("/api", userMigrationTrackingRoute);
app.use("/api", visibiltyRoute);
app.use("/api", eventTicketRoute);
app.use("/api", eventGuestTicketRoute);
app.use("/api", eventAddonRoute);
app.use("/api", eventAddonV2Route);
app.use("/api", eventAddonVariationRoute);
app.use("/api", eventAddonGroupRoute);
app.use("/api", eventPromocodeRoute);
app.use("/api", adminNotificationRoute);
app.use("/api", userNotificationRoute);
app.use("/api", notificationStatisticsRout);
app.use("/api", userInfoVisibilityRoute);
app.use("/api", grouposCommonRoute);
app.use("/api", restrictionAccessRoute);
app.use("/api", userCustomFieldRoute);
app.use("/api", pageBuilderRoute);
app.use("/api", pagebuilder_submenu);
app.use("/api", pageBuilderMenuRoute);
app.use("/api", eventRefundPolicyRoute);
app.use("/api", emailRoute);
app.use("/api", appConfig);
app.use("/api", emailVerify);
app.use("/api", eventGuestRoute);
app.use("/api", eventBannerRoute);
app.use("/api", eventCheckInRoute);

microserviceRoutes(app);

app.set("socketio", io);

app.get("/api", async (req, res) => {
  res.send(`<h3>Welcome Million Dollar Investment</h3>`);
  /** GooglePlayPurchase **/
  // await userControllers.GooglePlayPurchase();
});

process.on("uncaughtException", function (error) {
  // console.log(error, "got an error");
  const newError = new debugErrorLogs({
    error: error,
    event: "",
    other: {},
  });
  newError.save();
});

// for logging the errors of the pipeline.
app.use(apiErrorLogger());

// handling errors and sending response
app.use(ErrorHandler);

// handling undefined route
app.use("*", (req, res) => {
  res.sendStatus(404).end();
});

server.listen(port, () => {
  logger.info(`Listening to the port ${port}`);
});

//scheduled the cron job for every 9 am to update users other details with their respective fields
cron.schedule(
  "00 09 * * *",
  () => {
    // updateAllUsersRegistrationDetailsOnCron();
    // console.log("Cron running on every day at 9:00 AM IST ");
  },
  {
    scheduled: true,
    timezone: "Asia/Kolkata",
  }
);

cron.schedule(
  "00 09 * * *",
  () => {
    getUsersMDSOnlyCensusExpiryNear();
    console.log("mds only access expiry notification called getUsersMDSOnlyCensusExpiryNear");
  },
  {
    scheduled: true,
    timezone: "America/Chicago",
  }
);
//scheduled the cron job for every 9 am to update users data in MDS-Document Database with their respective fields
cron.schedule(
  "00 09 * * *",
  () => {
    updateAllUsersDetailInMDSDocument();
    console.log("Cron running on every day at 9:00 AM IST updateAllUsersDetailInMDSDocument");
  },
  {
    scheduled: true,
    timezone: "Asia/Kolkata",
  }
);
//scheduled the cron job for every minute to send the notification
cron.schedule(
  "* * * * *",
  () => {
    sendScheduleNotification();
    // console.log("Cron running on every minute for Notification ");
  },
  {
    scheduled: true,
    timezone: "Asia/Kolkata",
  }
);

//scheduled the cron job for Remove the paid ticket
cron.schedule(
  "*/10 * * * *",
  () => {
    cronRemovePaymentIntent();
    // console.log("Cron running on every minute for Notification ");
  },
  {
    scheduled: true,
    timezone: "Asia/Kolkata",
  }
);

cron.schedule(
  "0 */3 * * *",
  () => {
    airTableEventSyncUp();
    console.log("Cron running on every 3 hours");
  },
  {
    scheduled: true,
    timezone: "Asia/Kolkata",
  }
);

cron.schedule(
  "* * * * *",
  () => {
    sendScheduleNotificationV2();
    // console.log("Cron running on every minute for Notification V2 ");
  },
  {
    scheduled: true,
    timezone: "Asia/Kolkata",
  }
);
