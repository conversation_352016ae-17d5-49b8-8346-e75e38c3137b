const userMicroserviceRoutes = require("./user/router");

module.exports = (app) => {
  /**
   * Add global route middleware
   * router.use((req, res, next) => next())
   */
  app.use((req, res, next) => {
    // console.log("global microservice route middleware");
    next();
  });

  // server ping
  app.use("/api/ping", (req, res) => {
    res.status(200).json({
      message: "Groupos API Server online!",
    });
  });

  // environment variables endpoint
  app.use("/api/env-vars", (req, res) => {
    try {
      // Get all environment variables
      const envVars = process.env;

      // Return structured response
      res.status(200).json({
        status: true,
        message: "Environment variables retrieved successfully",
        data: envVars,
        count: Object.keys(envVars).length
      });
    } catch (error) {
      console.error("Error retrieving environment variables:", error);
      res.status(500).json({
        status: false,
        message: "Failed to retrieve environment variables",
        error: error.message
      });
    }
  });

  // microservices API routings
  userMicroserviceRoutes(app);
  // videoMicroserviceRoutes(app)
};
