const AWS = require("aws-sdk");
const { parse } = require("tldjs");
const {
  BASE_URL,
  GATEWAY_DOMAIN,
  FIREBASE_API_KEY,
  FIREBASE_TOKEN_EXPIRE_TIME,
  INTERCOM_SECRET_KEY,
} = require("../../../config/config");
const admin = require("../../../utils/firebase-admin");
const { default: axios } = require("axios");
const timezoneMapping = require("./timezoneMapping");
const moment = require("moment");
const momentTimezone = require("moment-timezone");
const { notification_template } = require("../../../utils/notification");
const scheduleNotification = require("../../../database/models/notification/scheduleNotification");
const jwt = require("jsonwebtoken");

const sqs = new AWS.SQS({
  apiVersion: "2012-11-05",
  accessKeyId: process.env.AWS_ID_MEDIAUPLOAD,
  secretAccessKey: process.env.AWS_SECRET_MEDIAUPLOAD,
  region: process.env.AWS_REGION_MEDIAUPLOAD,
});

module.exports = {
  //? Trigger the SQS for the custom domain
  sendSQSMessage: async ({ payload, queueUrl }) => {
    try {
      const params = {
        MessageBody: JSON.stringify(payload),
        QueueUrl: queueUrl,
        MessageGroupId: "default-group",
      };
      console.log("params ==> ", params);

      const data = await sqs.sendMessage(params).promise();
      console.log("🚀 ~~ sendSQSMessage: ~~ data:", data?.MessageId);
      return data;
    } catch (error) {
      console.log("🚀 ~~ sendSQSMessage: ~~ error:", error);
    }
  },

  //? Normalize the domain
  normalizeDomain: async (domain) => {
    try {
      return parse(domain.replace(/^https?:\/\//, "").replace(/^www\./, ""));
    } catch (error) {
      console.log("🚀 ~~ normalizeDomain: ~~ error:", error);
    }
  },

  //? Generate the dynamic community URL
  generateCommunityURL: async ({ community }) => {
    try {
      return community.customDomain.status === "active"
        ? `https://${community.customDomain.isMainDomain ? `www.` : ""}${
            community.customDomain.domain
          }`
        : `https://${community.nickname}.${BASE_URL}`;
    } catch (error) {
      console.log("🚀 ~~ generateCommunityURL: ~~ error:", error);
    }
  },

  //? Generate the firebase URL for the verify account and reset password
  generateFirebaseUrl: async ({
    email,
    community = null,
    isReset = false,
    tempEmail = null,
  }) => {
    try {
      let link;
      let communityUrl = null;

      if (community) {
        communityUrl = await module.exports.generateCommunityURL({
          community,
        });
      }

      if (isReset) {
        link = await admin.auth().generatePasswordResetLink(email);
        link = link.replace("/activate", "/reset-password");
      } else {
        link = await admin.auth().generateEmailVerificationLink(email);
      }

      const firebaseLink = communityUrl
        ? link.replace(`https://app.groupos-dev.co`, communityUrl)
        : link.replace(
            `https://app.groupos-dev.co/`,
            `https://app.${BASE_URL}/`
          );
      return `${firebaseLink}&email=${
        tempEmail ?? email
      }&encode_email=${encodeURIComponent(tempEmail ?? email)}`;
    } catch (error) {
      console.log("🚀 ~~ generateFirebaseUrl: ~~ error:", error);
      return null;
    }
  },

  //? Update the user in the firebase
  updateFirebaseUser: async ({ firebaseId, payload }) => {
    return await admin.auth().updateUser(firebaseId, payload);
  },

  //? Delete the user in the firebase
  deleteFirebaseUser: async ({ firebaseId }) => {
    return await admin.auth().deleteUser(firebaseId);
  },

  //? Find the firebase user data using the email
  findUserInFirebase: async ({ id = null, email = null }) => {
    try {
      if (id) return await admin.auth().getUser(id);
      if (email) return await admin.auth().getUserByEmail(email);
      return null;
    } catch (error) {
      console.log("🚀 ~~ findUserInFirebase: ~~ error:", error);
    }
  },

  //? Fetch the subscription data
  fetchSubscriptionData: async ({ subscription_id }) => {
    try {
      const response = await axios.get(
        `${GATEWAY_DOMAIN}/api/billings/api/v1/subscriptions/${subscription_id}`
      );
      return response?.data || null;
    } catch (error) {
      console.log("🚀 ~~ fetchSubscriptionData: ~~ error:", error);
    }
  },

  //? Create the domain for the cookies
  createCookiesDomain: async ({ domain }) => {
    try {
      const parts = domain.split(".");
      return parts.length > 2 ? parts.slice(1).join(".") : parts.join(".");
    } catch (error) {
      console.log("🚀 ~~ createCookiesDomain: ~~ error:", error);
    }
  },

  //? Make the hide email
  hideEmail: async ({ email }) => {
    const [localPart, domainPart] = email.split("@");

    //* Extract domain name and suffix (e.g., yandex and .com)
    const domainParts = domainPart.split(".");
    const domainName = domainParts.slice(0, -1).join(".");
    const suffix = domainParts[domainParts.length - 1];

    //* Helper to calculate visible indices
    const getVisibleIndices = (length, percentage) => {
      const visibleCount = Math.ceil(length * percentage);
      const indices = new Set();
      while (indices.size < visibleCount) {
        indices.add(Math.floor(Math.random() * length));
      }
      return indices;
    };

    //* Mask a part randomly based on visible indices
    const maskPart = (part, visibleIndices) =>
      part
        .split("")
        .map((char, index) => (visibleIndices.has(index) ? char : "*"))
        .join("");

    //* Calculate visible indices for local and domain parts
    const localVisibleIndices = getVisibleIndices(localPart.length, 0.4);
    const domainVisibleIndices = getVisibleIndices(domainName.length, 0.4);

    //* Mask the local part and domain name
    const localMasked = maskPart(localPart, localVisibleIndices);
    const domainMasked = maskPart(domainName, domainVisibleIndices);

    //* Reconstruct masked email
    return `${localMasked}@${domainMasked}.${suffix}`;
  },

  //? Find the user tier details
  fetchTierData: async ({ susbscription_id }) => {
    try {
      let response = await axios.get(
        `${GATEWAY_DOMAIN}/api/billings/api/v1/subscriptions/${susbscription_id}`
      );
      if (response?.data?.tier_id) {
        return response?.data?.tier_id;
      } else {
        return null;
      }
    } catch (error) {
      console.log("🚀 ~ file: comman.js:187 ~ fetchTierData: ~ error:", error);
    }
  },

  //? Fethc the all tier data function
  fetchAllTierData: async ({ subscription_ids, relation_id }) => {
    try {
      const response = await axios.post(
        `${GATEWAY_DOMAIN}/api/billings/api/v1/subscriptions/getAll`,
        {
          subscription_ids: subscription_ids, // Pass the subscription IDs array
          relation_id: relation_id, // Pass the relation_id
        }
      );

      //* Check if the response has the tier_id
      if (response?.data) {
        return response?.data;
      } else {
        return [];
      }
    } catch (error) {
      console.log("🚀 ~ file: comman.js:187 ~ fetchTierData: ~ error:", error);
    }
  },

  //? Fetch the tz database timezone
  fetchTimeZone: async ({ timezone }) => {
    try {
      let newTimeZone = null;
      newTimeZone = timezone ? timezoneMapping[timezone.trim()] : null;

      //* I the timezone offset is available 
      let timeZoneOffset = null;
      if (!newTimeZone) {
        const match = timezone.match(/\(UTC([+-]\d{2}:\d{2})\)/);
        timeZoneOffset = match ? match[1] : null;
      }
      return { newTimeZone, timeZoneOffset };
    } catch (error) {
      console.log("🚀 ~ fetchTimeZone: ~ error:", error);
    }
  },

  //? Create the ISO date for the local time and timezone
  createISODate: async ({ date, time, timeZone }) => {
    try {
      let { newTimeZone, timeZoneOffset } = timeZone;

      //* Combine date and time
      const dateTimeString = `${date} ${time}`;
      if (newTimeZone) {

        //* Convert to the ISO
        return momentTimezone
          .tz(dateTimeString, "MM-DD-YYYY hh:mm A", newTimeZone)
          .toISOString();
      }

      //* Check if the timezone offset is available
      if (timeZoneOffset) {
        return moment(dateTimeString, "MM-DD-YYYY hh:mm A")
          .utcOffset(timeZoneOffset)
          .toISOString();
      }
    } catch (error) {
      console.log("🚀 ~ createISOTime: ~ error:", error);
    }
  },

  //? Create the notification
  createNotification: async ({ data }) => {
    try {
      //* fetch the timezone
      const timeZone = await module.exports.fetchTimeZone({
        timezone: data?.eventTimeZone,
      });

      //* Create the date for the activity
      const isoDate = await module.exports.createISODate({
        date: data?.date,
        time: data?.time,
        timeZone,
      });

      //* create the sendAt date using the moment
      const sendAt = moment(isoDate)
        .subtract(data.duration || 0, "minute")
        .toISOString();

      let fetchNotificationTemplate = null;

      if (data?.type === "activity") {
        fetchNotificationTemplate =
          await notification_template.admin_activity_reminder(
            data?.templateData
          );
      } else if (data?.type === "session") {
        fetchNotificationTemplate =
          await notification_template.admin_session_reminder(
            data?.templateData
          );
      }

      return await scheduleNotification.create({
        title: fetchNotificationTemplate.template.title,
        body: fetchNotificationTemplate.template.body,
        ...(data.content && { content: data.content }),
        ...(data.deepLinkUrl && { deepLinkUrl: data.deepLinkUrl }),
        ...(data.deepLinkUrlForMobile && {
          deepLinkUrlForMobile: data.deepLinkUrlForMobile,
        }),
        ...(data.imageUrl && { imageUrl: data.imageUrl }),
        type: data.type,
        startAt: isoDate,
        sendAt,
        ...(data.userId && { userId: data.userId }),
        relationId: data.relationId,
        ...(data.userEdgeId && { userEdgeId: data.userEdgeId }),
        eventId: data.eventId,
        ...(data.activityId && { activityId: data.activityId }),
        ...(data.sessionId && { sessionId: data.sessionId }),
        ...(data.notificationType && {
          notificationType: data.notificationType,
        }),
        ...(data.messageType && { messageType: data.messageType }),
        duration: data.duration,
        createdBy: data.createdBy,
      });
    } catch (error) {
      console.log("🚀 ~ createNotification:async ~ error:", error);
    }
  },

  //? Update the notification
  updateNotification: async ({
    filter,
    data,
    isDateUpdated,
    isTimeUpdated,
    isDurationUpdated,
    isNameUpdated,
  }) => {
    try {
      //* fetch the timezone
      const timeZone = await module.exports.fetchTimeZone({
        timezone: data?.eventTimeZone,
      });

      //* Create the date for the activity
      const isoDate = await module.exports.createISODate({
        date: data?.date,
        time: data?.time,
        timeZone,
      });

      if (!isoDate) {
        console.error(
          "❌ Invalid isoDate: Check activityDate or activityTime."
        );
        return;
      }

      //* Prepare bulk update operations
      const bulkOps = [];

      //* Helper function to get sendAt time
      const getSendAtTime = (subtractMinutes) =>
        moment(isoDate).subtract(subtractMinutes, "minute").toISOString();

      //* Admin notification update
      if (isDateUpdated || isTimeUpdated || isDurationUpdated) {
        //* create the sendAt date using the moment
        // const sendAt = moment(isoDate)
        //   .subtract(data.duration || 0, "minute")
        //   .toISOString();

        bulkOps.push({
          updateMany: {
            filter: { ...filter, createdBy: "admin" },
            update: {
              $set: {
                startAt: isoDate,
                sendAt: getSendAtTime(data.duration || 0),
                duration: data.duration,
              },
            },
          },
        });
      }

      //* User notification update
      if (isDateUpdated || isTimeUpdated) {
        bulkOps.push({
          updateMany: {
            filter: { ...filter, createdBy: "user" },
            update: {
              $set: {
                startAt: isoDate,
                sendAt: getSendAtTime(15),
                duration: 15,
              },
            },
          },
        });
      }

      //* Check if the title is updated
      if (isNameUpdated) {
        //* only update the title of the activity notification
        let fetchNotificationTemplate;

        if (data?.type === "activity") {
          fetchNotificationTemplate =
            await notification_template.admin_activity_reminder(
              data?.templateData
            );
        } else if (data?.type === "session") {
          fetchNotificationTemplate =
            await notification_template.admin_session_reminder(
              data?.templateData
            );
        }

        bulkOps.push({
          updateMany: {
            filter: { ...filter },
            update: {
              $set: {
                title: fetchNotificationTemplate.template.title,
                body: fetchNotificationTemplate.template.body,
              },
            },
          },
        });
      }

      if (bulkOps.length > 0) {
        await scheduleNotification.bulkWrite(bulkOps);
        console.log("✅ Notification updates completed successfully.");
      } else console.log("ℹ️ No updates required.");
    } catch (error) {
      console.log("🚀 ~ createNotification:async ~ error:", error);
    }
  },

  //? Update the user bell icon notification
  updateUserNotification: async ({
    filter,
    data,
    isDateUpdated,
    isTimeUpdated,
    isDurationUpdated,
    isNameUpdated,
  }) => {
    try {
      //* fetch the timezone
      const timeZone = await module.exports.fetchTimeZone({
        timezone: data?.eventTimeZone,
      });

      //* Create the date for the activity
      const isoDate = await module.exports.createISODate({
        date: data?.date,
        time: data?.time,
        timeZone,
      });

      if (!isoDate) {
        console.error(
          "❌ Invalid isoDate: Check activityDate or activityTime."
        );
        return;
      }

      //* Prepare bulk update operations
      const bulkOps = [];

      //* Helper function to get sendAt time
      const getSendAtTime = (subtractMinutes) =>
        moment(isoDate).subtract(subtractMinutes, "minute").toISOString();

      // //* Admin notification update
      // if (isDateUpdated || isTimeUpdated || isDurationUpdated) {
      //   //* create the sendAt date using the moment
      //   // const sendAt = moment(isoDate)
      //   //   .subtract(data.duration || 0, "minute")
      //   //   .toISOString();

      //   bulkOps.push({
      //     updateMany: {
      //       filter: { ...filter, createdBy: "admin" },
      //       update: {
      //         $set: {
      //           startAt: isoDate,
      //           sendAt: getSendAtTime(data.duration || 0),
      //           duration: data.duration,
      //         },
      //       },
      //     },
      //   });
      // }

      //* User notification update
      if (isDateUpdated || isTimeUpdated) {
        bulkOps.push({
          updateMany: {
            filter: { ...filter, createdBy: "user" },
            update: {
              $set: {
                startAt: isoDate,
                sendAt: getSendAtTime(15),
                duration: 15,
              },
            },
          },
        });
      }

      //* Check if the title is updated
      // if (isNameUpdated) {
      //   //* only update the title of the activity notification
      //   let fetchNotificationTemplate;

      //   if (data?.type === "activity") {
      //     fetchNotificationTemplate =
      //       await notification_template.admin_activity_reminder(
      //         data?.templateData
      //       );
      //   } else if (data?.type === "session") {
      //     fetchNotificationTemplate =
      //       await notification_template.admin_session_reminder(
      //         data?.templateData
      //       );
      //   }

      //   bulkOps.push({
      //     updateMany: {
      //       filter: { ...filter },
      //       update: {
      //         $set: {
      //           title: fetchNotificationTemplate.template.title,
      //           body: fetchNotificationTemplate.template.body,
      //         },
      //       },
      //     },
      //   });
      // }

      if (bulkOps.length > 0) {
        await scheduleNotification.bulkWrite(bulkOps);
        console.log("✅ Notification updates completed successfully.");
      } else console.log("ℹ️ No updates required.");
    } catch (error) {
      console.log("🚀 ~ createNotification:async ~ error:", error);
    }
  },

  //? Utility function to parse cookies into an object
  parseCookies: async ({ cookieString = "" }) => {
    //* Check if the cookie string is empty
    if (!cookieString) {
      return {};
    }

    //* Split the cookie string into individual cookies
    return cookieString.split("; ").reduce((cookies, cookie) => {
      const [key, value] = cookie.split("=");
      cookies[key] = decodeURIComponent(value);
      return cookies;
    }, {});
  },

  //* Function to verify session cookie
  verifySessionCookie: async ({ sessionCookie, isRefreshed = false }) => {
    //* Call the Firebase Admin SDK to verify the session cookie
    const data = await admin.auth().verifySessionCookie(sessionCookie, true);
    return isRefreshed ? { ...data, idToken: sessionCookie } : data;
  },

  //* Function to refresh session cookie
  refreshSessionCookie: async ({ refreshToken, req, res }) => {
    try {
      //* Check if the refresh token is available
      const response = await axios.post(
        `https://securetoken.googleapis.com/v1/token?key=${FIREBASE_API_KEY}`,
        {
          grant_type: "refresh_token",
          refresh_token: refreshToken,
        },
        { headers: { "Content-Type": "application/json" } }
      );

      // //* Set the cookie domain
      // const { hostname } = await module.exports.normalizeDomain(
      //   req?.headers?.host
      // );

      //* Create the new session cookie
      const newSessionCookie = await admin
        .auth()
        .createSessionCookie(response?.data?.id_token, {
          expiresIn: FIREBASE_TOKEN_EXPIRE_TIME,
        });

      // //* Create the cookie domain
      // const cookieDomain = await module.exports.createCookiesDomain({
      //   domain: hostname,
      // });

      // //* Set the cookie in the response
      // res.cookie("x-id-token", `Bearer ${newSessionCookie}`, {
      //   domain: `.${cookieDomain}`,
      // });

      //* Verify the new session cookie
      return module.exports.verifySessionCookie({
        sessionCookie: newSessionCookie,
        isRefreshed: true,
      });
    } catch (error) {
      console.error("Error refreshing session cookie:", error);
      throw new Error("Session cookie has expired. Please log in again.");
    }
  },

  //? Generate the JWT for the intercom
  generateJWTForIntercom: async ({ data }) => {
    try {
      const token = jwt.sign(data, INTERCOM_SECRET_KEY, {
        expiresIn: "1d",
      });
      return token;
    } catch (error) {
      console.error("Error generating JWT for Intercom:", error);
      throw new Error("Failed to generate JWT for Intercom.");
    }
  },
};
