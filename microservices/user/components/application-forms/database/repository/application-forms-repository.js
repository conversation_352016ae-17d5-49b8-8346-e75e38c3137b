const { application_forms } = require("../models");

class ApplicationFormsRepository {
  /**
   *
   * @description object creation
   */
  createApplicationForm = async ({ applicationFormData }) => {
    const applicationFormObj = new application_forms(applicationFormData);
    return await applicationFormObj.save();
  };

  /**
   *
   * @description When api have pagination
   */
  getCount = async ({ filter }) => {
    return await application_forms.countDocuments(filter);
  };
  findAllApplicationForms = async ({
    filter,
    skip = 0,
    limit = 100,
    expand = false,
  }) => {
    if (expand) {
      const populateObj = {
        path: "fields",
        select: "_id label type",
      };
      return await application_forms
        .find(filter)
        .populate(populateObj)
        .sort({ name: 1 }) 
        // .collation({ locale: 'en', strength: 2 })
        .skip(skip)
        .limit(limit);
    } else {
      return await application_forms.find(filter).skip(skip).limit(limit);
    }
  };
  findAllApplicationFormsV2 = async ({
    filter,
    skip = 0,
    limit = 100,
  }) => {
    let aggregation = [
      {
        $match: {
          ...filter
        }
      },
      {
        $lookup: {
          from: "application_form_fields",
          localField: "fields",
          foreignField: "_id",
          as: "fields",
          pipeline: [
            {
              $project: {
                _id: 1,
                label: 1,
                type: 1
              }
            }
          ]
        }
      },
      {
        $lookup: {
          from: "event_tickets",
          localField: "_id",
          foreignField: "applicationForm",
          as: "event_tickets_data",
          pipeline: [
            {
              $match: {
                isDelete: false
              }
            },
            {
              $lookup: {
                from: "events",
                localField: "eventId",
                foreignField: "_id",
                as: "event_data",
                pipeline: [
                  {
                    $match: {
                      isDelete: false
                    }
                  },
                  {
                    $project: {
                      title: 1,
                      createdAt: 1,
                      updatedAt: 1
                    }
                  }
                ]
              }
            },
            {
              $unwind: {
                path: "$event_data",
                preserveNullAndEmptyArrays: false
              }
            },
            {
              $lookup: {
                from: "ticket_submissions",
                localField: "_id",
                foreignField: "ticketId",
                as: "ticket_submissions_data",
                pipeline: [
                  {
                    $lookup: {
                      from: "ticket_purchase_v2",
                      localField: "ticketPurchaseId",
                      foreignField: "_id",
                      as: "ticket_purchase_data",
                      pipeline: [
                        {
                          $match: {
                            ticketOrderStatus: {
                              $in: [
                                "requires_payment_method",
                                "succeeded"
                              ]
                            },
                            isDelete: false
                          }
                        }
                      ]
                    }
                  },
                  {
                    $unwind: {
                      path: "$ticket_purchase_data",
                      preserveNullAndEmptyArrays: false
                    }
                  },
                  {
                    $count: "submission_count"
                  }
                ]
              }
            },
            {
              $unwind: {
                path: "$ticket_submissions_data",
                preserveNullAndEmptyArrays: true
              }
            },
            {
              $project: {
                name: 1,
                type: 1,
                event_name: "$event_data.title",
                event_data: 1,
                submission_count: {
                  $ifNull: [
                    "$ticket_submissions_data.submission_count",
                    // The field to check
                    0
                  ]
                },
                createdAt: 1,
                updatedAt: 1
              }
            },
            {
              $group: {
                _id: "$event_data._id",
                event_name: { $first: "$event_data.title" },
                data: { $push: "$$ROOT" },
                total_submission_count: {
                  $sum: { $ifNull: ["$submission_count", 0] }
                }
              }
            }
          ]
        }
      },
      {
        $addFields: {
          total_submission_count: {
            $sum: {
              $map: {
                input: "$event_tickets_data",
                as: "ticket",
                in: {
                  $sum: {
                    $map: {
                      input: "$$ticket.data",
                      as: "ticketData",
                      in: {
                        $ifNull: [
                          "$$ticketData.submission_count",
                          0
                        ]
                      }
                    }
                  }
                }
              }
            }
          }
        }
      },
      {
        $project: {
          name: 1,
          fields: 1,
          total_submission_count: 1,
          event_tickets_data: 1,
          createdAt: 1,
          updatedAt: 1
        }
      },
      {
        $skip: skip
      },
      {
        $limit: limit
      }
    ];

    return await application_forms.aggregate(aggregation);
  };

  /**
   *
   * @description for accessing object in same service
   */
  get_ = async ({ _Id, expand = false }) => {
    if (expand) {
      const populateObj = {};
      return await application_forms.findById(_Id).populate(populateObj);
    } else {
      return await application_forms.findById(_Id);
    }
  };
  updateApplicationForm = async ({
    applicationFormId,
    applicationFormData,
    getUpdatedData = true,
  }) => {
    if (getUpdatedData) {
      return await application_forms.findByIdAndUpdate(
        applicationFormId,
        applicationFormData,
        {
          new: true,
        }
      );
    } else {
      return await application_forms.findByIdAndUpdate(
        applicationFormId,
        applicationFormData
      );
    }
  };
  deleteApplicationForm = async ({ applicationFormId }) => {
    return await application_forms.findByIdAndDelete(applicationFormId);
  };

  /**
   *
   * @description for accessing object in other service / same service
   */
  findApplicationForm = async ({ filter, expand = false }) => {
    if (expand) {
      const populateObj = {
        path: "fields",
      };
      return await application_forms.findOne(filter).populate(populateObj);
    } else {
      return await application_forms.findOne(filter);
    }
  };
  findAndUpdate_ = async ({ filter, _Data, getUpdatedData = true }) => {
    if (getUpdatedData) {
      return await application_forms.findOneAndUpdate(filter, _Data, {
        new: true,
      });
    } else {
      return await application_forms.findOneAndUpdate(filter, _Data);
    }
  };
  findAndDelete_ = async ({ filter }) => {
    return await application_forms.findOneAndDelete(filter);
  };
}

module.exports = ApplicationFormsRepository;
