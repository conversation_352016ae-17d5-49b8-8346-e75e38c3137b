const { userroles } = require("../models");

class UserRolesRepository {
  getCount = async ({ filter }) => {
    return await userroles.countDocuments(filter);
  };
  generateRole = async ({ roleData }) => {
    return new userroles(roleData);
  };
  saveGeneratedRole = async ({ generatedRole }) => {
    return await generatedRole.save();
  };
  createRole = async ({ roleData }) => {
    const role = new userroles(roleData);
    return await role.save();
  };
  getRole = async ({ role_id, expand = false }) => {
    if (expand) {
      const populateObj = {
        path: "admin_scope_id",
        populate: {
          path: "admin_modules",
        },
      };
      return await userroles.findById(role_id).populate(populateObj);
    } else {
      return await userroles.findById(role_id);
    }
  };
  findRole = async ({ filter, expand = false }) => {
    if (expand) {
      const populateObj = {
        path: "admin_scope_id",
        populate: {
          path: "admin_modules",
        },
      };
      return await userroles.findOne(filter).populate(populateObj);
    } else {
      return await userroles.findOne(filter);
    }
  };
  findAllRoles = async ({ filter, skip = 0, limit = 100, expand = false }) => {
    if (expand) {
      const populateObj = {
        path: "admin_scope_id",
        populate: {
          path: "admin_modules",
        },
      };
      
      return await userroles
        .find(filter)
        .populate(populateObj)
        .sort({ role_name: 1 }) 
        // .collation({ locale: 'en', strength: 2 })
        .skip(skip)
        .limit(limit);
    } else {
      return await userroles.find(filter).skip(skip).limit(limit);
    }
  };
  updateRole = async ({ role_id, roleData, getUpdatedData }) => {
    if (getUpdatedData) {
      return await userroles.findByIdAndUpdate(role_id, roleData, {
        new: true,
      });
    } else {
      return await userroles.findByIdAndUpdate(role_id, roleData);
    }
  };
  findAndUpdateRole = async ({ filter, roleData, getUpdatedData }) => {
    if (getUpdatedData) {
      return await userroles.findOneAndUpdate(filter, roleData, { new: true });
    } else {
      return await userroles.findOneAndUpdate(filter, roleData);
    }
  };
  deleteRole = async ({ role_id }) => {
    return await userroles.findByIdAndDelete(role_id);
  };
  findAndDeleteRole = async ({ filter }) => {
    return await userroles.findOneAndDelete(filter);
  };
}

module.exports = UserRolesRepository;
