const { user_edges } = require("../models");
const airtable_syncs = require("../../../../../../database/models/airTableSync");
const axios = require("axios");

class UserEdgesRepository {
  getCount = async ({ filter, search, isBlocked=false }) => {
    const pipeline = [
      { $match: filter },
      {
        $lookup: {
          from: "airtable-syncs",
          localField: "user_id",
          foreignField: "_id",
          pipeline: [
            {
              $match: {
                isDelete: false,
                ...(isBlocked ? { blocked: true } : { $or: [{ blocked: false }, { blocked: { $exists: false } }] })
              },
            },
          ],
          as: "user",
        },
      },
      {
        $match: {
            user: {
              $ne: []
          },
        }
      },
      {
        $lookup: {
          from: "user_roles",
          localField: "role_id",
          foreignField: "_id",
          as: "role",
        },
      },
      {
        $lookup: {
          from: "platform",
          localField: "relation_id",
          foreignField: "_id",
          as: "platformRelation",
        },
      },
      {
        $lookup: {
          from: "communities",
          localField: "relation_id",
          foreignField: "_id",
          as: "communityRelation",
        },
      },
      {
        $addFields: {
          relation_id: {
            $cond: {
              if: "$platform",
              then: { $arrayElemAt: ["$platformRelation", 0] },
              else: { $arrayElemAt: ["$communityRelation", 0] },
            },
          },
        },
      },
      { $unwind: { path: "$user", preserveNullAndEmptyArrays: true } },
      { $unwind: { path: "$role", preserveNullAndEmptyArrays: true } },
      ...(search && search != ""
        ? [
            {
              $match: {
                $or: [
                  {
                    "user.first_name": {
                      $regex: ".*" + search + ".*",
                      $options: "i",
                    },
                  },
                  {
                    "user.last_name": {
                      $regex: ".*" + search + ".*",
                      $options: "i",
                    },
                  },
                  {
                    "user.email": {
                      $regex: ".*" + search + ".*",
                      $options: "i",
                    },
                  },
                  {
                    $expr: {
                      $regexMatch: {
                        input: {
                          $concat: ["$user.first_name", " ", "$user.last_name"],
                        },
                        regex: ".*" + search.split(" ").join("\\s*") + ".*", // Replace spaces in search with \\s*
                        options: "i",
                      },
                    },
                  },
                  {
                    "user.Preferred Email": {
                      $regex: ".*" + search + ".*",
                      $options: "i",
                    },
                  },
                  {
                    "user.display_name": {
                      $regex: ".*" + search + ".*",
                      $options: "i",
                    },
                  },
                  {
                    "role.role_name": {
                      $regex: ".*" + search + ".*",
                      $options: "i",
                    },
                  },
                  {
                    "relation_id.name": {
                      $regex: ".*" + search + ".*",
                      $options: "i",
                    },
                  },
                  {
                    "relation_id.nickname": {
                      $regex: ".*" + search + ".*",
                      $options: "i",
                    },
                  },
                ],
              },
            },
          ]
        : []),
      {
        $project: {
          _id: 0,
          role: "$role.role_name",
          user_id: {
            _id: "$user._id",
            email: "$user.email",
            name: "$user.name",
            Access: "$user.Access",
            activation_code: "$user.activation_code",
            Chapter_Affiliation: "$user.Chapter Affiliation",
            Events_Attended: "$user.Events Attended",
            Preferred_Email: "$user.Preferred Email",
            passcode: "$user.passcode",
            facebookLinkedinId: "$user.facebookLinkedinId",
            auth0Id: "$user.auth0Id",
            socialauth0id: "$user.socialauth0id",
            thumb_profileImg: "$user.thumb_profileImg",
            profileCover: "$user.profileCover",
            active: "$user.active",
            blocked: "$user.blocked",
            verified: "$user.verified",
            following: "$user.following",
            followers: "$user.followers",
            savePosts: "$user.savePosts",
            saveVideos: "$user.saveVideos",
            token: "$user.token",
            provider: "$user.provider",
            isSocial: "$user.isSocial",
            accessible_groups: "$user.accessible_groups",
            last_login: "$user.last_login",
            last_activity_log: "$user.last_activity_log",
            isDelete: "$user.isDelete",
            register_status: "$user.register_status",
            personalDetail_status: "$user.personalDetail_status",
            payment_status: "$user.payment_status",
            QA_status: "$user.QA_status",
            user_role: "$user.user_role",
            forgot_ticket: "$user.forgot_ticket",
            blocked_chat: "$user.blocked_chat",
            blocked_by_who_chat: "$user.blocked_by_who_chat",
            deleted_group_of_user: "$user.deleted_group_of_user",
            star_chat: "$user.star_chat",
            latitude: "$user.latitude",
            longitude: "$user.longitude",
            migrate_user_status: "$user.migrate_user_status",
            muteNotification: "$user.muteNotification",
            deactivate_account_request: "$user.deactivate_account_request",
            deviceToken: "$user.deviceToken",
            webDeviceToken: "$user.webDeviceToken",
            speakerIcon: "$user.speakerIcon",
            guestIcon: "$user.guestIcon",
            partnerIcon: "$user.partnerIcon",
            Upcoming_Events: "$user.Upcoming Events",
            Upcoming_Events_Registered: "$user.Upcoming Events Registered",
            isCollaborator: "$user.isCollaborator",
            status: "$user.status",
            user_edges: "$user.user_edges",
            no_of_team_mate: "$user.no_of_team_mate",
            clear_chat_data: "$user.clear_chat_data",
            video_history_data: "$user.video_history_data",
            notificationFor: "$user.notificationFor",
            joinDate: "$user.joinDate",
            createdAt: "$user.createdAt",
            updatedAt: "$user.updatedAt",
            __v: "$user.__v",
            refresh_token: "$user.refresh_token",
            email: "$user.email",
            otherdetail: "$user.otherdetail",
            AT_Database_Status: "$user.AT Database Status",
            display_name: "$user.display_name",
            attendeeDetail: "$user.attendeeDetail",
            firebaseId: "$user.firebaseId",
            profileImg: "$user.profileImg",
            first_name: "$user.first_name",
            last_name: "$user.last_name",
            Denver_Check_in_Form: "$user.Denver Check in Form",
          },
          subscription_id: "$subscription_id",
          scopes: "$scopes",
          relation_id: "$relation_id",
        },
      },
    ];
    const getAllData = await user_edges.aggregate(pipeline);
    let count = getAllData.length;
    return count;
  };
  createUserEdge = async ({ userEdgeData }) => {
    const userEdge = new user_edges(userEdgeData);
    return await userEdge.save();
  };
  getUserEdge = async ({ userEdge_id, expand = false }) => {
    if (expand) {
      const relation = {
        path: "relation_id",
        select:
          "_id name nickname owner_id logo banner avtar subscription_reminder_renewal_config_id customDomain.domain customDomain.status customDomain.isMainDomain customSMTP",
      };
      const role = {
        path: "role_id",
        select: "_id role_name",
        populate: {
          path: "admin_scope_id",
          select: "admin_modules",
          populate: {
            path: "admin_modules",
          },
        },
      };
      return await user_edges
        .findById(userEdge_id)
        .populate([relation, role])
        .lean();
    } else {
      return await user_edges.findById(userEdge_id);
    }
  };
  updateUserEdge = async ({
    userEdgeId,
    userEdgeData,
    getUpdatedData = true,
  }) => {
    if (getUpdatedData) {
      return await user_edges.findByIdAndUpdate(userEdgeId, userEdgeData, {
        new: true,
      });
    } else {
      return await user_edges.findByIdAndUpdate(userEdgeId, userEdgeData);
    }
  };
  findUserEdge = async ({ filter, expand = false }) => {
    if (expand) {
      const user = {
        path: "user_id",
      };
      const relation = {
        path: "relation_id",
        select:
          "_id name nickname owner_id logo banner subscription_reminder_renewal_config_id",
      };
      const role = {
        path: "role_id",
        select: "_id role_name",
        populate: {
          path: "admin_scope_id",
          select: "admin_modules",
          populate: {
            path: "admin_modules",
          },
        },
      };
      return await user_edges.findOne(filter).populate([user, relation, role]);
    } else {
      return await user_edges.findOne(filter);
    }
  };
  findUserEdges = async ({ filter, expand = false }) => {
    if (expand) {
      const usual = {
        path: "role_id user_id",
      };
      const relation = {
        path: "relation_id",
        select:
          "_id name nickname owner_id logo banner avtar groupos_join_date migration_date",
        match: { isDelete: false },
      };
      return await user_edges.find(filter).populate([usual, relation]);
    } else {
      return await user_edges.find(filter);
    }
  };
  findAllEdges = async ({ filter, skip = 0, limit = 100, expand = false }) => {
    if (expand) {
      const populateObj = {
        path: "role_id user_id relation_id",
      };
      return await user_edges
        .find(filter)
        .populate(populateObj)
        .skip(skip)
        .limit(limit);
    } else {
      return await user_edges.find(filter).skip(skip).limit(limit);
    }
  };

  findAllEdgesV2 = async ({
    filter,
    skip = 0,
    limit = 100,
    expand = false,
    search,
    isBlocked = false,
    isMember = false,
  }) => {
    limit = Number(limit);

    //* Add the sorting logic for the community members 
    const sortStage = isMember
      ? { $sort: { migration_date: -1 } }
      : { $sort: { "user_id.first_name_lower": 1 } }
    if (expand) {
      const pipeline = [
        { $match: filter },
        {
          $lookup: {
            from: "airtable-syncs",
            localField: "user_id",
            foreignField: "_id",
            pipeline: [
              {
                $match: {
                  isDelete: false,
                  ...(isBlocked ? { blocked: true } : { $or: [{ blocked: false }, { blocked: { $exists: false } }] })
                },
              },
            ],
            as: "user",
          },
        },
        {
          $match: {
            user: {
              $ne: [],
            },
          },
        },
        {
          $lookup: {
            from: "user_roles",
            localField: "role_id",
            foreignField: "_id",
            as: "role_id",
          },
        },
        {
          $lookup: {
            from: "platform",
            localField: "relation_id",
            foreignField: "_id",
            as: "platformRelation",
          },
        },
        {
          $lookup: {
            from: "communities",
            localField: "relation_id",
            foreignField: "_id",
            as: "communityRelation",
          },
        },
        {
          $addFields: {
            relation_id: {
              $cond: {
                if: "$platform",
                then: { $arrayElemAt: ["$platformRelation", 0] },
                else: { $arrayElemAt: ["$communityRelation", 0] },
              },
            },
          },
        },
        { $unwind: { path: "$user", preserveNullAndEmptyArrays: true } },
        { $unwind: { path: "$role_id", preserveNullAndEmptyArrays: true } },
        ...(search && search != ""
          ? [
              {
                $match: {
                  $or: [
                    {
                      "user.first_name": {
                        $regex: ".*" + search + ".*",
                        $options: "i",
                      },
                    },
                    {
                      "user.Preferred Email": {
                        $regex: ".*" + search + ".*",
                        $options: "i",
                      },
                    },
                    {
                      "user.last_name": {
                        $regex: ".*" + search + ".*",
                        $options: "i",
                      },
                    },
                    {
                      $expr: {
                        $regexMatch: {
                          input: {
                            $concat: [
                              "$user.first_name",
                              " ",
                              "$user.last_name",
                            ],
                          },
                          regex: ".*" + search.split(" ").join("\\s*") + ".*", // Replace spaces in search with \\s*
                          options: "i",
                        },
                      },
                    },
                    {
                      "user.email": {
                        $regex: ".*" + search + ".*",
                        $options: "i",
                      },
                    },
                    {
                      "user.display_name": {
                        $regex: ".*" + search + ".*",
                        $options: "i",
                      },
                    },
                    {
                      "role.role_name": {
                        $regex: ".*" + search + ".*",
                        $options: "i",
                      },
                    },
                    {
                      "relation_id.name": {
                        $regex: ".*" + search + ".*",
                        $options: "i",
                      },
                    },
                    {
                      "relation_id.nickname": {
                        $regex: ".*" + search + ".*",
                        $options: "i",
                      },
                    },
                  ],
                },
              },
            ]
          : []),
          {
            $addFields: {
              "user_id.first_name_lower": { $toLower: "$user.first_name" }
            }
          },
        {
          $project: {
            _id: 0,
            role: "$role_id.role_name",
            user_id: {
              _id: "$user._id",
              email: "$user.email",
              name: "$user.name",
              Access: "$user.Access",
              activation_code: "$user.activation_code",
              Chapter_Affiliation: "$user.Chapter Affiliation",
              Events_Attended: "$user.Events Attended",
              Preferred_Email: "$user.Preferred Email",
              passcode: "$user.passcode",
              facebookLinkedinId: "$user.facebookLinkedinId",
              auth0Id: "$user.auth0Id",
              socialauth0id: "$user.socialauth0id",
              thumb_profileImg: "$user.thumb_profileImg",
              profileCover: "$user.profileCover",
              active: "$user.active",
              blocked: "$user.blocked",
              verified: "$user.verified",
              following: "$user.following",
              followers: "$user.followers",
              savePosts: "$user.savePosts",
              saveVideos: "$user.saveVideos",
              token: "$user.token",
              provider: "$user.provider",
              providers: "$user.providers",
              isSocial: "$user.isSocial",
              accessible_groups: "$user.accessible_groups",
              last_login: "$user.last_login",
              last_activity_log: "$user.last_activity_log",
              isDelete: "$user.isDelete",
              register_status: "$user.register_status",
              personalDetail_status: "$user.personalDetail_status",
              payment_status: "$user.payment_status",
              QA_status: "$user.QA_status",
              user_role: "$user.user_role",
              forgot_ticket: "$user.forgot_ticket",
              blocked_chat: "$user.blocked_chat",
              blocked_by_who_chat: "$user.blocked_by_who_chat",
              deleted_group_of_user: "$user.deleted_group_of_user",
              star_chat: "$user.star_chat",
              latitude: "$user.latitude",
              longitude: "$user.longitude",
              migrate_user_status: "$user.migrate_user_status",
              muteNotification: "$user.muteNotification",
              deactivate_account_request: "$user.deactivate_account_request",
              deviceToken: "$user.deviceToken",
              webDeviceToken: "$user.webDeviceToken",
              speakerIcon: "$user.speakerIcon",
              guestIcon: "$user.guestIcon",
              partnerIcon: "$user.partnerIcon",
              Upcoming_Events: "$user.Upcoming Events",
              Upcoming_Events_Registered: "$user.Upcoming Events Registered",
              isCollaborator: "$user.isCollaborator",
              status: "$user.status",
              user_edges: "$user.user_edges",
              no_of_team_mate: "$user.no_of_team_mate",
              clear_chat_data: "$user.clear_chat_data",
              video_history_data: "$user.video_history_data",
              notificationFor: "$user.notificationFor",
              joinDate: "$user.joinDate",
              groupos_join_date: "$user.groupos_join_date",
              migration_date: "$user.migration_date",
              createdAt: "$user.createdAt",
              updatedAt: "$user.updatedAt",
              __v: "$user.__v",
              refresh_token: "$user.refresh_token",
              email: "$user.email",
              otherdetail: "$user.otherdetail",
              AT_Database_Status: "$user.AT Database Status",
              display_name: "$user.display_name",
              attendeeDetail: "$user.attendeeDetail",
              userEvents: "$user.userEvents",
              firebaseId: "$user.firebaseId",
              profileImg: "$user.profileImg",
              first_name: "$user.first_name",
              last_name: "$user.last_name",
              Denver_Check_in_Form: "$user.Denver Check in Form",
              isBlocked: "$user.blocked",
            },
            subscription_id: "$subscription_id",
            scopes: "$scopes",
            relation_id: "$relation_id",
            "role_id._id": "$role_id._id",
            "role_id.name": "$role_id.role_name",
            isOnline: "$isOnline",
            groupos_join_date: 1,
            migration_date: 1,
          },
        },
        sortStage,
        { $skip: skip },
        { $limit: limit },
      ];
      return await user_edges.aggregate(pipeline);
    } else {
      return await userroles.find(filter).skip(skip).limit(limit).lean();
    }
  };

  findAllEdgesSuggestions = async ({ filter, expand = false, isBlocked = false }) => {
    if (expand) {
      const pipeline = [
        { $match: filter },
        {
          $lookup: {
            from: "airtable-syncs",
            localField: "user_id",
            foreignField: "_id",
            pipeline: [
              {
                $match: {
                  isDelete: false,
                  ...(isBlocked
                    ? { blocked: true }
                    : {
                        $or: [
                          { blocked: false },
                          { blocked: { $exists: false } },
                        ],
                      }),
                },
              },
            ],
            as: "user",
          },
        },
        {
          $match: {
            user: {
              $ne: [],
            },
          },
        },
        {
          $lookup: {
            from: "user_roles",
            localField: "role_id",
            foreignField: "_id",
            as: "role",
          },
        },
        {
          $lookup: {
            from: "platform",
            localField: "relation_id",
            foreignField: "_id",
            as: "platformRelation",
          },
        },
        {
          $lookup: {
            from: "communities",
            localField: "relation_id",
            foreignField: "_id",
            as: "communityRelation",
          },
        },
        {
          $addFields: {
            relation_id: {
              $cond: {
                if: "$platform",
                then: { $arrayElemAt: ["$platformRelation", 0] },
                else: { $arrayElemAt: ["$communityRelation", 0] },
              },
            },
          },
        },
        { $unwind: { path: "$user", preserveNullAndEmptyArrays: true } },
        { $unwind: { path: "$role", preserveNullAndEmptyArrays: true } },
        {
          $addFields: {
            first_name_lower: { $toLower: "$user.first_name" },
          },
        },
        {
          $project: {
            _id: 0,
            role: "$role.role_name",
            user_id: {
              _id: "$user._id",
              email: "$user.email",
              name: "$user.name",
              Preferred_Email: "$user.Preferred Email",
              email: "$user.email",
              display_name: "$user.display_name",
              attendeeDetail: "$user.attendeeDetail",
              first_name: "$user.first_name",
              last_name: "$user.last_name",
              groupos_join_date: "$user.groupos_join_date",
              migration_date: "$user.migration_date",
            },
            subscription_id: "$subscription_id",
            scopes: "$scopes",
            relation_id: "$relation_id",
            groupos_join_date: 1,
            migration_date: 1,
            first_name_lower: 1 
          },
        },
        { $sort: { first_name_lower : 1 } },
        { $unset: "first_name_lower" }
      ];
      return await user_edges.aggregate(pipeline);
      // .collation({ locale: "en", strength: 1 });
    }
  };

  findAllEdgesForActiveMember = async ({ filter }) => {
    return await user_edges.aggregate([
      {
        $match: filter,
      },
      {
        $lookup: {
          from: "airtable-syncs",
          localField: "user_id",
          foreignField: "_id",
          as: "user",
          pipeline: [
            {
              $match: {
                status: "ACTIVE",
              },
            },
            {
              $addFields: {
                email: "$Preferred Email",
              },
            },
            {
              $project: {
                email: 1,
                first_name: 1,
                last_name: 1,
                display_name: 1,
                groupos_join_date: 1,
                migration_date: 1,
              },
            },
          ],
        },
      },
      {
        $unwind: {
          path: "$user",
          preserveNullAndEmptyArrays: false,
        },
      },
      {
        $project: {
          _id: "$user._id",
          // email: "$user.email",
          "Preferred Email": "$user.email",
          first_name: "$user.first_name",
          last_name: "$user.last_name",
          display_name: "$user.display_name",
          groupos_join_date: "$user.groupos_join_date",
          migration_date: "$user.migration_date",
        },
      },
    ]);
  };

  findAndUpdateUserEdge = async ({
    filter,
    userEdgeData,
    getUpdatedData = true,
  }) => {
    if (getUpdatedData) {
      return await user_edges.findOneAndUpdate(filter, userEdgeData, {
        new: true,
      });
    } else {
      return await user_edges.findOneAndUpdate(filter, userEdgeData);
    }
  };

  findAllMemberEdges = async ({ filter, expand = false }) => {
    if (expand) {
      const populateObj = {
        path: "user_id",
        select: { _id: 1, "Preferred Email": 1 },
      };
      return await user_edges.find(filter).populate(populateObj);
    } else {
      return await user_edges.find(filter);
    }
  };

  removeDeviceToken = async ({ user_id, relation_id, deviceToken }) => {
    return await user_edges.updateMany(
      {
        user_id,
        relation_id: { $ne: relation_id },
        deviceToken: { $in: deviceToken },
      },
      { $pull: { deviceToken } }
    );
  };

  deleteDeviceTokenFromUser = async ({ user_id, relation_id, deviceToken }) => {
    try {
      const tokensToRemove = Array.isArray(deviceToken) ? deviceToken : [deviceToken];
      const result = await user_edges.updateOne(
        {
          user_id,
          relation_id,
          deviceToken: { $in: tokensToRemove },
        },
        { $pull: { deviceToken: { $in: tokensToRemove } } }
      );
  
      return result;
    } catch (error) {
      console.error("Error in deleteDeviceTokenFromUser:", error);
      throw new Error("Failed to delete device token from user");
    }
  };

  findUserEdgeAndAddDeviceToken = async ({ filter, deviceToken }) => {
    return await user_edges.findOneAndUpdate(
      { ...filter, isDelete: false },
      { $push: { deviceToken } },
      { new: true }
    );
  };

  findUserById = async ({ filter }) => {
    return await airtable_syncs.findOne(filter, {
      "Preferred Email": 1,
      first_name: 1,
      last_name: 1,
    });
  };

  findUserAndUpdatebyId = async ({ filter, updateData }) => {
    return await airtable_syncs
      .findOneAndUpdate(filter, updateData, { new: true })
      .select({ "Preferred Email": 1, first_name: 1, last_name: 1 });
  };

  findDataUsingAggregation = async ({ pipeline, skip, limit }) => {
    const count = await user_edges.aggregate(pipeline);
    let data = await user_edges.aggregate([
      ...pipeline,
      { $skip: skip },
      { $limit: limit },
    ]);

    data.forEach((detail) => {
      detail.user.groupos_join_date = detail?.user?.groupos_join_date ?? null;
      detail.user.migration_date = detail?.user?.migration_date ?? null;
    });

    return { data, count: count.length };
  };

  findDataUsingAggregationforSuggestion = async ({ pipeline }) => {
    const count = await user_edges.aggregate(pipeline);
    let data = await user_edges.aggregate([...pipeline]);
    // .collation({ locale: "en", strength: 1 });;

    data.forEach((detail) => {
      detail.user.groupos_join_date = detail?.user?.groupos_join_date ?? null;
      detail.user.migration_date = detail?.user?.migration_date ?? null;
    });

    return { data, count: count.length };
  };

  checkConditionOfUserEdge = async ({ edge }) => {
    try {
      // Assuming edges are part of the request body or query
      const _id = edge._id;
      const edges = await user_edges.findOne({ _id: _id });

      if (edges.type === "CO" && edges.subscription_id) {
        const response = await axios.get(
          `${process.env.GATEWAY_DOMAIN}/api/billings/api/v1/subscriptions/${edges.subscription_id}`
        );
        const scopeId = response.data.scope_id;
        if (edges.stripe_account_db_id) {
          const response = await axios.get(
            `${process.env.GATEWAY_DOMAIN}/api/billings/api/v1/stripe/getStripe-account/${edges.stripe_account_db_id}`
          );
          if (
            response.data.message.charges_enabled === true &&
            response.data.message.payouts_enabled === true
          ) {
            return { condition: true, scopeId };
          } else {
            return false;
          }
        } else {
          return false;
        }
      } else {
        return false;
      }
    } catch (error) {
      console.error("Error checking user edges:", error);
      return { success: false, message: "An error occurred." };
    }
  };

  fetchUserEdgesForTierUpdate = async ({ pipeline }) => {
    return await user_edges.aggregate(pipeline);
  };

  findAllNoSocialProviderMembers = async ({ filter }) => {
    return await user_edges.aggregate([
      {
        $match: filter,
      },
      {
        $lookup: {
          from: "airtable-syncs",
          localField: "user_id",
          foreignField: "_id",
          as: "user",
          pipeline: [
            {
              $match: {
                status: "ACTIVE",
                isDelete: false,
                // provider: "",
                // firebaseId:"",
                // providers: { $size: 0 }
              },
            },
            {
              $addFields: {
                email: "$Preferred Email",
              },
            },
            {
              $project: {
                email: 1,
                first_name: 1,
                last_name: 1,
                display_name: 1,
                profileImg:1
              },
            },
          ],
        },
      },
      {
        $unwind: {
          path: "$user",
          preserveNullAndEmptyArrays: false,
        },
      },
      {
        $project: {
          _id: "$user._id",
          // email: "$user.email",
          "Preferred Email": "$user.email",
          first_name: "$user.first_name",
          last_name: "$user.last_name",
          display_name: "$user.display_name",
          profileImg: "$user.profileImg",
        },
      },
    ]);
  };
}

module.exports = UserEdgesRepository;
