const { UserEdgesRepository } = require("../database");
const { ReturnDataObj } = require("../../../../user/utils");
const ObjectId = require("mongoose").Types.ObjectId;

class UserEdgesService {
  constructor() {
    this.repository = new UserEdgesRepository();
  }
  countEdges = async ({ filter, search, isBlocked=false }) => {
    const totalEdges = await this.repository.getCount({
      filter,
      search,
      isBlocked
    });
    return ReturnDataObj(totalEdges);
  };

  createUserEdge = async ({ userEdgeData }) => {
    const userEdge = await this.repository.createUserEdge({ userEdgeData });
    return ReturnDataObj(userEdge);
  };

  findUserEdge = async ({ filter, expand }) => {
    const userEdge = await this.repository.findUserEdge({
      filter,
      expand,
    });
    return ReturnDataObj(userEdge);
  };

  createUserMDSEdge = async ({ userEdgeData }) => {
    let userEdge = await this.repository.findUserEdge({
      filter: userEdgeData,
      expand: false,
    });

    if (!userEdge) {
      userEdge = await this.repository.createUserEdge({ userEdgeData });
    }
    return ReturnDataObj(userEdge);
  };

  saveDeviceToken = async ({ authUserId, relation_id, deviceToken }) => {
    const filter = {
      relation_id,
      user_id: authUserId,
      isDelete: false,
    };

    const findUserEdge = await this.repository.findUserEdge({
      filter,
    });
    if (!findUserEdge) return ReturnDataObj("User edge not found!", 404, false);

    const isDeviceTokenExist = findUserEdge.deviceToken.includes(deviceToken);
    // Find and remove the device token from other user records if it exists
    const conflictingRecords = await this.repository.findAllEdges({
      filter: { deviceToken, isDelete: false },
    });

    const removalPromises = conflictingRecords
      .filter(record => record.user_id.toString() !== authUserId.toString())
      .map(record =>
        this.repository.deleteDeviceTokenFromUser({
          user_id: record.user_id,
          relation_id: record.relation_id,
          deviceToken,
        })
      );

    if (removalPromises.length) {
      await Promise.all(removalPromises);
    }
    if (isDeviceTokenExist)
      return ReturnDataObj("Device token already exist!", 409, false);

    await this.repository.removeDeviceToken({
      user_id: authUserId,
      relation_id,
      deviceToken,
    });

    const updateUserEdge = await this.repository.findUserEdgeAndAddDeviceToken({
      filter,
      deviceToken,
    });

    if (!updateUserEdge)
      return ReturnDataObj("Something went wrong!", 409, false);

    return ReturnDataObj(updateUserEdge);
  };

  removeDeviceToken = async ({ authUserId, relation_id, deviceToken }) => {
    const filter = {
      relation_id,
      user_id: authUserId,
    };

    const userEdgeData = {
      $pull: { deviceToken },
    };

    const conflictingRecords = await this.repository.findAllEdges({
      filter: { deviceToken, isDelete: false },
    });

    const removalPromises = conflictingRecords
      .filter(record => record.user_id.toString() !== authUserId.toString())
      .map(record =>
        this.repository.deleteDeviceTokenFromUser({
          user_id: record.user_id,
          relation_id: record.relation_id,
          deviceToken,
        })
      );

    if (removalPromises.length) {
      await Promise.all(removalPromises);
    }

    const updateUserEdge = await this.repository.findAndUpdateUserEdge({
      filter,
      userEdgeData,
      getUpdatedData: false,
    });

    if (!updateUserEdge)
      return ReturnDataObj("User edge not found!", 404, false);

    return ReturnDataObj(updateUserEdge);
  };

  /**
   * Service for the get the community deleted member list
   */
  getDeletedCommunityMemeber = async ({
    page = 1,
    limit = 10,
    search = null,
    sortBy = "createdAt",
    sortOrder = "Desc",
    relation_id,
  }) => {
    const pageNumber = parseInt(page) || 1;
    const limitNumber = parseInt(limit) || 10;
    const skip = (pageNumber - 1) * limitNumber;

    const pipeline = [
      {
        $match: {
          relation_id: ObjectId(relation_id),
          isDelete: true,
          type: "M",
        },
      },
      {
        $lookup: {
          from: "airtable-syncs",
          localField: "user_id",
          foreignField: "_id",
          pipeline: [
            {
              $project: {
                "Preferred Email": 1,
                first_name: 1,
                last_name: 1,
                display_name: 1,
                groupos_join_date:1,
                migration_date:1,
              },
            },
          ],
          as: "user",
        },
      },
      {
        $unwind: {
          path: "$user",
          preserveNullAndEmptyArrays: false,
        },
      },
      { $project: { deviceToken: 0 } },
      ...(search && search != ""
        ? [
            {
              $match: {
                $or: [
                  {
                    $expr: {
                      $regexMatch: {
                        input: { $concat: ["$user.first_name", " ", "$user.last_name"] },
                        regex: search,
                        options: "i",
                      },
                    },
                  },
                  {
                    "user.first_name": {
                      $regex: ".*" + search + ".*",
                      $options: "i",
                    },
                  },
                  {
                    "user.last_name": {
                      $regex: ".*" + search + ".*",
                      $options: "i",
                    },
                  },
                  {
                    "user.display_name": {
                      $regex: ".*" + search + ".*",
                      $options: "i",
                    },
                  },
                ],
              },
            },
          ]
        : []),
      {
        $sort: {
          [sortBy]: sortOrder === "Asc" ? 1 : -1,
        },
      },
    ];

    const data = await this.repository.findDataUsingAggregation({
      pipeline,
      skip,
      limit: limitNumber,
    });

    return ReturnDataObj({ ...data, page: pageNumber });
  };

  getDeletedCommunityMemeberSuggestion = async ({
    relation_id,
  }) => {

    const pipeline = [
      {
        $match: {
          relation_id: ObjectId(relation_id),
          isDelete: true,
          type: "M",
        },
      },
      {
        $lookup: {
          from: "airtable-syncs",
          localField: "user_id",
          foreignField: "_id",
          pipeline: [
            {
              $project: {
                "Preferred Email": 1,
                first_name: 1,
                last_name: 1,
                display_name: 1,
                groupos_join_date:1,
                migration_date:1,
              },
            },
          ],
          as: "user",
        },
      },
      {
        $unwind: {
          path: "$user",
          preserveNullAndEmptyArrays: false,
        },
      },
      {
        $addFields: {
          first_name_lower: { $toLower: "$user.first_name" },
        },
      },
      {
        $sort: {
          first_name_lower: 1,
        },
      },
      {
        $unset: ["first_name_lower", "deviceToken"],
      }
    ];
    const data = await this.repository.findDataUsingAggregationforSuggestion({
      pipeline,
    });

    return ReturnDataObj({ ...data });
  };
}

module.exports = UserEdgesService;
