const amqp = require("amqplib");
const publishMessage = require("./publisher"); // Import the publisher
const {
  MESSAGE_QUEUE_URL,
  MESSAGE_QUEUE_EXCHANGE_NAME,
  MESSAGE_QUEUE_MONOLITH_SERVICE_NAME,
  FRONTEND_DOMAIN,
  MESSAGE_QUEUE_BILLING_SERVICE_NAME,
  BASE_URL,
  AWS_BUCKET,
} = require("../../../../../../config/config");
const { generateCommunityURL } = require("../../../../utils/comman");
const userEdgesRepository = require("../../database/repository/user-edges-repository");
const mongoose = require("mongoose");

const UserEdgesService = require("../../services/user-edges-service");
const UsersService = require("../../../users/services/users-service");
const SubscriptionSubmissionsService = require("../../../subscription-submissions/services/subscription-submissions-service");
const MailService = require("../../../../../../libraries/mail-service");
const PlatformService = require("../../../platform/services/platform-service");
const CommunitiesService = require("../../../communities/services/communities-service");
const ComunityMigrationLogsService = require("../../../comunity-migration-logs/services/comunity-migration-logs-service");
const UserMigrationsService = require("../../../user-migrations/services/user-migrations-service");
const script = require("../../../../../../microservices/user/utils/server-init");

const userEdgesRepositoryService = new userEdgesRepository();
const service = new UserEdgesService();
const usersService = new UsersService();
const subscriptionSubmissionsService = new SubscriptionSubmissionsService();
const mailService = new MailService();
const platformService = new PlatformService();
const communitiesService = new CommunitiesService();
const comunityMigrationLogsService = new ComunityMigrationLogsService();
const userMigrationsService = new UserMigrationsService();

async function startMessageConsumer() {
  try {
    const connection = await amqp.connect(MESSAGE_QUEUE_URL);
    const channel = await connection.createChannel();

    //? Common exchange for all services to communicate
    await channel.assertExchange(MESSAGE_QUEUE_EXCHANGE_NAME, "direct", {
      durable: false,
    });

    //* ------------------------- Community stripe admin customer added :start -------------------------
    /**
     * This queue is used to consume the event when the community added stripe community admin customer
     */
    //* Queue & Routing key for community added stripe community admin customer
    const communityAdminCustomerAddedQueue =
      "MONOLITH_COMMUNITY_ADDED_STRIPE_COMMUNITY_ADMIN_CUSTOMER_ADDED";
    const communityAdminCustomerAddedRoutingKey =
      "BILLING_COMMUNITY_ADDED_STRIPE_COMMUNITY_ADMIN_CUSTOMER_ADDED";

    await channel.assertQueue(communityAdminCustomerAddedQueue, {
      durable: false,
    });

    await channel.bindQueue(
      communityAdminCustomerAddedQueue,
      MESSAGE_QUEUE_EXCHANGE_NAME,
      // MESSAGE_QUEUE_MONOLITH_SERVICE_NAME
      communityAdminCustomerAddedRoutingKey
    );

    await channel.consume(communityAdminCustomerAddedQueue, (msg) => {
      try {
        if (msg !== null) {
          const payload = JSON.parse(msg.content);

          // get the event name
          const { event, data } = payload;

          async function trySwitch(event) {
            switch (event) {
              // stripe flow
              case "COMMUNITY_ADDED_STRIPE_COMMUNITY_ADMIN_CUSTOMER_ADDED": {
                console.log(
                  `[Community stripe admin customer added] -> user-edges Received message: ${payload.event}`
                );
                const { stripeCustomer, edge } = data;
                await service.repository.updateUserEdge({
                  userEdgeId: edge._id,
                  userEdgeData: {
                    stripe_customer_db_id: stripeCustomer._id,
                  },
                });
                break;
              }
            }
          }

          trySwitch(event);
          // Acknowledge the initial message
          channel.ack(msg);
        }
      } catch (error) {
        console.log(
          "🚀 ~~ file: consumer.js:93 ~~ awaitchannel.consume ~~ error:",
          error
        );
      }
    });
    //* ------------------------- Community stripe admin customer added :end -------------------------
    /**
     * //? ----------------------------------- Breaking Point -----------------------------------
     */

    //* ------------------------- Community stripe admin account added :start -------------------------
    /**
     * This queue is used to consume the event when the community added stripe community admin account
     */
    //* Queue & Routing key for community added stripe community admin account
    const communityAdminAccountAddedQueue =
      "MONOLITH_COMMUNITY_ADDED_STRIPE_COMMUNITY_ADMIN_ACCOUNT_ADDED";
    const communityAdminAccountAddedRoutingKey =
      "BILLING_COMMUNITY_ADDED_STRIPE_COMMUNITY_ADMIN_ACCOUNT_ADDED";

    await channel.assertQueue(communityAdminAccountAddedQueue, {
      durable: false,
    });

    await channel.bindQueue(
      communityAdminAccountAddedQueue,
      MESSAGE_QUEUE_EXCHANGE_NAME,
      // MESSAGE_QUEUE_MONOLITH_SERVICE_NAME
      communityAdminAccountAddedRoutingKey
    );

    await channel.consume(communityAdminAccountAddedQueue, (msg) => {
      try {
        if (msg !== null) {
          const payload = JSON.parse(msg.content);

          // get the event name
          const { event, data } = payload;

          async function trySwitch(event) {
            switch (event) {
              // stripe flow
              case "COMMUNITY_ADDED_STRIPE_COMMUNITY_ADMIN_ACCOUNT_ADDED": {
                console.log(
                  `[Community stripe admin account added] -> user-edges Received message: ${payload.event}`
                );
                const { stripeConnectedAccount, edge } = data;

                const fetchUserEdge = await service.repository.findUserEdge({
                  filter: {
                    _id: edge._id,
                    stripe_account_db_id: {
                      $exists: false,
                    },
                    isDelete: false,
                  },
                });

                if (fetchUserEdge) {
                  await service.repository.updateUserEdge({
                    userEdgeId: edge._id,
                    userEdgeData: {
                      stripe_account_db_id: stripeConnectedAccount._id,
                    },
                  });
                }
                break;
              }
            }
          }

          trySwitch(event);
          // Acknowledge the initial message
          channel.ack(msg);
        }
      } catch (error) {
        console.log(
          "🚀 ~~ file: consumer.js:155 ~~ awaitchannel.consume ~~ error:",
          error
        );
      }
    });
    //* ------------------------- Community stripe admin account added :end -------------------------
    /**
     * //? ----------------------------------- Breaking Point -----------------------------------
     */

    //* ------------------------- Stripe Community Member Customer added :start -------------------------
    /**
     * This queue is used to consume the event when the community added stripe community member customer
     */
    //* Queue & Routing key for community added stripe community member customer
    const communityStripeAdminCustomerAddedQueue =
      "MONOLITH_COMMUNITY_MEMBER_ADDED_STRIPE_COMMUNITY_MEMBER_CUSTOMER_ADDED";
    const communityStripeAdminCustomerAddedRoutingKey =
      "BILLING_COMMUNITY_MEMBER_ADDED_STRIPE_COMMUNITY_MEMBER_CUSTOMER_ADDED";

    await channel.assertQueue(communityStripeAdminCustomerAddedQueue, {
      durable: false,
    });

    await channel.bindQueue(
      communityStripeAdminCustomerAddedQueue,
      MESSAGE_QUEUE_EXCHANGE_NAME,
      // MESSAGE_QUEUE_MONOLITH_SERVICE_NAME
      communityStripeAdminCustomerAddedRoutingKey
    );

    await channel.consume(communityStripeAdminCustomerAddedQueue, (msg) => {
      try {
        if (msg !== null) {
          const payload = JSON.parse(msg.content);

          // get the event name
          const { event, data } = payload;

          async function trySwitch(event) {
            switch (event) {
              // stripe flow
              case "COMMUNITY_MEMBER_ADDED_STRIPE_COMMUNITY_MEMBER_CUSTOMER_ADDED": {
                console.log(
                  `[Stripe Community Member Customer added] -> user-edges Received message: ${payload.event}`
                );
                const { stripeCustomer, edge: edge } = data;

                await service.repository.updateUserEdge({
                  userEdgeId: edge._id,
                  userEdgeData: {
                    stripe_customer_db_id: stripeCustomer._id,
                  },
                });
                break;
              }
            }
          }

          trySwitch(event);
          // Acknowledge the initial message
          channel.ack(msg);
        }
      } catch (error) {
        console.log(
          "🚀 ~~ file: consumer.js:215 ~~ awaitchannel.consume ~~ error:",
          error
        );
      }
    });
    //* ------------------------- Stripe Community Member Customer added :end -------------------------
    /**
     * //? ----------------------------------- Breaking Point -----------------------------------
     */

    //* ------------------------- Subscription added :start -------------------------
    /**
     * This queue is used to consume the event when subscription added
     */
    //* Queue & Routing key for subscription added
    const subscriptionAddedQueue = "MONOLITH_STRIPE_SUBSCRIPTION_ADDED";
    const subscriptionAddedRoutingKey = "BILLING_STRIPE_SUBSCRIPTION_ADDED";

    await channel.assertQueue(subscriptionAddedQueue, {
      durable: false,
    });

    await channel.bindQueue(
      subscriptionAddedQueue,
      MESSAGE_QUEUE_EXCHANGE_NAME,
      // MESSAGE_QUEUE_MONOLITH_SERVICE_NAME
      subscriptionAddedRoutingKey
    );
    await channel.consume(subscriptionAddedQueue, (msg) => {
      try {
        if (msg !== null) {
          const payload = JSON.parse(msg.content);

          // get the event name
          const { event, data } = payload;

          async function trySwitch(event) {
            switch (event) {
              // stripe flow
              case "STRIPE_SUBSCRIPTION_ADDED":
                {
                  console.log(
                    `[Subscription added] -> user-edges Received message: ${payload.event}`
                  );
                  let {
                    subscription,
                    edge,
                    price,
                    recurringType,
                    tierName,
                    connectedAccountId,
                    isBillingSubcription
                  } = data;

                  await service.repository.updateUserEdge({
                    userEdgeId: edge._id,
                    userEdgeData: {
                      subscription_id: subscription._id,
                    },
                  });

                  // refresh user token
                  const user = await usersService.repository.updateUser({
                    user_id: subscription.user_id,
                    userData: {
                      refresh_token: true,
                    },
                  });

                  usersService.inviteUsersRepository.updateImportUser({
                    filter: {
                      email: user["Preferred Email"],
                      relation_id: edge.relation_id,
                      status: "PENDING",
                    },
                    data: {
                      status: "ACCEPTED",
                    },
                  });

                  // set subscription submission to done
                  await subscriptionSubmissionsService.repository.findAndUpdateSubscriptionSubmission(
                    {
                      filter: {
                        user_id: edge.user_id,
                        "tierData._id": subscription.tier_id,
                      },
                      subscriptionSubmissionData: {
                        status: "DONE",
                      },
                    }
                  );

                  // get relation data
                  let relation,
                    frontendDomain = FRONTEND_DOMAIN;
                  if (subscription.platform) {
                    const { data: platform } =
                      await platformService.getPlatform();
                    relation = platform;
                  } else {
                    const { data: community } =
                      await communitiesService.getCommunity({
                        communityId: subscription.relation_id,
                      });
                    relation = community;

                    if (community) {
                      frontendDomain = await generateCommunityURL({
                        community: community,
                      });
                    }
                  }

                  // send notification via email
                  if (user["Preferred Email"]) {
                    // if (data?.migrate_email) {
                    //   await mailService.sendMail({
                    //     to: data.migrate_email,
                    //     subject:
                    //       "Welcome Aboard! We're Excited to Have You with Us ",
                    //     template: "migrate-user",
                    //     context: {
                    //       Name: `${user.first_name}${user.last_name}`,
                    //       companyName: "MDS",
                    //       companyWebsite: `https://mds.groupos-dev.co/`,
                    //     },
                    //   });
                    // }
                    let isMigratedUser =
                      await userMigrationsService.repository.findUserMigration({
                        filter: {
                          email: user["Preferred Email"],
                        },
                      });
                    if (
                      isMigratedUser &&
                      isMigratedUser.tier_name == tierName
                    ) {
                      let subscriptionAddedObj =
                        await comunityMigrationLogsService.repository.findUserMigration(
                          {
                            filter: {
                              event: "SubscriptionAdded",
                              connected_account_id: connectedAccountId,
                            },
                          }
                        );
                      if (subscriptionAddedObj) {
                        let updateObj = {
                          "result.added": subscriptionAddedObj.result.added + 1,
                          "result.total": subscriptionAddedObj.result.total + 1,
                          "result_description.successfullyAdded": [
                            ...subscriptionAddedObj.result_description
                              .successfullyAdded,
                            {
                              user_id: subscription.user_id,
                              subscription_id: subscription._id,
                            },
                          ],
                        };
                        await comunityMigrationLogsService.repository.updateOne(
                          {
                            filter: { _id: subscriptionAddedObj._id },
                            data: updateObj,
                          }
                        );
                      } else {
                        await comunityMigrationLogsService.repository.createComunityMigrationLog(
                          {
                            data: {
                              event: "SubscriptionAdded",
                              connected_account_id: connectedAccountId,
                              action_number: 4,
                              // added_by: authUserId,
                              result: {
                                added: 1,
                                error: 0,
                                skip: 0,
                                total: 1,
                              },
                              result_description: {
                                skipRecourdDueToTheError: [],
                                skipRecourd: [],
                                successfullyAdded: [
                                  {
                                    user_id: subscription.user_id,
                                    subscription_id: subscription._id,
                                  },
                                ],
                              },
                            },
                          }
                        );
                      }
                    } else {
                      if (!data?.migrate_email) {
                        const mdsCommunity = await script.getMdsCommunity();
                        if (relation._id && mdsCommunity._id && relation._id.toString() == mdsCommunity._id.toString()) {
                          isBillingSubcription = true
                        }
                        // await mailService.sendMail({
                        //   to: user["Preferred Email"],
                        //   subject: `${relation.name} Subscription Active!`,
                        //   template: "subscription-active",
                        //   context: {
                        //     name: `${user.first_name}${user.last_name}`,
                        //     tierName: tierName,
                        //     relationName: relation.name,
                        //     price: price,
                        //     recurringType: recurringType,
                        //     redirectLink: `${frontendDomain}`,
                        //   },
                        //   relationId:relation?._id ? relation._id : "",
                        //   customsmtp:relation?.customSMTP ? relation.customSMTP : false,
                        //   isBillingSubcription
                        // });
                      }
                    }
                  }

                  if (edge && edge.type === "CO") {
                    const condition =
                      await userEdgesRepositoryService.checkConditionOfUserEdge(
                        { edge }
                      );
                    if (condition) {
                      const payload = {
                        event: "DRAFT_TIERS_ADDED_IN_TIERS",
                        data: {
                          relation_id: edge.relation_id._id,
                          stripe_account_db_id: edge.stripe_account_db_id,
                          scope_id: condition.scopeId,
                        },
                      };
                      publishMessage(
                        JSON.stringify(payload),
                        // MESSAGE_QUEUE_BILLING_SERVICE_NAME
                        "BILLING_DRAFTTIERS_ADDED_IN_TIERS"
                      );
                    }
                  } else {
                    console.log(
                      "No valid subscription found, skipping payload creation."
                    );
                  }
                }

                break;
            }
          }

          trySwitch(event);
          // Acknowledge the initial message
          channel.ack(msg);
        }
      } catch (error) {
        console.log(
          "🚀 ~~ file: consumer.js:405 ~~ awaitchannel.consume ~~ error:",
          error
        );
      }
    });
    //* ------------------------- Subscription added :end -------------------------
    /**
     * //? ----------------------------------- Breaking Point -----------------------------------
     */

    //* ------------------------- Subscription update :start -------------------------
    /**
     * This queue is used to consume the event when subscription update
     */
    //* Queue & Routing key for subscription update
    const subscriptionUpdateQueue = "MONOLITH_STRIPE_SUBSCRIPTION_UPDATED";
    const subscriptionUpdateRoutingKey = "BILLING_STRIPE_SUBSCRIPTION_UPDATED";

    await channel.assertQueue(subscriptionUpdateQueue, {
      durable: false,
    });

    await channel.bindQueue(
      subscriptionUpdateQueue,
      MESSAGE_QUEUE_EXCHANGE_NAME,
      // MESSAGE_QUEUE_MONOLITH_SERVICE_NAME
      subscriptionUpdateRoutingKey
    );

    await channel.consume(subscriptionUpdateQueue, (msg) => {
      try {
        if (msg !== null) {
          const payload = JSON.parse(msg.content);

          // get the event name
          const { event, data } = payload;

          async function trySwitch(event) {
            switch (event) {
              // stripe flow
              case "STRIPE_SUBSCRIPTION_UPDATED": {
                console.log(
                  `[Subscription update] -> user-edges Received message: ${payload.event}`
                );
                const { subscription, price, recurringType, tierName } = data;

                // refresh user token
                const user = await usersService.repository.updateUser({
                  user_id: subscription.user_id,
                  userData: {
                    refresh_token: true,
                  },
                });

                // set subscription submission to done
                // when user upgrades to a tier which had an application form attached to it
                await subscriptionSubmissionsService.repository.findAndUpdateSubscriptionSubmission(
                  {
                    filter: {
                      user_id: subscription.user_id,
                      "tierData._id": subscription.tier_id,
                    },
                    subscriptionSubmissionData: {
                      status: "DONE",
                    },
                  }
                );

                // get relation data
                let relation,
                  frontendDomain = FRONTEND_DOMAIN;
                if (subscription.platform) {
                  const { data: platform } =
                    await platformService.getPlatform();
                  relation = platform;
                } else {
                  const { data: community } =
                    await communitiesService.getCommunity({
                      communityId: subscription.relation_id,
                    });
                  relation = community;

                  if (community) {
                    frontendDomain = await generateCommunityURL({
                      community: community,
                    });
                  }
                }

                // send notification via email
                if (user["Preferred Email"]) {
                  const mdsCommunity = await script.getMdsCommunity();
                  // await mailService.sendMail({
                  //   to: user["Preferred Email"],
                  //   subject: `${relation.name} Subscription Updated!`,
                  //   template: "subscription-updated",
                  //   context: {
                  //     name: `${user.first_name}${user.last_name}`,
                  //     tierName: tierName,
                  //     relationName: relation.name,
                  //     price: price,
                  //     recurringType: recurringType,
                  //     redirectLink: `${frontendDomain}`,
                  //   },
                  //   relationId:relation?._id ? relation._id : "",
                  //   customsmtp:relation?.customSMTP ? relation.customSMTP : false,
                  //   isBillingSubcription: relation._id && mdsCommunity._id && relation._id.toString() === mdsCommunity._id.toString(),
                  // });
                }

                break;
              }
            }
          }

          trySwitch(event);
          // Acknowledge the initial message
          channel.ack(msg);
        }
      } catch (error) {
        console.log(
          "🚀 ~~ file: consumer.js:517 ~~ awaitchannel.consume ~~ error:",
          error
        );
      }
    });
    //* ------------------------- Subscription update :end -------------------------
    /**
     * //? ----------------------------------- Breaking Point -----------------------------------
     */

    //* ------------------------- Subscription opt out :start -------------------------
    /**
     * This queue is used to consume the event when subscription opt out
     */
    //* Queue & Routing key for subscription opt out
    const subscriptionOptOutQueue = "MONOLITH_STRIPE_SUBSCRIPTION_OPT_OUT";
    const subscriptionOptOutRoutingKey = "BILLING_STRIPE_SUBSCRIPTION_OPT_OUT";

    await channel.assertQueue(subscriptionOptOutQueue, {
      durable: false,
    });

    await channel.bindQueue(
      subscriptionOptOutQueue,
      MESSAGE_QUEUE_EXCHANGE_NAME,
      // MESSAGE_QUEUE_MONOLITH_SERVICE_NAME
      subscriptionOptOutRoutingKey
    );

    await channel.consume(subscriptionOptOutQueue, (msg) => {
      try {
        if (msg !== null) {
          const payload = JSON.parse(msg.content);

          // get the event name
          const { event, data } = payload;

          async function trySwitch(event) {
            switch (event) {
              // stripe flow
              case "STRIPE_SUBSCRIPTION_OPT_OUT": {
                console.log(
                  `[Subscription opt out] -> user-edges Received message: ${payload.event}`
                );
                const { subscription, price, recurringType, tierName } = data;

                // fetch user
                const user = await usersService.repository.findUser({
                  filter: {
                    _id: subscription.user_id,
                  },
                });

                // get relation data
                let relation,
                  frontendDomain = FRONTEND_DOMAIN;
                if (subscription.platform) {
                  const { data: platform } =
                    await platformService.getPlatform();
                  relation = platform;
                } else {
                  const { data: community } =
                    await communitiesService.getCommunity({
                      communityId: subscription.relation_id,
                    });
                  relation = community;

                  if (community) {
                    frontendDomain = await generateCommunityURL({
                      community: community,
                    });
                  }
                }

                // send notification via email
                if (user["Preferred Email"]) {
                  const mdsCommunity = await script.getMdsCommunity();
                  // await mailService.sendMail({
                  //   to: user["Preferred Email"],
                  //   subject: `${relation.name} Subscription cancelled!`,
                  //   template: "subscription-opt-out",
                  //   context: {
                  //     name: `${user.first_name}${user.last_name}`,
                  //     tierName: tierName,
                  //     relationName: relation.name,
                  //     price: price,
                  //     recurringType: recurringType,
                  //     redirectLink: `${frontendDomain}`,
                  //   },
                  //   relationId:relation?._id ? relation._id : "",
                  //   customsmtp:relation?.customSMTP ? relation.customSMTP : false,
                  //   isBillingSubcription: relation._id && mdsCommunity._id && relation._id.toString() === mdsCommunity._id.toString(),
                  // });
                }
                break;
              }
            }
          }

          trySwitch(event);
          // Acknowledge the initial message
          channel.ack(msg);
        }
      } catch (error) {
        console.log(
          "🚀 ~~ file: consumer.js:609 ~~ awaitchannel.consume ~~ error:",
          error
        );
      }
    });
    //* ------------------------- Subscription opt out :end -------------------------
    /**
     * //? ----------------------------------- Breaking Point -----------------------------------
     */

    //* ------------------------- Subscription opt out admin & custom date  :start -------------------------
    /**
     * This queue is used to consume the event when subscription opt out admin & custom date
     */
    //* Queue & Routing key for subscription opt out admin & custom date
    const subscriptionCancleOptOutQueue =
      "MONOLITH_STRIPE_CANCEL_SUBSCRIPTION_OPT_OUT";
    const subscriptionCancleOptOutRoutingKey =
      "BILLING_STRIPE_CANCEL_SUBSCRIPTION_OPT_OUT";

    await channel.assertQueue(subscriptionCancleOptOutQueue, {
      durable: false,
    });

    await channel.bindQueue(
      subscriptionCancleOptOutQueue,
      MESSAGE_QUEUE_EXCHANGE_NAME,
      // MESSAGE_QUEUE_MONOLITH_SERVICE_NAME
      subscriptionCancleOptOutRoutingKey
    );

    await channel.consume(subscriptionCancleOptOutQueue, (msg) => {
      try {
        if (msg !== null) {
          const payload = JSON.parse(msg.content);

          // get the event name
          const { event, data } = payload;

          async function trySwitch(event) {
            switch (event) {
              // stripe flow
              case "STRIPE_SUBSCRIPTION_OPT_OUT_ADMIN":
              case "STRIPE_SUBSCRIPTION_OPT_OUT_CUSTOM_DATE_ADMIN": {
                console.log(
                  `[Subscription opt out admin & custom date] -> user-edges Received message: ${payload.event}`
                );
                const {
                  subscription,
                  price,
                  recurringType,
                  tierName,
                  type,
                  cancellationDate,
                } = data;

                // fetch user
                const user = await usersService.repository.findUser({
                  filter: {
                    _id: subscription.user_id,
                  },
                });

                // get relation data
                let relation,
                  frontendDomain = FRONTEND_DOMAIN;
                if (subscription.platform) {
                  const { data: platform } =
                    await platformService.getPlatform();
                  relation = platform;
                } else {
                  const { data: community } =
                    await communitiesService.getCommunity({
                      communityId: subscription.relation_id,
                    });
                  relation = community;

                  if (community) {
                    frontendDomain = await generateCommunityURL({
                      community: community,
                    });
                  }
                }

                // send notification via email
                if (user["Preferred Email"]) {
                  const mdsCommunity = await script.getMdsCommunity();
                  // await mailService.sendMail({
                  //   to: user["Preferred Email"],
                  //   subject: `${relation.name} Subscription cancelled!`,
                  //   template:
                  //     type === "end_of_the_current_period"
                  //       ? "subscription-opt-out-admin"
                  //       : "subscription-opt-out-custom-date-admin",
                  //   context: {
                  //     name: `${user.first_name}${user.last_name}`,
                  //     tierName: tierName,
                  //     relationName: relation.name,
                  //     price: price,
                  //     recurringType: recurringType,
                  //     cancellationDate: new Date(
                  //       Number(cancellationDate)
                  //     ).toLocaleDateString(),
                  //     redirectLink: `${frontendDomain}`,
                  //   },
                  //   relationId:relation?._id ? relation._id : "",
                  //   customsmtp:relation?.customSMTP ? relation.customSMTP : false,
                  //   isBillingSubcription: relation._id && mdsCommunity._id && relation._id.toString() === mdsCommunity._id.toString(),
                  // });
                }
                break;
              }
            }
          }

          trySwitch(event);
          // Acknowledge the initial message
          channel.ack(msg);
        }
      } catch (error) {
        console.log(
          "🚀 ~~ file: consumer.js:719 ~~ awaitchannel.consume ~~ error:",
          error
        );
      }
    });
    //* ------------------------- Subscription opt out admin & custom date  :end -------------------------
    /**
     * //? ----------------------------------- Breaking Point -----------------------------------
     */

    //* ------------------------- Subscription Renew :start -------------------------
    /**
     * This queue is used to consume the event when subscription Renew
     */
    //* Queue & Routing key for subscription Renew
    const subscriptionRenewQueue = "MONOLITH_STRIPE_SUBSCRIPTION_RENEW";
    const subscriptionRenewRoutingKey = "BILLING_STRIPE_SUBSCRIPTION_RENEW";

    await channel.assertQueue(subscriptionRenewQueue, {
      durable: false,
    });

    await channel.bindQueue(
      subscriptionRenewQueue,
      MESSAGE_QUEUE_EXCHANGE_NAME,
      // MESSAGE_QUEUE_MONOLITH_SERVICE_NAME
      subscriptionRenewRoutingKey
    );

    await channel.consume(subscriptionRenewQueue, (msg) => {
      try {
        if (msg !== null) {
          const payload = JSON.parse(msg.content);

          // get the event name
          const { event, data } = payload;

          async function trySwitch(event) {
            switch (event) {
              // stripe flow
              case "STRIPE_SUBSCRIPTION_RENEW": {
                console.log(
                  `[Subscription Renew] -> user-edges Received message: ${payload.event}`
                );
                let { subscription, price, recurringType, tierName, isBillingSubcription } = data;

                // fetch user
                const user = await usersService.repository.findUser({
                  filter: {
                    _id: subscription.user_id,
                  },
                });

                // get relation data
                let relation,
                  frontendDomain = FRONTEND_DOMAIN;
                if (subscription.platform) {
                  const { data: platform } =
                    await platformService.getPlatform();
                  relation = platform;
                } else {
                  const { data: community } =
                    await communitiesService.getCommunity({
                      communityId: subscription.relation_id,
                    });
                  relation = community;

                  if (community) {
                    frontendDomain = await generateCommunityURL({
                      community: community,
                    });
                  }
                }

                // send notification via email
                if (user["Preferred Email"]) {
                  const mdsCommunity = await script.getMdsCommunity();
                  if (relation._id && mdsCommunity._id && relation._id.toString() == mdsCommunity._id.toString()) {
                    isBillingSubcription = true
                  }
                  // await mailService.sendMail({
                  //   to: user["Preferred Email"],
                  //   subject: `${relation.name} Subscription active!`,
                  //   template: "subscription-renew",
                  //   context: {
                  //     name: `${user.first_name}${user.last_name}`,
                  //     tierName: tierName,
                  //     relationName: relation.name,
                  //     price: price,
                  //     recurringType: recurringType,
                  //     redirectLink: `${frontendDomain}`,
                  //   },
                  //   relationId:relation?._id ? relation._id : "",
                  //   customsmtp:relation?.customSMTP ? relation.customSMTP : false,
                  //   isBillingSubcription: isBillingSubcription,
                  // });
                }
                break;
              }
            }
          }

          trySwitch(event);
          // Acknowledge the initial message
          channel.ack(msg);
        }
      } catch (error) {
        console.log(
          "🚀 ~~ file: consumer.js:808 ~~ awaitchannel.consume ~~ error:",
          error
        );
      }
    });
    //* ------------------------- Subscription Renew :end -------------------------
    /**
     * //? ----------------------------------- Breaking Point -----------------------------------
     */

    //* ------------------------- Subscription Cancel Expire :start -------------------------
    /**
     * This queue is used to consume the event when subscription Cancel Expire
     */
    //* Queue & Routing key for subscription Cancel Expire
    const subscriptionCancleExpireQueue =
      "MONOLITH_STRIPE_SUBSCRIPTION_CANCELLED_EXPIRED";
    const subscriptionCancleExpireRoutingKey =
      "BILLING_STRIPE_SUBSCRIPTION_CANCELLED_EXPIRED";

    await channel.assertQueue(subscriptionCancleExpireQueue, {
      durable: false,
    });

    await channel.bindQueue(
      subscriptionCancleExpireQueue,
      MESSAGE_QUEUE_EXCHANGE_NAME,
      // MESSAGE_QUEUE_MONOLITH_SERVICE_NAME
      subscriptionCancleExpireRoutingKey
    );

    await channel.consume(subscriptionCancleExpireQueue, (msg) => {
      try {
        if (msg !== null) {
          const payload = JSON.parse(msg.content);

          // get the event name
          const { event, data } = payload;

          async function trySwitch(event) {
            switch (event) {
              // stripe flow
              case "STRIPE_SUBSCRIPTION_CANCELLED_EXPIRED":
            }
          }

          trySwitch(event);
          // Acknowledge the initial message
          channel.ack(msg);
        }
      } catch (error) {
        console.log(
          "🚀 ~~ file: consumer.js:867 ~~ awaitchannel.consume ~~ error:",
          error
        );
      }
    });
    //* ------------------------- Subscription Cancel Expire  :end -------------------------
    /**
     * //? ----------------------------------- Breaking Point -----------------------------------
     */

    //* ------------------------- Subscription Cancel :start -------------------------
    /**
     * This queue is used to consume the event when subscription cancel
     */
    //* Queue & Routing key for subscription cancel
    const subscriptionCancleQueue = "MONOLITH_STRIPE_SUBSCRIPTION_CANCELLED";
    const subscriptionCancleRoutingKey =
      "BILLING_STRIPE_SUBSCRIPTION_CANCELLED";

    await channel.assertQueue(subscriptionCancleQueue, {
      durable: false,
    });

    await channel.bindQueue(
      subscriptionCancleQueue,
      MESSAGE_QUEUE_EXCHANGE_NAME,
      // MESSAGE_QUEUE_MONOLITH_SERVICE_NAME
      subscriptionCancleRoutingKey
    );

    await channel.consume(subscriptionCancleQueue, (msg) => {
      try {
        if (msg !== null) {
          const payload = JSON.parse(msg.content);

          // get the event name
          const { event, data } = payload;

          async function trySwitch(event) {
            switch (event) {
              // stripe flow
              case "STRIPE_SUBSCRIPTION_CANCELLED": {
                console.log(
                  `[Subscription Cancel] -> user-edges Received message: ${payload.event}`
                );
                const {
                  edge,
                  subscription,
                  price,
                  recurringType,
                  tierName,
                  type,
                } = data;
                // fetch user
                const user = await usersService.repository.findUser({
                  filter: {
                    _id: subscription.user_id,
                  },
                });

                const updateEdge = await service.repository.updateUserEdge({
                  userEdgeId: edge._id,
                  userEdgeData: {
                    $unset: { subscription_id: 1 },
                  },
                });

                // get relation data
                let relation,
                  frontendDomain = FRONTEND_DOMAIN;
                if (subscription.platform) {
                  const { data: platform } =
                    await platformService.getPlatform();
                  relation = platform;
                } else {
                  const { data: community } =
                    await communitiesService.getCommunity({
                      communityId: subscription.relation_id,
                    });
                  relation = community;

                  if (community) {
                    frontendDomain = await generateCommunityURL({
                      community: community,
                    });
                  }
                }

                if (user && updateEdge.type === "M") {
                  //* Fetch the community data
                  const community =
                    await communitiesService.repository.findCommunity({
                      filter: {
                        _id: updateEdge?.relation_id,
                        isDelete: false,
                      },
                    });

                  if (community) {
                    const inviteCUs =
                      await usersService.InviteCollaboratorsRepository.fetchAllInviteCollaborators(
                        {
                          filter: {
                            invitee_id: user?._id,
                            relation_id: updateEdge?.relation_id,
                            status: { $ne: "REVOKED" },
                            isDelete: false,
                          },
                        }
                      );

                    if (inviteCUs.length) {
                      for await (let data of inviteCUs) {
                        await usersService.revokeAllInviteCUbyCanclledEvent({
                          inviteCollaboratorUserData: data,
                          community,
                          relation_id: updateEdge?.relation_id,
                        });
                      }
                    }
                  }
                }

                // send notification via email
                if (user["Preferred Email"]) {
                  const mdsCommunity = await script.getMdsCommunity();
                  // await mailService.sendMail({
                  //   to: user["Preferred Email"],
                  //   subject: `${relation.name} Subscription cancelled!`,
                  //   template:
                  //     type === "CANCELLED"
                  //       ? "subscription-canceled"
                  //       : "subscription-canceled-expired",
                  //   context: {
                  //     name: `${user.first_name}${user.last_name}`,
                  //     tierName: tierName,
                  //     relationName: relation.name,
                  //     price: price,
                  //     recurringType: recurringType,
                  //     redirectLink: `${frontendDomain}`,
                  //   },
                  //   relationId:relation?._id ? relation._id : "",
                  //   customsmtp:relation?.customSMTP ? relation.customSMTP : false,
                  //   isBillingSubcription: relation._id && mdsCommunity._id && relation._id.toString() === mdsCommunity._id.toString(),
                  // });
                }
                break;
              }
            }
          }

          trySwitch(event);
          // Acknowledge the initial message
          channel.ack(msg);
        }
      } catch (error) {
        console.log(
          "🚀 ~~ file: consumer.js:976 ~~ awaitchannel.consume ~~ error:",
          error
        );
      }
    });

    //* ------------------------- Subscription Cancel :end -------------------------
    /**
     * //? ----------------------------------- Breaking Point -----------------------------------
     */

    //* ------------------------- Subscription Trial End :start -------------------------
    /**
     * This queue is used to consume the event when subscription trial end
     */
    //* Queue & Routing key for subscription trial end
    const subscriptionTrialEndQueue = "MONOLITH_STRIPE_SUBSCRIPTION_TRIAL_ENDS";
    const subscriptionTrialEndRoutingKey =
      "BILLING_STRIPE_SUBSCRIPTION_TRIAL_ENDS";

    await channel.assertQueue(subscriptionTrialEndQueue, {
      durable: false,
    });

    await channel.bindQueue(
      subscriptionTrialEndQueue,
      MESSAGE_QUEUE_EXCHANGE_NAME,
      // MESSAGE_QUEUE_MONOLITH_SERVICE_NAME
      subscriptionTrialEndRoutingKey
    );

    await channel.consume(subscriptionTrialEndQueue, (msg) => {
      try {
        if (msg !== null) {
          const payload = JSON.parse(msg.content);

          // get the event name
          const { event, data } = payload;

          async function trySwitch(event) {
            switch (event) {
              // stripe flow
              case "STRIPE_SUBSCRIPTION_TRIAL_ENDS": {
                console.log(
                  `[Subscription Trial End] -> user-edges Received message: ${payload.event}`
                );
                const { subscription, price, recurringType, tierName } = data;

                // fetch user
                const user = await usersService.repository.findUser({
                  filter: {
                    _id: subscription.user_id,
                  },
                });

                // get relation data
                let relation,
                  frontendDomain = FRONTEND_DOMAIN;
                if (subscription.platform) {
                  const { data: platform } =
                    await platformService.getPlatform();
                  relation = platform;
                } else {
                  const { data: community } =
                    await communitiesService.getCommunity({
                      communityId: subscription.relation_id,
                    });
                  relation = community;

                  if (community) {
                    frontendDomain = await generateCommunityURL({
                      community: community,
                    });
                  }
                }

                // send notification via email
                if (user["Preferred Email"]) {
                  let isMigratedUser =
                    await userMigrationsService.repository.findUserMigration({
                      filter: {
                        email: user["Preferred Email"],
                      },
                    });
                  if (
                    !isMigratedUser ||
                    (isMigratedUser && isMigratedUser.tier_name != tierName)
                  ) {
                    const mdsCommunity = await script.getMdsCommunity();
                    // await mailService.sendMail({
                    //   to: user["Preferred Email"],
                    //   subject: `${relation.name} Subscription Trial Ends Soon!`,
                    //   template: "subscription-trial-ends",
                    //   context: {
                    //     name: `${user.first_name}${user.last_name}`,
                    //     tierName: tierName,
                    //     relationName: relation.name,
                    //     price: price,
                    //     recurringType: recurringType,
                    //     redirectLink: `${frontendDomain}`,
                    //   },
                    //   relationId:relation?._id ? relation._id : "",
                    //   customsmtp:relation?.customSMTP ? relation.customSMTP : false,
                    //   isBillingSubcription: relation._id && mdsCommunity._id && relation._id.toString() === mdsCommunity._id.toString(),
                    // });
                    
                  }
                }
                break;
              }
            }
          }

          trySwitch(event);
          // Acknowledge the initial message
          channel.ack(msg);
        }
      } catch (error) {
        console.log(
          "🚀 ~~ file: consumer.js:770 ~~ awaitchannel.consume ~~ error:",
          error
        );
      }
    });
    //* ------------------------- Subscription Trial End :end -------------------------
    /**
     * //? ----------------------------------- Breaking Point -----------------------------------
     */

    //* ------------------------- Subscription refund receipt :start -------------------------
    /**
     * This queue is used to consume the event when subscription refund receipt
     */
    //* Queue & Routing key for subscription refund receipt
    const subscriptionRefundRecieptQueue =
      "MONOLITH_STRIPE_SUBSCRIPTION_REFUNDED_RECEIPT";
    const subscriptionRefundRecieptRoutingKey =
      "BILLING_STRIPE_SUBSCRIPTION_REFUNDED_RECEIPT";

    await channel.assertQueue(subscriptionRefundRecieptQueue, {
      durable: false,
    });

    await channel.bindQueue(
      subscriptionRefundRecieptQueue,
      MESSAGE_QUEUE_EXCHANGE_NAME,
      // MESSAGE_QUEUE_MONOLITH_SERVICE_NAME
      subscriptionRefundRecieptRoutingKey
    );

    await channel.consume(subscriptionRefundRecieptQueue, (msg) => {
      try {
        if (msg !== null) {
          const payload = JSON.parse(msg.content);

          // get the event name
          const { event, data } = payload;

          async function trySwitch(event) {
            switch (event) {
              // stripe flow
              case "STRIPE_SUBSCRIPTION_REFUNDED_RECEIPT": {
                console.log(
                  `[Subscription refund receipt] -> user-edges Received message: ${payload.event}`
                );
                const { subscription, price, receiptEmail, tierName } = data;
                // fetch user
                const user = await usersService.repository.findUser({
                  filter: {
                    _id: subscription.user_id,
                  },
                });
                const mdsCommunity = await script.getMdsCommunity();

                // get relation data
                let relation;
                if (subscription.platform) {
                  const { data: platform } =
                    await platformService.getPlatform();
                  relation = platform;
                } else {
                  const { data: community } =
                    await communitiesService.getCommunity({
                      communityId: subscription.relation_id,
                    });
                  relation = community;
                }

                // send notification via email
                // await mailService.sendMail({
                //   to: receiptEmail,
                //   subject: `${relation.name} Subscription payment refunded!`,
                //   template: "subscription-refund-receipt",
                //   context: {
                //     name: `${user.first_name}${user.last_name}`,
                //     tierName: tierName,
                //     relationName: relation.name,
                //     price: price,
                //   },
                //   relationId:relation?._id ? relation._id : "",
                //   customsmtp:relation?.customSMTP ? relation.customSMTP : false,
                //   isBillingSubcription: relation._id && mdsCommunity._id && relation._id.toString() === mdsCommunity._id.toString(),
                // });

                const payload = {
                  event: "CANCEL_USER_SUBSCRIPTION",
                  data: {
                    userId: userId,
                    relation_id: relation_id,
                  },
                };

                await publishMessage(
                  JSON.stringify(payload),
                  "CHAT_USER_MODIFICATION"
                );
              }
            }
          }

          trySwitch(event);
          // Acknowledge the initial message
          channel.ack(msg);
        }
      } catch (error) {
        console.log(
          "🚀 ~~ file: consumer.js:1175 ~~ awaitchannel.consume ~~ error:",
          error
        );
      }
    });
    //* ------------------------- Subscription refund receipt :end -------------------------
    /**
     * //? ----------------------------------- Breaking Point -----------------------------------
     */

    //* ------------------------- Community Owner subscription added :start -------------------------
    /**
     * This queue is used to consume the event when Community Owner subscription added
     */
    //* Queue & Routing key for Community Owner subscription added
    const communityOwnerSubscriptionQueue =
      "MONOLITH_COMMUNITY_OWNER_ADDED_COMMUNITY_OWNER_SUBSCRIPTION_ADDED";
    const communityOwnerSubscriptionRoutingKey =
      "BILLING_COMMUNITY_OWNER_ADDED_COMMUNITY_OWNER_SUBSCRIPTION_ADDED";

    await channel.assertQueue(communityOwnerSubscriptionQueue, {
      durable: false,
    });

    await channel.bindQueue(
      communityOwnerSubscriptionQueue,
      MESSAGE_QUEUE_EXCHANGE_NAME,
      // MESSAGE_QUEUE_MONOLITH_SERVICE_NAME
      communityOwnerSubscriptionRoutingKey
    );

    await channel.consume(communityOwnerSubscriptionQueue, (msg) => {
      try {
        if (msg !== null) {
          const payload = JSON.parse(msg.content);

          // get the event name
          const { event, data } = payload;

          async function trySwitch(event) {
            switch (event) {
              // community owners flow
              case "COMMUNITY_OWNER_ADDED_COMMUNITY_OWNER_SUBSCRIPTION_ADDED": {
                console.log(
                  `[Community Owner subscription added] -> user-edges Received message: ${payload.event}`
                );
                const { subscription, edge, stripeAccount, stripeCustomer } =
                  data;
                await service.repository.updateUserEdge({
                  userEdgeId: edge._id,
                  userEdgeData: {
                    subscription_id: subscription._id,
                    stripe_account_db_id: stripeAccount._id,
                    stripe_customer_db_id: stripeCustomer._id,
                  },
                });

                // refresh user token
                await usersService.repository.updateUser({
                  user_id: subscription.user_id,
                  userData: {
                    refresh_token: true,
                  },
                });
                break;
              }
            }
          }

          trySwitch(event);
          // Acknowledge the initial message
          channel.ack(msg);
        }
      } catch (error) {
        console.log(
          "🚀 ~~ file: consumer.js:1250 ~~ awaitchannel.consume ~~ error:",
          error
        );
      }
    });
    //* ------------------------- Community Owner subscription added :end -------------------------
    /**
     * //? ----------------------------------- Breaking Point -----------------------------------
     */

    //* ------------------------- Delete all community memebers :start -------------------------
    /**
     * This queue is used to consume the event when Delete all community memebers
     */
    //* Queue & Routing key for Delete all community memebers
    const deleteCommunityMemberEdgeQueue =
      "MONOLITH_DELETE_ALL_COMMUNITY_MEMBER_EDGE";
    const deleteCommunityMemberEdgeRoutingKey =
      "BILLING_DELETE_ALL_COMMUNITY_MEMBER_EDGE";

    await channel.assertQueue(deleteCommunityMemberEdgeQueue, {
      durable: false,
    });

    await channel.bindQueue(
      deleteCommunityMemberEdgeQueue,
      MESSAGE_QUEUE_EXCHANGE_NAME,
      // MESSAGE_QUEUE_MONOLITH_SERVICE_NAME
      deleteCommunityMemberEdgeRoutingKey
    );

    await channel.consume(deleteCommunityMemberEdgeQueue, (msg) => {
      try {
        if (msg !== null) {
          const payload = JSON.parse(msg.content);

          // get the event name
          const { event, data } = payload;

          async function trySwitch(event) {
            switch (event) {
              // community owners flow
              case "DELETE_ALL_COMMUNITY_MEMBER_EDGE": {
                console.log(
                  `[Delete all community memebers] -> user-edges Received message: ${payload.event}`
                );
                const { edge, subscription } = data;

                // fetch user
                const user = await usersService.repository.findUser({
                  filter: {
                    _id: edge.user_id._id,
                  },
                });

                // remove user edge from air-table-sync
                const updatedUser = await usersService.repository.updateUser({
                  user_id: edge.user_id._id,
                  userData: {
                    $pull: {
                      user_edges: new mongoose.Types.ObjectId(edge._id),
                    },
                  },
                });

                console.log(
                  "DELETE_ALL_COMMUNITY_MEMBER_EDGE",
                  "delete_user_edge",
                  new Date(),
                  { userEdgeId: edge._id }
                );

                // soft delete user edge data
                await service.repository.updateUserEdge({
                  userEdgeId: edge._id,
                  userEdgeData: {
                    $set: {
                      isDelete: true,
                      deleteFrom:
                        "MONOLITH_DELETE_ALL_COMMUNITY_MEMBER_EDGE : 1486",
                    },
                  },
                });

                // get relation data
                let relation,
                  frontendDomain = FRONTEND_DOMAIN;
                if (subscription.platform) {
                  const { data: platform } =
                    await platformService.getPlatform();
                  relation = platform;
                } else {
                  const { data: community } =
                    await communitiesService.getCommunity({
                      communityId: subscription.relation_id,
                    });
                  relation = community;

                  if (community) {
                    frontendDomain = await generateCommunityURL({
                      community: community,
                    });
                  }
                }
                const mdsCommunity = await script.getMdsCommunity();

                // send notification via email
                if (user["Preferred Email"]) {
                  await mailService.sendMail({
                    to: user["Preferred Email"],
                    subject: `${relation.name} Community close!`,
                    template: "community-close",
                    context: {
                      name: `${user.first_name}${user.last_name}`,
                      relationName: relation.name,
                      redirectLink: `${frontendDomain}`,
                    },
                    relationId:relation?._id ? relation._id : "",
                    customsmtp:relation?.customSMTP ? relation.customSMTP : false,
                    isBillingSubcription: relation._id && mdsCommunity._id && relation._id.toString() === mdsCommunity._id.toString(),
                  });
                }
                break;
              }
            }
          }

          trySwitch(event);
          // Acknowledge the initial message
          channel.ack(msg);
        }
      } catch (error) {
        console.log(
          "🚀 ~~ file: consumer.js:1357 ~~ awaitchannel.consume ~~ error:",
          error
        );
      }
    });
    //* ------------------------- Delete all community memebers :end -------------------------
    /**
     * //? ----------------------------------- Breaking Point -----------------------------------
     */

    //* ------------------------- Get MDS stripe customer :start -------------------------
    /**
     * This queue is used to consume the event when Get MDS stripe customer
     */
    //* Queue & Routing key for Get MDS stripe customer
    const getMDSStripeCustomerQueue =
      "MONOLITH_GET_MDS_STRIPE_CUSTOMER_STRIPE_COMMUNITY_MEMBER_CUSTOMER_ADDED";
    const getMDSStripeCustomerRoutingKey =
      "BILLING_GET_MDS_STRIPE_CUSTOMER_STRIPE_COMMUNITY_MEMBER_CUSTOMER_ADDED";

    await channel.assertQueue(getMDSStripeCustomerQueue, {
      durable: false,
    });

    await channel.bindQueue(
      getMDSStripeCustomerQueue,
      MESSAGE_QUEUE_EXCHANGE_NAME,
      // MESSAGE_QUEUE_MONOLITH_SERVICE_NAME
      getMDSStripeCustomerRoutingKey
    );

    await channel.consume(getMDSStripeCustomerQueue, (msg) => {
      try {
        if (msg !== null) {
          const payload = JSON.parse(msg.content);

          // get the event name
          const { event, data } = payload;

          async function trySwitch(event) {
            switch (event) {
              // community owners flow
              case "GET_MDS_STRIPE_CUSTOMER_STRIPE_COMMUNITY_MEMBER_CUSTOMER_ADDED": {
                console.log(
                  `[Get MDS stripe customer] -> user-edges Received message: ${payload.event}`
                );
                const {
                  stripeCustomer,
                  edge: edge,
                  stripeCustomerPaymentMethods,
                  connectedAccountId,
                  stripeCustomerData,
                } = data;
                // Comunity Migration Logs Start
                let subscriptionAddedObj =
                  await comunityMigrationLogsService.repository.findUserMigration(
                    {
                      filter: {
                        event: "RetrieveStripeCustomerAndHisPaymentMethods",
                        connected_account_id: connectedAccountId,
                      },
                    }
                  );
                if (subscriptionAddedObj) {
                  let updateObj = {
                    "result.added": subscriptionAddedObj.result.added + 1,
                    "result.total": subscriptionAddedObj.result.total + 1,
                    "result_description.successfullyAdded": [
                      ...subscriptionAddedObj.result_description
                        .successfullyAdded,
                      {
                        user_id: edge.user_id,
                        userEdgeId: edge._id,
                        stripe_customer_db_id: stripeCustomer._id,
                      },
                    ],
                  };
                  await comunityMigrationLogsService.repository.updateOne({
                    filter: { _id: subscriptionAddedObj._id },
                    data: updateObj,
                  });
                } else {
                  await comunityMigrationLogsService.repository.createComunityMigrationLog(
                    {
                      data: {
                        event: "RetrieveStripeCustomerAndHisPaymentMethods",
                        connected_account_id: connectedAccountId,
                        action_number: 3,
                        // added_by: authUserId,
                        result: {
                          added: 1,
                          error: 0,
                          skip: 0,
                          total: 1,
                        },
                        result_description: {
                          skipRecourdDueToTheError: [],
                          skipRecourd: [],
                          successfullyAdded: [
                            {
                              user_id: edge.user_id,
                              userEdgeId: edge._id,
                              stripe_customer_db_id: stripeCustomer._id,
                            },
                          ],
                        },
                      },
                    }
                  );
                }
                // Comunity Migration Logs end

                // Added stripe customer db id added in User Edge
                await service.repository.updateUserEdge({
                  userEdgeId: edge._id,
                  userEdgeData: {
                    stripe_customer_db_id: stripeCustomer._id,
                  },
                });

                // send rabbitMQ event for subscribe MDS teir
                let payloadObj = {
                  event:
                    "GET_MDS_STRIPE_CUSTOMER_STRIPE_COMMUNITY_MEMBER_CUSTOMER_ADDED_SUBSCRIBE_TIER",
                  data: {
                    connectedAccountId: connectedAccountId,
                    email: stripeCustomerData.email,
                    userId: stripeCustomerData.userId,
                    customerId: stripeCustomerData.customerId,
                    migrationData: stripeCustomerData.migrationData,
                    stripeCustomer: stripeCustomerData.stripeCustomer,
                    relationId: edge.relation_id,
                  },
                };

                publishMessage(
                  JSON.stringify(payloadObj),
                  // MESSAGE_QUEUE_BILLING_SERVICE_NAME
                  "MONOLITH_GET_MDS_STRIPE_CUSTOMER_STRIPE_COMMUNITY_MEMBER_CUSTOMER_ADDED_SUBSCRIBE_TIER"
                );
                break;
              }
            }
          }

          trySwitch(event);
          // Acknowledge the initial message
          channel.ack(msg);
        }
      } catch (error) {
        console.log(
          "🚀 ~~ file: consumer.js:1510 ~~ awaitchannel.consume ~~ error:",
          error
        );
      }
    });
    //* ------------------------- Get MDS stripe customer :end -------------------------
    /**
     * //? ----------------------------------- Breaking Point -----------------------------------
     */

    //* ------------------------- Invite collaborator user subscription added :start -------------------------
    /**
     * This queue is used to consume the event when Invite collaborator user subscription added
     */
    //* Queue & Routing key for Invite collaborator user subscription added
    const inviteCollaboratorSubscriptionQueue =
      "MONOLITH_INVITE_COLLABORATOR_USER_SUBSCRIPTION_ADDED";
    const inviteCollaboratorSubscriptionRoutingKey =
      "BILLING_INVITE_COLLABORATOR_USER_SUBSCRIPTION_ADDED";

    await channel.assertQueue(inviteCollaboratorSubscriptionQueue, {
      durable: false,
    });

    await channel.bindQueue(
      inviteCollaboratorSubscriptionQueue,
      MESSAGE_QUEUE_EXCHANGE_NAME,
      inviteCollaboratorSubscriptionRoutingKey
    );

    await channel.consume(inviteCollaboratorSubscriptionQueue, (msg) => {
      try {
        if (msg !== null) {
          const payload = JSON.parse(msg.content);

          // get the event name
          const { event, data } = payload;

          async function trySwitch(event) {
            switch (event) {
              //* Invite the collaborator user subscription added event
              case "INVITE_COLLABORATOR_USER_SUBSCRIPTION_ADDED": {
                console.log(
                  `[Invite Collaborator user subscription added] -> user-edges Received message: ${payload.event}`
                );
                const { subscription, edge } = data;
                await service.repository.updateUserEdge({
                  userEdgeId: edge._id,
                  userEdgeData: {
                    subscription_id: subscription._id,
                  },
                });

                // refresh user token
                await usersService.repository.updateUser({
                  user_id: subscription.user_id,
                  userData: {
                    refresh_token: true,
                  },
                });
                break;
              }
            }
          }

          trySwitch(event);
          // Acknowledge the initial message
          channel.ack(msg);
        }
      } catch (error) {
        console.log(
          "🚀 ~ file: consumer.js:1706 ~ awaitchannel.consume ~ error:",
          error
        );
      }
    });
    //* ------------------------- Community Owner subscription added :end -------------------------
    /**
     * //? ----------------------------------- Breaking Point -----------------------------------
     */

    //* ------------------------- Subcription Paymenet failed :start -------------------------
    /**
     * This queue is used to consume the event when Subcription Paymenet failed
     */
    //* Queue & Routing key for Subcription Paymenet failed
    const subscriptionPaymentFailedQueue =
      "MONOLITH_STRIPE_SUBSCRIPTION_REFUNDED_RECEIPT";
    const subscriptionPaymentFailedRoutingKey =
      "BILLING_STRIPE_SUBSCRIPTION_INVOICE_PAYMENT_FAILED";

    await channel.assertQueue(subscriptionPaymentFailedQueue, {
      durable: false,
    });

    await channel.bindQueue(
      subscriptionPaymentFailedQueue,
      MESSAGE_QUEUE_EXCHANGE_NAME,
      // MESSAGE_QUEUE_MONOLITH_SERVICE_NAME
      subscriptionPaymentFailedRoutingKey
    );

    await channel.consume(subscriptionPaymentFailedQueue, (msg) => {
      try {
        if (msg !== null) {
          const payload = JSON.parse(msg.content);

          // get the event name
          const { event, data } = payload;
          async function trySwitch(event) {
            switch (event) {
              // stripe flow
              case "STRIPE_SUBSCRIPTION_INVOICE_PAYMENT_FAILED": {
                console.log(
                  `[Subscription Payment failed] -> user-edges Received message: ${payload.event}`
                );
                const { hosted_invoice_url, invoice_pdf, id } = data;
                // fetch user
                const user = await userEdgesRepositoryService.findUserEdge({
                  filter: {
                    stripe_customer_db_id: id,
                  },
                  expand: true,
                });
                let communityLogo = null;
                // Create the community S3 logo
                if (user?.relation_id.logo) {
                  communityLogo = `https://${AWS_BUCKET}.s3.us-east-2.amazonaws.com/groupos/${user?.relation_id?._id}/${user?.relation_id?.logo}`;
                }
                const mdsCommunity = await script.getMdsCommunity();
                // send notification via email
                if (user?.user_id["Preferred Email"]) {
                  await mailService.sendMail({
                    to: user.user_id["Preferred Email"],
                    subject: `Payment Failed`,
                    template: `payment-failed`,
                    context: {
                      userName: `${user.user_id.first_name} ${user.user_id.last_name}`,
                      communityName: user.relation_id.name,
                      invoicePDFLink: invoice_pdf,
                      repaymentLink: `${hosted_invoice_url}`,
                      communityLogo: communityLogo,
                    },
                    relationId:user.relation_id?._id ? user.relation_id._id : "",
                    customsmtp:user.relation_id?.customSMTP ? user.relation_id.customSMTP : false,
                    isBillingSubcription: relation._id && mdsCommunity._id && relation._id.toString() === mdsCommunity._id.toString(),
                  });
                }
              }
            }
          }

          trySwitch(event);
          // Acknowledge the initial message
          channel.ack(msg);
        }
      } catch (error) {
        console.log(
          "🚀 ~~ file: consumer.js:1812 ~~ awaitchannel.consume ~~ error:",
          error
        );
      }
    });
    //* ------------------------- Subcription Paymenet failed :end -------------------------
    /**
     * //? ----------------------------------- Breaking Point -----------------------------------
     */
  } catch (error) {
    console.error(error);
  }
}

startMessageConsumer();
