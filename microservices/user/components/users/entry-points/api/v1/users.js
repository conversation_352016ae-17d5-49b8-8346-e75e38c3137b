const express = require("express");
const usersRouter = express.Router();
const { getAuth } = require("firebase-admin/auth");

const { tryCatch, stringToBoolean } = require("../../../../../utils");
const UsersService = require("../../../services/users-service");
const publishMessage = require("../../message-queue/publisher");
const {
  BASE_URL,
  MESSAGE_QUEUE_BILLING_SERVICE_NAME,
  SOCIAL_PROVIDERS,
  FIREBASE_TOKEN_EXPIRE_TIME,
} = require("../../../../../../../config/config");
const multerConfig = require("../../../../../utils/multer");
const {
  isAuth,
  isAdmin,
  isAuthForFirebase,
  isAuthForFirebaseV2,
  isAdminForDefaultEdge,
} = require("../../../../../../../middleware/authtoken");
const { normalizeDomain } = require("../../../../../utils/comman");
const admin = require("../../../../../../../utils/firebase-admin");

const service = new UsersService();

const multer = require("multer");
const { default: mongoose } = require("mongoose");

const {
  createCookiesDomain,
  parseCookies,
  verifySessionCookie,
  refreshSessionCookie,
} = require("../../../../../utils/comman");
const { addDebugLog } = require("../../../../../utils/debug-log");
const { default: axios } = require("axios");

// Set up multer to handle CSV files
const upload = multer({
  dest: "uploads/", // Directory where uploaded files will be stored temporarily
  fileFilter: (req, file, cb) => {
    if (file.mimetype !== "text/csv") {
      return cb(new Error("Only CSV files are allowed"), false);
    }
    cb(null, true);
  },
});

/**
 * PUBLIC APIS
 */

/**
 * Validate link
 */
usersRouter.get(
  "/validate-link",
  tryCatch(async (req, res) => {
    const { activationCode } = req.query;

    if (!activationCode) {
      throw new Error("activation code is required!");
    }

    const { data, code } = await service.validateAccountActivationLink({
      activationCode: activationCode,
    });

    res.status(code).json(data);
  })
);

/**
 * Activate account
 */
usersRouter.post(
  "/activate-account",
  tryCatch(async (req, res) => {
    const { activationCode, password } = req.body;

    if (!activationCode) {
      throw new Error("activation code is required!");
    }

    const { data, code } = await service.activateAccount({
      activationCode: activationCode,
      password: password,
    });

    res.status(code).json(data);
  })
);

/**
 * Activate account using the firebase function
 */
usersRouter.post(
  "/activate-firebase-account",
  tryCatch(async (req, res) => {
    const { preferred_email } = req.body;

    const { data, code } = await service.activateFirebaseAccount({
      preferred_email: decodeURIComponent(preferred_email),
    });

    return res.status(code).json(data);
  })
);

/** *
 * Verify session cookie
 */

usersRouter.post("/verify-session-cookie", (req, res) => {
  const sessionCookie = req.headers["x-id-token"] || ""; // Get session cookie from the request
  const cookieString = req.headers.cookie;

  // Function to parse cookies into an object
  function parseCookies(cookieString) {
    const cookies = {};
    cookieString.split("; ").forEach((cookie) => {
      const [key, value] = cookie.split("=");
      cookies[key] = decodeURIComponent(value);
    });
    return cookies;
  }
  // Extract x-id-token
  const cookies = parseCookies(cookieString);
  const xIdToken = cookies["x-id-token"];

  if (!xIdToken) {
    return res.status(400).json({
      success: false,
      message: "Session cookie is missing.",
    });
  }

  admin
    .auth()
    .verifySessionCookie(xIdToken.split("Bearer ")[1], true)
    .then((decodedClaims) => {
      res.status(200).json({
        success: true,
        message: "Session cookie successfully verified.",
        user: decodedClaims,
      });
    })
    .catch((error) => {
      console.error("Session verification failed:", error);
      res.clearCookie("x-id-token");
      res.status(401).json({
        success: false,
        message: "Session cookie is invalid or expired.",
        action: "logout",
        redirect: "/",
      });
    });
});

/**
 * @name Verify session cookie v2
 * @description In this API implemented the refresh token functionality
 * @version 2.0 - the session cookie is expired then it will refresh the session cookie using the refresh token
 */
usersRouter.post("/v2/verify-session-cookie", async (req, res) => {
  try {
    const { idToken, refreshToken } = req?.body;

    if (!idToken && !refreshToken)
      return res.status(400).json({
        success: false,
        message: "Access token or refresh token is required.",
      });

    // const cookieString = req.headers.cookie || "";
    // const cookies = await parseCookies({ cookieString });
    // const xIdToken = cookies["x-id-token"]?.replace("Bearer ", "");
    const xIdToken = idToken?.replace("Bearer ", "");
    // const refreshToken = cookies["refresh-token"];

    if (!xIdToken && !refreshToken)
      return res
        .status(400)
        .json({ success: false, message: "Session cookie is missing." });

    try {
      const decodedClaims = await verifySessionCookie({
        sessionCookie: xIdToken,
      });
      return res.status(200).json({
        success: true,
        message: "Session cookie successfully verified.",
        user: { ...decodedClaims, idToken: xIdToken },
      });
    } catch (error) {
      if (error.code === "auth/session-cookie-expired" && refreshToken) {
        try {
          const decodedClaims = await refreshSessionCookie({
            refreshToken: refreshToken,
            req,
            res,
          });
          return res.status(200).json({
            success: true,
            message: "Session cookie successfully refreshed.",
            user: decodedClaims,
          });
        } catch (refreshError) {
          return res
            .status(401)
            .json({ success: false, message: refreshError.message });
        }
      }

      if (error.code === "auth/session-cookie-revoked")
        return res.status(401).json({
          success: false,
          message: "Session cookie has been revoked. Please log in again.",
        });

      throw error;
    }
  } catch (error) {
    console.error("Error verifying session cookie:", error);
    return res
      .status(500)
      .json({ success: false, message: "Internal server error." });
  }
});

usersRouter.post(
  "/sessionLogout",
  tryCatch(async (req, res) => {
    const { hostname } = await normalizeDomain(req?.headers?.host);
    const cookieDomain = await createCookiesDomain({ domain: hostname });

    const options = {
      domain: `.${cookieDomain}`,
    };

    res.clearCookie("x-id-token", options);
    res.clearCookie("refresh-token", options);

    res.status(200).json({
      success: true,
      message: "Session logged out successfully.",
    });
  })
);

/**
 * Check Apple relay email(Apple Private Email) is exist in system or not
 */
usersRouter.post(
  "/check-relay-email",
  tryCatch(async (req, res) => {
    try {
      if (req.body) {
        await addDebugLog({
          payload: { body: req?.body, type: "check-relay-email" },
        });
      }
    } catch (error) {
      console.log("🚀 ~ file: users.js:182 ~ tryCatch ~ error:", error);
    }

    let { email = null, socialId = null } = req.body;

    if (!email && !socialId) {
      return res.status(400).json({
        success: false,
        message: "Email or Social Id is required!",
        data: {},
      });
    }

    if (email) email = email.toLowerCase();

    const { data, code } = await service.checkAppleRelayEmail({
      email,
      socialId,
    });

    return res.status(code).json(data);
  })
);

/**
 * SSO Login
 */
usersRouter.post(
  "/sso-login",
  isAuthForFirebase,
  tryCatch(async (req, res) => {
    try {
      if (req.body && req.headers.authorization) {
        await addDebugLog({
          payload: { body: req?.body, token: req?.headers, type: "sso-login" },
        });
      }
    } catch (error) {
      console.log("🚀 ~ file: users.js:220 ~ tryCatch ~ error:", error);
    }

    let {
      first_name,
      last_name,
      preferred_email,
      sub,
      profileImg,
      provider,
      relation_id,
    } = req.body;

    if (!preferred_email) {
      return res.status(400).json("Preferred Email is required!");
    }

    preferred_email = preferred_email.toLowerCase();

    const { hostname } = await normalizeDomain(req.headers.host);
    const { data, code } = await service.ssoLogin({
      first_name,
      last_name,
      preferred_email,
      sub: sub,
      profileImg,
      host: hostname,
      provider,
      ...(req.body.facebookLinkedinId && {
        facebookLinkedinId: req.body.facebookLinkedinId,
      }),
      ...(req.body.socialauth0id && {
        socialauth0id: req.body.socialauth0id,
      }),
      nickname: hostname.includes(BASE_URL) ? hostname.split(".")[0] : null,
      relation_id,
      apple_private_email: req?.body?.apple_private_email || null,
      firebaseEmail: req?.body?.firebaseEmail,
    });

    if (data?.data?.migrationEvent) {
      // MDS migration
      // send mds migration subscription event
      // subscribe to groupos mds tier
      const payload = {
        event: "COMMUNITY_MEMBER_ADDED",
        data: {
          first_name: data.data.migrationEvent.first_name,
          last_name: data.data.migrationEvent.last_name,
          email: data.data.migrationEvent.email,
          edge: data.data.migrationEvent.edge,
          communityOwnerEdge: data.data.migrationEvent.communityOwnerEdge,
        },
      };
      publishMessage(
        JSON.stringify(payload),
        // MESSAGE_QUEUE_BILLING_SERVICE_NAME
        "MONOLITH_COMMUNITY_MEMBER_ADDED"
      );
    }

    const expiresIn = 60 * 60 * 24 * 5 * 1000; // 5 days
    const sessionCookie = await admin
      .auth()
      .createSessionCookie(req.headers.authorization.split("Bearer ")[1], {
        expiresIn,
      });

    const cookieDomain = await createCookiesDomain({ domain: hostname });
    const options = {
      domain: `.${cookieDomain}`,
    };
    res.cookie("x-id-token", `Bearer ${sessionCookie}`, options);
    res.status(code).json(data);
  })
);

/**
 * @title SSO Login v2
 * @description This API is used to login the user using SSO
 * @version 2.0 Implemented the refresh token functionals and add the tokens in the response
 */
usersRouter.post(
  "/v2/sso-login",
  isAuthForFirebaseV2,
  tryCatch(async (req, res) => {
    try {
      if (req.body && req.headers.authorization) {
        await addDebugLog({
          payload: { body: req?.body, token: req?.headers, type: "sso-login" },
        });
      }
    } catch (error) {
      console.log("🚀 ~ file: users.js:220 ~ tryCatch ~ error:", error);
    }

    let {
      first_name,
      last_name,
      preferred_email,
      sub,
      profileImg,
      provider,
      relation_id,
    } = req.body;

    if (!preferred_email) {
      return res.status(400).json("Preferred Email is required!");
    }

    preferred_email = preferred_email.toLowerCase();

    const { hostname } = await normalizeDomain(req.headers.host);
    let { data, code } = await service.ssoLogin({
      first_name,
      last_name,
      preferred_email,
      sub: sub,
      profileImg,
      host: hostname,
      provider,
      ...(req.body.facebookLinkedinId && {
        facebookLinkedinId: req.body.facebookLinkedinId,
      }),
      ...(req.body.socialauth0id && {
        socialauth0id: req.body.socialauth0id,
      }),
      nickname: hostname.includes(BASE_URL) ? hostname.split(".")[0] : null,
      relation_id,
      apple_private_email: req?.body?.apple_private_email || null,
      firebaseEmail: req?.body?.firebaseEmail,
    });

    if (data?.data?.migrationEvent) {
      // MDS migration
      // send mds migration subscription event
      // subscribe to groupos mds tier
      const payload = {
        event: "COMMUNITY_MEMBER_ADDED",
        data: {
          first_name: data.data.migrationEvent.first_name,
          last_name: data.data.migrationEvent.last_name,
          email: data.data.migrationEvent.email,
          edge: data.data.migrationEvent.edge,
          communityOwnerEdge: data.data.migrationEvent.communityOwnerEdge,
        },
      };
      publishMessage(
        JSON.stringify(payload),
        // MESSAGE_QUEUE_BILLING_SERVICE_NAME
        "MONOLITH_COMMUNITY_MEMBER_ADDED"
      );
    }

    if (code === 200 && data) {
      const expiresIn = FIREBASE_TOKEN_EXPIRE_TIME;
      const sessionCookie = await admin
        .auth()
        .createSessionCookie(req.headers.authorization.split("Bearer ")[1], {
          expiresIn,
        });

      data.data.idToken = sessionCookie ?? null;
      data.data.refreshToken = req?.headers["x-firebase-refresh-token"] ?? null;
    }

    // const expiresIn = FIREBASE_TOKEN_EXPIRE_TIME;
    // const sessionCookie = await admin
    //   .auth()
    //   .createSessionCookie(req.headers.authorization.split("Bearer ")[1], {
    //     expiresIn,
    //   });

    // const cookieDomain = await createCookiesDomain({ domain: hostname });
    // const options = {
    //   domain: `.${cookieDomain}`,
    // };
    // res.cookie("x-id-token", `Bearer ${sessionCookie}`, options);
    // res.cookie(
    //   "refresh-token",
    //   req?.headers["x-firebase-refresh-token"],
    //   options
    // );

    res.status(code).json(data);
  })
);

/**
 * Resend Email API
 */
usersRouter.post(
  "/resent-email",
  tryCatch(async (req, res) => {
    let { preferred_email } = req.body;

    const { hostname } = await normalizeDomain(req.headers.host);

    preferred_email = preferred_email.toLowerCase();
    const { data, code } = await service.resendEmail({
      preferred_email,
      domain: hostname,
    });

    return res.status(code).json({ success: true, message: data, data: {} });
  })
);

/**
 * Forgot password API
 */
usersRouter.post(
  "/forgot-password",
  tryCatch(async (req, res) => {
    const { preferred_email } = req.body;

    const { hostname } = await normalizeDomain(req.headers.host);

    const { data, code } = await service.forgotPassword({
      preferred_email,
      domain: hostname,
    });

    return res.status(code).json({ success: true, message: data, data: {} });
  })
);

/**
 * PROTECTED APIS
 */

/**
 * Edge Login
 */
usersRouter.post(
  "/edge-login",
  isAuth,
  tryCatch(async (req, res) => {
    const { userId, userEdgeId } = req.body;

    const { data, code } = await service.edgeLogin({
      userId,
      userEdgeId,
    });

    res.status(code).json(data);
  })
);

/**
 * fetch session info
 */
usersRouter.get(
  "/fetch-session-info",
  isAdminForDefaultEdge,
  tryCatch(async (req, res) => {
    const token = req.headers["authorization"].split(" ")[1];

    const { data, code } = await service.fetchSessionInfo({
      token: token,
      isDefaultEdge: req?.isDefaultEdge ?? false,
    });

    res.status(code).json(data);
  })
);

/**
 * User-info
 */
usersRouter.get(
  "/user-info",
  isAuth,
  tryCatch(async (req, res) => {
    const { userId, relation_id } = req.query;

    if (!userId || !relation_id) {
      throw new Error("Data is required!");
    }
    const { data, code } = await service.fetchUserInfo({
      userId,
      relation_id,
    });

    res.status(code).json(data);
  })
);

usersRouter.post(
  "/import-user",
  isAuth,
  tryCatch(async (req, res) => {
    let { email, first_name, last_name, tiers } = req.body;

    if (!email || !first_name || !last_name) {
      return res.status(400).json("Data is required!");
    }

    if (email === req?.userEmail) {
      return res.status(400).json("You can't invite yourself!");
    }

    if (email) email = email.toLowerCase();

    const { data, code } = await service.importUser({
      email,
      first_name,
      last_name,
      tiers,
      relation_id: req.relation_id,
    });

    return res.status(code).json(data);
  })
);

usersRouter.post(
  "/bulk-import-users",
  isAuth,
  upload.single("file"),
  tryCatch(async (req, res) => {
    const { file } = req;

    if (!file) {
      return res.status(400).json({
        status: false,
        message: "File upload failed.",
      });
    }

    const { data, code } = await service.bulkImportUsers({
      relation_id: req.relation_id,
      tempFilePath: file.path,
    });

    return res.status(code).json(data);
  })
);

/**
 * Get all invite user API
 */
usersRouter.get(
  "/get-import-user",
  isAuth,
  tryCatch(async (req, res) => {
    const {
      page,
      limit,
      search,
      sortBy,
      sortOrder,
      isAllData = false,
      type,
    } = req.query;

    const { data, code } = await service.getImportUser({
      page,
      limit,
      search,
      sortBy,
      sortOrder,
      relation_id: req.relation_id,
      isAllData: stringToBoolean(isAllData),
      type,
    });

    return res.status(code).json(data);
  })
);

/**
 * Update invite user API
 */
usersRouter.put(
  "/update-import-user/:id",
  isAuth,
  tryCatch(async (req, res) => {
    if (!req.params.id) {
      return res.status(400).json("Import user id is require");
    }

    // if (!req.body.first_name && req.body.last_name) {
    //   return res.status(400).json("First name and Last name is require");
    // }

    const { data, code } = await service.updateImportUser({
      id: req?.params?.id,
      body: req.body,
    });

    return res.status(code).json(data);
  })
);

/**
 * Resend the invite user API
 */
usersRouter.put(
  "/resend-invite-user/:id",
  isAuth,
  tryCatch(async (req, res) => {
    const { id } = req.params;

    if (!id) {
      return res.status(code).json("invite user id required!");
    }

    const { data, code } = await service.resendInviteUser({ id });

    return res.status(code).json(data);
  })
);

/**
 * Revoke invite user by Id API
 */
usersRouter.put(
  "/revoke-import-user/:id",
  isAuth,
  tryCatch(async (req, res) => {
    if (!req.params.id) {
      return res.status(400).json("Import user id is required");
    }

    const { data, code } = await service.revokeImportUserById({
      id: req?.params?.id,
    });

    return res.status(code).json(data);
  })
);

/**
 * Get invite user by Id API
 */
usersRouter.get(
  "/get-import-user-by-id/:id",
  isAuth,
  tryCatch(async (req, res) => {
    if (!req.params.id) {
      return res.status(400).json("Import user id is require");
    }

    const { data, code } = await service.getImportUserById({
      id: req?.params?.id,
    });

    return res.status(code).json(data);
  })
);

usersRouter.get(
  "/get-import-user-by-email",
  tryCatch(async (req, res) => {
    const { email, relation_id } = req.query;

    const { data, code } = await service.getImportUserByEmail({
      relation_id: relation_id,
      email,
    });

    return res.status(code).json(data);
  })
);

/**
 * Get invite user by Id API for the billing service
 */
usersRouter.post(
  "/fetch-import-user",
  tryCatch(async (req, res) => {
    const { id, relation_id, email } = req.body;

    if (!id && !relation_id && !email) {
      return res.status(400).json("Data required!");
    }

    const { data, code } = await service.fetchImportUser({
      id,
      relation_id,
      email,
    });

    return res.status(code).json(data);
  })
);

/**
 * Get suggestion list for the invite user
 */
usersRouter.get(
  "/get-suggestion-import-user",
  isAuth,
  tryCatch(async (req, res) => {
    const { status } = req.query;

    const { data, code } = await service.getSuggestionImportUser({
      relation_id: req.relation_id,
      status,
    });

    return res.status(code).json(data);
  })
);

/*------------Create Invite guest-----------*/
/*
 * Create Invite Guest User API
 */
usersRouter.post(
  "/create-invite-guest-user",
  isAuth,
  tryCatch(async (req, res) => {
    const { email, first_name, last_name, type } = req.body;

    if (!email || !first_name || !last_name) {
      return res.status(400).json("Data is required!");
    }

    if (email === req?.userEmail) {
      return res.status(400).json("You can't invite yourself!");
    }

    const { data, code } = await service.inviteGuestUserCreate({
      email,
      first_name,
      last_name,
      type,
      relation_id: req.relation_id,
    });

    return res.status(code).json(data);
  })
);

/*
 * Bulk invite guest user api
 */
usersRouter.post(
  "/bulk-invite-guest-users",
  isAuth,
  upload.single("file"),
  tryCatch(async (req, res) => {
    const { file } = req;
    if (!file) {
      return res.status(400).json({
        status: false,
        message: "File upload failed.",
      });
    }
    const { data, code } = await service.bulkInviteGuestUsers({
      relation_id: req.relation_id,
      tempFilePath: file.path,
    });

    res.status(code).json(data);
  })
);

/*
 * get invite guest user
 */
usersRouter.get(
  "/get-invite-guest-user",
  isAuth,
  tryCatch(async (req, res) => {
    const {
      page,
      limit,
      search,
      sortBy,
      sortOrder,
      isAllData = false,
      type,
    } = req.query;

    const { data, code } = await service.getInviteGuestUserList({
      page,
      limit,
      search,
      sortBy,
      sortOrder,
      relation_id: req.relation_id,
      isAllData: stringToBoolean(isAllData),
      type,
    });

    return res.status(code).json(data);
  })
);

/**
 * Update invite guest user API
 */
usersRouter.put(
  "/update-invite-guest-user/:id",
  isAuth,
  tryCatch(async (req, res) => {
    if (!req.params.id) {
      return res.status(400).json("invite guest user id is require");
    }

    if (!req.body.first_name && req.body.last_name) {
      return res.status(400).json("First name and Last name is require");
    }

    const { data, code } = await service.updateInviteGuestUser({
      id: req?.params?.id,
      body: req.body,
    });

    return res.status(code).json(data);
  })
);

/**
 * Resend the guest invite user API
 */

usersRouter.put(
  "/resend-invite-guest-user/:id",
  isAuth,
  tryCatch(async (req, res) => {
    const { id } = req.params;

    if (!id) {
      return res.status(code).json("invite guest user id required!");
    }

    const { data, code } = await service.resendInviteGuestUser({ id });

    return res.status(code).json(data);
  })
);

/**
 * Revoke guest invite user by Id API
 */
usersRouter.put(
  "/revoke-invite-guest-user/:id",
  isAuth,
  tryCatch(async (req, res) => {
    if (!req.params.id) {
      return res.status(400).json("invite guest user id is required");
    }

    const { data, code } = await service.revokeInviteGuestUserById({
      id: req?.params?.id,
    });

    return res.status(code).json(data);
  })
);

/**
 * Get invite user by Id API
 */
usersRouter.get(
  "/get-invite-guest-user-by-id/:id",
  isAuth,
  tryCatch(async (req, res) => {
    if (!req.params.id) {
      return res.status(400).json("invite guest user id is require");
    }

    const { data, code } = await service.getInviteGuestUserById({
      id: req?.params?.id,
    });

    return res.status(code).json(data);
  })
);

/**
 * Get suggestion list for the invite user
 */
usersRouter.get(
  "/get-suggestion-invite-guest-user",
  isAuth,
  tryCatch(async (req, res) => {
    const { status } = req.query;

    const { data, code } = await service.getSuggestionInviteGuestUser({
      relation_id: req.relation_id,
      status,
    });

    return res.status(code).json(data);
  })
);

/**
 * Revoke guest invite user by Id API
 */
usersRouter.put(
  "/inactive-invite-guest-user/:id",
  isAuth,
  tryCatch(async (req, res) => {
    if (!req.params.id) {
      return res.status(400).json("invite guest user id is required");
    }

    const { data, code } = await service.inActiveInviteGuestUserById({
      id: req?.params?.id,
    });

    return res.status(code).json(data);
  })
);

/**
 * Get all community guest and member user API
 */
usersRouter.get(
  "/community-members-guest-users",
  isAuth,
  tryCatch(async (req, res) => {
    const { page, limit, expand, relation_id, search, type } = req.query;

    const { code, data } = await service.getAllCommunityMembersAndGuest({
      page,
      limit,
      expand,
      relation_id,
      search,
      type,
    });

    res.status(code).json(data);
  })
);

/**
 * Platform owners
 */
usersRouter.post(
  "/platform-owners",
  isAuth,
  tryCatch(async (req, res) => {
    const { first_name, last_name, preferred_email, role_id } = req.body;

    const { code, data } = await service.createPlatformOwner({
      first_name,
      last_name,
      preferred_email,
      role_id,
    });

    res.status(code).json(data);
  })
);

usersRouter.get(
  "/platform-owners",
  isAuth,
  tryCatch(async (req, res) => {
    const { page, limit, user_id, expand, search } = req.query;

    const { code, data } = await service.getPlatformOwners({
      page,
      limit,
      expand: stringToBoolean(expand),
      user_id,
      search,
    });

    res.status(code).json(data);
  })
);

usersRouter.get(
  "/platform-owners-suggestion",
  isAuth,
  tryCatch(async (req, res) => {
    const { user_id, expand } = req.query;

    const { code, data } = await service.getPlatformOwnersSuggestion({
      expand: stringToBoolean(expand),
      user_id,
    });

    res.status(code).json(data);
  })
);

usersRouter.get(
  "/platform-owners/:platform_owner_id",
  isAuth,
  tryCatch(async (req, res) => {
    const { platform_owner_id } = req.params;
    const { expand } = req.query;

    const { code, data } = await service.getPlatformOwner({
      platform_owner_id,
      expand: stringToBoolean(expand),
    });

    res.status(code).json(data);
  })
);

usersRouter.patch(
  "/platform-owners/:platform_owner_id",
  isAuth,
  tryCatch(async (req, res) => {
    const { platform_owner_id } = req.params;
    const { first_name, last_name, preferred_email, role_id } = req.body;

    const { code, data } = await service.editPlatformOwner({
      platform_owner_id,
      first_name,
      last_name,
      preferred_email,
      role_id,
    });

    res.status(code).json(data);
  })
);

usersRouter.delete(
  "/platform-owners/:platform_owner_id",
  isAuth,
  tryCatch(async (req, res) => {
    const { platform_owner_id } = req.params;

    const { code, data } = await service.removePlatformOwner({
      platform_owner_id,
    });

    res.status(code).json(data);
  })
);

/**
 * Community owners
 */

usersRouter.post(
  "/community-owners",
  isAuth,
  tryCatch(async (req, res) => {
    const {
      first_name,
      last_name,
      preferred_email,
      role_id,
      modules,
      owner_id,
      communityName,
    } = req.body;

    const { code, data } = await service.createCommunityOwner({
      first_name,
      last_name,
      preferred_email,
      role_id,
      modules,
      owner_id,
      relation_id: req.relation_id,
      communityName,
    });

    // send community owner creation event
    const payload = {
      event: "COMMUNITY_OWNER_ADDED",
      data: {
        edge: data.edge,
        user_id: data.user_id,
        owner_id: data.owner_id,
        modules: data.modules,
        relation_id: data.relation_id,
      },
    };
    // publishMessage(JSON.stringify(payload), MESSAGE_QUEUE_BILLING_SERVICE_NAME);
    publishMessage(JSON.stringify(payload), "MONOLITH_COMMUNITY_OWNER_ADDED");
    res.status(code).json(data);
  })
);

usersRouter.get(
  "/community-owners",
  isAuth,
  tryCatch(async (req, res) => {
    const { page, limit, user_id, expand, search } = req.query;

    const { code, data } = await service.getCommunityOwners({
      page,
      limit,
      expand: stringToBoolean(expand),
      user_id,
      relation_id: req.relation_id,
      search,
    });

    res.status(code).json(data);
  })
);

usersRouter.get(
  "/community-owners-suggestion",
  isAuth,
  tryCatch(async (req, res) => {
    const { page, limit, user_id, expand, search } = req.query;

    const { code, data } = await service.getCommunityOwnersSuggestions({
      expand: stringToBoolean(expand),
      user_id,
      relation_id: req.relation_id,
    });

    res.status(code).json(data);
  })
);

usersRouter.get(
  "/community-owners/:community_owner_id",
  isAuth,
  tryCatch(async (req, res) => {
    const { community_owner_id } = req.params;
    const { expand } = req.query;

    const { code, data } = await service.getCommunityOwner({
      community_owner_id,
      relation_id: req.relation_id,
      expand: stringToBoolean(expand),
    });

    res.status(code).json(data);
  })
);

usersRouter.patch(
  "/community-owners/:community_owner_id",
  isAuth,
  tryCatch(async (req, res) => {
    const { community_owner_id } = req.params;
    const { first_name, last_name, preferred_email, role_id, modules } =
      req.body;

    const { code, data } = await service.editCommunityOwner({
      community_owner_id,
      first_name,
      last_name,
      preferred_email,
      role_id,
      modules,
      relation_id: req.relation_id,
    });

    // send community owner updated event
    const payload = {
      event: "COMMUNITY_OWNER_UPDATED",
      data: {
        edge: data.edge,
        modules: data.modules,
      },
    };
    // publishMessage(JSON.stringify(payload), MESSAGE_QUEUE_BILLING_SERVICE_NAME);
    publishMessage(JSON.stringify(payload), "MONOLITH_COMMUNITY_OWNER_UPDATED");
    res.status(code).json(data);
  })
);

usersRouter.delete(
  "/community-owners/:community_owner_id",
  isAuth,
  tryCatch(async (req, res) => {
    const { community_owner_id } = req.params;

    const { code, data } = await service.removeCommunityOwner({
      community_owner_id,
      relation_id: req.relation_id,
    });

    // send community owner updated event
    const payload = {
      event: "COMMUNITY_OWNER_REMOVED",
      data: {
        edge: data.edge,
      },
    };
    publishMessage(JSON.stringify(payload), "MONOLITH_COMMUNITY_OWNER_REMOVED");
    res.status(code).json(data);
  })
);

/**
 * Financial Analytics
 */

usersRouter.get(
  "/platform-users",
  isAuth,
  tryCatch(async (req, res) => {
    const { page, limit, expand, search } = req.query;

    const { code, data } = await service.getPlatformUsers({
      page,
      limit,
      expand: stringToBoolean(expand),
      search,
    });

    res.status(code).json(data);
  })
);

usersRouter.get(
  "/platform-users-suggestions",
  isAuth,
  tryCatch(async (req, res) => {
    const { expand } = req.query;

    const { code, data } = await service.getPlatformUsersSuggestions({
      expand: stringToBoolean(expand),
    });

    res.status(code).json(data);
  })
);

usersRouter.get(
  "/community-members",
  isAuth,
  tryCatch(async (req, res) => {
    const { page, limit, expand, relation_id, search, membership } = req.query;

    const { code, data } = await service.getCommunityMembers({
      page,
      limit,
      expand: stringToBoolean(expand),
      relation_id,
      search,
      membership,
    });

    res.status(code).json(data);
  })
);

usersRouter.get(
  "/community-guests",
  isAuth,
  tryCatch(async (req, res) => {
    const { page, limit, expand, relation_id, search } = req.query;

    const { code, data } = await service.getCommunityGuests({
      page,
      limit,
      expand: stringToBoolean(expand),
      relation_id,
      search,
    });

    res.status(code).json(data);
  })
);

usersRouter.get(
  "/community-members-suggestions",
  isAuth,
  tryCatch(async (req, res) => {
    const { expand, relation_id, membership } = req.query;

    const { code, data } = await service.getCommunityMembersSuggestion({
      expand: stringToBoolean(expand),
      relation_id,
      membership,
    });

    res.status(code).json(data);
  })
);

usersRouter.get(
  "/community-guests-suggestions",
  isAuth,
  tryCatch(async (req, res) => {
    const { expand, relation_id } = req.query;

    const { code, data } = await service.getCommunityGuestsSuggestion({
      expand: stringToBoolean(expand),
      relation_id,
    });

    res.status(code).json(data);
  })
);

usersRouter.get(
  "/get-user-by-id",
  isAuth,
  tryCatch(async (req, res) => {
    const { user_id } = req?.query;

    const { code, data } = await service.getUserById({
      user_id,
      auth_user_id: req?.userId,
    });

    return res.status(code).json(data);
  })
);

usersRouter.put(
  "/update-user-by-id/:id",
  isAuth,
  tryCatch(async (req, res) => {
    const { id } = req?.params;

    const { code, data } = await service.updateUserById({
      user_id: id,
      auth_user_id: req?.userId,
      body: req.body,
    });

    return res.status(code).json(data);
  })
);

usersRouter.get(
  "/active-community-members",
  isAuth,
  tryCatch(async (req, res) => {
    const { code, data } = await service.getActiveCommunityMembers({
      relation_id: req.relation_id,
    });

    res.status(code).json(data);
  })
);

usersRouter.get(
  "/all-community-members",
  isAuth,
  tryCatch(async (req, res) => {
    const { code, data } = await service.getAllCommunityMembers({
      relation_id: req.relation_id,
    });

    res.status(code).json(data);
  })
);
usersRouter.get(
  "/all-community-members-subcription",
  isAuth,
  tryCatch(async (req, res) => {
    const { code, data } = await service.getAllSubcriptionCommunityMembers({
      relation_id: req.relation_id,
    });

    res.status(code).json(data);
  })
);

usersRouter.get(
  "/community-visitors",
  isAuth,
  tryCatch(async (req, res) => {
    const { page, limit, expand, relation_id } = req.query;

    const { code, data } = await service.getCommunityVisitors({
      page,
      limit,
      expand: stringToBoolean(expand),
      relation_id,
    });

    res.status(code).json(data);
  })
);

usersRouter.get(
  "/system-users",
  isAuth,
  tryCatch(async (req, res) => {
    const { page, limit, search } = req.query;

    const { code, data } = await service.getSystemUsers({
      page,
      limit,
      search,
    });
    res.status(code).json(data);
  })
);

usersRouter.get(
  "/system-users-suggestions",
  isAuth,
  tryCatch(async (req, res) => {
    const { code, data } = await service.getSystemUsersSuggestion({});

    res.status(code).json(data);
  })
);

/**
 * Check that the Preferred Email is exists of not
 */
usersRouter.post(
  "/check-email-exists",
  tryCatch(async (req, res) => {
    try {
      if (req.body) {
        await addDebugLog({
          payload: { body: req?.body, type: "check-email-exists" },
        });
      }
    } catch (error) {
      console.log("🚀 ~ file: users.js:1303 ~ tryCatch ~ error:", error);
    }

    if (!req?.body?.preferred_email) {
      return res.status(400).json({
        status: false,
        message: "Email is require!",
        data: {},
      });
    }

    req.body.preferred_email = req?.body?.preferred_email.toLowerCase();
    const { code, data } = await service.checkEmailExists({
      email: req.body.preferred_email,
    });

    return res.status(code).json(data);
  })
);

/**
 * Add the Social Login Provider if not exists
 */
usersRouter.post(
  "/add-social-provider",
  isAuthForFirebase,
  tryCatch(async (req, res) => {
    try {
      if (req.body && req.headers.authorization) {
        await addDebugLog({
          payload: { body: req?.body, type: "add-social-provider" },
        });
      }
    } catch (error) {
      console.log("🚀 ~ file: users.js:1336 ~ tryCatch ~ error:", error);
    }

    if (!req?.body?.providerName || !req?.body?.preferred_email) {
      return res.status(400).json({
        status: false,
        message: "Provider Name and Preferred Email Email are require!",
        data: {},
      });
    }

    if (!SOCIAL_PROVIDERS.includes(req?.body?.providerName)) {
      return res.status(400).json({
        status: false,
        message: `Provider ${req?.body?.providerName} is not supported!`,
        data: {},
      });
    }

    req.body.preferred_email = req?.body?.preferred_email.toLowerCase();
    const { code, data } = await service.addProviderEmail({
      preferred_email: req?.body?.preferred_email,
      name: req?.body?.providerName,
      email: req?.body?.providerEmail || null,
      firebaseId: req.body.sub,
      socialProviderId: req.socialId || null,
    });

    return res.status(code).json(data);
  })
);

/**
 * API for the invite the collaborator user by admin
 */

usersRouter.post(
  "/invite-collaborator-user-by-admin/:invitee_id",
  isAuth,
  tryCatch(async (req, res) => {
    const { first_name, last_name, email, scope } = req.body;
    if (req?.currentEdge?.type !== "CO") {
      return res.status(401).json({
        success: false,
        message: "Unauthorized!",
        data: {},
      });
    }

    const { invitee_id } = req?.params;
    if (!invitee_id || !mongoose.Types.ObjectId.isValid(invitee_id)) {
      return res.status(400).json({
        success: false,
        message: "Invitee ID is not valid!",
        data: {},
      });
    }

    const { code, data } = await service.inviteCollaboratorUser({
      first_name,
      last_name,
      email,
      invitee_id,
      scope,
      relation_id: req?.relation_id,
    });
    return res.status(code).json(data);
  })
);

/**
 * API for the invite the collaborator user from user side
 */
usersRouter.post(
  "/invite-collaborator-user",
  isAuth,
  tryCatch(async (req, res) => {
    const { first_name, last_name, email, scope } = req.body;
    if (req.currentEdge.type !== "M") {
      return res.status(401).json({
        success: false,
        message: "Unauthorized!",
        data: {},
      });
    }

    const { code, data } = await service.inviteCollaboratorUser({
      first_name,
      last_name,
      email,
      invitee_id: req?.authUserId,
      scope,
      relation_id: req?.relation_id,
    });
    return res.status(code).json(data);
  })
);

/**
 * Get all invite collaborator user API
 */
usersRouter.get(
  "/get-all-invite-collaborate-user",
  isAuth,
  tryCatch(async (req, res) => {
    const {
      page,
      limit,
      search,
      sortBy,
      sortOrder,
      isAllData = false,
      type,
      tier_id,
    } = req.query;

    const { data, code } = await service.getInviteCollaboratorUser({
      page,
      limit,
      search,
      sortBy,
      sortOrder,
      relation_id: req?.relation_id,
      isAllData: stringToBoolean(isAllData),
      type,
      ...(req?.currentEdge?.type === "M" && { invitee_id: req?.authUserId }),
      tier_id,
    });

    return res.status(code).json(data);
  })
);

/**
 * Get suggestion list for the invite collaborator user
 */
usersRouter.get(
  "/get-suggestion-invite-collaborator-user",
  isAuth,
  tryCatch(async (req, res) => {
    const { status, tier_id } = req.query;

    const { data, code } = await service.getSuggestionInviteCU({
      relation_id: req?.relation_id,
      status,
      ...(req?.currentEdge?.type === "M" && { invitee_id: req?.authUserId }),
      tier_id,
    });

    return res.status(code).json(data);
  })
);

/**
 * Get invite collaborator user by Id API
 */
usersRouter.get(
  "/get-invite-collaborate-user/:id",
  isAuth,
  tryCatch(async (req, res) => {
    if (!req?.params?.id) {
      return res.status(400).json({
        success: false,
        message: "Import user id is require",
        data: {},
      });
    }

    const { data, code } = await service.getInviteCUById({
      id: req?.params?.id,
    });

    return res.status(code).json(data);
  })
);

/**
 * API for the invite the collaborator user by admin
 */
usersRouter.put(
  "/update-invite-collaborator-by-admin/:id",
  isAuth,
  tryCatch(async (req, res) => {
    const { first_name, last_name, scope } = req.body;
    if (req.currentEdge.type !== "CO") {
      return res.status(401).json({
        success: false,
        message: "Unauthorized!",
        data: {},
      });
    }

    const { id } = req?.params;
    if (!id || !mongoose.Types.ObjectId.isValid(id)) {
      return res.status(400).json({
        success: false,
        message: "Invitee ID is not valid!",
        data: {},
      });
    }

    const { code, data } = await service.updateInviteCU({
      id,
      first_name,
      last_name,
      scope,
      relation_id: req?.relation_id,
    });
    return res.status(code).json(data);
  })
);

/**
 * API for the invite the collaborator user by user
 */
usersRouter.put(
  "/update-invite-collaborator-by-user/:id",
  isAuth,
  tryCatch(async (req, res) => {
    const { first_name, last_name, scope } = req.body;

    if (req.currentEdge.type !== "M") {
      return res.status(401).json({
        success: false,
        message: "Unauthorized!",
        data: {},
      });
    }

    const { id } = req?.params;
    if (!id || !mongoose.Types.ObjectId.isValid(id)) {
      return res.status(400).json({
        success: false,
        message: "Invitee ID is not valid!",
        data: {},
      });
    }

    const { code, data } = await service.updateInviteCU({
      id,
      first_name,
      last_name,
      scope,
      relation_id: req?.relation_id,
      invitee_id: req?.authUserId,
    });
    return res.status(code).json(data);
  })
);

/**
 * API for the revoke the invite collaborator user by admin
 */
usersRouter.put(
  "/revoke-invite-collaborator-by-admin/:id",
  isAuth,
  tryCatch(async (req, res) => {
    if (req.currentEdge.type !== "CO") {
      return res.status(401).json({
        success: false,
        message: "Unauthorized!",
        data: {},
      });
    }

    const { id } = req?.params;
    if (!id || !mongoose.Types.ObjectId.isValid(id)) {
      return res.status(400).json({
        success: false,
        message: "Invitee ID is not valid!",
        data: {},
      });
    }

    const { code, data } = await service.revokeInviteCU({
      id,
      relation_id: req?.relation_id,
    });
    return res.status(code).json(data);
  })
);

/**
 * API for the revoke the invite collaborator user by user
 */
usersRouter.put(
  "/revoke-invite-collaborator-by-user/:id",
  isAuth,
  tryCatch(async (req, res) => {
    if (req.currentEdge.type !== "M") {
      return res.status(401).json({
        success: false,
        message: "Unauthorized!",
        data: {},
      });
    }

    const { id } = req?.params;
    if (!id || !mongoose.Types.ObjectId.isValid(id)) {
      return res.status(400).json({
        success: false,
        message: "Invitee ID is not valid!",
        data: {},
      });
    }

    const { code, data } = await service.revokeInviteCU({
      id,
      relation_id: req?.relation_id,
      invitee_id: req?.authUserId,
    });
    return res.status(code).json(data);
  })
);

/**
 * API for the revoke the invite collaborator user by user and admin
 */
usersRouter.put(
  "/resend-invite-collaborator/:id",
  isAuth,
  tryCatch(async (req, res) => {
    if (req.currentEdge.type !== "M" && req.currentEdge.type !== "CO") {
      return res.status(401).json({
        success: false,
        message: "Unauthorized!",
        data: {},
      });
    }

    const { id } = req?.params;
    if (!id || !mongoose.Types.ObjectId.isValid(id)) {
      return res.status(400).json({
        success: false,
        message: "Invitee ID is not valid!",
        data: {},
      });
    }

    const { code, data } = await service.resendInviteCU({
      id,
      relation_id: req?.relation_id,
      ...(req?.currentEdge?.type === "M" && { invitee_id: req?.authUserId }),
    });
    return res.status(code).json(data);
  })
);

usersRouter.put(
  "/revoke-all-collabrator",
  isAuth,
  tryCatch(async (req, res) => {
    if (req.currentEdge.type !== "M" && req.currentEdge.type !== "CO") {
      return res.status(401).json({
        success: false,
        message: "Unauthorized!",
        data: {},
      });
    }

    if (!req?.authUserId || !mongoose.Types.ObjectId.isValid(req?.authUserId)) {
      return res.status(400).json({
        success: false,
        message: "Invitee ID is not valid!",
        data: {},
      });
    }

    const { tier_id } = req.body;
    if (tier_id && !mongoose.Types.ObjectId.isValid(tier_id)) {
      return res.status(400).json({
        success: false,
        message: "Invalid tier ID!",
        data: {},
      });
    }

    const { code, data } = await service.revokeAllCollaborators({
      invitee_id: req?.authUserId,
      relation_id: req?.relation_id,
      tier_id: tier_id || null,
    });
    return res.status(code).json(data);
  })
);
usersRouter.post(
  "/reconnect-all-collabrator",
  isAuth,
  tryCatch(async (req, res) => {
    if (req.currentEdge.type !== "M" && req.currentEdge.type !== "CO") {
      return res.status(401).json({
        success: false,
        message: "Unauthorized!",
        data: {},
      });
    }

    if (!req?.authUserId || !mongoose.Types.ObjectId.isValid(req?.authUserId)) {
      return res.status(400).json({
        success: false,
        message: "Invitee ID is not valid!",
        data: {},
      });
    }

    const { tier_id } = req.body;
    if (tier_id && !mongoose.Types.ObjectId.isValid(tier_id)) {
      return res.status(400).json({
        success: false,
        message: "Invalid tier ID!",
        data: {},
      });
    }

    const { code, data } = await service.reconnectAllCollaborators({
      invitee_id: req?.authUserId,
      relation_id: req?.relation_id,
      tier_id: tier_id || null,
    });
    return res.status(code).json(data);
  })
);

/**
 * API for the Migrate Invite collaborator from MDS to groupos
 */
usersRouter.post(
  "/migrate-invite-collaborator",
  isAuth,
  tryCatch(async (req, res) => {
    if (
      req.currentEdge.type !== "CO" ||
      req.currentEdge.relation_id.nickname !== "mds"
    ) {
      return res.status(401).json({
        success: false,
        message: "Unauthorized!",
        data: {},
      });
    }

    const { records } = req.body;
    if (!records.length) {
      return res.status(400).json({
        success: false,
        message: "records is required!",
        data: {},
      });
    }

    const { code, data } = await service.migrateInviteCU({
      relation_id: req?.relation_id,
      data: records,
    });
    return res.status(code).json(data);
  })
);

/**
 * API for the Migrate Invite collaborator from MDS to groupos
 */
usersRouter.post(
  "/get-invite-cu-list-billing",
  tryCatch(async (req, res) => {
    const { subscriptionData, linked_subscription_number } = req.body;
    if (!subscriptionData.length) {
      return res.status(400).json({
        success: false,
        message: "subscription data is required!",
        data: {},
      });
    }

    if (!linked_subscription_number) {
      return res.status(400).json({
        success: false,
        message: "Link subscription is not found!",
        data: {},
      });
    }

    const { code, data } = await service.getInviteCUForTierUpdate({
      subscriptionData,
      linked_subscription_number,
    });
    return res.status(code).json(data);
  })
);

/**
 * Check User is ready for login or not
 */
usersRouter.post(
  "/check-user-login-status",
  tryCatch(async (req, res) => {
    const { preferred_email } = req.body;

    const { data, code } = await service.checkUserLoginStatus({
      preferred_email,
    });

    return res.status(code).json(data);
  })
);

/**
 * Add user login error logs
 */
usersRouter.post(
  "/user-login-error-logs",
  tryCatch(async (req, res) => {
    if (!req?.body.data) {
      return res
        .status(400)
        .json({ message: "Missing required parameters: data" });
    }

    const { data, code } = await service.addUserLogInErrorLog({
      body: req?.body,
    });

    return res.status(code).json(data);
  })
);

usersRouter.get(
  "/no-social-provider",
  isAuth,
  tryCatch(async (req, res) => {
    const { code, data } = await service.getAllNoSocialProviderMembers({
      relation_id: req.relation_id,
    });
    res.status(code).json(data);
  })
);
usersRouter.post(
  "/connect-social-provider",
  tryCatch(async (req, res) => {
    let { connectTo, connectFrom } = req.body;
    if (!connectTo || !connectFrom) {
      res.status(400).json({
        status: false,
        message: "connectTo and connectFrom field is required",
        data: [],
      });
    }
    const { hostname } = await normalizeDomain(req.headers.host);
    const { code, data } = await service.connectSocialAccountMember({
      connectTo,
      connectFrom,
      domain: hostname,
    });
    if (code == 200) {
      res.status(code).json({ status: true, message: data, data: [] });
    } else {
      res.status(code).json({ status: false, message: data, data: [] });
    }
  })
);

usersRouter.get(
  "/community-block-members",
  isAuth,
  tryCatch(async (req, res) => {
    const { page, limit, relation_id, search, membership } = req.query;

    const { code, data } = await service.getCommunityBlockedMembers({
      page,
      limit,
      expand: true,
      relation_id,
      search,
      membership,
    });

    res.status(code).json(data);
  })
);

usersRouter.get(
  "/community-block-members-suggestions",
  isAuth,
  tryCatch(async (req, res) => {
    const { relation_id, membership } = req.query;

    const { code, data } = await service.getCommunityBlockedMembersSuggestion({
      expand: true,
      relation_id,
      membership,
    });

    res.status(code).json(data);
  })
);
usersRouter.post(
  "/create-migration-user-database",
  isAuth,
  tryCatch(async (req, res) => {
    const { jsonData } = req.body;

    if (!jsonData || !jsonData.length) {
      return res.status(400).json({
        success: false,
        message: "Missing required parameters: jsonData",
        data: {},
      });
    }

    const { data, code } = await service.createUserForMigration({
      data: jsonData,
    });

    return res.status(code).json(data);
  })
);

/**
 * Create the API for generate intercom JWT token
 * @param {string} token - Need to pass the CO token
 * @param {string} user_id - Need to pass the user id
 * @param {string} email - Need to pass the user email
 * @return {string} - return the JWT token
 * @description - This API is used to generate the JWT token for intercom
 */
usersRouter.get(
  "/generate-intercom-jwt",
  isAuth,
  tryCatch(async (req, res) => {
    const { userId, userEmail, currentEdge } = req;

    if (currentEdge.type !== "CO") {
      return res.status(401).json({
        success: false,
        message: "Unauthorized!",
        data: {},
      });
    }

    if (!userId || !userEmail) {
      return res.status(400).json({
        success: false,
        message: "Invalid token data!",
        data: {},
      });
    }

    const jwtToken = await service.generateIntercomJWT({
      user_id: userId,
      email: userEmail,
    });

    if (!jwtToken) {
      return res.status(500).json({
        success: false,
        message: "Failed to generate JWT token!",
        data: {},
      });
    }

    return res.status(200).json({
      success: true,
      message: "JWT token generated successfully!",
      data: jwtToken,
    });
  })
);

module.exports = usersRouter;
