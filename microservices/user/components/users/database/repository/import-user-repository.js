// const { users } = require("../models");
const inviteUsers = require("../models/invite-users");

class UserRepository {
  //* Create the import user
  createImportUser = async ({ payload }) => {
    return await inviteUsers.create(payload);
  };

  //* get User filter
  getImportUser = async ({ filter }) => {
    return await inviteUsers.findOne(filter).sort({ createdAt: -1 }).lean();
  };

  //* Get all invite user list count
  getAllImportUserCount = async ({ filter }) => {
    return await inviteUsers.countDocuments(filter);
  };

  //* Get all invite user list
  getAllImportUser = async ({
    filter,
    skip = 0,
    limit = 100,
    sortBy,
    sortOrder,
    isAllData = false,
  }) => {
    if (isAllData) {
      return await inviteUsers.find(filter).sort({ [sortBy]: sortOrder });
    } else {
      return await inviteUsers
        .find(filter)
        .sort({ [sortBy]: sortOrder })
        .skip(skip)
        .limit(limit)
        .lean();
    }
  };

  //* Get invite user suggestion list
  getSuggestionImportUser = async ({ filter }) => {
    return await inviteUsers
      .find(filter)
      .sort({ first_name: 1 })
      .select("first_name last_name email -__v")
      .lean();
  };

  //* Update invited user data
  updateImportUser = async ({ filter, data }) => {
    return await inviteUsers.findOneAndUpdate(
      filter,
      { $set: data },
      { new: true }
    );
  };
}

module.exports = UserRepository;
