const inviteCollaborators = require("../models/invite-collaborators");

class inviteCollaboratorsRepository {
  //* Create the invite collaborators
  createInviteCollaborators = async ({ payload }) => {
    return await inviteCollaborators.create(payload);
  };

  //* get invite collaborators filter
  getInviteCollaborators = async ({ filter, expand = false }) => {
    if (expand) {
      const user = {
        path: "user_id",
        select: "_id first_name last_name 'Preferred Email'",
      };

      const invitee = {
        path: "invitee_id",
        select: "_id first_name last_name 'Preferred Email'",
      };
      return await inviteCollaborators
        .findOne(filter)
        .populate([user, invitee])
        .lean();
    } else {
      return await inviteCollaborators.findOne(filter).lean();
    }
  };

  getAllRevokeInviteCollaborators = async ({ filter, expand = false }) => {
    if (expand) {
      const user = {
        path: "user_id",
        select: "_id first_name last_name 'Preferred Email'",
      };

      const invitee = {
        path: "invitee_id",
        select: "_id first_name last_name 'Preferred Email'",
      };
      return await inviteCollaborators
        .find(filter) // Changed to find() for multiple results
        .populate([user, invitee])
        .lean();
    } else {
      return await inviteCollaborators.find(filter).lean(); // Changed to find() for multiple results
    }
  };

  //* Get all invite collaborators list count
  getAllInviteCollaboratorsCount = async ({ pipeline }) => {
    return (await inviteCollaborators.aggregate(pipeline)).length;
  };

  //* Get all invite collaborators list
  getAllInviteCollaborators = async ({
    pipeline,
    skip = 0,
    limit = 100,
    sortBy,
    sortOrder,
    isAllData = false,
  }) => {
    let data;
    if (isAllData) {
      data = await inviteCollaborators.aggregate([
        ...pipeline,
        { $sort: { [sortBy]: sortOrder } },
      ]);
    } else {
      data = await inviteCollaborators.aggregate([
        ...pipeline,
        { $sort: { [sortBy]: sortOrder } },
        { $skip: skip },
        { $limit: limit },
      ]);
    }
    data.forEach((detail) => {
      if (detail?.invitee_user) {
        detail.invitee_user.groupos_join_date =
          detail?.invitee_user?.groupos_join_date ?? null;
        detail.invitee_user.migration_date =
          detail?.invitee_user?.migration_date ?? null;
      }
      if (detail?.user) {
        detail.user.groupos_join_date = detail?.user?.groupos_join_date ?? null;
        detail.user.migration_date = detail?.user?.migration_date ?? null;
      }
    });
    return data;
  };

  //* Fetch all invite collaborators list
  fetchAllInviteCollaborators = async ({ filter }) => {
    return await inviteCollaborators.find(filter);
  };

  //* Get invite collaborators suggestion list
  getSuggestionInviteCollaborators = async ({ filter }) => {
    let data = await inviteCollaborators
      .find(filter)
      .select("first_name last_name email -__v")
      .populate("user_id", {
        first_name: 1,
        last_name: 1,
        groupos_join_date: 1,
        migration_date: 1,
        "Preferred Email": 1,
      })
      .lean();
    data.sort((a, b) => {
      const nameA = a.user_id?.first_name?.toLowerCase() || "";
      const nameB = b.user_id?.first_name?.toLowerCase() || "";
      return nameA.localeCompare(nameB);
    });
    data.forEach((detail) => {
      if (detail?.user_id) {
        detail.user_id.groupos_join_date =
          detail?.user_id?.groupos_join_date ?? null;
        detail.user_id.migration_date = detail?.user_id?.migration_date ?? null;
      }
    });
    return data;
  };

  //* Update invite collaborators data
  updateInviteCollaborators = async ({ filter, data }) => {
    return await inviteCollaborators.findOneAndUpdate(
      filter,
      { $set: data },
      { new: true }
    );
  };
}

module.exports = inviteCollaboratorsRepository;
