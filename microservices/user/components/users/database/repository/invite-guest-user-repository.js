// const { users } = require("../models");
const guestUsers = require("../models/invite-guest-users");

class UserRepository {
    //* Create the import user
    createInviteGuestUser = async ({ payload }) => {
        return await guestUsers.create(payload);
    };

    //* get User filter
    getInviteGuestUser = async ({ filter }) => {
        return await guestUsers.findOne(filter).sort({ createdAt: -1 }).lean();
    };

    //* Get all invite user list count
    getAllInviteGuestUserCount = async ({ filter }) => {
        return await guestUsers.countDocuments(filter);
    };

    //* Get all invite user list
    getAllInviteGuestUser = async ({
        filter,
        skip = 0,
        limit = 100,
        sortBy,
        sortOrder,
        isAllData = false,
    }) => {
        if (isAllData) {
            return await guestUsers.find(filter).sort({ [sortBy]: sortOrder });
        } else {
            return await guestUsers
                .find(filter)
                .sort({ [sortBy]: sortOrder })
                .skip(skip)
                .limit(limit)
                .lean();
        }
    };

    //* Get invite user suggestion list
    getSuggestionGuestUser = async ({ filter }) => {
        return await guestUsers
            .find(filter)
            .select("first_name last_name email -__v")
            .lean();
    };

    //* Update invited user data
    updateInviteGuestUser = async ({ filter, data }) => {
        return await guestUsers.findOneAndUpdate(
            filter,
            { $set: data },
            { new: true }
        );
    };
    findGuestUsers = async ({ filter }) => {
        return await guestUsers.find(filter);
    };
    createInviteGuestUsersBulk = async ({ payloads }) => {
        const { emailRows, relation_id } = payloads;
        // Prepare bulk operations
        const bulkOps = emailRows.map(({ email, first_name, last_name }) => ({
            updateOne: {
                filter: { email }, // Filter to find user by email
                update: {
                    $set: {
                        first_name, // Set the first name
                        last_name, // Set the last name
                        relation_id, // Set the relation ID
                    },
                    $setOnInsert: {
                        createdAt: new Date(), // Optionally set a createdAt field for new users
                    },
                },
                upsert: true, // Create new document if it doesn't exist
            },
        }));

        // Perform bulk write operation
        const result = await guestUsers.bulkWrite(bulkOps);
        const insertedIds = [];
        for (let i = 0; i < bulkOps.length; i++) {
            if (result.upsertedCount > 0 && result.upsertedIds[i]) {
                insertedIds.push(result.upsertedIds[i]);
            }
        }

        return { insertedIds, result }; // Return the result of the bulk operation
    };

}

module.exports = UserRepository;
