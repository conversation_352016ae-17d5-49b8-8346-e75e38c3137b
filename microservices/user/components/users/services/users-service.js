const { v4: uuidv4 } = require("uuid");
const jwt = require("jsonwebtoken");
const axios = require("axios");
const fs = require("fs");
const { parse } = require("csv-parse");
const ObjectId = require("mongoose").Types.ObjectId;

const {
  FRONTEND_DOMAIN,
  GATEWAY_DOMAIN,
  BASE_URL,
  SOCIAL_URL,
  AWS_BUCKET,
  AWS_REGION,
  MESSAGE_QUEUE_BILLING_SERVICE_NAME,
} = require("../../../../../config/config");
const script = require("../../../utils/server-init");
const {
  generateFirebaseUrl,
  updateFirebaseUser,
  findUserInFirebase,
  fetchSubscriptionData,
  generateCommunityURL,
  hideEmail,
  fetchTierData,
  generateJWTForIntercom,
} = require("../../../utils/comman");

// repositories
const {
  UsersRepository,
  InviteUsersRepository,
  GuestUserRepository,
  InviteCollaboratorsRepository,
  UserLogInErrorLogRepository,
  UserMigrationLogRepository,
} = require("../database");
const { encrypt } = require("../../../../../utils/crpyto");

const { ReturnDataObj } = require("../../../utils");
const publishMessage = require("../entry-points/message-queue/publisher");

const admin = require("../../../../../utils/firebase-admin");
const inviteUser = require("../database/models/invite-users");

const ComunityMigrationLogsService = require("../../comunity-migration-logs/services/comunity-migration-logs-service");
class UsersService {
  #mailService = new MailService();
  #userEdgesService = new UserEdgesService();
  #rolesService = new RolesService();
  #platformService = new PlatformService();
  #s3fileUploadService = new S3FileUpload();
  #userMigrationsService = new UserMigrationsService();
  #comunityMigrationLogsService = new ComunityMigrationLogsService();

  constructor() {
    this.repository = new UsersRepository();
    this.inviteUsersRepository = new InviteUsersRepository();
    this.guestUserRepository = new GuestUserRepository();
    this.InviteCollaboratorsRepository = new InviteCollaboratorsRepository();
    this.userLogInErrorLogRepository = new UserLogInErrorLogRepository();
    this.userMigrationLogRepository = new UserMigrationLogRepository();
  }

  validateAccountActivationLink = async ({ activationCode }) => {
    // check account activision link
    const validLink = await this.repository.findUser({
      filter: {
        activation_code: activationCode,
      },
    });

    if (!validLink) {
      return ReturnDataObj("Invalid link", 400);
    }

    return ReturnDataObj({
      data: { message: "Link validated", requiresPassword: false },
    });
  };

  activateAccount = async ({ activationCode }) => {
    // check account activision link
    const validLink = await this.repository.findUser({
      filter: {
        activation_code: activationCode,
      },
    });
    if (!validLink) {
      return ReturnDataObj("Invalid link", 400);
    }

    // remove account activision code and set status to ACTIVE
    const activatedUser = await this.repository.updateUser({
      user_id: validLink._id,
      userData: {
        status: "ACTIVE",
        activation_code: null,
      },
    });
    return ReturnDataObj(activatedUser);
  };

  activateFirebaseAccount = async ({ preferred_email }) => {
    try {
      if (!preferred_email || preferred_email === "") {
        return ReturnDataObj("Preferred Email is required!", 400);
      }

      const fetchUser = await this.repository.findAndUpdateUser({
        filter: {
          $or: [
            { "Preferred Email": preferred_email },
            { apple_private_email: preferred_email },
          ],
          isDelete: false,
        },
      });
      if (!fetchUser) {
        return ReturnDataObj("This user is not found in system!", 400);
      }

      const user = await admin
        .auth()
        .getUserByEmail(
          fetchUser.apple_private_email
            ? fetchUser.apple_private_email
            : preferred_email.toString()
        );

      if (user.emailVerified) {
        const activatedUser = await this.repository.findAndUpdateUser({
          filter: {
            _id: fetchUser?._id,
          },
          userData: {
            $set: {
              status: "ACTIVE",
              activation_code: null,
            },
          },
        });
        return ReturnDataObj(activatedUser);
      } else {
        return ReturnDataObj("Email is not verify!", 402);
      }
    } catch (error) {
      if (error?.code === "auth/invalid-email")
        return ReturnDataObj(error.message, 500, false);

      if (error.code === "auth/argument-error")
        return res.status(500).send(error.message);

      return ReturnDataObj(error.message, 500, false);
    }
  };

  generateToken = async ({ data }) => {
    return jwt.sign(data, process.env.JWT_SECRET);
  };

  migrateMDSUser = async ({ email, userData }) => {
    // check if the user belongs to MDS migration flow
    const belongsToMigration =
      await userMigrationsService.repository.findUserMigration({
        filter: {
          email: email,
          migrated: false,
        },
      });

    if (!belongsToMigration) {
      return false;
    }

    // find mds community
    const community = await script.getMdsCommunity();

    // find member role
    const { data: role } = await rolesService.findRole({
      filter: {
        role_name: "MEMBER",
      },
    });

    // create MDS member edge
    const { data: userEdge } = await userEdgesService.createUserEdge({
      userEdgeData: {
        relation_id: community._id,
        platform: false,
        user_id: userData._id,
        role_id: role._id,
        type: "M",
      },
    });

    // add edge record to user data
    await this.repository.updateUser({
      user_id: userData._id,
      status: "ACTIVE",
      userData: {
        user_edges: [...userData.user_edges, userEdge._id],
      },
    });

    // complete the user migration
    await userMigrationsService.repository.findAndUpdateUserMigration({
      filter: {
        email: email,
      },
      userMigrationData: {
        migrated: true,
      },
    });

    return true;
  };

  /**
   * Service for the check that the apple relay email is exist in system or not
   */
  checkAppleRelayEmail = async ({ email, socialId }) => {
    //* For the relay email case
    if (email) {
      //* Validate the relay email
      if (!email.includes("privaterelay")) {
        return ReturnDataObj({
          success: false,
          message: "Not valid email!",
          data: {},
        });
      }

      //* Fetch the user from the database
      const fetchData = await this.repository.findUser({
        filter: {
          $or: [
            {
              apple_private_email: email,
            },
            {
              "providers.email": email,
            },
          ],
          isDelete: false,
        },
      });

      return ReturnDataObj({
        success: fetchData ? true : false,
        message: "success",
        data: {},
      });
    }

    //* For the Social Id case
    if (socialId) {
      //* Fetch the user from the database using the social id
      const fetchData = await this.repository.findUser({
        filter: {
          "providers.socialId": socialId,
        },
      });

      return ReturnDataObj({
        success: fetchData ? true : false,
        message: "success",
        data: {
          ...(fetchData && { email: fetchData["Preferred Email"] }),
        },
      });
    }

    return ReturnDataObj(
      {
        success: fetchData ? true : false,
        message: "Somthing went wrong!",
        data: {},
      },
      400
    );
  };

  signUp = async ({
    first_name,
    last_name,
    preferred_email,
    firebaseId,
    profileImg,
    skipEmail = false,
    defaultActivate = false,
    host = null,
    communityId = null,
    provider = null,
    facebookLinkedinId = null,
    socialauth0id = null,
    isInvitedUser,
    isInviteGuestUser = false,
    apple_private_email = null,
    firebaseEmail = null,
  }) => {
    let filter;
    if (skipEmail) {
      filter = { firebaseId: firebaseId, isDelete: false };
    } else {
      filter = { "Preferred Email": preferred_email, isDelete: false };
    }
    // search for existing account
    const userExists = await this.repository.findUser({
      filter,
    });
    if (userExists) {
      if (userExists?.firebaseId || userExists?.providers.length) {
        if (userExists?.providers.length) {
          const providerName = userExists?.providers[0]?.name || "unknown";
          if (!userExists?.providers[0]["email"]) {
            return res.status(400).json({
              success: false,
              status: false,
              message: `${
                userExists["Preferred Email"]
              } preferred email is already connected ${
                userExists?.providers[0]?.name
                  ? `with ${userExists.providers[0].name} provider.`
                  : "."
              }`,
            });
          }
          const email = await hideEmail({
            email: userExists?.providers[0]["email"],
          });

          return ReturnDataObj(
            {
              status: false,
              message: `${
                firebaseEmail ?? "This"
              } preferred email is already connected with ${
                userExists?.providers[0]["name"]
              } provider associated with this ${email}.`,
              data: {
                provider: providerName,
                hideEmail: email,
                email: encrypt(userExists?.providers[0]["email"]),
              },
            },
            409,
            false
          );
        } else {
          return ReturnDataObj("User Already exists", 409);
        }
      }
    }

    // find default role
    const { data: role } = await rolesService.findRole({
      filter: {
        role_name: "DEFAULT",
      },
    });

    // generate activation link
    const activationCode = uuidv4();

    // create user
    let generatedUser = await this.repository.generateUser({
      userData: {
        first_name: first_name,
        last_name: last_name,
        display_name: `${first_name} ${last_name}`,
        "Preferred Email": preferred_email ? preferred_email : null,
        firebaseId: firebaseId,
        profileImg: profileImg ? profileImg : null,
        status: defaultActivate || isInvitedUser ? "ACTIVE" : "INACTIVE",
        ...(provider && { provider: provider }),
        ...(facebookLinkedinId && { facebookLinkedinId: facebookLinkedinId }),
        ...(socialauth0id && { socialauth0id: socialauth0id }),
        activation_code: defaultActivate ? null : activationCode,
        ...(provider === "apple" && { apple_private_email }),
        ...(firebaseEmail && {
          providers: [
            { name: provider, email: firebaseEmail, socialId: socialauth0id },
          ],
        }),
      },
    });

    // create user edge
    const { data: userEdge } = await userEdgesService.createUserEdge({
      userEdgeData: {
        default: true,
        owner: true,
        user_id: generatedUser._id,
        role_id: role._id,
        type: "DEFAULT",
      },
    });

    generatedUser.user_edges = [userEdge._id];

    let community = null;
    if (host || isInvitedUser || isInviteGuestUser) {
      community = await communitiesService.repository.findCommunity({
        filter: {
          _id: communityId,
        },
      });

      if (
        ((community || community?.customDomain?.status === "active") && community.isSignUpEnable) ||
        isInvitedUser
      ) {
        const { data: role } = await rolesService.findRole({
          filter: {
            role_name: "MEMBER",
          },
        });

        const { data: userEdgeForCommunity } =
          await userEdgesService.createUserEdge({
            userEdgeData: {
              relation_id: community._id,
              user_id: generatedUser._id,
              role_id: role._id,
              platform: false,
              type: "M",
              groupos_join_date: new Date(),
            },
          });

        generatedUser.user_edges.push(userEdgeForCommunity._id);

        const communityOwnerEdge =
          await userEdgesService.repository.findUserEdge({
            filter: {
              user_id: community.owner_id,
              relation_id: community._id,
              isDelete: false,
            },
          });

        //* Update the email verification in the firebase
        await updateFirebaseUser({
          firebaseId,
          payload: {
            emailVerified: true,
          },
        });

        const payload = {
          event: "COMMUNITY_MEMBER_ADDED",
          data: {
            first_name: generatedUser.first_name || "",
            last_name: generatedUser.last_name || "",
            email: generatedUser["Preferred Email"] || "",
            edge: userEdgeForCommunity || null,
            communityOwnerEdge: communityOwnerEdge,
          },
        };

        publishMessage(
          JSON.stringify(payload),
          "MONOLITH_COMMUNITY_MEMBER_ADDED"
        );
      }

      if (
        (community && community?.customDomain?.status === "active") ||
        isInviteGuestUser
      ) {
        //GUEST_USER code commented
        // const { data: role } = await rolesService.findRole({
        //   filter: {
        //     role_name: "GUEST_USER",
        //   },
        // });
        // const { data: userEdgeForCommunity } =
        //   await userEdgesService.createUserEdge({
        //     userEdgeData: {
        //       relation_id: community._id,
        //       user_id: generatedUser._id,
        //       role_id: role._id,
        //       platform: false,
        //       type: "GU",
        //     },
        //   });
        // generatedUser.user_edges.push(userEdgeForCommunity._id);
      }
    }

    let link = null;
    if (firebaseId) {
      link = await generateFirebaseUrl({
        email:
          generatedUser.provider === "apple" &&
          apple_private_email &&
          generatedUser.apple_private_email.includes("privaterelay")
            ? apple_private_email
            : preferred_email,
        community,
        tempEmail: preferred_email,
      });
    }

    generatedUser.activationLink = link;

    await this.repository.saveGeneratedUser({ generatedUser });

    if (isInvitedUser) {
      // login flow
      const {
        data: tryLogin,
        code,
        status,
      } = await this.login({
        email: preferred_email ? preferred_email : null,
        firebaseId: firebaseId,
        skipEmail: preferred_email ? false : true,
        // migration: migration,
        migration: false,
        communityId: community ? community._id : null,
        isInvitedUser,
      });

      generatedUser = tryLogin;
    }

    let communitiyLogo = null;
    //* create the community s3 logo
    if (community && community?.logo) {
      communitiyLogo = `https://${AWS_BUCKET}.s3.us-east-2.amazonaws.com/groupos/${community?._id}/${community?.logo}`;
    } else {
      communitiyLogo =
        "https://mds-community.s3.us-east-2.amazonaws.com/uploads/groupos-util/groupos_logo.png";
    }

    if (skipEmail || defaultActivate) {
      // do nothing for email related logic
    } else {
      if (!isInvitedUser) {
        await mailService.sendMail({
          to: generatedUser["Preferred Email"],
          subject: "Account Created!",
          template: "account-activation",
          context: {
            email: preferred_email,
            communityName: community ? community.name : "GROUPOS",
            activationLink: link,
            communitiyLogo,
            communityChar: community ? community.name.split("")[0] : "G",
          },
          relationId: community?._id ? community._id : "",
          customsmtp: community?.customSMTP ? community.customSMTP : false,
        });
      }
    }

    if (generatedUser?.status === "INACTIVE") {
      return ReturnDataObj(
        {
          success: false,
          message: "Account not activated!",
        },
        401,
        false
      );
    }

    return ReturnDataObj(generatedUser, 200, true);
  };

  login = async ({
    email,
    firebaseId,
    skipEmail = false,
    migration = false,
    communityId = null,
    isInvitedUser = false,
    isInviteGuestUser = false,
    apple_private_email = null,
    isSignUpEnable = null,
  }) => {
    let filter;
    if (skipEmail) {
      filter = { firebaseId: firebaseId, isDelete: false };
    } else {
      filter = {
        $or: [
          { "Preferred Email": email },
          { "providers.email": email },
          ...(apple_private_email ? [{ apple_private_email }] : []),
        ],
        isDelete: false,
      };
    }

    // check if user exist
    const userExists = await this.repository.findUser({
      filter,
    });

    if (!userExists) {
      return ReturnDataObj("User does not exists!", 401, false);
    }

    if (userExists.blocked && userExists.blocked == true ) {
      return ReturnDataObj("User is blocked!", 400, false);
    }

    // check if user is active
    if (userExists.status === "INACTIVE") {
      let community = null;

      if (communityId) {
        community = await communitiesService.repository.findCommunity({
          filter: {
            _id: communityId,
            isDelete: false,
          },
        });
      }

      let link = await generateFirebaseUrl({
        email:
          userExists.provider === "apple" &&
          apple_private_email &&
          userExists.apple_private_email.includes("privaterelay")
            ? apple_private_email
            : userExists["Preferred Email"],
        community,
        tempEmail: userExists["Preferred Email"],
      });

      if (!link) link = userExists?.activationLink;
      else {
        await this.repository.findAndUpdateUser({
          filter: {
            _id: userExists._id,
          },
          userData: {
            $set: {
              activationLink: link,
            },
          },
        });
      }

      let communitiyLogo = null;
      //* create the community s3 logo
      if (community && community?.logo) {
        communitiyLogo = `https://${AWS_BUCKET}.s3.us-east-2.amazonaws.com/groupos/${community?._id}/${community?.logo}`;
      } else {
        communitiyLogo =
          "https://mds-community.s3.us-east-2.amazonaws.com/uploads/groupos-util/groupos_logo.png";
      }

      await mailService.sendMail({
        to: userExists["Preferred Email"],
        subject: "Account Created!",
        template: "account-activation",
        context: {
          email: userExists["Preferred Email"],
          communityName: community ? community.name : "GROUPOS",
          activationLink: link,
          communitiyLogo,
          communityChar: community ? community.name.split("")[0] : "G",
        },
        relationId: community?._id ? community._id : "",
        customsmtp: community?.customSMTP ? community.customSMTP : false,
      });
      return ReturnDataObj("Account not activated!", 401, false);
    }

    // check if user is blocked
    if (userExists.status === "BLOCKED") {
      return ReturnDataObj("Account blocked!", 401, false);
    }

    // find user edges
    const userEdgesData = await userEdgesService.repository.findUserEdges({
      filter: {
        user_id: userExists._id,
        isDelete: false,
        type: { $nin: ["GU"] },
        ...(communityId && {
          relation_id: communityId,
        }),
      },
      expand: true,
    });

    const userEdges = userEdgesData.filter((doc) => doc.relation_id !== null);

    if (!isInvitedUser && communityId && isSignUpEnable === false) {
      //* Find the user edge for community
      let fetchEdge = userEdges?.find(
        (edge) =>
          edge?.relation_id &&
          edge?.relation_id?._id.toString() === communityId.toString()
      );

      if (!fetchEdge) {
        return ReturnDataObj(
          {
            success: false,
            message: "Preferred Email is not exists in system!",
            data: {},
          },
          400,
          false
        );
      }
    }

    let edgeData;
    if (userEdges.length == 2) {
      // prepare data for the primary edge (skip default edge)
      const edge = await userEdgesService.repository.getUserEdge({
        userEdge_id: userEdges.find((edge) => edge.type !== "DEFAULT")._id,
        expand: true,
      });
      edgeData = edge;
    } else if (userEdges.length && communityId) {
      // prepare data for the community edge
      let edge = await userEdgesService.repository.getUserEdge({
        userEdge_id: userEdges.find(
          (edge) => edge?.relation_id?._id.toString() === communityId.toString()
        )?._id,
        expand: true,
      });

      if (!edge) {
        const community = await communitiesService.repository.findCommunity({
          filter: {
            _id: communityId,
          },
        });

        if (community && community?.customDomain?.status === "active") {
          const { data: role } = await rolesService.findRole({
            filter: {
              role_name: "MEMBER",
            },
          });

          const { data: userEdgeForCommunity } =
            await userEdgesService.createUserEdge({
              userEdgeData: {
                relation_id: community._id,
                user_id: userExists._id,
                role_id: role._id,
                platform: false,
                type: "M",
                groupos_join_date: new Date(),
              },
            });

          await this.repository.findAndUpdateUser({
            filter: {
              _id: userExists._id,
            },
            userData: {
              $push: {
                user_edges: userEdgeForCommunity._id,
              },
            },
          });

          const communityOwnerEdge =
            await userEdgesService.repository.findUserEdge({
              filter: {
                user_id: community.owner_id,
                relation_id: community._id,
                isDelete: false,
              },
            });

          const edge = await userEdgesService.repository.getUserEdge({
            userEdge_id: userEdgeForCommunity._id,
            expand: true,
          });

          const payload = {
            event: "COMMUNITY_MEMBER_ADDED",
            data: {
              first_name: userExists.first_name || "",
              last_name: userExists.last_name || "",
              email: userExists["Preferred Email"] || "",
              edge: userEdgeForCommunity || null,
              communityOwnerEdge: communityOwnerEdge,
            },
          };

          publishMessage(
            JSON.stringify(payload),
            "MONOLITH_COMMUNITY_MEMBER_ADDED"
          );

          edgeData = edge;
        }
      } else {
        edgeData = edge;
      }
    } else if (isInvitedUser && communityId && !userEdges.length) {
      // prepare data for the community edge
      let edge = await userEdgesService.repository.getUserEdge({
        userEdge_id: userEdges.find(
          (edge) => edge?.relation_id?._id.toString() === communityId.toString()
        )?._id,
        expand: true,
      });
      if (!edge) {
        const community = await communitiesService.repository.findCommunity({
          filter: {
            _id: communityId,
          },
        });

        if (
          (community && community?.customDomain?.status === "active") ||
          isInvitedUser
        ) {
          const { data: role } = await rolesService.findRole({
            filter: {
              role_name: "MEMBER",
            },
          });

          const { data: userEdgeForCommunity } =
            await userEdgesService.createUserEdge({
              userEdgeData: {
                relation_id: community._id,
                user_id: userExists._id,
                role_id: role._id,
                platform: false,
                type: "M",
                groupos_join_date: new Date(),
              },
            });

          await this.repository.findAndUpdateUser({
            filter: {
              _id: userExists._id,
            },
            userData: {
              $push: {
                user_edges: userEdgeForCommunity._id,
              },
            },
          });

          const communityOwnerEdge =
            await userEdgesService.repository.findUserEdge({
              filter: {
                user_id: community.owner_id,
                relation_id: community._id,
                isDelete: false,
              },
            });

          const edge = await userEdgesService.repository.getUserEdge({
            userEdge_id: userEdgeForCommunity._id,
            expand: true,
          });

          const payload = {
            event: "COMMUNITY_MEMBER_ADDED",
            data: {
              first_name: userExists.first_name || "",
              last_name: userExists.last_name || "",
              email: userExists["Preferred Email"] || "",
              edge: userEdgeForCommunity || null,
              communityOwnerEdge: communityOwnerEdge,
            },
          };

          publishMessage(
            JSON.stringify(payload),
            "MONOLITH_COMMUNITY_MEMBER_ADDED"
          );

          edgeData = edge;
        }
      } else {
        edgeData = edge;
      }
    } else if (isInviteGuestUser && communityId && !userEdges.length) {
      // prepare data for the community edge
      let edge = await userEdgesService.repository.getUserEdge({
        userEdge_id: userEdges.find(
          (edge) => edge?.relation_id?._id.toString() === communityId.toString()
        )?._id,
        expand: true,
      });
      if (!edge) {
        const community = await communitiesService.repository.findCommunity({
          filter: {
            _id: communityId,
          },
        });

        if (
          (community && community?.customDomain?.status === "active") ||
          isInviteGuestUser
        ) {
          // GUEST_USER code commented
          // const { data: role } = await rolesService.findRole({
          //   filter: {
          //     role_name: "GUEST_USER",
          //   },
          // });

          // const { data: userEdgeForCommunity } =
          //   await userEdgesService.createUserEdge({
          //     userEdgeData: {
          //       relation_id: community._id,
          //       user_id: userExists._id,
          //       role_id: role._id,
          //       platform: false,
          //       type: "GU",
          //     },
          //   });

          // await this.repository.findAndUpdateUser({
          //   filter: {
          //     _id: userExists._id,
          //   },
          //   userData: {
          //     $push: {
          //       user_edges: userEdgeForCommunity._id,
          //     },
          //   },
          // });

          const edge = await userEdgesService.repository.getUserEdge({
            userEdge_id: userEdgeForCommunity._id,
            expand: true,
          });
          edgeData = edge;
        }
      } else {
        edgeData = edge;
      }
    } else if (!userEdges.length && communityId) {
      const community = await communitiesService.repository.findCommunity({
        filter: {
          _id: communityId,
        },
      });

      const { data: role } = await rolesService.findRole({
        filter: {
          role_name: "MEMBER",
        },
      });

      const { data: userEdgeForCommunity } =
        await userEdgesService.createUserEdge({
          userEdgeData: {
            relation_id: community._id,
            user_id: userExists._id,
            role_id: role._id,
            platform: false,
            type: "M",
            groupos_join_date: new Date(),
          },
        });

      await this.repository.findAndUpdateUser({
        filter: {
          _id: userExists._id,
        },
        userData: {
          $push: {
            user_edges: userEdgeForCommunity._id,
          },
        },
      });

      const communityOwnerEdge = await userEdgesService.repository.findUserEdge(
        {
          filter: {
            user_id: community.owner_id,
            relation_id: community._id,
            isDelete: false,
          },
        }
      );

      const edge = await userEdgesService.repository.getUserEdge({
        userEdge_id: userEdgeForCommunity._id,
        expand: true,
      });

      const payload = {
        event: "COMMUNITY_MEMBER_ADDED",
        data: {
          first_name: userExists.first_name || "",
          last_name: userExists.last_name || "",
          email: userExists["Preferred Email"] || "",
          edge: userEdgeForCommunity || null,
          communityOwnerEdge: communityOwnerEdge,
        },
      };

      publishMessage(
        JSON.stringify(payload),
        "MONOLITH_COMMUNITY_MEMBER_ADDED"
      );

      edgeData = edge;
    } else {
      // prepare data for the default edge
      const edge = await userEdgesService.repository.getUserEdge({
        userEdge_id: userEdges.find((edge) => edge.type === "DEFAULT")._id,
        expand: true,
      });
      edgeData = edge;
    }

    const preparedData = {};

    preparedData["_id"] = edgeData._id;

    if (edgeData.relation_id) {
      const domain = edgeData.relation_id.customDomain?.isMainDomain
        ? `www.${edgeData.relation_id.customDomain.domain}`
        : edgeData.relation_id.customDomain?.domain;

      edgeData.relation_id.custom_domain = {
        domain:
          edgeData.relation_id.customDomain?.status === "active" ? domain : "",
        status: edgeData.relation_id.customDomain?.status || "pending",
        isMainDomain: edgeData.relation_id.customDomain?.isMainDomain,
      };

      if (edgeData.relation_id.banner) {
        edgeData.relation_id.banner =
          await s3fileUploadService.generatePresignedUrl({
            key: `groupos/${edgeData.relation_id._id}/${edgeData.relation_id.banner}`,
          });
      }
      if (edgeData.relation_id.avtar) {
        edgeData.relation_id.avtar =
          await s3fileUploadService.generatePresignedUrl({
            key: `groupos/${edgeData.relation_id._id}/${edgeData.relation_id.avtar}`,
          });
      }
      edgeData.relation_id.logo =
        await s3fileUploadService.generatePresignedUrl({
          key: `groupos/${edgeData.relation_id._id}/${edgeData.relation_id.logo}`,
        });
      preparedData["relation_id"] = edgeData.relation_id;
    }

    preparedData["platform"] = edgeData.platform;
    preparedData["user_id"] = edgeData.user_id;
    preparedData["owner"] = edgeData.owner;
    preparedData["no_of_team_mate"] = edgeData?.no_of_team_mate || 0;
    preparedData["role_id"] = {
      _id: edgeData.role_id._id,
      role_name: edgeData.role_id.role_name,
    };
    preparedData["tagId"] = edgeData?.tagId;
    preparedData["accessible_groups"] = edgeData?.accessible_groups;
    preparedData["AdminScopes"] =
      edgeData.role_id.admin_scope_id.admin_modules.map(
        (module) => module.name
      );
    preparedData["type"] = edgeData.type;
    preparedData["stripe_customer_db_id"] = edgeData.stripe_customer_db_id;
    preparedData["groupos_join_date"] = edgeData.groupos_join_date || null;
    preparedData["migration_date"] = edgeData.migration_date || null;

    if (edgeData.stripe_account_db_id) {
      preparedData["stripe_account_db_id"] = edgeData.stripe_account_db_id;
    }

    if (edgeData.subscription_id) {
      preparedData["subscription_id"] = edgeData.subscription_id;
      const response = await axios.get(
        `${GATEWAY_DOMAIN}/api/billings/api/v1/subscriptions/${edgeData.subscription_id}`
      );
      preparedData["scopes"] = response.data.scope_id.modules.map(
        (module) => module.system_id
      );
    }

    if (preparedData?.type === "CU") {
      this.InviteCollaboratorsRepository.updateInviteCollaborators({
        filter: {
          email: userExists["Preferred Email"],
          isDelete: false,
          relation_id: preparedData.relation_id._id,
          status: "PENDING",
        },
        data: {
          status: "ACTIVE",
        },
      });

      if (!preparedData?.groupos_join_date && preparedData?._id) {
        const cuEdgeUpdate = await userEdgesService.repository.updateUserEdge({
          userEdgeId: preparedData._id,
          userEdgeData: {
            $set: {
              groupos_join_date: new Date(),
            },
          },
        });
        if (cuEdgeUpdate) {
          preparedData["groupos_join_date"] = cuEdgeUpdate?.groupos_join_date;
        }
      }
    }

    // get platform
    const { data: platformExists } = await platformService.getPlatform();
    if (!platformExists) {
      return ReturnDataObj("Platform does not exists!", 400, false);
    }

    // add logo to user edges
    const userEdgesWithLogo = Promise.all(
      userEdges.map(async (edge) => {
        let data = {};
        if (edge.type !== "DEFAULT") {
          data = {
            ...edge.toJSON(),
            relation_id: {
              ...edge.relation_id.toJSON(),
              logo: await s3fileUploadService.generatePresignedUrl({
                key: `groupos/${edge.relation_id._id}/${edge.relation_id.logo}`,
              }),
              banner: await s3fileUploadService.generatePresignedUrl({
                key: `groupos/${edge.relation_id._id}/${edge.relation_id.banner}`,
              }),
              ...(edge.relation_id.avtar && {
                avtar: await s3fileUploadService.generatePresignedUrl({
                  key: `groupos/${edge.relation_id._id}/${edge.relation_id.avtar}`,
                }),
              }),
            },
          };
        } else {
          data = {
            ...edge.toJSON(),
          };
        }
        return data;
      })
    );

    let filterData = JSON.parse(JSON.stringify(preparedData));
    delete filterData?.userAvatar;
    delete filterData?.relation_id?.banner;
    delete filterData?.relation_id?.logo;
    delete filterData?.relation_id?.subscription_reminder_renewal_config_id;
    delete filterData?.relation_id?.avtar;
    delete filterData?.relation_id?.tagId;
    delete filterData?.relation_id?.accessible_groups;
    delete filterData?.relation_id?.groupos_join_date;
    delete filterData?.relation_id?.migration_date;

    const token = await this.generateToken({
      data: {
        userId: userExists._id,
        userName: `${userExists.first_name}${userExists.last_name}`,
        userEmail: userExists["Preferred Email"],
        // userAvatar: userExists?.profileImg,
        userStatus: userExists.status,
        // userEdges: await userEdgesWithLogo,
        currentEdge: { ...filterData },
        // platform: platformExists.data,
      },
    });

    let data = {
      token: `Bearer ${token}`,
      userId: userExists._id,
      userName: `${userExists.first_name} ${userExists.last_name}`,
      display_name: userExists.display_name,
      userEmail: userExists["Preferred Email"],
      userAvatar: userExists?.profileImg,
      userStatus: userExists.status,
      userEdges: await userEdgesWithLogo,
      currentEdge: preparedData,
      platform: platformExists,
    };

    // MDS migration
    let migrationData;
    if (
      edgeData?.relation_id &&
      edgeData?.relation_id?.nickname === "mds" &&
      !edgeData.subscription_id
    ) {
      // get the migration data
      migrationData = await userMigrationsService.repository.findUserMigration({
        filter: {
          email: email,
        },
      });
    }

    if (migrationData) {
      data.migrationData = migrationData;
    }

    if (migration) {
      // find mds community owner edge({})
      const mdsCAEdge = await userEdgesService.repository.findUserEdge({
        filter: {
          owner: true,
          type: "CO",
          relation_id: edgeData.relation_id,
          isDelete: false,
        },
      });

      data.migrationEvent = {
        first_name: userExists.first_name,
        last_name: userExists.last_name,
        email: userExists["Preferred Email"],
        edge: edgeData,
        communityOwnerEdge: mdsCAEdge,
      };
    }

    // prepare user data
    return ReturnDataObj({
      data: data,
    });
  };

  ssoLogin = async ({
    first_name,
    last_name,
    preferred_email,
    sub,
    profileImg,
    host = null,
    provider,
    facebookLinkedinId = null,
    socialauth0id = null,
    nickname,
    relation_id,
    apple_private_email = null,
    firebaseEmail = null,
    socialProviderId,
  }) => {
    if (!sub) {
      return ReturnDataObj("Login Failed!", 401, false);
    }

    if (host.includes(SOCIAL_URL)) {
      const checkEmail = await this.repository.findUser({
        filter: {
          $or: [
            { "Preferred Email": preferred_email },
            { "providers.email": preferred_email },
            ...(apple_private_email ? [{ apple_private_email }] : []),
          ],
          // $and: [
          //   {
          //     firebaseId: {
          //       $exists: false,
          //     },
          //   },
          //   {
          //     firebaseId: "",
          //   },
          // ],
          isDelete: false,
        },
      });
      if (!checkEmail) {
        if (!relation_id) {
          return ReturnDataObj(
            {
              status: false,
              message: "Relation Id is require!",
            },
            400,
            false
          );
        }
        if (relation_id) {
          const findInviteUser = await this.inviteUsersRepository.getImportUser(
            {
              filter: {
                email: preferred_email,
                relation_id,
              },
            }
          );
          if (!findInviteUser) {
            return ReturnDataObj(
              {
                status: false,
                message: "Preferred Email is not exists in system!",
              },
              400,
              false
            );
          }
        }
      }
    }

    let userExists = null;
    // find user with either auth0Id or preferred email
    if (host.includes(SOCIAL_URL)) {
      userExists = await this.repository.findUser({
        filter: {
          firebaseId: sub,
          ...(preferred_email && {
            $or: [
              { "Preferred Email": preferred_email },
              { "providers.email": preferred_email },
              ...(apple_private_email ? [{ apple_private_email }] : []),
            ],
          }),
        },
      });
    } else {
      userExists = await this.repository.findUser({
        filter: {
          firebaseId: sub,
          ...(preferred_email && {
            $or: [
              { "Preferred Email": preferred_email },
              { "providers.email": preferred_email },
              ...(apple_private_email ? [{ apple_private_email }] : []),
            ],
          }),
        },
      });
    }

    if (!userExists) {
      let userExistsTemp = await this.repository.findUser({
        filter: {
          isDelete: false,
          "Preferred Email": preferred_email,
        },
      });
      if (userExistsTemp && !userExistsTemp?.firebaseId) {
        userExists = await this.repository.updateUser({
          user_id: userExistsTemp._id,
          userData: {
            $set: {
              firebaseId: sub,
              groupos_join_date: new Date(),
            },
            $addToSet: {
              providers: {
                name: provider,
                email: firebaseEmail,
                socialId: socialauth0id,
              },
            },
          },
        });

        if (userExists) {
          updateFirebaseUser({
            firebaseId: sub,
            payload: { emailVerified: true },
          });
        }
      }
    }

    if (userExists) {
      // migrate MDS user
      // const migration = await this.migrateMDSUser({
      //   email: preferred_email,
      //   userData: userExists,
      // });

      let isInvitedUser = false;
      let isInviteGuestUser = false;

      //* check if user has the custom domain community
      let community = await communitiesService.repository.findCommunity({
        filter: {
          "customDomain.domain": host,
          "customDomain.status": "active",
          isDelete: false,
        },
      });
      if (community && community?.customDomain?.status === "active") {
        host = community?.customDomain?.isMainDomain
          ? `https://www.${community?.customDomain?.domain}`
          : `https://${community?.customDomain?.domain}`;
      } else {
        community = await communitiesService.repository.findCommunity({
          filter: {
            nickname,
            isDelete: false,
          },
        });
      }

      if (community) {
        const checkInvitedUser = await this.inviteUsersRepository.getImportUser(
          {
            filter: {
              email: preferred_email,
              relation_id: community._id,
              isDelete: false,
            },
          }
        );

        const checkGuestInvitedUser =
          await this.guestUserRepository.getInviteGuestUser({
            filter: {
              email: preferred_email,
              relation_id: community._id,
              isDelete: false,
            },
          });

        if (checkInvitedUser && checkInvitedUser?.status === "REVOKED") {
          return ReturnDataObj("Your invitation is revoked!", 401, false);
        }

        if (
          checkGuestInvitedUser &&
          checkGuestInvitedUser?.status === "REVOKED"
        ) {
          return ReturnDataObj("Your invitation is revoked!", 401, false);
        }

        if (checkInvitedUser) isInvitedUser = true;
        if (checkGuestInvitedUser) isInviteGuestUser = true;
      }

      // login flow
      const {
        data: tryLogin,
        code,
        status,
      } = await this.login({
        email: preferred_email ? preferred_email : null,
        firebaseId: sub,
        skipEmail: preferred_email ? false : true,
        // migration: migration,
        migration: false,
        communityId: community ? community._id : null,
        isInvitedUser,
        isInviteGuestUser,
        apple_private_email: apple_private_email || null,
        isSignUpEnable: community?.isSignUpEnable,
      });

      // platform owner creation / community owner creation will not have the auth0Id upon creation so adding that upon first time login
      if (preferred_email && sub && !userExists.firebaseId) {
        // add auth0Id to the user data
        await this.repository.findAndUpdateUser({
          filter: {
            "Preferred Email": preferred_email,
          },
          userData: {
            firebaseId: sub,
            groupos_join_date: new Date(),
          },
        });
      }

      return ReturnDataObj(tryLogin, code, status);
    } else {
      let isInvitedUser = false;
      let isInviteGuestUser = false;
      let community = await communitiesService.repository.findCommunity({
        filter: {
          "customDomain.domain": host,
          "customDomain.status": "active",
          isDelete: false,
        },
      });

      if (community && community?.customDomain?.status === "active") {
        host = community?.customDomain?.isMainDomain
          ? `https://www.${community?.customDomain?.domain}`
          : `https://${community?.customDomain?.domain}`;
      } else {
        community = await communitiesService.repository.findCommunity({
          filter: {
            nickname,
            isDelete: false,
          },
        });
      }

      if (community) {
        const checkInvitedUser = await this.inviteUsersRepository.getImportUser(
          {
            filter: {
              email: preferred_email,
              community_id: community._id,
              isDelete: false,
            },
          }
        );

        const checkGuestInvitedUser =
          await this.guestUserRepository.getInviteGuestUser({
            filter: {
              email: preferred_email,
              community_id: community._id,
              isDelete: false,
            },
          });

        if (checkInvitedUser && checkInvitedUser?.status === "REVOKED") {
          return ReturnDataObj("Your invitation is revoked!", 401, false);
        }

        if (
          checkGuestInvitedUser &&
          checkGuestInvitedUser?.status === "REVOKED"
        ) {
          return ReturnDataObj("Your invitation is revoked!", 401, false);
        }

        if (checkInvitedUser) isInvitedUser = true;
        if (checkGuestInvitedUser) isInviteGuestUser = true;
      }

      // sign up the new user
      const { data: newUser, code } = await this.signUp({
        first_name: first_name,
        last_name: last_name,
        preferred_email: preferred_email ? preferred_email : null,
        firebaseId: sub,
        profileImg: profileImg ? profileImg : null,
        skipEmail: preferred_email ? false : true,
        host: community ? host : null,
        communityId: community ? community._id : null,
        provider: provider,
        ...(facebookLinkedinId && { facebookLinkedinId: facebookLinkedinId }),
        ...(socialauth0id && { socialauth0id: socialauth0id }),
        isInvitedUser,
        isInviteGuestUser,
        apple_private_email: apple_private_email || null,
        firebaseEmail,
      });

      // status is set to 401 just for showing the fucking error modal on the frontend
      return ReturnDataObj(newUser, code ?? 401, code === 200 ? true : false);
    }
  };

  resendEmail = async ({ preferred_email, domain }) => {
    if (!preferred_email) {
      return ReturnDataObj("Preferred Email is require!", 400, false);
    }

    let community = null;
    if (domain.includes(BASE_URL)) {
      community = await communitiesService.repository.findCommunity({
        filter: {
          nickname: domain.split(".")[0],
          isDelete: false,
        },
      });
    } else {
      community = await communitiesService.repository.findCommunity({
        filter: {
          "customDomain.domain": domain,
          "customDomain.status": "active",
          isDelete: false,
        },
      });
    }

    // find user with either auth0Id or preferred email
    const userExists = await this.repository.findUser({
      filter: {
        "Preferred Email": preferred_email,
        firebaseId: { $exists: true },
        isDelete: false,
      },
    });

    if (!userExists) {
      return ReturnDataObj("User does not exists.", 400, false);
    }

    if (userExists?.status === "ACTIVE") {
      return ReturnDataObj("User is already active.", 400, false);
    } else {
      const link = await generateFirebaseUrl({
        email: userExists?.apple_private_email ?? preferred_email,
        community,
      });

      if (!link) {
        return ReturnDataObj(
          "Something went wrong while generating the link.",
          400,
          false
        );
      }

      let communitiyLogo = null;
      //* create the community s3 logo
      if (community && community?.logo) {
        communitiyLogo = `https://${AWS_BUCKET}.s3.us-east-2.amazonaws.com/groupos/${community?._id}/${community?.logo}`;
      } else {
        communitiyLogo =
          "https://mds-community.s3.us-east-2.amazonaws.com/uploads/groupos-util/groupos_logo.png";
      }

      mailService.sendMail({
        to: preferred_email,
        subject: "Account Created!",
        template: "account-activation",
        context: {
          email: preferred_email,
          communityName: community ? community.name : "GROUPOS",
          activationLink: link,
          communitiyLogo,
          communityChar: community ? community.name.split("")[0] : "G",
        },
        relationId: community?._id ? community._id : "",
        customsmtp: community?.customSMTP ? community.customSMTP : false,
      });

      return ReturnDataObj("Email send successfully", 200, false);
    }
  };

  forgotPassword = async ({ preferred_email, domain }) => {
    if (!preferred_email) {
      return ReturnDataObj("Preferred Email is require!", 400, false);
    }

    let community = null;
    if (domain.includes(BASE_URL)) {
      community = await communitiesService.repository.findCommunity({
        filter: {
          nickname: domain.split(".")[0],
          isDelete: false,
        },
      });
    } else {
      community = await communitiesService.repository.findCommunity({
        filter: {
          "customDomain.domain": domain,
          "customDomain.status": "active",
          isDelete: false,
        },
      });
    }

    // find user with either auth0Id or preferred email
    const userExists = await this.repository.findUser({
      filter: {
        "Preferred Email": preferred_email,
        firebaseId: { $exists: true },
      },
    });

    if (!userExists) {
      return ReturnDataObj("User does not exists.", 400, false);
    }

    const link = await generateFirebaseUrl({
      email: preferred_email,
      community,
      isReset: true,
    });

    if (!link) {
      return ReturnDataObj(
        "Something went wrong while generating the link.",
        400,
        false
      );
    }

    let communitiyLogo = null;
    //* create the community s3 logo
    if (community && community?.logo) {
      communitiyLogo = `https://${AWS_BUCKET}.s3.us-east-2.amazonaws.com/groupos/${community?._id}/${community?.logo}`;
    } else {
      communitiyLogo =
        "https://mds-community.s3.us-east-2.amazonaws.com/uploads/groupos-util/groupos_logo.png";
    }

    mailService.sendMail({
      to: preferred_email,
      subject: "Reset Password!",
      template: "forget-password",
      context: {
        email: preferred_email,
        communityName: community ? community.name : "GROUPOS",
        link,
        communitiyLogo,
        communityChar: community ? community.name.split("")[0] : "G",
      },
      relationId: community?._id ? community._id : "",
      customsmtp: community?.customSMTP ? community.customSMTP : false,
    });

    return ReturnDataObj("Email send successfully", 200, false);
  };

  edgeLogin = async ({ userId, userEdgeId, isPresignRequire = true }) => {
    // check if user exist
    const userExists = await this.repository.getUser({
      userId,
    });
    if (!userExists) {
      return ReturnDataObj("User does not exists!", 401, false);
    }

    // check if user is active
    if (userExists.status === "INACTIVE") {
      return ReturnDataObj("Account not activated!", 401);
    }

    // check if user is blocked
    if (userExists.status === "BLOCKED") {
      return ReturnDataObj("Account blocked!", 401);
    }

    // find user edges
    const userEdgesData = await userEdgesService.repository.findUserEdges({
      filter: {
        user_id: userExists._id,
        isDelete: false,
      },
      expand: true,
    });

    const userEdges = userEdgesData.filter((doc) => doc.relation_id !== null);

    const edgeData = await userEdgesService.repository.getUserEdge({
      userEdge_id: userEdgeId,
      expand: true,
    });
    if (!edgeData) {
      return ReturnDataObj("Edge does not exists!", 401);
    }

    const preparedData = {};

    preparedData["_id"] = edgeData._id;

    if (edgeData.relation_id) {
      const domain = edgeData.relation_id.customDomain?.isMainDomain
        ? `www.${edgeData.relation_id.customDomain.domain}`
        : edgeData.relation_id.customDomain?.domain;
      edgeData.relation_id.custom_domain = {
        domain:
          edgeData.relation_id.customDomain?.status === "active" ? domain : "",
        status: edgeData.relation_id.customDomain?.status || "pending",
        isMainDomain: edgeData.relation_id.customDomain?.isMainDomain,
      };
      if (edgeData.relation_id.banner && isPresignRequire) {
        edgeData.relation_id.banner =
          await s3fileUploadService.generatePresignedUrl({
            key: `groupos/${edgeData.relation_id._id}/${edgeData.relation_id.banner}`,
          });
      }
      if (edgeData.relation_id.avtar && isPresignRequire) {
        edgeData.relation_id.avtar =
          await s3fileUploadService.generatePresignedUrl({
            key: `groupos/${edgeData.relation_id._id}/${edgeData.relation_id.avtar}`,
          });
      }

      if (edgeData.relation_id.logo && isPresignRequire) {
        edgeData.relation_id.logo =
          await s3fileUploadService.generatePresignedUrl({
            key: `groupos/${edgeData.relation_id._id}/${edgeData.relation_id.logo}`,
          });
      }
      preparedData["relation_id"] = edgeData.relation_id;
    }

    preparedData["platform"] = edgeData.platform;
    preparedData["user_id"] = edgeData.user_id;
    preparedData["owner"] = edgeData.owner;
    preparedData["no_of_team_mate"] = edgeData?.no_of_team_mate || 0;
    preparedData["role_id"] = {
      _id: edgeData.role_id._id,
      role_name: edgeData.role_id.role_name,
    };
    preparedData["tagId"] = edgeData?.tagId;
    preparedData["accessible_groups"] = edgeData?.accessible_groups;
    preparedData["AdminScopes"] =
      edgeData.role_id.admin_scope_id.admin_modules.map(
        (module) => module.name
      );
    preparedData["type"] = edgeData.type;
    preparedData["stripe_customer_db_id"] = edgeData.stripe_customer_db_id;

    if (edgeData.stripe_account_db_id) {
      preparedData["stripe_account_db_id"] = edgeData.stripe_account_db_id;
    }

    if (edgeData.subscription_id) {
      preparedData["subscription_id"] = edgeData.subscription_id;
      const response = await axios.get(
        `${GATEWAY_DOMAIN}/api/billings/api/v1/subscriptions/${edgeData.subscription_id}`
      );
      preparedData["scopes"] =
        response?.data.scope_id?.modules?.map((module) => module?.system_id) ??
        [];
    }

    if (preparedData?.type === "CU") {
      this.InviteCollaboratorsRepository.updateInviteCollaborators({
        filter: {
          email: userExists["Preferred Email"],
          isDelete: false,
          relation_id: preparedData.relation_id._id,
          status: "PENDING",
        },
        data: {
          status: "ACTIVE",
        },
      });
    }

    // get platform
    const { data: platformExists } = await platformService.getPlatform();
    if (!platformExists) {
      return ReturnDataObj("Platform does not exists!", 400, false);
    }

    // add logo to user edges
    const userEdgesWithLogo = Promise.all(
      userEdges.map(async (edge) => {
        let data = {};
        if (edge.type !== "DEFAULT") {
          data = {
            ...edge.toJSON(),
            relation_id: {
              ...edge.relation_id.toJSON(),
              ...(edge.relation_id.logo && {
                logo: await s3fileUploadService.generatePresignedUrl({
                  key: `groupos/${edge.relation_id._id}/${edge.relation_id.logo}`,
                }),
              }),
              ...(edge.relation_id.banner && {
                banner: await s3fileUploadService.generatePresignedUrl({
                  key: `groupos/${edge.relation_id._id}/${edge.relation_id.banner}`,
                }),
              }),
              ...(edge.relation_id.avtar && {
                avtar: await s3fileUploadService.generatePresignedUrl({
                  key: `groupos/${edge.relation_id._id}/${edge.relation_id.avtar}`,
                }),
              }),
            },
          };
        } else {
          data = {
            ...edge.toJSON(),
          };
        }
        return data;
      })
    );

    const filterData = JSON.parse(JSON.stringify(preparedData));
    delete filterData?.userAvatar;
    delete filterData?.relation_id?.banner;
    delete filterData?.relation_id?.logo;
    delete filterData?.relation_id?.subscription_reminder_renewal_config_id;
    delete filterData?.relation_id?.avtar;
    delete filterData?.relation_id?.tagId;
    delete filterData?.relation_id?.accessible_groups;
    delete filterData?.relation_id?.groupos_join_date;
    delete filterData?.relation_id?.migration_date;

    const token = await this.generateToken({
      data: {
        userId: userExists._id,
        userName: `${userExists.first_name}${userExists.last_name}`,
        userEmail: userExists["Preferred Email"],
        userAvatar: userExists?.profileImg,
        userStatus: userExists.status,
        // userEdges: await userEdgesWithLogo,
        currentEdge: { ...filterData },
        // platform: platformExists.data,
      },
    });

    let data = {
      token: `Bearer ${token}`,
      userId: userExists._id,
      first_name: userExists.first_name,
      last_name: userExists.last_name,
      userName: `${userExists.first_name} ${userExists.last_name}`,
      display_name: userExists.display_name,
      userEmail: userExists["Preferred Email"],
      userAvatar: userExists?.profileImg,
      userStatus: userExists.status,
      userEdges: isPresignRequire ? await userEdgesWithLogo : userEdges,
      currentEdge: preparedData,
      platform: platformExists,
    };

    // MDS migration
    // check if user belongs to mds and missing subscription_id
    // let migrationData;
    // if (
    //   edgeData?.relation_id &&
    //   edgeData?.relation_id?.nickname === "mds" &&
    //   !edgeData.subscription_id
    // ) {
    //   // get the migration data
    //   migrationData = await userMigrationsService.repository.findUserMigration({
    //     filter: {
    //       email: userExists["Preferred Email"],
    //     },
    //   });
    // }

    // if (migrationData) {
    //   data.migrationData = migrationData;
    // }

    // prepare user data
    return ReturnDataObj({
      data: data,
    });
  };

  /**
   * @description Account information for PO, CO, M, V
   */
  fetchSessionInfo = async ({ token, isDefaultEdge }) => {
    // decode jwt token and get user info
    const sessionData = jwt.decode(token);
    // check if user exist
    const userExists = await this.repository.findUser({
      filter: { "Preferred Email": sessionData.userEmail, isDelete: false },
    });
    if (!userExists) {
      return ReturnDataObj("User does not exists!", 401, false);
    }

    // check if user is active
    if (userExists.status === "INACTIVE") {
      return ReturnDataObj("Account not activated!", 401, false);
    }

    // check if user is blocked
    if (userExists.status === "BLOCKED") {
      return ReturnDataObj("Account blocked!", 401, false);
    }
    let userEdgeId = sessionData.currentEdge._id;

    // find the current user edge if exists
    if (sessionData.currentEdge) {
      const userEdges = await userEdgesService.repository.findUserEdges({
        filter: {
          user_id: userExists._id,
          isDelete: false,
        },
        expand: true,
      });

      if (isDefaultEdge && isDefaultEdge === true) {
        userEdgeId = userEdges.find((edge) => edge.type === "DEFAULT")._id;
      } else if (userEdges.length == 2) {
        // prepare data for the primary edge (skip default edge)
        userEdgeId = userEdges.find((edge) => edge.type !== "DEFAULT")._id;
      }
      const edgeExists = userEdges.find(
        (edge) => edge._id.toString() === sessionData.currentEdge._id
      );

      // check if user edge is exists
      if (!edgeExists) {
        return ReturnDataObj("You are no longer part of this edge", 401, false);
      }
    }

    if (userExists.refresh_token) {
      // remove the refresh token flag
      await this.repository.updateUser({
        user_id: userExists._id,
        userData: {
          refresh_token: null,
        },
      });
    }

    // return the jwt data if everything is okay
    // login user with new edge
    let { data: userLogin } = await this.edgeLogin({
      userEdgeId: userEdgeId,
      userId: userExists._id,
    });

    //* Add the groupos join date for the user if date is not present in the user edge
    if (userEdgeId) {
      const data = await userEdgesService.repository.findAndUpdateUserEdge({
        filter: {
          _id: userEdgeId,
          groupos_join_date: null,
        },
        userEdgeData: {
          $set: {
            groupos_join_date: new Date(),
          },
        },
      });
    }

    // return the new login data
    return ReturnDataObj(userLogin.data);
  };

  fetchUserInfo = async ({ userId, relation_id }) => {
    // check if user exist
    const userExists = await this.repository.getUser({
      userId,
    });
    if (!userExists) {
      return ReturnDataObj("User does not exists!", 401, false);
    }

    // check if user is active
    if (userExists.status === "INACTIVE") {
      return ReturnDataObj("Account not activated!", 401);
    }

    // check if user is blocked
    if (userExists.status === "BLOCKED") {
      return ReturnDataObj("Account blocked!", 401);
    }

    // find user edge
    const edgeData = await userEdgesService.repository.findUserEdge({
      filter: {
        user_id: userId,
        relation_id: relation_id,
        isDelete: false,
      },
      expand: true,
    });
    if (!edgeData) {
      return ReturnDataObj("Edge does not exists!", 401);
    }

    const preparedData = {};
    preparedData["_id"] = edgeData._id;

    if (edgeData.relation_id) {
      if (edgeData.relation_id.banner) {
        edgeData.relation_id.banner =
          await s3fileUploadService.generatePresignedUrl({
            key: `groupos/${edgeData.relation_id._id}/${edgeData.relation_id.banner}`,
          });
      }
      edgeData.relation_id.logo =
        await s3fileUploadService.generatePresignedUrl({
          key: `groupos/${edgeData.relation_id._id}/${edgeData.relation_id.logo}`,
        });
      preparedData["relation_id"] = edgeData.relation_id;
    }

    preparedData["platform"] = edgeData.platform;
    preparedData["user_id"] = edgeData.user_id;
    preparedData["owner"] = edgeData.owner;
    preparedData["role_id"] = {
      _id: edgeData.role_id._id,
      role_name: edgeData.role_id.role_name,
    };
    preparedData["AdminScopes"] =
      edgeData.role_id.admin_scope_id.admin_modules.map(
        (module) => module.name
      );
    preparedData["type"] = edgeData.type;
    preparedData["stripe_customer_db_id"] = edgeData.stripe_customer_db_id;

    if (edgeData.stripe_account_db_id) {
      preparedData["stripe_account_db_id"] = edgeData.stripe_account_db_id;
    }

    if (edgeData.subscription_id) {
      preparedData["subscription_id"] = edgeData.subscription_id;
      const response = await axios.get(
        `${GATEWAY_DOMAIN}/api/billings/api/v1/subscriptions/${edgeData.subscription_id}`
      );
      preparedData["scopes"] = response.data.scope_id.modules.map(
        (module) => module.system_id
      );
    }

    // return the result
    return ReturnDataObj({
      userId: userExists._id,
      userName: `${userExists.first_name} ${userExists.last_name}`,
      userEmail: userExists["Preferred Email"],
      userAvatar: userExists?.profileImg,
      userStatus: userExists.status,
      currentEdge: preparedData,
    });
  };

  addUserEdge = async ({ userId, userEdgeId }) => {
    // find user
    const userExists = await this.repository.getUser({ userId });
    if (!userExists) {
      return ReturnDataObj("User does not exists!", 200, false);
    }

    const checkForUpdate = userExists?._doc.user_edges.filter((edge) => {
      console.log({
        edge: `${edge ?? ""}`.toString(),
        userEdgeId: userEdgeId.toString(),
      });
      return `${edge ?? ""}`?.toString() === userEdgeId.toString();
    });

    if (checkForUpdate.length !== 0) {
      return ReturnDataObj(userExists);
    } else {
      // add user edge
      const user = await this.repository.updateUser({
        user_id: userId,
        userData: {
          user_edges: [...userExists.user_edges, userEdgeId],
        },
      });
      return ReturnDataObj(user);
    }
  };

  removeUserEdge = async ({ userId, userEdgeId }) => {
    // find user
    const userExists = await this.repository.getUser({ userId });
    if (!userExists) {
      return ReturnDataObj("User does not exists!", 200, false);
    }
    // remove user edge
    const user = await this.repository.updateUser({
      user_id: userId,
      userData: {
        user_edges: userExists.user_edges.filter(
          (edge) => edge.toString() !== userEdgeId.toString()
        ),
      },
    });
    return ReturnDataObj(user);
  };

  importUser = async ({ email, first_name, last_name, tiers, relation_id }) => {
    let host = null;
    // find community
    const communityExists = await communitiesService.repository.findCommunity({
      filter: {
        _id: relation_id,
        isDelete: false,
      },
    });
    if (!communityExists) {
      return ReturnDataObj("Community does not exists!", 400, false);
    }

    const checkInvitedUser = await this.inviteUsersRepository.getImportUser({
      filter: {
        email,
        relation_id,
        status: { $nin: ["REVOKED"] },
      },
    });

    if (checkInvitedUser) {
      return ReturnDataObj(
        "User with same email already invited in this community",
        400,
        false
      );
    }

    // check if user exists
    let userExists = await this.repository.findUser({
      filter: {
        "Preferred Email": email,
      },
    });

    if (userExists) {
      // check if user already have an edge with the provided relation_id
      if (
        userExists.user_edges.find(
          (edge) =>
            edge.relation_id?.toString() === communityExists._id?.toString()
        )
      ) {
        return ReturnDataObj(
          "User with same email already exists in this community",
          400,
          false
        );
      }

      if (
        communityExists &&
        communityExists?.customDomain?.status === "active"
      ) {
        host = communityExists?.customDomain?.isMainDomain
          ? `https://www.${communityExists?.customDomain?.domain}`
          : `https://${communityExists?.customDomain?.domain}`;
      } else {
        host = `https://${communityExists.nickname}.${BASE_URL}`;
      }

      //* Add invited user in the saperate table
      userExists = await this.inviteUsersRepository.createImportUser({
        payload: {
          first_name,
          last_name,
          email,
          tiers,
          relation_id,
        },
      });
    } else {
      // create user
      if (
        communityExists &&
        communityExists?.customDomain?.status === "active"
      ) {
        host = communityExists?.customDomain?.isMainDomain
          ? `https://www.${communityExists?.customDomain?.domain}`
          : `https://${communityExists?.customDomain?.domain}`;
      } else {
        host = `https://${communityExists.nickname}.${BASE_URL}`;
      }

      //* Add invited user in the saperate table
      userExists = await this.inviteUsersRepository.createImportUser({
        payload: {
          first_name,
          last_name,
          email,
          tiers,
          relation_id,
        },
      });
    }

    let communitiyLogo = null;
    //* create the community s3 logo
    if (communityExists?.logo) {
      communitiyLogo = `https://${AWS_BUCKET}.s3.us-east-2.amazonaws.com/groupos/${communityExists?._id}/${communityExists?.logo}`;
    }

    let encodeEmail = encodeURIComponent(userExists?.email);
    // send invitation email notification to user
    mailService.sendMail({
      to: email,
      subject: `Welcome to ${communityExists.name}!`,
      template: "community-invitation",
      context: {
        name: `${first_name} ${last_name}`,
        communityName: communityExists.name,
        grouposLink: `${host ?? FRONTEND_DOMAIN}/community/${
          communityExists.nickname
        }?invite-id=${userExists?._id}&email=${userExists?.email}&first_name=${
          userExists?.first_name
        }&last_name=${userExists?.last_name}&encode_email=${encodeEmail}`,
        communitiyLogo,
        communityChar: communityExists.name.split("")[0],
      },
      relationId: communityExists?._id ? communityExists._id : "",
      customsmtp: communityExists?.customSMTP
        ? communityExists.customSMTP
        : false,
    });
    return ReturnDataObj({ userExists /* userEdge, CAEdge */ });
  };

  /**
   * Service for the resend the email for the invite user
   */
  resendInviteUser = async ({ id }) => {
    let host = null;

    let getInviteUser = await this.inviteUsersRepository.getImportUser({
      filter: {
        _id: id,
        status: {
          $ne: "ACCEPTED",
        },
      },
    });

    if (!getInviteUser) {
      return ReturnDataObj("Invite User not found", 400, false);
    }

    // find community
    const communityExists = await communitiesService.repository.findCommunity({
      filter: {
        _id: getInviteUser.relation_id,
        isDelete: false,
      },
    });
    if (!communityExists) {
      return ReturnDataObj("Community does not exists!", 400, false);
    }

    if (communityExists && communityExists?.customDomain?.status === "active") {
      host = communityExists?.customDomain?.isMainDomain
        ? `https://www.${communityExists?.customDomain?.domain}`
        : `https://${communityExists?.customDomain?.domain}`;
    } else {
      host = `https://${communityExists.nickname}.${BASE_URL}`;
    }

    let communitiyLogo = null;
    //* create the community s3 logo
    if (communityExists?.logo) {
      communitiyLogo = `https://${AWS_BUCKET}.s3.us-east-2.amazonaws.com/groupos/${communityExists?._id}/${communityExists?.logo}`;
    }

    let encodeEmail = encodeURIComponent(getInviteUser?.email);
    // send invitation email notification to user
    mailService.sendMail({
      to: getInviteUser.email,
      subject: `Welcome to ${communityExists.name}!`,
      template: "community-invitation",
      context: {
        name: `${getInviteUser.first_name} ${getInviteUser.last_name}`,
        communityName: communityExists.name,
        grouposLink: `${host ?? FRONTEND_DOMAIN}/community/${
          communityExists.nickname
        }?invite-id=${getInviteUser._id}&email=${
          getInviteUser?.email
        }&first_name=${getInviteUser?.first_name}&last_name=${
          getInviteUser?.last_name
        }&encode_email=${encodeEmail}`,
        communitiyLogo,
        communityChar: communityExists.name.split("")[0],
      },
      relationId: communityExists?._id ? communityExists._id : "",
      customsmtp: communityExists?.customSMTP
        ? communityExists.customSMTP
        : false,
    });

    if (getInviteUser.status === "REVOKED") {
      getInviteUser = await this.inviteUsersRepository.updateImportUser({
        filter: {
          _id: getInviteUser._id,
        },
        data: {
          status: "PENDING",
        },
      });
    }
    return ReturnDataObj({ data: getInviteUser });
  };

  bulkImportUsers = async ({ relation_id, tempFilePath }) => {
    const HashMap = {};
    let totalData = 0;

    const operation = new Promise((resolve) => {
      const thisRef = this;
      fs.createReadStream(tempFilePath)
        .pipe(parse({ delimiter: ",", from_line: 2 }))
        .on("data", async function (row) {
          if (row[0].trim()) {
            totalData = totalData + 1;

            const {
              data: { userExists },
            } = await thisRef.importUser({
              email: row[0].trim(),
              first_name: row[1].trim(),
              last_name: row[2].trim(),
              relation_id,
            });

            HashMap[row[0]] = {
              userExists,
            };
            if (totalData === Object.keys(HashMap).length) {
              resolve();
            }
          }
        })
        .on("end", function () {
          console.log("finished", totalData);
        })
        .on("error", function (error) {
          console.log("error", error.message);
        });
    });

    await operation;
    return ReturnDataObj({ HashMap: Object.values(HashMap) });
  };

  /**
   * Service for the get the invite user list
   */
  getImportUser = async ({
    page,
    limit,
    search,
    sortBy = "createdAt",
    sortOrder = "Desc",
    relation_id,
    isAllData,
    type,
  }) => {
    const pageNumber = parseInt(page) || 1;
    const limitNumber = parseInt(limit) || 10;
    const skip = (pageNumber - 1) * limitNumber;

    let searchFilter = search
      ? {
          $or: [
            { email: { $regex: search, $options: "i" } },
            ...(search.trim().split(" ").length > 1
              ? [
                  {
                    $and: [
                      {
                        first_name: {
                          $regex: search.split(" ")[0],
                          $options: "i",
                        },
                      },
                      {
                        last_name: {
                          $regex: search.split(" ")[1],
                          $options: "i",
                        },
                      },
                    ],
                  },
                ]
              : [
                  { first_name: { $regex: search, $options: "i" } },
                  { last_name: { $regex: search, $options: "i" } },
                ]),
          ],
        }
      : null;

    const filter = {
      relation_id,
      ...(type && {
        status: type,
      }),
      ...searchFilter,
    };

    const getAllInviteUserCount =
      await this.inviteUsersRepository.getAllImportUserCount({
        filter,
      });

    const getAllInviteUser = await this.inviteUsersRepository.getAllImportUser({
      filter,
      skip,
      limit,
      sortBy,
      sortOrder: sortOrder === "Asc" ? 1 : -1,
      isAllData,
    });

    return ReturnDataObj({
      data: getAllInviteUser,
      count: getAllInviteUserCount,
      page: pageNumber,
    });
  };

  /**
   * Service for the update the invite user
   */
  updateImportUser = async ({ id, body }) => {
    const updateInviteUser = await this.inviteUsersRepository.updateImportUser({
      filter: {
        _id: id,
      },
      data: {
        first_name: body?.first_name,
        last_name: body?.last_name,
        tiers: body?.tiers,
      },
    });

    if (!updateInviteUser) {
      return ReturnDataObj("Invite User not found", 400, false);
    }

    return ReturnDataObj({
      data: updateInviteUser,
    });
  };

  /**
   * Service for the revoke the invite user by id
   */
  revokeImportUserById = async ({ id }) => {
    let host = null;
    const getInviteUser = await this.inviteUsersRepository.getImportUser({
      filter: {
        _id: id,
        status: "PENDING",
        isDelete: false,
      },
    });
    if (!getInviteUser) {
      return ReturnDataObj("Invite User not found", 400, false);
    }

    const communityExists = await communitiesService.repository.findCommunity({
      filter: {
        _id: getInviteUser.relation_id,
        isDelete: false,
      },
    });
    if (!communityExists) {
      return ReturnDataObj("Community does not exists!", 400, false);
    }

    const updateInviteUser = await this.inviteUsersRepository.updateImportUser({
      filter: {
        _id: getInviteUser._id,
      },
      data: {
        status: "REVOKED",
      },
    });

    if (!updateInviteUser) {
      return ReturnDataObj("Invite User not found", 400, false);
    }

    if (communityExists && communityExists?.customDomain?.status === "active") {
      host = communityExists?.customDomain?.isMainDomain
        ? `https://www.${communityExists?.customDomain?.domain}`
        : `https://${communityExists?.customDomain?.domain}`;
    } else {
      host = `https://${communityExists.nickname}.${BASE_URL}`;
    }

    let communitiyLogo = null;
    //* create the community s3 logo
    if (communityExists?.logo) {
      communitiyLogo = `https://${AWS_BUCKET}.s3.us-east-2.amazonaws.com/groupos/${communityExists?._id}/${communityExists?.logo}`;
    }

    // send invitation email notification to user
    mailService.sendMail({
      to: getInviteUser.email,
      subject: "Community Revoke Invitation!",
      template: "revoke-invitation",
      context: {
        name: `${getInviteUser.first_name} ${getInviteUser.last_name}`,
        communityName: communityExists.name,
        grouposLink: `${host ?? FRONTEND_DOMAIN}/community/${
          communityExists.nickname
        }`,
        communitiyLogo,
        communityChar: communityExists.name.split("")[0],
      },
      relationId: communityExists?._id ? communityExists._id : "",
      customsmtp: communityExists?.customSMTP
        ? communityExists.customSMTP
        : false,
    });

    //* check that user is login or signup in airtable-sync
    const userData = await this.repository.findUser({
      filter: { "Preferred Email": getInviteUser.email, isDelete: false },
    });

    if (userData) {
      //* cehck that the user edge is created of not for the community
      const userEdge = await this.#userEdgesService.repository.findUserEdge({
        filter: {
          user_id: userData._id,
          relation_id: getInviteUser.relation_id,
          type: "M",
          isDelete: false,
        },
      });

      if (userEdge) {
        await this.#userEdgesService.repository.updateUserEdge({
          userEdgeId: userEdge._id,
          userEdgeData: {
            isDelete: true,
            deleteFrom: "revokeImportUserById",
          },
        });
      }
    }
    return ReturnDataObj({
      data: updateInviteUser,
    });
  };

  /**
   * Service for the get the invite user by id
   */
  getImportUserById = async ({ id }) => {
    const getInviteUser = await this.inviteUsersRepository.getImportUser({
      filter: {
        _id: id,
      },
    });

    if (!getInviteUser) {
      return ReturnDataObj("Invite User not found", 400, false);
    }

    return ReturnDataObj({
      data: getInviteUser,
    });
  };

  getImportUserByEmail = async ({ email }) => {
    const getInviteUser = await this.inviteUsersRepository.getImportUser({
      filter: {
        email: email,
        status: "PENDING",
        isDelete: false,
      },
    });

    if (!getInviteUser) {
      return ReturnDataObj("Invite User not found", 200, false);
    }

    return ReturnDataObj({
      data: getInviteUser,
    });
  };

  /**
   * Service for the get the invite user for the billing service
   */
  fetchImportUser = async ({ id = null, relation_id = null, email = null }) => {
    const getInviteUser = await this.inviteUsersRepository.getImportUser({
      filter: {
        ...(id && { _id: id }),
        ...(relation_id && { relation_id }),
        ...(email && { email }),
      },
    });

    return ReturnDataObj({
      data: getInviteUser || {},
    });
  };

  /**
   * Service for the get the suggestion for invite user
   */
  getSuggestionImportUser = async ({ relation_id, status }) => {
    const filter = {
      relation_id,
      isDelete: false,
      ...(status && { status }),
    };

    const getSuggestionInviteUser =
      await this.inviteUsersRepository.getSuggestionImportUser({
        filter,
      });

    return ReturnDataObj({ data: getSuggestionInviteUser });
  };

  /*
   * create guest user service
   */
  inviteGuestUserCreate = async ({
    email,
    first_name,
    last_name,
    relation_id,
  }) => {
    let host = null;
    // find community
    const communityExists = await communitiesService.repository.findCommunity({
      filter: {
        _id: relation_id,
        isDelete: false,
      },
    });
    if (!communityExists) {
      return ReturnDataObj("Community does not exists!", 400, false);
    }

    const checkInvitedUser = await this.guestUserRepository.getInviteGuestUser({
      filter: {
        email,
        relation_id,
      },
    });

    if (checkInvitedUser) {
      return ReturnDataObj(
        "User with same email already invited in this community",
        400,
        false
      );
    }
    // check if user exists
    let userExists = await this.repository.findUser({
      filter: {
        "Preferred Email": email,
      },
    });
    if (userExists) {
      // check if user already have an edge with the provided relation_id
      if (
        userExists.user_edges.find(
          (edge) =>
            edge.relation_id?.toString() === communityExists._id?.toString()
        )
      ) {
        return ReturnDataObj(
          "User with same email already exists in this community",
          400,
          false
        );
      }

      if (
        communityExists &&
        communityExists?.customDomain?.status === "active"
      ) {
        host = communityExists?.customDomain?.isMainDomain
          ? `https://www.${communityExists?.customDomain?.domain}`
          : `https://${communityExists?.customDomain?.domain}`;
      } else {
        host = `https://${communityExists.nickname}.${BASE_URL}`;
      }

      //* Add invited user in the saperate table
      userExists = await this.guestUserRepository.createInviteGuestUser({
        payload: {
          first_name,
          last_name,
          email,
          relation_id,
        },
      });
    } else {
      // create user
      if (
        communityExists &&
        communityExists?.customDomain?.status === "active"
      ) {
        host = communityExists?.customDomain?.isMainDomain
          ? `https://www.${communityExists?.customDomain?.domain}`
          : `https://${communityExists?.customDomain?.domain}`;
      } else {
        host = `https://${communityExists.nickname}.${BASE_URL}`;
      }

      //* Add invited user in the saperate table
      userExists = await this.guestUserRepository.createInviteGuestUser({
        payload: {
          first_name,
          last_name,
          email,
          relation_id,
        },
      });
    }

    let communitiyLogo = null;
    //* create the community s3 logo
    if (communityExists?.logo) {
      communitiyLogo = `https://${AWS_BUCKET}.s3.us-east-2.amazonaws.com/groupos/${communityExists?._id}/${communityExists?.logo}`;
    }

    // send invitation email notification to user
    mailService.sendMail({
      to: email,
      subject: "Community Guest Invitation!",
      template: "community-invitation",
      context: {
        name: `${first_name} ${last_name}`,
        communityName: communityExists.name,
        // grouposLink: `${host ?? FRONTEND_DOMAIN}/community/${communityExists.nickname
        //   }?invite-id=${userExists._id}`,
        grouposLink: `${host ?? FRONTEND_DOMAIN}/login`,
        communitiyLogo,
        communityChar: communityExists.name.split("")[0],
      },
      relationId: communityExists?._id ? communityExists._id : "",
      customsmtp: communityExists?.customSMTP
        ? communityExists.customSMTP
        : false,
    });
    return ReturnDataObj({ userExists /* userEdge, CAEdge */ });
  };

  /**
   * Service for the resend the email for the invite guest user
   */
  resendInviteGuestUser = async ({ id }) => {
    let host = null;

    let getInviteGuestUser = await this.guestUserRepository.getInviteGuestUser({
      filter: {
        _id: id,
        status: {
          $ne: "ACCEPTED",
        },
      },
    });

    if (!getInviteGuestUser) {
      return ReturnDataObj("Invite guest User not found", 400, false);
    }

    // find community
    const communityExists = await communitiesService.repository.findCommunity({
      filter: {
        _id: getInviteGuestUser.relation_id,
        isDelete: false,
      },
    });
    if (!communityExists) {
      return ReturnDataObj("Community does not exists!", 400, false);
    }

    if (communityExists && communityExists?.customDomain?.status === "active") {
      host = communityExists?.customDomain?.isMainDomain
        ? `https://www.${communityExists?.customDomain?.domain}`
        : `https://${communityExists?.customDomain?.domain}`;
    } else {
      host = `https://${communityExists.nickname}.${BASE_URL}`;
    }

    let communitiyLogo = null;
    //* create the community s3 logo
    if (communityExists?.logo) {
      communitiyLogo = `https://${AWS_BUCKET}.s3.us-east-2.amazonaws.com/groupos/${communityExists?._id}/${communityExists?.logo}`;
    }

    // send invitation email notification to user
    mailService.sendMail({
      to: getInviteGuestUser.email,
      subject: "Community Invitation!",
      template: "community-invitation",
      context: {
        name: `${getInviteGuestUser.first_name} ${getInviteGuestUser.last_name}`,
        communityName: communityExists.name,
        grouposLink: `${host ?? FRONTEND_DOMAIN}/community/${
          communityExists.nickname
        }`,
        communitiyLogo,
        communityChar: communityExists.name.split("")[0],
      },
      relationId: communityExists?._id ? communityExists._id : "",
      customsmtp: communityExists?.customSMTP
        ? communityExists.customSMTP
        : false,
    });

    if (getInviteGuestUser.status === "REVOKED") {
      getInviteGuestUser = await this.guestUserRepository.updateInviteGuestUser(
        {
          filter: {
            _id: getInviteGuestUser._id,
          },
          data: {
            status: "PENDING",
          },
        }
      );
    }
    return ReturnDataObj({ data: getInviteGuestUser });
  };

  /**
   * Bulk Invite guest user service
   */

  bulkInviteGuestUsers = async ({ relation_id, tempFilePath }) => {
    let host = null;
    const emailRows = [];
    let totalData = 0;
    let userExists;

    // Find community
    const communityExists = await communitiesService.repository.findCommunity({
      filter: {
        _id: relation_id,
        isDelete: false,
      },
    });
    if (!communityExists) {
      return ReturnDataObj("Community does not exist!", 400, false);
    }

    // Load emails from CSV into memory
    await new Promise((resolve, reject) => {
      fs.createReadStream(tempFilePath)
        .pipe(parse({ delimiter: ",", from_line: 2 }))
        .on("data", (row) => {
          totalData += 1;
          emailRows.push({
            email: row[0],
            first_name: row[1],
            last_name: row[2],
          });
        })
        .on("end", resolve)
        .on("error", reject);
    });

    if (communityExists.customDomain?.status === "active") {
      host = communityExists.customDomain.isMainDomain
        ? `https://www.${communityExists.customDomain.domain}`
        : `https://${communityExists.customDomain.domain}`;
    } else {
      host = `https://${communityExists.nickname}.${BASE_URL}`;
    }

    const payloadData = {
      emailRows,
      relation_id,
    };
    // Bulk create invited users
    if (emailRows.length > 0) {
      userExists = await this.guestUserRepository.createInviteGuestUsersBulk({
        payloads: payloadData,
      });
    }
    let createdUsers = [];
    if (userExists.insertedIds.length > 0) {
      const filter = {
        _id: { $in: userExists.insertedIds },
      };
      createdUsers = await this.guestUserRepository.findGuestUsers({ filter });
    } else {
      return ReturnDataObj(
        "All User with same email already invited in this community",
        400,
        false
      );
    }

    // Format the response to match the required structure
    const userResponse = createdUsers.map((user) => ({
      first_name: user.first_name,
      last_name: user.last_name,
      email: user.email,
      relation_id: user.relation_id,
      status: user.status,
      isDelete: user.isDelete,
      _id: user._id,
    }));

    let communityLogo = null;
    // Create the community S3 logo
    if (communityExists?.logo) {
      communityLogo = `https://${AWS_BUCKET}.s3.us-east-2.amazonaws.com/groupos/${communityExists?._id}/${communityExists?.logo}`;
    }

    // Send invitation emails
    for (const { email, first_name, last_name } of emailRows) {
      mailService.sendMail({
        to: email,
        subject: "Community Guest Invitation!",
        template: "community-invitation",
        context: {
          name: `${first_name} ${last_name}`,
          communityName: communityExists.name,
          grouposLink: `${host ?? FRONTEND_DOMAIN}/login`,
          communityLogo,
          communityChar: communityExists.name.split("")[0],
        },
        relationId: communityExists?._id ? communityExists._id : "",
        customsmtp: communityExists?.customSMTP
          ? communityExists.customSMTP
          : false,
      });
    }

    // Delete the CSV file after processing
    fs.unlink(tempFilePath, (err) => {
      if (err) {
        console.error("Error deleting the file:", err.message);
      } else {
        console.log("CSV file deleted successfully");
      }
    });

    return ReturnDataObj({ userExists: userResponse });
  };

  /**
   * Service for the get the guest invite user list
   */
  getInviteGuestUserList = async ({
    page,
    limit,
    search,
    sortBy = "createdAt",
    sortOrder = "Desc",
    relation_id,
    isAllData,
    type,
  }) => {
    const pageNumber = parseInt(page) || 1;
    const limitNumber = parseInt(limit) || 10;
    const skip = (pageNumber - 1) * limitNumber;

    let searchFilter = search
      ? {
          $or: [
            { email: { $regex: search, $options: "i" } },
            ...(search.trim().split(" ").length > 1
              ? [
                  {
                    $and: [
                      {
                        first_name: {
                          $regex: search.split(" ")[0],
                          $options: "i",
                        },
                      },
                      {
                        last_name: {
                          $regex: search.split(" ")[1],
                          $options: "i",
                        },
                      },
                    ],
                  },
                ]
              : [
                  { first_name: { $regex: search, $options: "i" } },
                  { last_name: { $regex: search, $options: "i" } },
                ]),
          ],
        }
      : null;

    const filter = {
      relation_id,
      ...(type && {
        status: type,
      }),
      ...searchFilter,
    };

    const getAllInviteUserCount =
      await this.guestUserRepository.getAllInviteGuestUserCount({
        filter,
      });

    const getAllInviteUser =
      await this.guestUserRepository.getAllInviteGuestUser({
        filter,
        skip,
        limit,
        sortBy,
        sortOrder: sortOrder === "Asc" ? 1 : -1,
        isAllData,
      });

    return ReturnDataObj({
      data: getAllInviteUser,
      count: getAllInviteUserCount,
      page: pageNumber,
    });
  };

  /**
   * Service for the update the invite guest user
   */
  updateInviteGuestUser = async ({ id, body }) => {
    const updateInviteUser =
      await this.guestUserRepository.updateInviteGuestUser({
        filter: {
          _id: id,
        },
        data: {
          first_name: body.first_name,
          last_name: body.last_name,
        },
      });

    if (!updateInviteUser) {
      return ReturnDataObj("Invite User not found", 400, false);
    }

    return ReturnDataObj({
      data: updateInviteUser,
    });
  };

  /**
   * Service for the revoke the invite guest user by id
   */
  revokeInviteGuestUserById = async ({ id }) => {
    let host = null;
    const getInviteUser = await this.guestUserRepository.getInviteGuestUser({
      filter: {
        _id: id,
        status: "PENDING",
        isDelete: false,
      },
    });
    if (!getInviteUser) {
      return ReturnDataObj("Invite User not found", 400, false);
    }

    const communityExists = await communitiesService.repository.findCommunity({
      filter: {
        _id: getInviteUser.relation_id,
        isDelete: false,
      },
    });
    if (!communityExists) {
      return ReturnDataObj("Community does not exists!", 400, false);
    }

    const updateInviteUser =
      await this.guestUserRepository.updateInviteGuestUser({
        filter: {
          _id: getInviteUser._id,
        },
        data: {
          status: "REVOKED",
        },
      });

    if (!updateInviteUser) {
      return ReturnDataObj("Guest invite User not found", 400, false);
    }

    if (communityExists && communityExists?.customDomain?.status === "active") {
      host = communityExists?.customDomain?.isMainDomain
        ? `https://www.${communityExists?.customDomain?.domain}`
        : `https://${communityExists?.customDomain?.domain}`;
    } else {
      host = `https://${communityExists.nickname}.${BASE_URL}`;
    }

    let communitiyLogo = null;
    //* create the community s3 logo
    if (communityExists?.logo) {
      communitiyLogo = `https://${AWS_BUCKET}.s3.us-east-2.amazonaws.com/groupos/${communityExists?._id}/${communityExists?.logo}`;
    }

    // send invitation email notification to user
    mailService.sendMail({
      to: getInviteUser.email,
      subject: "Community Revoke Invitation!",
      template: "revoke-invitation",
      context: {
        name: `${getInviteUser.first_name} ${getInviteUser.last_name}`,
        communityName: communityExists.name,
        grouposLink: `${host ?? FRONTEND_DOMAIN}/community/${
          communityExists.nickname
        }`,
        communitiyLogo,
        communityChar: communityExists.name.split("")[0],
      },
      relationId: communityExists?._id ? communityExists._id : "",
      customsmtp: communityExists?.customSMTP
        ? communityExists.customSMTP
        : false,
    });

    //* check that user is login or signup in airtable-sync
    const userData = await this.repository.findUser({
      filter: { "Preferred Email": getInviteUser.email, isDelete: false },
    });

    if (userData) {
      //* cehck that the user edge is created of not for the community
      const userEdge = await this.#userEdgesService.repository.findUserEdge({
        filter: {
          email: userData._id,
          relation_id: getInviteUser.relation_id,
          type: "GU",
          isDelete: false,
        },
      });

      if (userEdge) {
        await this.#userEdgesService.repository.updateUserEdge({
          userEdgeId: userEdge._id,
          userEdgeData: {
            isDelete: true,
            deleteFrom: "revokeInviteGuestUserById",
          },
        });
      }
    }
    return ReturnDataObj({
      data: updateInviteUser,
    });
  };

  /**
   * Service for the get the invite user by id
   */
  getInviteGuestUserById = async ({ id }) => {
    const getInviteGuestUser =
      await this.guestUserRepository.getInviteGuestUser({
        filter: {
          _id: id,
        },
      });

    if (!getInviteGuestUser) {
      return ReturnDataObj(" Guest invite User not found", 400, false);
    }

    return ReturnDataObj({
      data: getInviteGuestUser,
    });
  };

  /**
   * Service for the get the suggestion for invite user
   */
  getSuggestionInviteGuestUser = async ({ relation_id, status }) => {
    const filter = {
      relation_id,
      isDelete: false,
      ...(status && { status }),
    };

    const getSuggestionInviteGuestUser =
      await this.guestUserRepository.getSuggestionGuestUser({
        filter,
      });

    return ReturnDataObj({ data: getSuggestionInviteGuestUser });
  };

  /**
   * Service for the inActive the invite guest user by id
   */
  inActiveInviteGuestUserById = async ({ id }) => {
    let host = null;
    const getInviteUser = await this.guestUserRepository.getInviteGuestUser({
      filter: {
        _id: id,
        isDelete: false,
      },
    });
    if (!getInviteUser) {
      return ReturnDataObj("Invite User not found", 400, false);
    }

    const communityExists = await communitiesService.repository.findCommunity({
      filter: {
        _id: getInviteUser.relation_id,
        isDelete: false,
      },
    });
    if (!communityExists) {
      return ReturnDataObj("Community does not exists!", 400, false);
    }

    const updateInviteUser =
      await this.guestUserRepository.updateInviteGuestUser({
        filter: {
          _id: getInviteUser._id,
        },
        data: {
          status: "INACTIVE",
        },
      });

    if (!updateInviteUser) {
      return ReturnDataObj("Guest invite User not found", 400, false);
    }

    if (communityExists && communityExists?.customDomain?.status === "active") {
      host = communityExists?.customDomain?.isMainDomain
        ? `https://www.${communityExists?.customDomain?.domain}`
        : `https://${communityExists?.customDomain?.domain}`;
    } else {
      host = `https://${communityExists.nickname}.${BASE_URL}`;
    }

    let communitiyLogo = null;
    //* create the community s3 logo
    if (communityExists?.logo) {
      communitiyLogo = `https://${AWS_BUCKET}.s3.us-east-2.amazonaws.com/groupos/${communityExists?._id}/${communityExists?.logo}`;
    }

    // send invitation email notification to user
    mailService.sendMail({
      to: getInviteUser.email,
      subject: "Community inactive guest user",
      template: "inactive-guest-user",
      context: {
        name: `${getInviteUser.first_name} ${getInviteUser.last_name}`,
        communityName: communityExists.name,
        grouposLink: `${host ?? FRONTEND_DOMAIN}/community/${
          communityExists.nickname
        }`,
        communitiyLogo,
        communityChar: communityExists.name.split("")[0],
      },
      relationId: communityExists?._id ? communityExists._id : "",
      customsmtp: communityExists?.customSMTP
        ? communityExists.customSMTP
        : false,
    });

    //* check that user is login or signup in airtable-sync
    const userData = await this.repository.findUser({
      filter: { "Preferred Email": getInviteUser.email, isDelete: false },
    });

    if (userData) {
      //* check that the user edge is created of not for the community
      const userEdge = await this.#userEdgesService.repository.findUserEdge({
        filter: {
          email: userData._id,
          relation_id: getInviteUser.relation_id,
          type: "GU",
          isDelete: false,
        },
      });

      if (userEdge) {
        await this.#userEdgesService.repository.updateUserEdge({
          userEdgeId: userEdge._id,
          userEdgeData: {
            isDelete: true,
            deleteFrom: "inActiveInviteGuestUserById",
          },
        });
      }
    }
    return ReturnDataObj({
      data: updateInviteUser,
    });
  };

  /**
   * Service for the get all the member and guest user
   */
  getAllCommunityMembersAndGuest = async ({
    page,
    limit,
    expand,
    relation_id,
    search,
    type,
  }) => {
    const pageNumber = parseInt(page) || 1;
    const limitNumber = parseInt(limit) || 10;
    // calculate the skip amount from the page and limit
    const skip = (pageNumber - 1) * limitNumber;

    // Build the filter object
    const filterObj = {
      relation_id: ObjectId(relation_id),
      owner: false,
      isDelete: false,
      type: type ? type : { $in: ["M", "GU"] },
    };

    const { data: count } = await userEdgesService.countEdges({
      filter: filterObj,
      search,
    });

    // Get the paginated list of community members
    const communityMembers = await userEdgesService.repository.findAllEdgesV2({
      filter: filterObj,
      skip,
      limit: limitNumber,
      expand,
      search,
      type,
    });

    // Prepare the response list
    const communityMembersList = [];
    for (const communityMember of communityMembers) {
      const communityMemberObj = {};

      // If user_id and name exist, populate details
      if (communityMember.user_id?.name) {
        communityMemberObj["_id"] = communityMember.user_id._id;
        communityMemberObj["name"] = communityMember.user_id.name;
        communityMemberObj["email"] = communityMember.user_id.email;
        communityMemberObj["status"] = communityMember.user_id.status;
      } else {
        communityMemberObj["user_id"] = communityMember.user_id;
      }

      // Add subscription details if available
      if (communityMember.subscription_id) {
        communityMemberObj["subscription_id"] = communityMember.subscription_id;
      }

      // Push the processed object to the list
      communityMembersList.push(communityMemberObj);
    }

    // Return the final result
    return ReturnDataObj({
      data: communityMembersList,
      count,
      page: pageNumber,
      limit: limitNumber,
    });
  };

  /**
   * Platform owners
   */

  createPlatformOwner = async ({
    first_name,
    last_name,
    preferred_email,
    role_id,
  }) => {
    // check if platform exists
    const { data: platformExists } = await platformService.getPlatform();
    if (!platformExists) {
      return ReturnDataObj("Platform does not exists!", 400, false);
    }

    let community = null;
    let host = null;
    if (platformExists) {
      community = await communitiesService.repository.findCommunity({
        filter: {
          _id: platformExists._id,
        },
      });

      if (community) {
        host =
          community?.customDomain?.isMainDomain &&
          community?.customDomain?.status === "active"
            ? `https://www.${community?.customDomain?.domain}`
            : `https://${community?.customDomain?.domain}`;
      }
    }

    // check for role
    const roleExists = await rolesService.findRole({
      filter: { role_id, relation_id: platformExists._id },
    });
    if (!roleExists) {
      return ReturnDataObj("Role does not exists!", 400, false);
    }

    // check for existing user with same email
    const userExists = await this.repository.findUser({
      filter: { "Preferred Email": preferred_email },
      expand: true,
    });

    if (userExists && userExists.user_edges) {
      console.log("user exists creating new edge");

      if (
        userExists.user_edges.find(
          (edge) =>
            edge.relation_id?.toString() === platformExists._id?.toString()
        )
      ) {
        return ReturnDataObj(
          "User with same email already exists in this platform",
          400,
          false
        );
      }

      // create user edge
      const { data: userEdge } = await userEdgesService.createUserEdge({
        userEdgeData: {
          relation_id: platformExists._id,
          user_id: userExists._id,
          role_id: role_id,
          platform: true,
          type: "PO",
        },
      });

      // add the created user edge to user data
      const { data: updatedUserData } = await this.repository.updateUser({
        user_id: userExists._id,
        userData: {
          user_edges: [...userExists.user_edges, userEdge._id],
          refresh_token: true,
        },
      });

      await mailService.sendMail({
        to: userExists["Preferred Email"],
        subject: "You have been invited!",
        template: "platform-owner-invitation",
        context: {
          name: `${userExists.first_name}${userExists.last_name}`,
          platform: platformExists.name,
          link: `${host ?? FRONTEND_DOMAIN}/edge-login`,
        },
        relationId: "",
        customsmtp: false,
      });
      return ReturnDataObj(updatedUserData);
    } else {
      console.log("creating new user and edge");

      // create user
      // sign up the new user
      const { data: newUser } = await this.signUp({
        first_name: first_name,
        last_name: last_name,
        preferred_email: preferred_email,
        skipEmail: false,
        defaultActivate: false,
        host: host ?? null,
        communityId: community ? community._id : null,
        defaultActivate: true,
      });

      // create user edge
      const { data: userEdge } = await userEdgesService.createUserEdge({
        userEdgeData: {
          relation_id: platformExists._id,
          user_id: newUser._id,
          role_id: role_id,
          platform: true,
          type: "PO",
        },
      });

      // update user edges
      await this.addUserEdge({
        userId: newUser._id,
        userEdgeId: userEdge._id,
      });

      await mailService.sendMail({
        to: newUser["Preferred Email"],
        subject: "You have been invited!",
        template: "owner-account-activation",
        context: {
          name: `${newUser.first_name}${newUser.last_name}`,
          relation: platformExists.name,
          type: "Platform",
          activationLink: `${host ?? FRONTEND_DOMAIN}/signup`,
        },
        relationId: "",
        customsmtp: false,
      });

      return ReturnDataObj(newUser);
    }
  };

  getPlatformOwners = async ({ page, limit, user_id, expand, search }) => {
    // check if platform exists
    const { data: platformExists } = await platformService.getPlatform();
    if (!platformExists) {
      return ReturnDataObj("Platform does not exists!", 400, false);
    }

    const pageNumber = parseInt(page) || 1;
    const limitNumber = parseInt(limit) || 10;

    // calculate the skip amount from the
    const skip = pageNumber && limitNumber ? (pageNumber - 1) * limitNumber : 0;

    const filterObj = {
      relation_id: platformExists._id,
      user_id: { $ne: user_id },
      $and: [{ type: { $ne: "DEFAULT" } }],
      isDelete: false,
    };

    const { data: count } = await userEdgesService.countEdges({
      filter: filterObj,
      search,
    });

    // get user info and user role from the users edge
    // fetch all platform owners accept the user making API call
    const platformOwners = await userEdgesService.repository.findAllEdgesV2({
      filter: filterObj,
      skip,
      limit,
      expand,
      search,
    });

    // Do not Delete this code
    // const platformOwnersList = [];
    // for (const platformOwner of platformOwners) {
    //   const platformOwnerObj = {};
    //   if (platformOwner.role_id?.role_name) {
    //     platformOwnerObj["role"] = platformOwner.role_id.role_name;
    //   } else {
    //     platformOwnerObj["role_id"] = platformOwner.role_id;
    //   }
    //   platformOwnerObj["owner"] = platformOwner.owner;
    //   if (platformOwner.user_id?.name) {
    //     platformOwnerObj["_id"] = platformOwner.user_id._id;
    //     platformOwnerObj["name"] = platformOwner.user_id.name;
    //     platformOwnerObj["email"] = platformOwner.user_id.email;
    //     platformOwnerObj["status"] = platformOwner.user_id.status;
    //   } else {
    //     platformOwnerObj["user_id"] = platformOwner.user_id;
    //   }

    //   platformOwnersList.push(platformOwnerObj);
    // }

    return ReturnDataObj({
      data: platformOwners,
      count,
      page: pageNumber,
      limit: limitNumber,
    });
  };

  getPlatformOwnersSuggestion = async ({ user_id, expand }) => {
    // check if platform exists
    const { data: platformExists } = await platformService.getPlatform();
    if (!platformExists) {
      return ReturnDataObj("Platform does not exists!", 400, false);
    }

    const filterObj = {
      relation_id: platformExists._id,
      user_id: { $ne: user_id },
      $and: [{ type: { $ne: "DEFAULT" } }],
      isDelete: false,
    };

    // get user info and user role from the users edge
    // fetch all platform owners accept the user making API call
    const platformOwners = await userEdgesService.repository.findAllEdgesV2({
      filter: filterObj,
      expand,
    });

    return ReturnDataObj({
      data: platformOwners,
    });
  };

  getPlatformOwner = async ({ platform_owner_id, expand }) => {
    // check if platform exists
    const { data: platformExists } = await platformService.getPlatform();
    if (!platformExists) {
      return ReturnDataObj("Platform does not exists!", 400, false);
    }

    // check if user exists
    const userExists = await this.repository.getUser({
      userId: platform_owner_id,
    });
    if (!userExists) {
      return ReturnDataObj("User does not exists!", 400, false);
    }

    // get user role from the users edge
    const platformOwner = await userEdgesService.repository.findUserEdge({
      filter: {
        user_id: platform_owner_id,
        relation_id: platformExists._id,
        isDelete: false,
      },
      expand,
    });

    // send user data with role info
    const platformOwnerObj = {};
    if (platformOwner.role_id?.role_name) {
      platformOwnerObj["role"] = platformOwner.role_id._id;
    } else {
      platformOwnerObj["role_id"] = platformOwner.role_id;
    }
    if (platformOwner.user_id?.name) {
      platformOwnerObj["_id"] = platformOwner.user_id._id;
      platformOwnerObj["name"] = platformOwner.user_id.name;
      platformOwnerObj["email"] = platformOwner.user_id.email;
      platformOwnerObj["status"] = platformOwner.user_id.status;
    } else {
      platformOwnerObj["user_id"] = platformOwner.user_id;
    }

    return ReturnDataObj(platformOwnerObj);
  };

  editPlatformOwner = async ({
    platform_owner_id,
    first_name,
    last_name,
    preferred_email,
    role_id,
  }) => {
    // check if platform exists
    const { data: platformExists } = await platformService.getPlatform();
    if (!platformExists) {
      return ReturnDataObj("Platform does not exists!", 400, false);
    }

    // check for role
    const { data: roleExists } = await rolesService.findRole({
      filter: { _id: role_id, relation_id: platformExists._id },
    });

    if (!roleExists) {
      return ReturnDataObj("Role does not exists!", 400, false);
    }

    // check if user exists
    const userExists = await this.repository.getUser({
      userId: platform_owner_id,
    });
    if (!userExists) {
      return ReturnDataObj("User does not exists!", 400, false);
    }

    // update user role in user edge
    if (role_id) {
      await userEdgesService.repository.findAndUpdateUserEdge({
        filter: {
          relation_id: platformExists._id,
          user_id: platform_owner_id,
        },
        userEdgeData: {
          role_id: role_id,
        },
      });

      // update user data in users table
      const updatedUserData = await this.repository.updateUser({
        user_id: platform_owner_id,
        userData: {
          first_name: first_name,
          last_name: last_name,
          "Preferred Email": preferred_email,
          refresh_token: true,
        },
      });

      return ReturnDataObj(updatedUserData);
    }

    // update user data in users table
    const updatedUserData = await this.repository.updateUser({
      user_id: platform_owner_id,
      userData: {
        first_name: first_name,
        last_name: last_name,
        "Preferred Email": preferred_email,
        refresh_token: true,
      },
    });

    return ReturnDataObj(updatedUserData);
  };

  removePlatformOwner = async ({ platform_owner_id }) => {
    // check if platform exists
    const { data: platformExists } = await platformService.getPlatform();
    if (!platformExists) {
      return ReturnDataObj("Platform does not exists!", 400, false);
    }

    // check if user exists
    const userExists = await this.repository.getUser({
      userId: platform_owner_id,
    });
    if (!userExists) {
      return ReturnDataObj("User does not exists!", 400, false);
    }

    console.log("removePlatformOwner", "delete_user_edge", new Date(), {
      relation_id: platformExists._id,
      user_id: platform_owner_id,
      isDelete: false,
    });
    // soft delete user edge
    const updatedUserEdge =
      await userEdgesService.repository.findAndUpdateUserEdge({
        filter: {
          relation_id: platformExists._id,
          user_id: platform_owner_id,
          isDelete: false,
        },
        userEdgeData: {
          isDelete: true,
          deleteFrom: "removePlatformOwner",
        },
      });

    // remove user edge
    await this.removeUserEdge({
      userEdgeId: updatedUserEdge._id,
      userId: userExists._id,
    });

    // refresh user token
    await this.repository.updateUser({
      user_id: userExists._id,
      userData: {
        refresh_token: true,
      },
    });

    return ReturnDataObj(updatedUserEdge);
  };

  /**
   * Community owners
   */

  createCommunityOwner = async ({
    first_name,
    last_name,
    preferred_email,
    role_id,
    modules,
    owner_id,
    relation_id,
    communityName,
  }) => {
    // check for role
    const roleExists = await rolesService.findRole({
      filter: { role_id, relation_id },
    });
    if (!roleExists) {
      return ReturnDataObj("Role does not exists!", 400, false);
    }

    // check for existing user with same email
    const userExists = await this.repository.findUser({
      filter: { "Preferred Email": preferred_email },
      expand: true,
    });

    const community = await communitiesService.repository.findCommunity({
      filter: {
        _id: relation_id,
      },
    });

    let host = null;
    if (community && community?.customDomain?.status === "active") {
      host = community?.customDomain?.isMainDomain
        ? `https://www.${community?.customDomain?.domain}`
        : `https://${community?.customDomain?.domain}`;
    }

    if (userExists && userExists.user_edges) {
      console.log("user exists creating new edge");

      if (
        userExists.user_edges.find(
          (edge) => edge.relation_id?.toString() === relation_id
        )
      ) {
        return ReturnDataObj(
          "User with same email already exists in this platform",
          400,
          false
        );
      }

      // create user edge
      const { data: userEdge } = await userEdgesService.createUserEdge({
        userEdgeData: {
          relation_id: relation_id,
          user_id: userExists._id,
          role_id: role_id,
          platform: false,
          type: "CO",
        },
      });

      // add the created user edge to user data
      const updatedUserData = await this.repository.updateUser({
        user_id: userExists._id,
        userData: {
          user_edges: [...userExists.user_edges, userEdge._id],
        },
      });

      await mailService.sendMail({
        to: userExists["Preferred Email"],
        subject: "You have been invited!",
        template: "community-owner-invitation",
        context: {
          name: `${userExists.first_name}${userExists.last_name}`,
          community: communityName,
          link: `${host ?? FRONTEND_DOMAIN}/edge-login`,
        },
        relationId:community._id ? community._id : "",
        customsmtp:community.customSMTP ? community.customSMTP : false
      });

      /**
       * return
       * edge [for adding subscription_id in user edge -> return trip from billing server]
       * user_id
       * owner_id -> subscription -> tier_id
       * modules -> scope_id
       * relation_id
       */
      return ReturnDataObj({
        edge: userEdge,
        user_id: updatedUserData._id,
        owner_id: owner_id,
        modules: modules,
        relation_id: relation_id,
      });
    } else {
      console.log("creating new user and edge");

      // create user
      // sign up the new user
      const { data: newUser } = await this.signUp({
        first_name: first_name,
        last_name: last_name,
        preferred_email: preferred_email,
        skipEmail: false,
        defaultActivate: true,
        host: community ? host : null,
        communityId: community ? community._id : null,
      });

      // create user edge
      const { data: userEdge } = await userEdgesService.createUserEdge({
        userEdgeData: {
          relation_id: relation_id,
          user_id: newUser._id,
          role_id: role_id,
          platform: false,
          type: "CO",
        },
      });

      // update user edges
      await this.addUserEdge({
        userId: newUser._id,
        userEdgeId: userEdge._id,
      });

      await mailService.sendMail({
        to: newUser["Preferred Email"],
        subject: "You have been invited!",
        template: "owner-account-activation",
        context: {
          name: `${newUser.first_name}${newUser.last_name}`,
          relation: communityName,
          type: "Community",
          activationLink: `${host ?? FRONTEND_DOMAIN}/activate?code=${
            newUser.activation_code
          }`,
        },
        relationId:community._id ? community._id : "",
        customsmtp:community.customSMTP ? community.customSMTP : false
      });

      /**
       * return
       * edge [for adding subscription_id in user edge -> return trip from billing server]
       * user_id
       * owner_id -> subscription -> tier_id
       * modules -> scope_id
       * relation_id
       */
      return ReturnDataObj({
        edge: userEdge,
        user_id: newUser._id,
        owner_id: owner_id,
        modules: modules,
        relation_id: relation_id,
      });
    }
  };

  getCommunityOwners = async ({
    page,
    limit,
    user_id,
    relation_id,
    expand,
    search,
  }) => {
    const pageNumber = parseInt(page) || 1;
    const limitNumber = parseInt(limit) || 10;

    // calculate the skip amount from the
    const skip = pageNumber && limitNumber ? (pageNumber - 1) * limitNumber : 0;

    const filterObj = {
      relation_id: ObjectId(relation_id),
      // $and: [
      //   { type: { $ne: "M" } },
      //   { type: { $ne: "V" } },
      //   { type: { $ne: "DEFAULT" } },
      // ],
      type: "CO",
      user_id: { $ne: ObjectId(user_id) },
      isDelete: false,
    };

    const { data: count } = await userEdgesService.countEdges({
      filter: filterObj,
      search,
    });
    const filterObjTemp = {
      relation_id: ObjectId(relation_id),
      // $and: [
      //   { type: { $ne: "M" } },
      //   { type: { $ne: "V" } },
      //   { type: { $ne: "CU" } },
      //   { type: { $ne: "GU" } },
      //   { type: { $ne: "DEFAULT" } },
      // ],
      type: "CO",
      user_id: { $ne: ObjectId(user_id) },
      isDelete: false,
    };
    const { data: countAllData } = await userEdgesService.countEdges({
      filter: filterObjTemp,
    });

    // get user info and user role from the users edge
    // fetch all platform owners accept the user making API call
    const communityOwners = await userEdgesService.repository.findAllEdgesV2({
      filter: filterObj,
      skip,
      limit,
      expand,
      search,
    });

    const communityOwnersList = [];
    for (const communityOwner of communityOwners) {
      const communityOwnerObj = {};
      if (communityOwner.role_id?.role_name) {
        communityOwnerObj["role"] = communityOwner.role_id.role_name;
      }
      if (communityOwner.role_id) {
        communityOwnerObj["role_id"] = communityOwner.role_id;
      }
      communityOwnerObj["owner"] = communityOwner.owner;
      if (communityOwner.user_id?.name) {
        communityOwnerObj["_id"] = communityOwner.user_id._id;
        communityOwnerObj["name"] = communityOwner.user_id.name;
        communityOwnerObj["email"] = communityOwner.user_id.email;
        communityOwnerObj["status"] = communityOwner.user_id.status;
      } else {
        communityOwnerObj["user_id"] = communityOwner.user_id;
      }
      if (communityOwner.subscription_id) {
        communityOwnerObj["subscription_id"] = communityOwner.subscription_id;
        const response = await axios.get(
          `${GATEWAY_DOMAIN}/api/billings/api/v1/subscriptions/${communityOwner.subscription_id}`
        );
        communityOwnerObj["scopes"] = response.data.scope_id.modules.map(
          (module) => module.system_id
        );
      }

      communityOwnersList.push(communityOwnerObj);
    }

    return ReturnDataObj({
      data: communityOwnersList,
      count,
      page: pageNumber,
      limit: limitNumber,
      countAllData: countAllData,
    });
  };

  getCommunityOwnersSuggestions = async ({ user_id, relation_id, expand }) => {
    const filterObj = {
      relation_id: ObjectId(relation_id),
      $and: [
        { type: { $ne: "M" } },
        { type: { $ne: "V" } },
        { type: { $ne: "DEFAULT" } },
      ],
      user_id: { $ne: ObjectId(user_id) },
      isDelete: false,
    };

    // get user info and user role from the users edge
    // fetch all platform owners accept the user making API call
    const communityOwners = await userEdgesService.repository.findAllEdgesV2({
      filter: filterObj,
      expand,
    });

    const communityOwnersList = [];
    for (const communityOwner of communityOwners) {
      const communityOwnerObj = {};
      if (communityOwner.role_id?.role_name) {
        communityOwnerObj["role"] = communityOwner.role_id.role_name;
      }
      if (communityOwner.role_id) {
        communityOwnerObj["role_id"] = communityOwner.role_id;
      }
      communityOwnerObj["owner"] = communityOwner.owner;
      if (communityOwner.user_id?.name) {
        communityOwnerObj["_id"] = communityOwner.user_id._id;
        communityOwnerObj["name"] = communityOwner.user_id.name;
        communityOwnerObj["email"] = communityOwner.user_id.email;
        communityOwnerObj["status"] = communityOwner.user_id.status;
      } else {
        communityOwnerObj["user_id"] = communityOwner.user_id;
      }
      if (communityOwner.subscription_id) {
        communityOwnerObj["subscription_id"] = communityOwner.subscription_id;
        const response = await axios.get(
          `${GATEWAY_DOMAIN}/api/billings/api/v1/subscriptions/${communityOwner.subscription_id}`
        );
        communityOwnerObj["scopes"] = response?.data.scope_id?.modules.map(
          (module) => module.system_id
        );
      }

      communityOwnersList.push(communityOwnerObj);
    }

    return ReturnDataObj({
      data: communityOwnersList,
    });
  };

  getCommunityOwner = async ({ community_owner_id, relation_id, expand }) => {
    // check if user exists
    const userExists = await this.repository.getUser({
      userId: community_owner_id,
    });
    if (!userExists) {
      return ReturnDataObj("User does not exists!", 400, false);
    }

    // get user role from the users edge
    const userEdges = await userEdgesService.repository.findUserEdges({
      filter: {
        user_id: community_owner_id,
        isDelete: false,
      },
      expand,
    });

    const communityOwner = userEdges.find(
      (edge) => edge.relation_id?._id?.toString() === relation_id
    );

    // send user data with role info
    const communityOwnerObj = {};
    if (communityOwner.role_id?.role_name) {
      communityOwnerObj["role"] = communityOwner.role_id._id;
    } else {
      communityOwnerObj["role_id"] = communityOwner.role_id;
    }
    if (communityOwner.user_id?.name) {
      communityOwnerObj["_id"] = communityOwner.user_id._id;
      communityOwnerObj["name"] = communityOwner.user_id.name;
      communityOwnerObj["email"] = communityOwner.user_id.email;
      communityOwnerObj["status"] = communityOwner.user_id.status;
    } else {
      communityOwnerObj["user_id"] = communityOwner.user_id;
    }
    if (communityOwner.subscription_id) {
      communityOwnerObj["subscription_id"] = communityOwner.subscription_id;
      const response = await axios.get(
        `${GATEWAY_DOMAIN}/api/billings/api/v1/subscriptions/${communityOwner.subscription_id}`
      );
      communityOwnerObj["scopes"] = response.data.scope_id.modules;
    }

    return ReturnDataObj(communityOwnerObj);
  };

  editCommunityOwner = async ({
    community_owner_id,
    first_name,
    last_name,
    preferred_email,
    role_id,
    modules,
    relation_id,
  }) => {
    // check for role
    const roleExists = await rolesService.findRole({
      filter: { role_id, relation_id },
    });
    if (!roleExists) {
      return ReturnDataObj("Role does not exists!", 400, false);
    }

    // check if user exists
    const userExists = await this.repository.getUser({
      userId: community_owner_id,
    });
    if (!userExists) {
      return ReturnDataObj("User does not exists!", 400, false);
    }

    // find user edge
    const userEdge = await userEdgesService.repository.findUserEdge({
      filter: {
        relation_id: relation_id,
        user_id: community_owner_id,
        isDelete: false,
      },
    });

    // update user role in user edge
    if (role_id) {
      await userEdgesService.repository.updateUserEdge({
        userEdgeId: userEdge._id,
        userEdgeData: {
          role_id: role_id,
        },
      });

      // update user data in users table
      await this.repository.updateUser({
        user_id: community_owner_id,
        userData: {
          first_name: first_name,
          last_name: last_name,
          "Preferred Email": preferred_email,
          refresh_token: true,
        },
      });

      /**
       * return
       * edge [for adding subscription_id in user edge -> return trip from billing server]
       * modules -> scope_id
       */
      return ReturnDataObj({
        edge: userEdge,
        modules: modules,
      });
    }

    // update user data in users table
    await this.repository.updateUser({
      user_id: platform_owner_id,
      userData: {
        first_name: first_name,
        last_name: last_name,
        "Preferred Email": preferred_email,
        refresh_token: true,
      },
    });

    /**
     * return
     * edge [for adding subscription_id in user edge -> return trip from billing server]
     * modules -> scope_id
     */
    return ReturnDataObj({
      edge: userEdge,
      modules: modules,
    });
  };

  removeCommunityOwner = async ({ relation_id, community_owner_id }) => {
    // check if user exists
    const userExists = await this.repository.getUser({
      userId: community_owner_id,
    });
    if (!userExists) {
      return ReturnDataObj("User does not exists!", 400, false);
    }

    console.log("removeCommunityOwner", "delete_user_edge", new Date(), {
      relation_id: relation_id,
      user_id: community_owner_id,
    });
    // soft delete user edge
    const updatedUserEdge =
      await userEdgesService.repository.findAndUpdateUserEdge({
        filter: {
          relation_id: relation_id,
          user_id: community_owner_id,
        },
        userEdgeData: {
          isDelete: true,
          deleteFrom: "removeCommunityOwner",
        },
      });

    // remove user edge
    await this.removeUserEdge({
      userEdgeId: updatedUserEdge._id,
      userId: userExists._id,
    });

    // refresh user token
    await this.repository.updateUser({
      user_id: userExists._id,
      userData: {
        refresh_token: true,
      },
    });

    /**
     * return
     * edge [for adding subscription_id in user edge -> return trip from billing server]
     */
    return ReturnDataObj({
      edge: updatedUserEdge,
    });
  };

  /**
   * Financial Analytics
   */

  getPlatformUsers = async ({ page, limit, expand, search }) => {
    const pageNumber = parseInt(page) || 1;
    const limitNumber = parseInt(limit) || 10;

    // calculate the skip amount from the
    const skip = pageNumber && limitNumber ? (pageNumber - 1) * limitNumber : 0;

    const filterObj = {
      owner: true,
      type: "CO",
      isDelete: false,
    };

    const { data: count } = await userEdgesService.countEdges({
      filter: filterObj,
      search,
    });

    // get user info and user role from the users edge
    // fetch all platform users accept the user making API call
    let platformUsers = await userEdgesService.repository.findAllEdgesV2({
      filter: filterObj,
      skip,
      limit,
      expand,
      search,
    });

    if (platformUsers.length) {
      platformUsers = await Promise.all(
        platformUsers.map(async (item) => {
          if (item?.relation_id?.logo) {
            const logo = await s3fileUploadService.generatePresignedUrl({
              key: `groupos/${item.relation_id._id}/${item.relation_id.logo}`,
            });
            item.relation_id.logo = logo;
          }

          if (item?.relation_id?.avtar) {
            const avtar = await s3fileUploadService.generatePresignedUrl({
              key: `groupos/${item.relation_id._id}/${item.relation_id.avtar}`,
            });
            item.relation_id.avtar = avtar;
          }

          return item;
        })
      );
    }

    return ReturnDataObj({
      data: platformUsers,
      count,
      page: pageNumber,
      limit: limitNumber,
    });
  };

  getPlatformUsersSuggestions = async ({ expand }) => {
    const filterObj = {
      owner: true,
      type: "CO",
      isDelete: false,
    };

    const platformUsers = await userEdgesService.repository.findAllEdgesV2({
      filter: filterObj,
      expand,
    });

    return ReturnDataObj({
      data: platformUsers,
    });
  };

  getCommunityMembers = async ({
    page,
    limit,
    expand,
    relation_id,
    search,
    membership,
  }) => {
    const pageNumber = parseInt(page) || 1;
    const limitNumber = parseInt(limit) || 10;

    // calculate the skip amount from the
    const skip = pageNumber && limitNumber ? (pageNumber - 1) * limitNumber : 0;

    const filterObj = {
      relation_id: ObjectId(relation_id),
      owner: false,
      type: "M",
      isDelete: false,
    };

    switch (membership) {
      case "subscribe":
        filterObj.subscription_id = {
          $ne: null,
          $regex: /^[a-fA-F0-9]{24}$/,
        };
        break;
      case "nonsubscribe":
        filterObj.subscription_id = null;
        break;
      case "all":
        break;

      default:
      // console.log('Invalid membership filter');
    }

    const { data: count } = await userEdgesService.countEdges({
      filter: filterObj,
      search,
    });

    // get user info and user role from the users edge
    // fetch all platform users accept the user making API call
    const communityMembers = await userEdgesService.repository.findAllEdgesV2({
      filter: filterObj,
      skip,
      limit,
      expand,
      search,
      isMember: true,
    });

    const communityMembersList = [];
    for (const communityMember of communityMembers) {
      let communityMemberObj = { ...communityMember };
      if (communityMember?.subscription_id) {
        communityMemberObj["subscription_id"] =
          communityMember?.subscription_id;

        const data = await fetchTierData({
          susbscription_id: communityMember?.subscription_id,
        });

        communityMemberObj["tier_id"] = data ?? null;
      }
      communityMembersList.push(communityMemberObj);
    }

    return ReturnDataObj({
      data: communityMembersList,
      count,
      page: pageNumber,
      limit: limitNumber,
    });
  };

  getCommunityMembersSuggestion = async ({
    expand,
    relation_id,
    membership,
  }) => {
    const filterObj = {
      relation_id: ObjectId(relation_id),
      owner: false,
      type: "M",
      isDelete: false,
    };
    switch (membership) {
      case "subscribe":
        filterObj.subscription_id = {
          $ne: null,
          $regex: /^[a-fA-F0-9]{24}$/,
        };
        break;
      case "nonsubscribe":
        filterObj.subscription_id = null;
        break;
      case "all":
        break;

      default:
      // console.log('Invalid membership filter');
    }
    // get user info and user role from the users edge
    // fetch all platform users accept the user making API call
    const communityMembers =
      await userEdgesService.repository.findAllEdgesSuggestions({
        filter: filterObj,
        expand,
      });

    const communityMembersList = [];
    for (let communityMember of communityMembers) {
      const communityMemberObj = {};
      if (communityMember.user_id?.name) {
        communityMemberObj["_id"] = communityMember.user_id._id;
        communityMemberObj["name"] = communityMember.user_id.name;
        communityMemberObj["email"] = communityMember.user_id.email;
        communityMemberObj["status"] = communityMember.user_id.status;
      } else {
        communityMember.user_id.groupos_join_date =
          communityMember.user_id.groupos_join_date ?? null;
        communityMember.user_id.migration_date =
          communityMember.user_id.migration_date ?? null;
        communityMemberObj["user_id"] = communityMember.user_id;
      }
      if (communityMember.subscription_id) {
        communityMemberObj["subscription_id"] = communityMember.subscription_id;
      }

      communityMembersList.push(communityMemberObj);
    }

    return ReturnDataObj({
      data: communityMembers,
    });
  };
  getCommunityGuests = async ({ page, limit, expand, relation_id, search }) => {
    const pageNumber = parseInt(page) || 1;
    const limitNumber = parseInt(limit) || 10;

    // calculate the skip amount from the
    const skip = pageNumber && limitNumber ? (pageNumber - 1) * limitNumber : 0;

    const filterObj = {
      relation_id: ObjectId(relation_id),
      owner: false,
      type: "GU",
      isDelete: false,
    };

    const { data: count } = await userEdgesService.countEdges({
      filter: filterObj,
      search,
    });

    // get user info and user role from the users edge
    // fetch all platform users accept the user making API call
    const communityMembers = await userEdgesService.repository.findAllEdgesV2({
      filter: filterObj,
      skip,
      limit,
      expand,
      search,
    });

    const communityMembersList = [];
    for (const communityMember of communityMembers) {
      let communityMemberObj = { ...communityMember };
      if (communityMember?.subscription_id) {
        communityMemberObj["subscription_id"] =
          communityMember?.subscription_id;

        const data = await fetchTierData({
          susbscription_id: communityMember?.subscription_id,
        });

        communityMemberObj["tier_id"] = data ?? null;
      }
      communityMembersList.push(communityMemberObj);
    }

    return ReturnDataObj({
      data: communityMembersList,
      count,
      page: pageNumber,
      limit: limitNumber,
    });
  };
  getCommunityGuestsSuggestion = async ({ expand, relation_id }) => {
    const filterObj = {
      relation_id: ObjectId(relation_id),
      owner: false,
      type: "GU",
      isDelete: false,
    };

    // get user info and user role from the users edge
    // fetch all platform users accept the user making API call
    const communityMembers =
      await userEdgesService.repository.findAllEdgesSuggestions({
        filter: filterObj,
        expand,
      });

    const communityMembersList = [];
    for (let communityMember of communityMembers) {
      const communityMemberObj = {};
      if (communityMember.user_id?.name) {
        communityMemberObj["_id"] = communityMember.user_id._id;
        communityMemberObj["name"] = communityMember.user_id.name;
        communityMemberObj["email"] = communityMember.user_id.email;
        communityMemberObj["status"] = communityMember.user_id.status;
      } else {
        communityMember.user_id.groupos_join_date =
          communityMember.user_id.groupos_join_date ?? null;
        communityMember.user_id.migration_date =
          communityMember.user_id.migration_date ?? null;
        communityMemberObj["user_id"] = communityMember.user_id;
      }
      if (communityMember.subscription_id) {
        communityMemberObj["subscription_id"] = communityMember.subscription_id;
      }

      communityMembersList.push(communityMemberObj);
    }

    return ReturnDataObj({
      data: communityMembers,
    });
  };

  getUserById = async ({ user_id, auth_user_id, body }) => {
    if (user_id !== auth_user_id) {
      return ReturnDataObj("Unauthorized access", 401, false);
    }

    const filterObj = {
      _id: ObjectId(user_id),
      $or: [{ isDelete: false }, { isDelete: { $exists: false } }],
    };

    const user = await userEdgesService.repository.findUserById({
      filter: filterObj,
    });

    return ReturnDataObj({
      data: user,
    });
  };

  updateUserById = async ({ user_id, auth_user_id, body }) => {
    if (user_id !== auth_user_id) {
      return ReturnDataObj("Unauthorized access", 401, false);
    }

    const filterObj = {
      _id: ObjectId(user_id),
      $or: [{ isDelete: false }, { isDelete: { $exists: false } }],
    };

    if (body.first_name && body.last_name) {
      const updateData = {
        $set: {
          first_name: body?.first_name,
          last_name: body?.last_name,
        },
      };

      const user = await userEdgesService.repository.findUserAndUpdatebyId({
        filter: filterObj,
        updateData,
      });

      return ReturnDataObj({
        data: user,
      });
    } else {
      return ReturnDataObj("First name and last name are required", 400, false);
    }
  };

  getActiveCommunityMembers = async ({ relation_id }) => {
    const filterObj = {
      relation_id: ObjectId(relation_id),
      owner: false,
      type: "M",
      isDelete: false,
      subscription_id: {
        $exists: true,
      },
    };

    const communityMembers =
      await userEdgesService.repository.findAllEdgesForActiveMember({
        filter: filterObj,
      });

    return ReturnDataObj({
      data: communityMembers,
    });
  };

  getAllCommunityMembers = async ({ relation_id }) => {
    const filterObj = {
      relation_id: ObjectId(relation_id),
      owner: false,
      type: { $in: [ "M", "GU", "CU"] },
      isDelete: false,
    };

    const communityMembers =
      await userEdgesService.repository.findAllEdgesForActiveMember({
        filter: filterObj,
      });

    return ReturnDataObj({
      data: communityMembers,
    });
  };

  getAllSubcriptionCommunityMembers = async ({ relation_id }) => {
    const filterObj = {
      relation_id: ObjectId(relation_id),
      owner: false,
      type: "M",
      subscription_id: {
        $exists: true,
      },
      isDelete: false,
    };

    const communityMembers =
      await userEdgesService.repository.findAllEdgesForActiveMember({
        filter: filterObj,
      });

    return ReturnDataObj({
      data: communityMembers,
    });
  };

  getCommunityVisitors = async ({ page, limit, expand, relation_id }) => {
    const pageNumber = parseInt(page) || 1;
    const limitNumber = parseInt(limit) || 10;

    // calculate the skip amount from the
    const skip = pageNumber && limitNumber ? (pageNumber - 1) * limitNumber : 0;

    const filterObj = {
      relation_id: relation_id,
      owner: false,
      type: "V",
      isDelete: false,
    };

    const { data: count } = await userEdgesService.countEdges({
      filter: filterObj,
    });

    // get user info and user role from the users edge
    // fetch all platform users accept the user making API call
    const communityMembers = await userEdgesService.repository.findAllEdges({
      filter: filterObj,
      skip,
      limit,
      expand,
    });

    const communityMembersList = [];
    for (const communityMember of communityMembers) {
      const communityMemberObj = {};
      if (communityMember.user_id?.name) {
        communityMemberObj["_id"] = communityMember.user_id._id;
        communityMemberObj["name"] = communityMember.user_id.name;
        communityMemberObj["email"] = communityMember.user_id.email;
        communityMemberObj["status"] = communityMember.user_id.status;
      } else {
        communityMemberObj["user_id"] = communityMember.user_id;
      }
      if (communityMember.subscription_id) {
        communityMemberObj["subscription_id"] = communityMember.subscription_id;
      }

      communityMembersList.push(communityMemberObj);
    }

    return ReturnDataObj({
      data: communityMembersList,
      count,
      page: pageNumber,
      limit: limitNumber,
    });
  };

  /**
   * user data create if not exist
   * create default and MDS community edges
   */
  migrateMDSUserAndEdges = async ({ connected_account_id, authUserId }) => {
    try {
      console.log(":::MDS USERS MIGRATION FROM USER_MIGRATIONS COLLECTION:::");
      // initialize a counter
      const successfullyAdded = [];
      const skipRecourdDueToTheError = [];
      const skipRecourd = [];

      const getAllNonMigratedUsers =
        await this.#userMigrationsService.repository.find_({
          filter: { migrated: false },
        });

      let allEmail = [];
      getAllNonMigratedUsers.map(async (userRowData) => {
        allEmail.push(userRowData.email);
      });

      const userInfo = await this.repository.find({
        filter: { "Preferred Email": { $in: allEmail }, isDelete: false },
        expand: true,
      });

      // find mds community and member role
      const community = await script.getMdsCommunity();
      const { data: memberRole } = await this.#rolesService.findRole({
        filter: { role_name: "MEMBER" },
      });

      for (let i = 0; i < userInfo.length; i++) {
        let user = userInfo[i];

        if (user["Preferred Email"]) {
          let migrationRowData = getAllNonMigratedUsers.find(
            (item) => item.email === user["Preferred Email"]
          );
          if (migrationRowData && migrationRowData.stripe_customer_db_id) {
            if (user.user_edges.length <= 0) {
              // find default role
              const { data: defaultRole } = await this.#rolesService.findRole({
                filter: {
                  role_name: "DEFAULT",
                },
              });

              // create user default edge
              const { data: userDefaultEdge } =
                await this.#userEdgesService.createUserMDSEdge({
                  userEdgeData: {
                    default: true,
                    owner: true,
                    user_id: user._id,
                    role_id: defaultRole._id,
                    type: "DEFAULT",
                    isDelete: false,
                  },
                });

              // create MDS member edge
              const { data: userMemberEdge } =
                await this.#userEdgesService.createUserMDSEdge({
                  userEdgeData: {
                    relation_id: community._id,
                    platform: false,
                    user_id: user._id,
                    role_id: memberRole._id,
                    type: "M",
                    migration_date: new Date(),
                    isDelete: false,
                  },
                });

              // add created user edge in the user data
              await this.repository.updateUser({
                user_id: user._id,
                userData: {
                  user_edges: [userDefaultEdge._id, userMemberEdge._id],
                  status: "ACTIVE",
                  activation_code: null,
                  refresh_token: false,
                  migration_date: new Date(),
                },
              });

              const payload = {
                event: "GET_MDS_STRIPE_CUSTOMER",
                data: {
                  user_id: user._id,
                  email: migrationRowData.email,
                  stripe_customer_db_id: migrationRowData.stripe_customer_db_id,
                  migration_data: migrationRowData,
                  edge: userMemberEdge,
                  connected_account_id,
                },
              };

              publishMessage(
                JSON.stringify(payload),
                // MESSAGE_QUEUE_BILLING_SERVICE_NAME
                "MONOLITH_GET_MDS_STRIPE_CUSTOMER"
              );

              //:TODO  store user data object and payment method object from strip to our db

              // complete the user migration
              await this.#userMigrationsService.repository.findAndUpdateUserMigration(
                {
                  filter: {
                    email: user["Preferred Email"],
                  },
                  userMigrationData: {
                    migrated: true,
                  },
                }
              );
              successfullyAdded.push({
                email: user["Preferred Email"],
                reason:
                  "DEFAULT and Comunity M ( MEMBER ) edge added successfully!",
              });
            } else {
              // checking if user have MDS member edge
              let mdsMemberEdge = user.user_edges.find(
                (x) => x.type === "M" && x.relation_id == community._id
              );
              if (!mdsMemberEdge) {
                // //TODO need to check if allrady exist then skip it
                // create MDS member edge
                const { data: userMemberEdge } =
                  await this.#userEdgesService.createUserMDSEdge({
                    userEdgeData: {
                      relation_id: community._id,
                      platform: false,
                      user_id: user._id,
                      role_id: memberRole._id,
                      type: "M",
                      migration_date: new Date(),
                      isDelete: false,
                    },
                  });

                // add created user edge in the user data
                await this.repository.updateUser({
                  user_id: user._id,
                  userData: {
                    user_edges: [...user.user_edges, userMemberEdge._id],
                    status: "ACTIVE",
                    activation_code: null,
                    refresh_token: false,
                    migration_date: new Date(),
                  },
                });

                const payload = {
                  event: "GET_MDS_STRIPE_CUSTOMER",
                  data: {
                    user_id: user._id,
                    email: migrationRowData.email,
                    stripe_customer_db_id:
                      migrationRowData.stripe_customer_db_id,
                    migration_data: migrationRowData,
                    edge: userMemberEdge,
                    connected_account_id,
                  },
                };

                publishMessage(
                  JSON.stringify(payload),
                  // MESSAGE_QUEUE_BILLING_SERVICE_NAME
                  "MONOLITH_GET_MDS_STRIPE_CUSTOMER"
                );

                // //:TODO  store user data object and payment method object from strip to our db

                // complete the user migration
                await this.#userMigrationsService.repository.findAndUpdateUserMigration(
                  {
                    filter: {
                      email: user["Preferred Email"],
                    },
                    userMigrationData: {
                      migrated: true,
                    },
                  }
                );
                successfullyAdded.push({
                  email: user["Preferred Email"],
                  reason: "Comunity M ( MEMBER ) edge added successfully!",
                });
              } else {
                skipRecourd.push({
                  email: user["Preferred Email"],
                  reason: "User alredy have cominity M ( MEMBER ) edge!",
                });
              }
            }
          }
        }

        // console.log({ user });
      }
      console.log(
        `Default and MDS member edges added: ${successfullyAdded.length}`
      );
      console.log(`Users skipped: ${skipRecourd.length} ids ${skipRecourd}`);
      let data = {
        event: "createUserDefaultAndComunityMemberEdges",
        connected_account_id: connected_account_id,
        added_by: authUserId,
        action_number: 2,
        result: {
          added: successfullyAdded.length,
          error: skipRecourdDueToTheError.length,
          skip: skipRecourd.length,
          total: userInfo.length,
          getAllNonMigratedUsers: getAllNonMigratedUsers.length,
        },
        result_description: {
          skipRecourdDueToTheError: skipRecourdDueToTheError,
          skipRecourd: skipRecourd,
          successfullyAdded: successfullyAdded,
        },
      };
      await this.#comunityMigrationLogsService.repository.createComunityMigrationLog(
        {
          data: data,
        }
      );
      return ReturnDataObj(data);
    } catch (error) {
      console.log({ error });
    }
  };

  getSystemUsers = async ({ page, limit, search }) => {
    const pageNumber = parseInt(page) || 1;
    const limitNumber = parseInt(limit) || 10;

    // calculate the skip amount from the
    const skip = pageNumber && limitNumber ? (pageNumber - 1) * limitNumber : 0;

    const filterObj = {
      user_edges: { $size: 1 },
      isDelete: false,
    };

    if (search) {
      filterObj.$or = [
        { first_name: { $regex: ".*" + search + ".*", $options: "i" } },
        { last_name: { $regex: ".*" + search + ".*", $options: "i" } },
        { "Preferred Email": { $regex: ".*" + search + ".*", $options: "i" } },
      ];
    }

    const count = await this.repository.getCount({
      filter: filterObj,
    });

    const systemUsers = await this.repository.findAllUsers({
      filter: filterObj,
      skip,
      limit,
    });

    return ReturnDataObj({
      data: systemUsers,
      count,
      page: pageNumber,
      limit: limitNumber,
    });
  };

  getSystemUsersSuggestion = async ({ page, limit, search }) => {
    const filterObj = {
      user_edges: { $size: 1 },
      isDelete: false,
    };
    const systemUsers = await this.repository.findAllUsers({
      filter: filterObj,
    });

    return ReturnDataObj({
      data: systemUsers,
    });
  };

  deleteAllCommunityOwner = async ({
    community_id,
    super_admin_Id,
    user_edges,
    stripe_account_db_id,
  }) => {
    // community member -> subscription delete + refund from stripe and groupos DB
    const payload = {
      event: "DELETE_ALL_COMMUNITY_OWNER",
      data: {
        communityId: community_id,
        superAdminId: super_admin_Id,
        userEdges: user_edges,
        stripeAccountDBId: stripe_account_db_id,
      },
    };
    // publishMessage(JSON.stringify(payload), MESSAGE_QUEUE_BILLING_SERVICE_NAME);
    publishMessage(
      JSON.stringify(payload),
      "MONOLITH_DELETE_ALL_COMMUNITY_OWNER"
    );
    return ReturnDataObj({});
  };

  deleteAllCommunityMember = async ({
    community_id,
    super_admin_Id,
    user_edges,
    stripe_account_db_id,
  }) => {
    // community member -> subscription delete + refund from stripe and groupos DB
    const payload = {
      event: "DELETE_ALL_COMMUNITY_MEMBER",
      data: {
        communityId: community_id,
        superAdminId: super_admin_Id,
        userEdges: user_edges,
        stripeAccountDBId: stripe_account_db_id,
      },
    };
    // publishMessage(JSON.stringify(payload), MESSAGE_QUEUE_BILLING_SERVICE_NAME);
    publishMessage(
      JSON.stringify(payload),
      "MONOLITH_DELETE_ALL_COMMUNITY_MEMBER"
    );

    return ReturnDataObj({});
  };

  //* Service for Check Preferred Email is exists or not in the system
  checkEmailExists = async ({ email }) => {
    const filterObj = {
      "Preferred Email": email,
    };

    let systemUsers = await this.repository.findUser({
      filter: filterObj,
    });

    if (!systemUsers) {
      systemUsers = await this.inviteUsersRepository.getImportUser({
        filter: {
          email,
          status: "PENDING",
          isDelete: false,
        },
      });

      if (systemUsers) {
        //* create user in the air-table sync
        let generatedUser = await this.repository.generateUser({
          userData: {
            first_name: systemUsers?.first_name,
            last_name: systemUsers?.last_name,
            display_name: `${systemUsers?.first_name} ${systemUsers?.last_name}`,
            "Preferred Email": email ?? null,
            profileImg: null,
            status: systemUsers ? "ACTIVE" : "INACTIVE",
          },
        });

        //* find default edge role
        const { data: role } = await rolesService.findRole({
          filter: {
            role_name: "DEFAULT",
          },
        });

        //* create user edge
        const userEdge = await userEdgesService.createUserEdge({
          userEdgeData: {
            default: true,
            owner: true,
            user_id: generatedUser._id,
            role_id: role._id,
            type: "DEFAULT",
          },
        });

        generatedUser.user_edges = [userEdge?.data?._id];

        await this.repository.saveGeneratedUser({ generatedUser });
      }
    }

    return ReturnDataObj({
      success: systemUsers ? true : false,
      message: systemUsers
        ? "User have exists in system"
        : "Email not found in our system. Please check and try again.",
      data: {},
    });
  };

  //* Service for adding the provider email in the user Providers list
  addProviderEmail = async ({
    preferred_email,
    name,
    email = null,
    firebaseId,
    socialProviderId = null,
  }) => {
    if (!email && !socialProviderId) {
      return res.status(400).json({
        status: false,
        message: "Provider data is required!",
      });
    }

    const filterObj = {
      "Preferred Email": preferred_email,
      "providers.name": {
        $ne: name,
      },
      isDelete: false,
    };

    const findUser = await this.repository.findUser({
      filter: {
        "Preferred Email": preferred_email,
        status: "ACTIVE",
        isDelete: false,
      },
    });
    if (!findUser) {
      return ReturnDataObj(
        {
          success: false,
          message: "User does not exist in system!",
        },
        400,
        false
      );
    }

    if (findUser.firebaseId && findUser.firebaseId !== "") {
      if (findUser?.providers.length) {
        if (!findUser?.providers[0]["email"]) {
          return res.status(400).json({
            success: false,
            status: false,
            message: `${preferred_email} preferred email is already connected ${
              findUser?.providers[0]?.name
                ? `with ${findUser.providers[0].name} provider.`
                : "."
            }`,
          });
        }
        const email = await hideEmail({
          email: findUser?.providers[0]["email"],
        });
        return ReturnDataObj(
          {
            success: false,
            message: `${
              preferred_email ?? "This"
            } preferred email is already connected with ${
              findUser?.providers[0]["name"]
            } provider associated with this ${email}.`,
          },
          400,
          false
        );
      } else {
        return ReturnDataObj(
          {
            success: false,
            message: `${
              preferred_email ?? "This"
            } preferred email is already connected with other social account.`,
          },
          400,
          false
        );
      }
    }

    if (findUser?.providers.length) {
      if (!findUser?.providers[0]["email"]) {
        return res.status(400).json({
          success: false,
          status: false,
          message: `${preferred_email} preferred email is already connected ${
            findUser?.providers[0]?.name
              ? `with ${findUser.providers[0].name} provider.`
              : "."
          }`,
        });
      }
      const email = await hideEmail({
        email: findUser?.providers[0]["email"],
      });
      return ReturnDataObj(
        {
          success: false,
          message: `${
            preferred_email ?? "This"
          } preferred email is already connected with ${
            findUser?.providers[0]["name"]
          } provider associated with this ${email}.`,
        },
        400,
        false
      );
    }

    const checkProviderEmail = await this.repository.findUser({
      filter: {
        $or: [
          ...(email
            ? [{ "Preferred Email": email }, { "providers.email": email }]
            : []),
          { "providers.socialId": socialProviderId },
        ],
        isDelete: false,
      },
    });
    if (checkProviderEmail) {
      return ReturnDataObj(
        {
          success: false,
          message: "Social Email is already exists!",
        },
        400,
        false
      );
    }

    const firebaseUser = await findUserInFirebase({ id: firebaseId });
    if (!firebaseUser) {
      return ReturnDataObj(
        {
          success: false,
          message: "Account not found in firebase!",
        },
        400,
        false
      );
    }

    const provider = `${name === "email" ? "password" : `${name}.com`}`;
    const socialId = firebaseUser?.providerData.find(
      (item) => item?.providerId === provider
    ).uid;

    const updateUserProvider = await this.repository.findAndUpdateUser({
      filter: filterObj,
      userData: {
        $set: {
          firebaseId: firebaseUser?.uid,
          groupos_join_date: new Date(),
        },
        $addToSet: {
          providers: {
            name,
            email,
            socialId,
          },
        },
      },
    });
    if (!updateUserProvider) {
      return ReturnDataObj(
        {
          success: false,
          message: "User does not exist in system",
        },
        400,
        false
      );
    }

    return ReturnDataObj({
      success: true,
      message: "Email added successfully.",
      data: {},
    });
  };

  /**
   * Service for invite the collaborator user by admin
   */
  inviteCollaboratorUser = async ({
    first_name,
    last_name,
    email,
    invitee_id,
    scope,
    relation_id,
  }) => {
    //* Validate the scope
    if (!scope.length) {
      return ReturnDataObj(
        {
          success: false,
          message: "Scope is required!",
          data: {},
        },
        400,
        false
      );
    }

    //* check for existing user with same email
    let userExists = await this.repository.findUser({
      filter: { "Preferred Email": email, isDelete: false },
      expand: true,
    });

    //* Fetch the community data
    const community = await communitiesService.repository.findCommunity({
      filter: {
        _id: relation_id,
      },
    });
    if (!community) {
      return ReturnDataObj(
        {
          success: false,
          message: "Community is not found!",
          data: {},
        },
        400,
        false
      );
    }

    //* Validation for the duplicate invitation
    const fetchData =
      await this.InviteCollaboratorsRepository.getInviteCollaborators({
        filter: {
          email,
          relation_id,
          status: {
            $in: ["PENDING", "ACTIVE"],
          },
          isDelete: false,
        },
      });

    if (fetchData) {
      return ReturnDataObj(
        {
          success: false,
          message: "This user has already been invited in community!",
          data: {},
        },
        400,
        false
      );
    }

    //* Fetch the collaborator user role
    const { data: role } = await rolesService.findRole({
      filter: {
        role_name: "COLLABORATOR_USER",
      },
    });
    if (!role) {
      return ReturnDataObj(
        {
          success: false,
          message: "Collaborator role is not found!",
          data: {},
        },
        400,
        false
      );
    }

    const fetchEdge = await this.#userEdgesService.repository.findUserEdge({
      filter: {
        user_id: invitee_id,
        relation_id: community._id,
        type: "M",
        isDelete: false,
      },
    });
    if (!fetchEdge?.subscription_id) {
      return ReturnDataObj(
        {
          success: false,
          message: "User does not have subscription!",
          data: {},
        },
        400,
        false
      );
    }

    const subscriptionData = await fetchSubscriptionData({
      subscription_id: fetchEdge?.subscription_id,
    });
    if (!subscriptionData) {
      return ReturnDataObj(
        {
          success: false,
          message: "Subscription not found!",
          data: {},
        },
        400,
        false
      );
    }

    if (!subscriptionData?.tier_id?.linked_subscription) {
      return ReturnDataObj(
        {
          success: false,
          message: "You don't have access for the link account!",
          data: {},
        },
        400,
        false
      );
    }

    const totalLinkAccountLimit =
      subscriptionData?.tier_id?.linked_subscription_number +
        fetchEdge?.no_of_team_mate || 0;

    if (totalLinkAccountLimit <= subscriptionData?.linked_subscription_count) {
      return ReturnDataObj(
        {
          success: false,
          message: "Linked subscription limit reached.",
          data: {},
        },
        400,
        false
      );
    }

    //* Check the scope is under the subscription
    if (!subscriptionData?.scope_id) {
      return ReturnDataObj(
        {
          success: false,
          message: "Scope is not found in the subscription!",
          data: {},
        },
        400,
        false
      );
    }

    //* Simplify the modules Ids
    const moduleIds = subscriptionData?.scope_id.modules.map(
      (module) => module?._id
    );
    const missingScopeId = scope.filter((id) => !moduleIds.includes(id));
    if (missingScopeId.length) {
      return ReturnDataObj(
        {
          success: false,
          message: `Scope ${missingScopeId.join(
            ", "
          )} are not valid scope accordingly user subscription!`,
          data: {},
        },
        400,
        false
      );
    }

    const host = await generateCommunityURL({ community });
    if (!host) {
      return ReturnDataObj(
        {
          success: false,
          message: "Error generating community URL!",
          data: {},
        },
        400,
        false
      );
    }

    let CUEdge = null;
    //* If user already exists & have the edges
    if (userExists && userExists.user_edges) {
      if (
        userExists.user_edges.find(
          (edge) => edge.relation_id?.toString() === relation_id
        )
      ) {
        return ReturnDataObj(
          {
            success: false,
            message: "User with same email already exists in this platform",
            data: {},
          },
          400,
          false
        );
      }

      // create user edge
      const { data: userEdge } = await userEdgesService.createUserEdge({
        userEdgeData: {
          relation_id: relation_id,
          user_id: userExists._id,
          role_id: role?._id,
          platform: false,
          type: "CU",
        },
      });
      CUEdge = userEdge;

      // add the created user edge to user data
      const updatedUserData = await this.repository.updateUser({
        user_id: userExists._id,
        userData: {
          user_edges: [...userExists.user_edges, userEdge._id],
          status: "ACTIVE"
        },
      });
    } else {
      // sign up the new user
      const { data } = await this.signUp({
        first_name,
        last_name,
        preferred_email: email,
        skipEmail: false,
        defaultActivate: true,
      });
      userExists = data;

      // create user edge
      const { data: userEdge } = await userEdgesService.createUserEdge({
        userEdgeData: {
          relation_id: relation_id,
          user_id: userExists?._id,
          role_id: role?._id,
          platform: false,
          type: "CU",
        },
      });
      CUEdge = userEdge;

      // update user edges
      await this.addUserEdge({
        userId: userExists._id,
        userEdgeId: userEdge._id,
      });
    }

    //* create the invite cocollaborator entry in db
    const inviteCollaborators =
      await this.InviteCollaboratorsRepository.createInviteCollaborators({
        payload: {
          first_name,
          last_name,
          email,
          invitee_id,
          user_id: userExists?._id,
          scope,
          relation_id,
          tier_id: subscriptionData.tier_id?._id,
          isAdminInvite: true,
        },
      });
    if (!inviteCollaborators) {
      return ReturnDataObj(
        {
          success: false,
          message: "Failed to create invite collaborator entry!",
          data: {},
        },
        400,
        false
      );
    }

    //* send the invite collaborator user event
    const payload = {
      event: "INVITE_COLLABORATOR_USER",
      data: {
        edge: CUEdge,
        user_id: userExists?._id,
        owner_id: invitee_id,
        modules: scope,
        relation_id: relation_id,
        subscription_id: fetchEdge?.subscription_id,
      },
    };
    publishMessage(
      JSON.stringify(payload),
      "MONOLITH_INVITE_COLLABORATOR_USER"
    );

    //* create the community s3 logo
    let communitiyLogo = null;
    if (community?.logo) {
      communitiyLogo = `https://${AWS_BUCKET}.s3.us-east-2.amazonaws.com/groupos/${community?._id}/${community?.logo}`;
    }

    //* Create the URL for the invite collaborator user email redirection to the signup page 
    const grouposLink = `${
      host ?? FRONTEND_DOMAIN
    }/signup?email=${email}&first_name=${userExists?.first_name}&last_name=${
      userExists?.last_name
    }&encode_email=${encodeURIComponent(email)}`;
    
    mailService.sendMail({
      to: userExists["Preferred Email"],
      subject: "You have been invited for the Team user!",
      template: "collaborator-invitation",
      context: {
        name: `${userExists.first_name} ${userExists.last_name}`,
        communityName: community.name,
        grouposLink,
        communitiyLogo,
        communityChar: community.name.split("")[0],
      },
      relationId: community?._id ? community._id : "",
      customsmtp: community?.customSMTP ? community.customSMTP : false,
    });

    /**
     * return
     * edge [for adding subscription_id in user edge -> return trip from billing server]
     * user_id
     * owner_id -> subscription -> tier_id
     * modules -> scope_id
     * relation_id
     */
    return ReturnDataObj({
      success: true,
      message: "Invite collaborator user successfully.",
      data: inviteCollaborators,
    });
  };

  /**
   * Service for the get the invite collaborate user list
   */
  getInviteCollaboratorUser = async ({
    page,
    limit,
    search,
    sortBy = "createdAt",
    sortOrder = "Desc",
    relation_id,
    isAllData,
    type,
    invitee_id = null,
    tier_id = null,
  }) => {
    const pageNumber = parseInt(page) || 1;
    const limitNumber = parseInt(limit) || 10;
    const skip = (pageNumber - 1) * limitNumber;

    let searchFilter = search
      ? {
          $or: [
            { email: { $regex: search, $options: "i" } },
            ...(search.trim().split(" ").length > 1
              ? [
                  {
                    $and: [
                      {
                        first_name: {
                          $regex: search.split(" ")[0],
                          $options: "i",
                        },
                      },
                      {
                        last_name: {
                          $regex: search.split(" ")[1],
                          $options: "i",
                        },
                      },
                    ],
                  },
                ]
              : [
                  { first_name: { $regex: search, $options: "i" } },
                  { last_name: { $regex: search, $options: "i" } },
                ]),
            ...(search.trim().split(" ").length > 1
              ? [
                  {
                    $and: [
                      {
                        "invitee_user.first_name": {
                          $regex: search.split(" ")[0],
                          $options: "i",
                        },
                      },
                      {
                        "invitee_user.last_name": {
                          $regex: search.split(" ")[1],
                          $options: "i",
                        },
                      },
                    ],
                  },
                ]
              : [
                  {
                    "invitee_user.first_name": {
                      $regex: search,
                      $options: "i",
                    },
                  },
                  {
                    "invitee_user.last_name": {
                      $regex: search,
                      $options: "i",
                    },
                  },
                  {
                    "invitee_user.Preferred Email": {
                      $regex: search,
                      $options: "i",
                    },
                  },
                ]),
            ...(search.trim().split(" ").length > 1
              ? [
                  {
                    $and: [
                      {
                        "user.first_name": {
                          $regex: search.split(" ")[0],
                          $options: "i",
                        },
                      },
                      {
                        "user.last_name": {
                          $regex: search.split(" ")[1],
                          $options: "i",
                        },
                      },
                    ],
                  },
                ]
              : [
                  {
                    "user.first_name": {
                      $regex: search,
                      $options: "i",
                    },
                  },
                  {
                    "user.last_name": {
                      $regex: search,
                      $options: "i",
                    },
                  },
                  {
                    "user.Preferred Email": {
                      $regex: search,
                      $options: "i",
                    },
                  },
                ]),
          ],
        }
      : null;

    const filter = {
      relation_id: ObjectId(relation_id),
      ...(type
        ? {
            status: type,
          }
        : {
            status: {
              $ne: "REVOKED",
            },
          }),
      ...(invitee_id && { invitee_id: ObjectId(invitee_id) }),
      ...(tier_id && { tier_id }),
    };

    let pipeline = [
      {
        $match: filter,
      },
      {
        $lookup: {
          from: "airtable-syncs",
          localField: "invitee_id",
          foreignField: "_id",
          as: "invitee_user",
          pipeline: [
            {
              $match: {
                isDelete: false,
              },
            },
            {
              $project: {
                first_name: 1,
                last_name: 1,
                display_name: 1,
                groupos_join_date: 1,
                migration_date: 1,
                "Preferred Email": 1,
              },
            },
          ],
        },
      },
      {
        $unwind: {
          path: "$invitee_user",
          preserveNullAndEmptyArrays: false,
        },
      },
      {
        $lookup: {
          from: "airtable-syncs",
          localField: "user_id",
          foreignField: "_id",
          as: "user",
          pipeline: [
            {
              $match: {
                isDelete: false,
              },
            },
            {
              $project: {
                first_name: 1,
                last_name: 1,
                display_name: 1,
                groupos_join_date: 1,
                migration_date: 1,
                "Preferred Email": 1,
              },
            },
          ],
        },
      },
      {
        $unwind: {
          path: "$user",
          preserveNullAndEmptyArrays: false,
        },
      },
      {
        $lookup: {
          from: "user_edges",
          localField: "user_id",
          foreignField: "user_id",
          as: "user_edges_data",
          pipeline: [
            {
              $match: {
                relation_id: ObjectId(relation_id),
                isDelete: false,
                type: "CU",
              },
            },
            {
              $project: {
                _id: 0,
                groupos_join_date: {
                  $ifNull: ["$groupos_join_date", null],
                },
                migration_date: {
                  $ifNull: ["$migration_date", null],
                },
              },
            },
          ],
        },
      },
      {
        $unwind: {
          path: "$user_edges_data",
          preserveNullAndEmptyArrays: true,
        },
      },
      ...(searchFilter
        ? [
            {
              $match: searchFilter,
            },
          ]
        : []),
    ];

    const getAllInviteCUCount =
      await this.InviteCollaboratorsRepository.getAllInviteCollaboratorsCount({
        pipeline,
      });

    const getAllInviteCU =
      await this.InviteCollaboratorsRepository.getAllInviteCollaborators({
        pipeline,
        skip,
        limit: limitNumber,
        sortBy,
        sortOrder: sortOrder === "Asc" ? 1 : -1,
        isAllData,
      });

    return ReturnDataObj({
      success: true,
      message: "Invite collaborator user fetch successfully.",
      data: getAllInviteCU,
      count: getAllInviteCUCount,
      page: pageNumber,
    });
  };

  /**
   * Service for the get the invite collaborate user by id
   */
  getInviteCUById = async ({ id }) => {
    let getInviteCU =
      await this.InviteCollaboratorsRepository.getInviteCollaborators({
        filter: {
          _id: id,
        },
        expand: true,
      });

    if (getInviteCU?.user_id?._id) {
      const fetchEdge = await this.#userEdgesService.repository.findUserEdge({
        filter: {
          user_id: getInviteCU?.user_id?._id,
          relation_id: getInviteCU?.relation_id,
          type: "CU",
          isDelete: false,
        },
      });

      if (fetchEdge?.subscription_id) {
        const subscriptionData = await fetchSubscriptionData({
          subscription_id: fetchEdge?.subscription_id,
        });
        getInviteCU.scope = subscriptionData?.scope_id || null;
      }
    }
    if (!getInviteCU) {
      return ReturnDataObj("Invite User not found", 400, false);
    }

    return ReturnDataObj({
      success: true,
      message: "Invite collaborator user fetch successfully.",
      data: getInviteCU,
    });
  };

  /**
   * Service for the get the suggestion for invite collaborator user
   */
  getSuggestionInviteCU = async ({
    relation_id,
    status,
    invitee_id = null,
    tier_id = null,
  }) => {
    const filter = {
      relation_id,
      isDelete: false,
      ...(status && { status }),
      ...(invitee_id && { invitee_id }),
      ...(tier_id && { tier_id }),
      status: {
        $ne: "REVOKED",
      },
    };

    let getSuggestionInviteCU =
      await this.InviteCollaboratorsRepository.getSuggestionInviteCollaborators(
        {
          filter,
        }
      );

    getSuggestionInviteCU.forEach((detail) => {
      if (detail?.user_id) {
        detail.user_id.groupos_join_date =
          detail?.user_id?.groupos_join_date ?? null;
        detail.user_id.migration_date = detail?.user_id?.migration_date ?? null;
      }
    });

    return ReturnDataObj({
      success: true,
      message: "Invite collaborator user suggestion list fetch successfully.",
      data: getSuggestionInviteCU,
    });
  };

  /**
   * Service for update invite the collaborator user by admin
   */
  updateInviteCU = async ({
    id,
    first_name,
    last_name,
    scope = [],
    relation_id,
    invitee_id = null,
  }) => {
    //* Fetch the community data
    const community = await communitiesService.repository.findCommunity({
      filter: {
        _id: relation_id,
      },
    });
    if (!community) {
      return ReturnDataObj(
        {
          success: false,
          message: "Community is not found!",
          data: {},
        },
        400,
        false
      );
    }

    //* Validation for the duplicate invitation
    const fetchData =
      await this.InviteCollaboratorsRepository.getInviteCollaborators({
        filter: {
          _id: id,
          relation_id,
          isDelete: false,
          ...(invitee_id && { invitee_id }),
        },
      });
    if (!fetchData) {
      return ReturnDataObj(
        {
          success: false,
          message: "Invite Collaborator is not found!",
          data: {},
        },
        400,
        false
      );
    }

    const fetchEdge = await this.#userEdgesService.repository.findUserEdge({
      filter: {
        user_id: fetchData?.invitee_id,
        relation_id: community._id,
        type: "M",
        isDelete: false,
      },
    });
    if (!fetchEdge.subscription_id) {
      return ReturnDataObj(
        {
          success: false,
          message: "User does not have subscription!",
          data: {},
        },
        400,
        false
      );
    }

    const fetchCUEdge = await this.#userEdgesService.repository.findUserEdge({
      filter: {
        user_id: fetchData?.user_id,
        relation_id: community._id,
        type: "CU",
        isDelete: false,
      },
    });
    if (!fetchCUEdge || !fetchCUEdge?.subscription_id) {
      return ReturnDataObj(
        {
          success: false,
          message: "Collaborator edge not found!",
          data: {},
        },
        400,
        false
      );
    }

    const subscriptionData = await fetchSubscriptionData({
      subscription_id: fetchEdge?.subscription_id,
    });
    if (!subscriptionData) {
      return ReturnDataObj(
        {
          success: false,
          message: "Subscription not found!",
          data: {},
        },
        400,
        false
      );
    }

    if (!subscriptionData?.tier_id?.linked_subscription) {
      return ReturnDataObj(
        {
          success: false,
          message: "You don't have access for the link account!",
          data: {},
        },
        400,
        false
      );
    }

    const totalLinkAccountLimit =
      subscriptionData?.tier_id?.linked_subscription_number +
        fetchEdge?.no_of_team_mate || 0;

    // if (totalLinkAccountLimit <= subscriptionData?.linked_subscription_count) {
    //   return ReturnDataObj(
    //     {
    //       success: false,
    //       message: "Linked subscription limit reached.",
    //       data: {},
    //     },
    //     400,
    //     false
    //   );
    // }

    //* Check the scope is under the subscription
    if (!subscriptionData?.scope_id) {
      return ReturnDataObj(
        {
          success: false,
          message: "Scope is not found in the subscription!",
          data: {},
        },
        400,
        false
      );
    }

    //* Simplify the modules Ids and validate the modules
    if (scope.length) {
      const moduleIds = subscriptionData?.scope_id.modules.map(
        (module) => module?._id
      );
      const missingScopeId = scope.filter((id) => !moduleIds.includes(id));
      if (missingScopeId.length) {
        return ReturnDataObj(
          {
            success: false,
            message: `Scope ${missingScopeId.join(
              ", "
            )} are not valid scope accordingly user subscription!`,
            data: {},
          },
          400,
          false
        );
      }

      //* send the invite collaborator user event
      const payload = {
        event: "UPDATE_INVITE_COLLABORATOR_USER",
        data: {
          subscription_id: fetchCUEdge?.subscription_id,
          modules: scope,
        },
      };
      publishMessage(
        JSON.stringify(payload),
        "MONOLITH_UPDATE_INVITE_COLLABORATOR_USER"
      );
    }

    const updateInviteCU =
      await this.InviteCollaboratorsRepository.updateInviteCollaborators({
        filter: {
          _id: fetchData?._id,
        },
        data: {
          ...(first_name && { first_name }),
          ...(last_name && { last_name }),
        },
      });

    if (first_name || last_name) {
      await this.repository.findAndUpdateUser({
        filter: {
          _id: fetchData?.user_id,
        },
        userData: {
          ...(first_name && { first_name }),
          ...(last_name && { last_name }),
        },
      });
    }

    return ReturnDataObj({
      success: true,
      message: "Invite collaborator user updated successfully.",
      data: updateInviteCU,
    });
  };

  /**
   * Service for revoke the invite collaborator user by admin
   */
  revokeInviteCU = async ({ id, relation_id, invitee_id = null }) => {
    //* Fetch the community data
    const community = await communitiesService.repository.findCommunity({
      filter: {
        _id: relation_id,
      },
    });
    if (!community) {
      return ReturnDataObj(
        {
          success: false,
          message: "Community is not found!",
          data: {},
        },
        400,
        false
      );
    }

    //* Validation for the duplicate invitation
    const fetchData =
      await this.InviteCollaboratorsRepository.getInviteCollaborators({
        filter: {
          _id: id,
          relation_id,
          isDelete: false,
          ...(invitee_id && { invitee_id }),
        },
      });
    if (!fetchData) {
      return ReturnDataObj(
        {
          success: false,
          message: "Invite collaborator user is not found!",
          data: {},
        },
        400,
        false
      );
    }

    const fetchEdge = await this.#userEdgesService.repository.findUserEdge({
      filter: {
        user_id: fetchData?.invitee_id,
        relation_id: relation_id,
        type: "M",
        isDelete: false,
      },
    });

    if (!fetchEdge.subscription_id) {
      return ReturnDataObj(
        {
          success: false,
          message: "User does not have subscription!",
          data: {},
        },
        400,
        false
      );
    }

    const subscriptionData = await fetchSubscriptionData({
      subscription_id: fetchEdge?.subscription_id,
    });
    if (!subscriptionData) {
      return ReturnDataObj(
        {
          success: false,
          message: "Subscription not found!",
          data: {},
        },
        400,
        false
      );
    }
    // Check if there are exactly 2 user edges that are need to deleted firebaseid
    const removeCollaborator =
      (await this.#userEdgesService.repository.findUserEdge({
        filter: {
          user_id: fetchData?.user_id,
          isDelete: false,
        },
        expand: true,
      }).length) === 2;
    //* Fetch the user Collaborator edge
    const fetchCUEdge = await this.#userEdgesService.repository.findUserEdge({
      filter: {
        user_id: fetchData?.user_id,
        relation_id: relation_id,
        type: "CU",
        isDelete: false,
      },
      expand: true,
    });
    if (!fetchCUEdge || !fetchCUEdge?.subscription_id) {
      return ReturnDataObj(
        {
          success: false,
          message: "Collaborator edge not found!",
          data: {},
        },
        400,
        false
      );
    }

    //* Update the Invite Collaborator user data
    const updateInviteCU =
      await this.InviteCollaboratorsRepository.updateInviteCollaborators({
        filter: {
          _id: fetchData?._id,
        },
        data: {
          status: "REVOKED",
        },
      });

    //* If invite collaborator user is updated then update the collaborator edge
    if (updateInviteCU) {
      //* Update the collaborator edge
      const updateCUEdge =
        await this.#userEdgesService.repository.findAndUpdateUserEdge({
          filter: {
            _id: fetchCUEdge._id,
          },
          userEdgeData: {
            isDelete: true,
            deleteFrom: "revokeInviteCU",
          },
        });

      //* Remove the edge Id in air-sync Table
      if (updateCUEdge) {
        this.#userEdgesService.repository.findUserAndUpdatebyId({
          filter: {
            _id: fetchData?.user_id,
          },
          updateData: {
            $pull: {
              user_edges: fetchCUEdge._id,
            },
          },
        });
        if (removeCollaborator) {
          try {
            if (fetchCUEdge.user_id.firebaseId) {
              await admin.auth().deleteUser(fetchCUEdge.user_id.firebaseId);
            }
            let firebaseUser = await admin
              .auth()
              .getUserByEmail(fetchCUEdge.user_id["Preferred Email"]);
            if (firebaseUser) {
              // Delete the user from Firebase
              await admin.auth().deleteUser(firebaseUser.uid); // Use the UID to delete
            }
          } catch (error) {
            console.log("Firebase error, ", error.message);
          }
          await this.repository.updateUser({
            user_id: fetchCUEdge.user_id._id,
            userData: {
              firebaseId: "",
              providers: [],
              provider: "",
              groupos_join_date: null,
            },
          });
        }
      }

      //* send the revoke collaborator user event
      const payload = {
        event: "REVOKE_INVITE_COLLABORATOR_USER",
        data: {
          subscription_id: fetchCUEdge?.subscription_id,
          owner_subscription_id: fetchEdge?.subscription_id,
        },
      };
      publishMessage(
        JSON.stringify(payload),
        "MONOLITH_REVOKE_INVITE_COLLABORATOR_USER"
      );
    } else {
      return ReturnDataObj(
        {
          success: false,
          message: "Failed to revoke the invite collaborator user!",
          data: {},
        },
        400,
        false
      );
    }

    //* create the community s3 logo
    let communitiyLogo = null;
    if (community?.logo) {
      communitiyLogo = `https://${AWS_BUCKET}.s3.us-east-2.amazonaws.com/groupos/${community?._id}/${community?.logo}`;
    }

    mailService.sendMail({
      to: updateInviteCU?.email,
      subject: "Your invitation to join as a Team user has been revoked!",
      template: "collaborator-invitation-revoked",
      context: {
        name: `${updateInviteCU.first_name} ${updateInviteCU.last_name}`,
        communityName: community.name,
        communitiyLogo,
        communityChar: community.name.split("")[0],
      },
      relationId: community?._id ? community._id : "",
      customsmtp: community?.customSMTP ? community.customSMTP : false,
    });

    return ReturnDataObj({
      success: true,
      message: "Invite collaborator user revoked successfully.",
      data: updateInviteCU,
    });
  };

  revokeAllCollaborators = async ({
    invitee_id,
    relation_id,
    tier_id = null,
  }) => {
    //* Fetch the community data
    const community = await communitiesService.repository.findCommunity({
      filter: { _id: relation_id },
    });
    if (!community) {
      return ReturnDataObj(
        { success: false, message: "Community not found!", data: {} },
        400,
        false
      );
    }

    const communitiyLogo = community?.logo
      ? `https://${AWS_BUCKET}.s3.us-east-2.amazonaws.com/groupos/${community?._id}/${community?.logo}`
      : null;

    //* Fetch all matching invitations
    const invitations =
      await this.InviteCollaboratorsRepository.getAllRevokeInviteCollaborators({
        filter: {
          invitee_id,
          relation_id,
          isDelete: false,
          status: { $in: ["PENDING", "ACCEPTED", "ACTIVE"] },
          ...(tier_id && { tier_id }),
        },
        expand: false,
      });

    if (!invitations || invitations.length === 0) {
      return ReturnDataObj(
        {
          success: false,
          message: "No valid collaborator invitations found to revoke.",
          data: {},
        },
        400,
        false
      );
    }

    //* Validation for the main user's subscription edge
    const ownerEdge = await this.#userEdgesService.repository.findUserEdge({
      filter: { user_id: invitee_id, relation_id, type: "M", isDelete: false },
    });

    if (!ownerEdge?.subscription_id) {
      return ReturnDataObj(
        {
          success: false,
          message: "User does not have a valid subscription!",
          data: {},
        },
        400,
        false
      );
    }

    //* Process all invitations in bulk
    const results = await Promise.all(
      invitations.map(async (invitation) => {
        try {
          //* Fetch collaborator edge
          const collaboratorEdge =
            await this.#userEdgesService.repository.findUserEdge({
              filter: {
                user_id: invitation.user_id,
                relation_id,
                type: "CU",
                isDelete: false,
              },
            });

          if (!collaboratorEdge || !collaboratorEdge.subscription_id) {
            return null; // Skip this collaborator
          }

          //* Update the invite status to REVOKED
          const updatedInvite =
            await this.InviteCollaboratorsRepository.updateInviteCollaborators({
              filter: { _id: invitation._id },
              data: { status: "REVOKED" },
            });

          if (!updatedInvite) {
            console.log(`Failed to update invitation ID: ${invitation._id}`);
            return null;
          }

          //* Update the collaborator edge
          await this.#userEdgesService.repository.findAndUpdateUserEdge({
            filter: { _id: collaboratorEdge._id },
            userEdgeData: {
              isDelete: true,
              deleteFrom: "revokeAllCollaborators",
            },
          });

          //* Remove the edge ID in the air-sync table
          await this.#userEdgesService.repository.findUserAndUpdatebyId({
            filter: { _id: invitation.user_id },
            updateData: { $pull: { user_edges: collaboratorEdge._id } },
          });

          //* Send the revoke event
          const payload = {
            event: "REVOKE_INVITE_COLLABORATOR_USER",
            data: {
              subscription_id: collaboratorEdge.subscription_id,
              owner_subscription_id: ownerEdge.subscription_id,
            },
          };
          publishMessage(
            JSON.stringify(payload),
            "MONOLITH_REVOKE_INVITE_COLLABORATOR_USER"
          );

          //* Send email notification
          await mailService.sendMail({
            to: updatedInvite.email,
            subject: "Your invitation to join as a Team user has been revoked!",
            template: "collaborator-invitation-revoked",
            context: {
              name: `${updatedInvite.first_name} ${updatedInvite.last_name}`,
              communityName: community.name,
              communitiyLogo,
              communityChar: community.name[0],
            },
            relationId: community?._id ? community._id : "",
            customsmtp: community?.customSMTP ? community.customSMTP : false,
          });

          return updatedInvite; // Successfully processed
        } catch (error) {
          console.error(
            `Error processing invitation ID: ${invitation._id}`,
            error
          );
          return null; // Skip this collaborator
        }
      })
    );

    //* Filter successful results
    const successfulInvitations = results.filter(Boolean);

    return ReturnDataObj({
      success: true,
      message: `${successfulInvitations.length} invitations revoked successfully, and emails sent.`,
      data: successfulInvitations,
    });
  };
  reconnectAllCollaborators = async ({
    invitee_id,
    relation_id,
    tier_id = null,
  }) => {
    try {
      //* Fetch the community data
      const community = await communitiesService.repository.findCommunity({
        filter: { _id: relation_id },
      });
      if (!community) {
        return ReturnDataObj(
          { success: false, message: "Community not found!", data: {} },
          400,
          false
        );
      }

      //* Fetch the collaborator user role
      const { data: role } = await rolesService.findRole({
        filter: { role_name: "COLLABORATOR_USER" },
      });
      if (!role) {
        return ReturnDataObj(
          {
            success: false,
            message: "Collaborator role is not found!",
            data: {},
          },
          400,
          false
        );
      }

      //* Validation for the main user's subscription edge
      const ownerEdge = await this.#userEdgesService.repository.findUserEdge({
        filter: {
          user_id: invitee_id,
          relation_id,
          type: "M",
          isDelete: false,
        },
      });

      if (!ownerEdge?.subscription_id) {
        return ReturnDataObj(
          {
            success: false,
            message: "User does not have a valid subscription!",
            data: {},
          },
          400,
          false
        );
      }

      const subscriptionData = await fetchSubscriptionData({
        subscription_id: ownerEdge?.subscription_id,
      });
      if (!subscriptionData) {
        return ReturnDataObj(
          {
            success: false,
            message: "Subscription not found!",
            data: {},
          },
          400,
          false
        );
      }

      //* Fetch all matching invitations
      const invitations =
        await this.InviteCollaboratorsRepository.getAllRevokeInviteCollaborators(
          {
            filter: {
              invitee_id,
              relation_id,
              isDelete: false,
              status: { $in: ["PENDING", "ACCEPTED", "ACTIVE"] },
              ...(tier_id && { tier_id }),
            },
            expand: false,
          }
        );

      if (!invitations || invitations.length === 0) {
        return ReturnDataObj(
          {
            success: false,
            message: "No valid collaborator invitations found to revoke.",
            data: {},
          },
          400,
          false
        );
      }

      let scope = subscriptionData.scope_id.modules.map((item) => item._id);

      // Check if the linked subscription count is less than or equal to zero
      if (subscriptionData?.tier_id?.linked_subscription_number <= 0) {
        return await this.revokeAllCollaborators({
          invitee_id,
          relation_id,
        });
      }
      const totalLinkAccountLimit =
        (subscriptionData?.tier_id?.linked_subscription_number ?? 0) +
        (ownerEdge?.no_of_team_mate ?? 0);

      let invitationsToProcess;

      if (totalLinkAccountLimit < invitations.length) {
        // Calculate extra collaborators to remove
        let extraCollaborator = Math.abs(
          invitations.length - totalLinkAccountLimit
        );

        // Remove excess invitations
        const invitationsToRemove = invitations.slice(0, extraCollaborator);

        for (const invitation of invitationsToRemove) {
          await this.revokeInviteCU({
            id: invitation._id,
            relation_id,
            invitee_id,
          });
        }

        // Adjust invitations to process based on the new total limit
        invitationsToProcess = invitations.slice(extraCollaborator);
      } else {
        // Process all invitations up to the total limit
        invitationsToProcess = invitations.slice(0, totalLinkAccountLimit);
      }

      //* Process all invitations in bulk
      const results = await Promise.all(
        invitationsToProcess.map(async (invitation) => {
          try {
            //* Update the invite status to PENDING
            const updatedInvite =
              await this.InviteCollaboratorsRepository.updateInviteCollaborators(
                {
                  filter: { _id: invitation._id },
                  data: { tier_id: subscriptionData.tier_id?._id },
                }
              );

            if (!updatedInvite) {
              console.error(
                `Failed to update invitation ID: ${invitation._id}`
              );
              return null; // Skip this invitation if the update fails
            }

            //* Fetch collaborator edge
            const collaboratorEdge =
              await this.#userEdgesService.repository.findUserEdge({
                filter: {
                  user_id: invitation.user_id,
                  relation_id,
                  type: "CU",
                  isDelete: false,
                },
              });

            if (!collaboratorEdge || !collaboratorEdge.subscription_id) {
              return null; // Skip this collaborator
            }

            // * reinvite collaborator user event
            const payload = {
              event: "REINVITE_COLLABORATOR_USER",
              data: {
                edge: collaboratorEdge,
                user_id: invitation.user_id,
                owner_id: invitee_id,
                modules: scope,
                relation_id: relation_id,
                subscription_id: ownerEdge?.subscription_id,
              },
            };
            publishMessage(
              JSON.stringify(payload),
              "MONOLITH_REINVITE_COLLABORATOR_USER"
            );

            //* Optional: Email notification (currently commented out)
            /*
            await mailService.sendMail({
              to: updatedInvite.email,
              subject: "Your invitation to join as a Team user has been revoked!",
              template: "collaborator-invitation-revoked",
              context: {
                name: `${updatedInvite.first_name} ${updatedInvite.last_name}`,
                communityName: community.name,
                communitiyLogo, // Ensure communitiyLogo is defined elsewhere in your code
                communityChar: community.name[0],
              },
            });
            */

            return updatedInvite; // Successfully processed
          } catch (error) {
            console.error(
              `Error processing invitation ID: ${invitation._id}`,
              error
            );
            return null; // Skip this invitation on error
          }
        })
      );

      //* Filter successful results
      const successfulInvitations = results.filter(Boolean);

      return ReturnDataObj({
        success: true,
        message: `${successfulInvitations.length} invitations revoked successfully.`,
        data: successfulInvitations,
      });
    } catch (error) {
      console.error("Error in reconnectAllCollaborators:", error);
      return ReturnDataObj(
        {
          success: false,
          message: "An error occurred while processing the collaborators.",
          data: {},
        },
        500,
        false
      );
    }
  };

  /**
   * Service for revoke the invite collaborator user when community user subscription will be canclled
   */
  revokeAllInviteCUbyCanclledEvent = async ({
    inviteCollaboratorUserData,
    community,
    relation_id,
  }) => {
    try {
      //* Fetch the user Collaborator edge
      const fetchCUEdge = await this.#userEdgesService.repository.findUserEdge({
        filter: {
          user_id: inviteCollaboratorUserData?.user_id,
          relation_id: relation_id,
          type: "CU",
          isDelete: false,
        },
      });
      if (!fetchCUEdge || !fetchCUEdge?.subscription_id) {
        return ReturnDataObj(
          {
            success: false,
            message: "Collaborator edge not found!",
            data: {},
          },
          400,
          false
        );
      }

      //* Update the Invite Collaborator user data
      const updateInviteCU =
        await this.InviteCollaboratorsRepository.updateInviteCollaborators({
          filter: {
            _id: inviteCollaboratorUserData?._id,
          },
          data: {
            status: "REVOKED",
          },
        });

      //* If invite collaborator user is updated then update the collaborator edge
      if (updateInviteCU) {
        //* Update the collaborator edge
        const updateCUEdge =
          await this.#userEdgesService.repository.findAndUpdateUserEdge({
            filter: {
              _id: fetchCUEdge._id,
            },
            userEdgeData: {
              isDelete: true,
              deleteFrom: "revokeAllInviteCUbyCanclledEvent",
            },
          });

        //* Remove the edge Id in air-sync Table
        if (updateCUEdge) {
          this.#userEdgesService.repository.findUserAndUpdatebyId({
            filter: {
              _id: fetchCUEdge?.user_id,
            },
            updateData: {
              $pull: {
                user_edges: fetchCUEdge._id,
              },
            },
          });
        }

        //* send the revoke collaborator user event
        const payload = {
          event: "REVOKE_INVITE_COLLABORATOR_USER",
          data: {
            subscription_id: fetchCUEdge?.subscription_id,
            owner_subscription_id: null,
          },
        };
        publishMessage(
          JSON.stringify(payload),
          "MONOLITH_REVOKE_INVITE_COLLABORATOR_USER"
        );
      } else {
        return ReturnDataObj(
          {
            success: false,
            message: "Failed to revoke the invite collaborator user!",
            data: {},
          },
          400,
          false
        );
      }

      //* create the community s3 logo
      let communitiyLogo = null;
      if (community?.logo) {
        communitiyLogo = `https://${AWS_BUCKET}.s3.us-east-2.amazonaws.com/groupos/${community?._id}/${community?.logo}`;
      }

      mailService.sendMail({
        to: updateInviteCU?.email,
        subject: "Your invitation to join as a Team user has been revoked!",
        template: "collaborator-invitation-revoked",
        context: {
          name: `${updateInviteCU.first_name} ${updateInviteCU.last_name}`,
          communityName: community.name,
          communitiyLogo,
          communityChar: community.name.split("")[0],
        },
        relationId: community?._id ? community._id : "",
        customsmtp: community?.customSMTP ? community.customSMTP : false,
      });

      return ReturnDataObj({
        success: true,
        message: "Invite collaborator user revoked successfully.",
        data: updateInviteCU,
      });
    } catch (error) {
      console.log(
        "🚀 ~ file: users-service.js:5986 ~ UsersService ~ error:",
        error
      );
    }
  };

  /**
   * Service for resend the invite collaborator user by admin and user
   */
  resendInviteCU = async ({ id, relation_id, invitee_id = null }) => {
    //* Validation for the duplicate invitation
    const fetchData =
      await this.InviteCollaboratorsRepository.getInviteCollaborators({
        filter: {
          _id: id,
          relation_id,
          isDelete: false,
          ...(invitee_id && { invitee_id }),
        },
      });
    if (!fetchData) {
      return ReturnDataObj(
        {
          success: false,
          message: "This user has already been invited.",
          data: {},
        },
        400,
        false
      );
    }

    //* Fetch the community data
    const community = await communitiesService.repository.findCommunity({
      filter: {
        _id: relation_id,
      },
    });
    if (!community) {
      return ReturnDataObj(
        {
          success: false,
          message: "Community is not found!",
          data: {},
        },
        400,
        false
      );
    }

    const host = await generateCommunityURL({ community });
    if (!host) {
      return ReturnDataObj(
        {
          success: false,
          message: "Error generating community URL!",
          data: {},
        },
        400,
        false
      );
    }

    let communitiyLogo = null;
    //* create the community s3 logo
    if (community?.logo) {
      communitiyLogo = `https://${AWS_BUCKET}.s3.us-east-2.amazonaws.com/groupos/${community?._id}/${community?.logo}`;
    }

    mailService.sendMail({
      to: fetchData.email,
      subject: "You have been invited for the Team user!",
      template: "collaborator-invitation",
      context: {
        name: `${fetchData.first_name} ${fetchData.last_name}`,
        communityName: community.name,
        grouposLink: `${host ?? FRONTEND_DOMAIN}/signup?email=${
          fetchData?.email
        }&first_name=${fetchData?.first_name}&last_name=${
          fetchData?.last_name
        }&encode_email=${encodeURIComponent(fetchData?.email)}`,
        communitiyLogo,
        communityChar: community.name.split("")[0],
      },
      relationId: community?._id ? community._id : "",
      customsmtp: community?.customSMTP ? community.customSMTP : false,
    });

    return ReturnDataObj({
      success: true,
      message: "Invite collaborator invitation sent successfully.",
      data: {},
    });
  };

  /**
   * Service for migrate the invite collaborator user from MDS to GroupOs
   */
  migrateInviteCU = async ({ relation_id, data }) => {
    try {
      //* Fetch the community data
      const community = await communitiesService.repository.findCommunity({
        filter: {
          _id: relation_id,
          isDelete: false,
        },
      });
      if (!community) {
        return ReturnDataObj(
          {
            success: false,
            message: "Community is not found!",
            data: {},
          },
          400,
          false
        );
      }

      //* Fetch the collaborator user role
      const { data: role } = await rolesService.findRole({
        filter: {
          role_name: "COLLABORATOR_USER",
        },
      });
      if (!role) {
        return ReturnDataObj(
          {
            success: false,
            message: "Collaborator role is not found!",
            data: {},
          },
          400,
          false
        );
      }

      //* Error variable for the handle the error
      const traceData = new Set();

      //* Manage the link subscription limit for the all user
      const teamUserLimit = new Map();

      //* Outer loop for the invitee user data
      for await (let migrateUserData of data) {
        if (
          !migrateUserData.userData.length ||
          !migrateUserData?.inviteeUserId
        ) {
          traceData.add({ message: "Invalid Body!" });
          continue;
        }

        //* Find the Invitee user edges and data
        const fetchEdge = await this.#userEdgesService.repository.findUserEdge({
          filter: {
            user_id: migrateUserData?.inviteeUserId,
            relation_id: community._id,
            type: "M",
            isDelete: false,
          },
        });
        if (!fetchEdge?.subscription_id) {
          traceData.add({
            message:
              "Invitee user edge not found or subscription id not found!",
            data: {
              inviteeUserId: migrateUserData?.inviteeUserId,
            },
          });
          continue;
        }

        //* Find the invitee user subscription details
        const subscriptionData = await fetchSubscriptionData({
          subscription_id: fetchEdge?.subscription_id,
        });
        if (!subscriptionData) {
          traceData.add({
            message: "Invitee user subscription not found!",
            data: {
              inviteeUserId: migrateUserData?.inviteeUserId,
            },
          });
          continue;
        }

        //* Check user have the link subscription tier or not
        if (!subscriptionData?.tier_id?.linked_subscription) {
          traceData.add({
            message: "You don't have access for the link account!",
            data: {
              inviteeUserId: migrateUserData?.inviteeUserId,
            },
          });
          continue;
        }

        //* Create the total limit of the linked subscription
        const totalLinkAccountLimit =
          subscriptionData?.tier_id?.linked_subscription_number +
            fetchEdge?.no_of_team_mate || 0;

        //* Check that the subscription linked limit is exceed or not
        if (
          totalLinkAccountLimit <= subscriptionData?.linked_subscription_count
        ) {
          traceData.add({
            message: "Linked subscription limit reached!",
            data: {
              inviteeUserId: migrateUserData?.inviteeUserId,
            },
          });
          continue;
        }

        //* Set the data of the linked subscription
        teamUserLimit.set(migrateUserData?.inviteeUserId, {
          totalLinkAccountLimit,
          usedLimit: subscriptionData?.linked_subscription_count,
          // usedLimit: 22,
        });

        //* Check the scope is under the subscription
        if (!subscriptionData?.scope_id) {
          traceData.add({
            message: "Scope is not found in the subscription!",
            data: {
              inviteeUserId: migrateUserData?.inviteeUserId,
            },
          });
          continue;
        }

        //* Inner loop for the Migration user data
        for await (let user of migrateUserData.userData) {
          const fetchInviteUser =
            await this.InviteCollaboratorsRepository.getInviteCollaborators({
              filter: {
                email: user.email,
                relation_id: relation_id,
                isDelete: false,
                status: {
                  $ne: "REVOKED",
                },
              },
            });
          if (fetchInviteUser) {
            traceData.add({
              message: "User have already invited in this community!",
              data: {
                user,
                inviteeUserId: migrateUserData?.inviteeUserId,
              },
            });
            continue;
          }
          user.scope = user?.scope.map((scopeItem) => scopeItem.toLowerCase());

          //* Get the relavent scope from the invitee scope modules
          const scope_id = subscriptionData?.scope_id?.modules
            .filter((module) => user?.scope.includes(module.name))
            .map((module) => module._id);

          if (!scope_id.length) {
            traceData.add({
              message: "Scopes Ids not found!",
              data: {
                user,
                inviteeUserId: migrateUserData?.inviteeUserId,
              },
            });
            continue;
          }

          //* Get the Link subscription limit data
          const teamlimitData = teamUserLimit.get(user.inviteeUserId);

          //* Check that the subscription linked limit is exceed or not
          if (teamlimitData.totalLinkAccountLimit <= teamlimitData.usedLimit) {
            traceData.add({
              message: "Linked subscription limit reached!",
              data: {
                inviteeUserId: migrateUserData?.inviteeUserId,
                user,
              },
            });
            continue;
          }

          //* check for existing user with same email
          let userExists = await this.repository.findUser({
            filter: { "Preferred Email": user?.email },
            expand: true,
          });

          let CUEdge = null;
          //* Check If user already exist or not
          if (userExists && userExists.user_edges) {
            if (
              userExists.user_edges.find(
                (edge) => edge.relation_id?.toString() === relation_id
              )
            ) {
              traceData.add({
                message:
                  "User with same email already exists in this platform!",
                data: {
                  inviteeUserId: migrateUserData?.inviteeUserId,
                  user,
                },
              });
              continue;
            }

            //* create collaborator user user edge
            const { data: userEdge } = await userEdgesService.createUserEdge({
              userEdgeData: {
                relation_id: relation_id,
                user_id: userExists._id,
                role_id: role?._id,
                platform: false,
                type: "CU",
              },
            });
            CUEdge = userEdge;

            //* add the created user edge to user data
            await this.repository.updateUser({
              user_id: userExists._id,
              userData: {
                user_edges: [...userExists.user_edges, userEdge._id],
                status: "ACTIVE"
              },
            });
          } else {
            //* sign up the new user
            const { data } = await this.signUp({
              first_name: user.first_name ?? user.email,
              last_name: user.last_name ?? "",
              preferred_email: user.email,
              skipEmail: false,
              defaultActivate: true,
            });
            userExists = data;

            //* create collaborator user edge
            const { data: userEdge } = await userEdgesService.createUserEdge({
              userEdgeData: {
                relation_id: relation_id,
                user_id: userExists?._id,
                role_id: role?._id,
                platform: false,
                type: "CU",
              },
            });
            CUEdge = userEdge;

            //* update user edge id in the air-sync ntable
            await this.addUserEdge({
              userId: userExists._id,
              userEdgeId: userEdge._id,
            });
          }

          //* create the invite cocollaborator entry in db
          const inviteCollaborators =
            await this.InviteCollaboratorsRepository.createInviteCollaborators({
              payload: {
                first_name: userExists?.first_name,
                last_name: userExists?.last_name,
                email: user?.email,
                invitee_id: migrateUserData?.inviteeUserId,
                user_id: userExists?._id,
                scope: scope_id,
                relation_id,
                tier_id: subscriptionData.tier_id?._id,
                isAdminInvite: true,
              },
            });
          if (!inviteCollaborators) {
            traceData.add({
              message: "Failed to create invite collaborator entry!",
              data: {
                user,
                inviteeUserId: migrateUserData?.inviteeUserId,
              },
            });
            continue;
          }

          //* send the invite collaborator user event
          const payload = {
            event: "INVITE_COLLABORATOR_USER",
            data: {
              edge: CUEdge,
              user_id: userExists?._id,
              owner_id: migrateUserData?.inviteeUserId,
              modules: scope_id,
              relation_id: relation_id,
              subscription_id: fetchEdge?.subscription_id,
            },
          };
          publishMessage(
            JSON.stringify(payload),
            "MONOLITH_INVITE_COLLABORATOR_USER"
          );

          //* Increate the linked subscription limit in the Map Array
          if (teamUserLimit.has(user?.inviteeUserId)) {
            let teamlimit = teamUserLimit.get(user.inviteeUserId);
            teamlimit.usedLimit += 1;

            teamUserLimit.set(user?.inviteeUserId, teamlimit);
          }
        }
      }

      return ReturnDataObj({
        success: true,
        message: "Migration run successfully.",
        data: { traceError: Array.from(traceData) },
      });
    } catch (error) {
      console.log(
        "🚀 ~ file: users-service.js:6041 ~ UsersService ~ migrateInviteCU= ~ error:",
        error
      );
      return ReturnDataObj({
        success: false,
        message: `${error.message}`,
        data: {},
      });
    }
  };

  /**
   * Service for the get the invite collaborate user list for when tier update
   */
  getInviteCUForTierUpdate = async ({
    subscriptionData,
    linked_subscription_number,
  }) => {
    const userIds = subscriptionData.map((data) => ObjectId(data?.user_id));

    let pipeline = [
      {
        $match: {
          user_id: { $in: userIds },
          type: "M",
          isDelete: false,
        },
      },
      {
        $lookup: {
          from: "airtable-syncs",
          localField: "user_id",
          foreignField: "_id",
          as: "user",
          pipeline: [
            {
              $project: {
                first_name: 1,
                last_name: 1,
                "Preferred Email": 1,
              },
            },
          ],
        },
      },
      {
        $unwind: {
          path: "$user",
          preserveNullAndEmptyArrays: true,
        },
      },
      {
        $lookup: {
          from: "invite_collaborators",
          localField: "user_id",
          foreignField: "invitee_id",
          as: "invite_collaborators",
          pipeline: [
            {
              $match: {
                status: {
                  $ne: "REVOKED",
                },
                isDelete: false,
              },
            },
            {
              $project: {
                first_name: 1,
                last_name: 1,
                email: 1,
                status: 1,
              },
            },
          ],
        },
      },
      {
        $project: {
          user_id: 1,
          type: 1,
          no_of_team_mate: 1,
          subscription_id: 1,
          user: 1,
          invite_collaborators: 1,
        },
      },
    ];

    const getAllInviteCU =
      await this.#userEdgesService.repository.fetchUserEdgesForTierUpdate({
        pipeline,
      });

    const data = getAllInviteCU
      .filter((item) => {
        const subscriptionDataItem = subscriptionData.find(
          (sub) => sub.user_id.toString() === item.user_id.toString()
        );

        const totalCount =
          subscriptionDataItem?.linked_subscription_count +
            item?.no_of_team_mate || 0;

        return totalCount > linked_subscription_number;
      })
      .map((item) => item);

    return ReturnDataObj({
      success: data.length ? true : false,
      message: data.length
        ? "You are not able to update the linked subscription"
        : "You are able to update the linked subscription",
      data: data,
    });
  };

  /**
   * Service for check user is able to login or not
   */
  checkUserLoginStatus = async ({ preferred_email }) => {
    try {
      if (!preferred_email || preferred_email === "") {
        return ReturnDataObj("Preferred Email is required!", 400);
      }

      const fetchUser = await this.repository.findAndUpdateUser({
        filter: {
          $or: [
            { "Preferred Email": preferred_email },
            { apple_private_email: preferred_email },
          ],
          isDelete: false,
        },
      });
      if (!fetchUser) {
        return ReturnDataObj("This user is not found in system!", 400);
      }

      const user = await admin
        .auth()
        .getUserByEmail(
          fetchUser.apple_private_email
            ? fetchUser.apple_private_email
            : preferred_email.toString()
        );

      return ReturnDataObj({
        success:
          fetchUser.status === "ACTIVE" && user?.emailVerified ? true : false,
        message: "success",
        data: {},
      });
    } catch (error) {
      if (error?.code === "auth/invalid-email")
        return ReturnDataObj(error.message, 500, false);

      if (error.code === "auth/argument-error")
        return res.status(500).send(error.message);

      return ReturnDataObj(error.message, 500, false);
    }
  };

  /**
   * Service for add user logn error log
   */
  addUserLogInErrorLog = async ({ body }) => {
    try {
      if (body?.id) {
        const updatelog =
          await this.userLogInErrorLogRepository.updateUserLogInErrorLog({
            filter: {
              _id: body?.id,
              isDelete: false,
            },
            data: {
              $push: {
                data: body.data,
              },
            },
          });

        if (!updatelog) {
          return ReturnDataObj(
            {
              success: false,
              message: "Record not found!",
              data: {},
            },
            404,
            false
          );
        }

        return ReturnDataObj({
          success: true,
          message: "success",
          data: updatelog,
        });
      } else {
        const createLog =
          await this.userLogInErrorLogRepository.createUserLogInErrorLog({
            payload: {
              data: [body?.data],
            },
          });

        if (!createLog) {
          return ReturnDataObj(
            {
              success: false,
              message: "Error while creating the log!",
              data: {},
            },
            400,
            false
          );
        }
        return ReturnDataObj({
          success: true,
          message: "success",
          data: createLog,
        });
      }
    } catch (error) {
      if (error?.code === "auth/invalid-email")
        return ReturnDataObj(error.message, 500, false);

      if (error.code === "auth/argument-error")
        return res.status(500).send(error.message);

      return ReturnDataObj(error.message, 500, false);
    }
  };

  getAllNoSocialProviderMembers = async ({ relation_id }) => {
    const filterObj = {
      relation_id: ObjectId(relation_id),
      owner: false,
      type: "M",
      isDelete: false,
      stripe_customer_db_id: { $exists: true },
      subscription_id: { $exists: true },
    };

    const communityMembers =
      await userEdgesService.repository.findAllNoSocialProviderMembers({
        filter: filterObj,
      });

    return ReturnDataObj({
      data: communityMembers,
    });
  };
  connectSocialAccountMember = async ({
    connectTo,
    connectFrom,
    domain,
  }) => {
    try {
      let community = null;
      if (domain.includes(BASE_URL)) {
        community = await communitiesService.repository.findCommunity({
          filter: {
            nickname: domain.split(".")[0],
            isDelete: false,
          },
        });
      } else {
        community = await communitiesService.repository.findCommunity({
          filter: {
            "customDomain.domain": domain,
            "customDomain.status": "active",
            isDelete: false,
          },
        });
      }
      if(!community){
        return ReturnDataObj("Community not found!", 400, false);
      }
      // Fetch users from the repository concurrently
      const [connectToUser, connectFromUser] = await Promise.all([
        this.repository.findUser({
          filter: { _id: ObjectId(connectTo), isDelete: false },
        }),
        this.repository.findUser({
          filter: { _id: ObjectId(connectFrom), isDelete: false },
          expand: true,
        }),
      ]);

      if (!connectToUser || !connectFromUser) {
        return ReturnDataObj("User not found!", 400, false);
      }

      // if (connectToUser.provider || connectToUser.providers.length > 0) {
      //   return ReturnDataObj(
      //     "Connect To user already has a social connected account.",
      //     400,
      //     false
      //   );
      // }

      if (
        !connectFromUser.providers ||
        connectFromUser.firebaseId.length == 0 ||
        connectFromUser.providers.length === 0
      ) {
        return ReturnDataObj(
          "Connect From user does not have a social connected account.",
          400,
          false
        );
      }

      let updateDataForConnectTo = {
        provider: connectFromUser.provider,
        firebaseId: connectFromUser.firebaseId,
        providers: connectFromUser.providers,
      };
      let providers_history =
        connectFromUser.providers_history.length > 0
          ? connectFromUser.providers_history
          : [];

      providers_history.push({
        name: connectFromUser.providers[0].name,
        email: connectFromUser.providers[0].email,
        socialId: connectFromUser.providers[0].socialId,
        date: connectFromUser.providers[0].date,
        isDelete: connectFromUser.providers[0].isDelete,
        deletedAt: new Date().toISOString(),
        firebaseId: connectFromUser.firebaseId,
        connectTo: connectToUser._id,
      });
      let updateDataForConnectFrom = {
        provider: "",
        firebaseId: "",
        providers: [],
        user_edges: connectFromUser.user_edges,
        providers_history 
      };

      await this.repository.updateUser({
        user_id: connectToUser._id,
        userData: updateDataForConnectTo,
      });

      const memberEdge = connectFromUser.user_edges.find(
        (edge) =>
          edge.type === "M" && edge.relation_id.toString() === community._id.toString()
      );

      if (memberEdge) {
        if (memberEdge.stripe_customer_db_id) {
          const payload = {
            event: "DELETE_CUSTOMER_FROM_STRIPE",
            data: {
              stripeCustomerDBId: memberEdge.stripe_customer_db_id,
            },
          };
          publishMessage(
            JSON.stringify(payload),
            "MONOLITH_GET_MDS_STRIPE_CUSTOMER"
          );
        }

        // Mark the user edge as deleted
        await userEdgesService.repository.updateUserEdge({
          userEdgeId: memberEdge._id,
          userEdgeData: {
            isDelete: true,
            groupos_join_date: null,
            migration_date: null,
            deleteFrom: "deleteMDSuserMigrations",
          },
        });

        updateDataForConnectFrom.user_edges = connectFromUser.user_edges.filter(
          (data) => data._id.toString() !== memberEdge._id.toString()
        );
      }

      // Update the connectFrom user's data in a single update
      await this.repository.updateUser({
        user_id: connectFromUser._id,
        userData: updateDataForConnectFrom,
      });
      let getInviteUser = await inviteUser.findOne({
        email: connectFromUser["Preferred Email"],
        isDelete: false,
        relation_id: community._id,
        status: { $ne: "REVOKED" },
      });
      if (getInviteUser) {
        await inviteUser.findByIdAndUpdate(getInviteUser._id, {
          $set: { isDelete: true, status: "REVOKED" },
        });
      }
      // Delete user migration
      await this.#userMigrationsService.repository.findAndDeleteUserMigration({
        filter: { email: connectFromUser["Preferred Email"] },
      });

      return ReturnDataObj(
        "connected social account successfully!",
        200,
        false
      );
    } catch (error) {
      console.error("Error connecting social account:", error);
      return ReturnDataObj(
        "An error occurred while connecting social account.",
        500,
        false
      );
    }
  };

  getCommunityBlockedMembers = async ({
    page,
    limit,
    expand,
    relation_id,
    search,
    membership
  }) => {
    const pageNumber = parseInt(page) || 1;
    const limitNumber = parseInt(limit) || 10;

    // calculate the skip amount from the
    const skip = pageNumber && limitNumber ? (pageNumber - 1) * limitNumber : 0;

    let filterObj = {
      relation_id: ObjectId(relation_id),
      owner: false,
      type: { $in: ["M", "GU", "CU"] },
      isDelete: false,
    };
    switch (membership) {
      case "subscribe":
        filterObj.subscription_id = {
          $ne: null,
          $regex: /^[a-fA-F0-9]{24}$/,
        };
        break;
      case "nonsubscribe":
        filterObj.subscription_id = null;
        break;
      case "all":
        break;

      default:
      // console.log('Invalid membership filter');
    }

    const { data: count } = await userEdgesService.countEdges({
      filter: filterObj,
      search,
      isBlocked: true,
    });

    // get user info and user role from the users edge
    // fetch all platform users accept the user making API call
    const communityMembers = await userEdgesService.repository.findAllEdgesV2({
      filter: filterObj,
      skip,
      limit,
      expand,
      search,
      isBlocked: true,
    });

    const communityMembersList = [];
    for (const communityMember of communityMembers) {
      let communityMemberObj = { ...communityMember };
      if (communityMember?.subscription_id) {
        communityMemberObj["subscription_id"] =
          communityMember?.subscription_id;

        const data = await fetchTierData({
          susbscription_id: communityMember?.subscription_id,
        });

        communityMemberObj["tier_id"] = data ?? null;
      }
      communityMembersList.push(communityMemberObj);
    }

    return ReturnDataObj({
      data: communityMembersList,
      count,
      page: pageNumber,
      limit: limitNumber,
    });
  };

  getCommunityBlockedMembersSuggestion = async ({
    expand,
    relation_id,
    membership,
  }) => {
    const filterObj = {
      relation_id: ObjectId(relation_id),
      owner: false,
      type: { $in: ["M", "GU", "CU"] },
      isDelete: false,
    };
    switch (membership) {
      case "subscribe":
        filterObj.subscription_id = {
          $ne: null,
          $regex: /^[a-fA-F0-9]{24}$/,
        };
        break;
      case "nonsubscribe":
        filterObj.subscription_id = null;
        break;
      case "all":
        break;

      default:
      // console.log('Invalid membership filter');
    }
    // get user info and user role from the users edge
    // fetch all platform users accept the user making API call
    const communityMembers =
      await userEdgesService.repository.findAllEdgesSuggestions({
        filter: filterObj,
        expand,
        isBlocked: true,
      });

    const communityMembersList = [];
    for (let communityMember of communityMembers) {
      const communityMemberObj = {};
      if (communityMember.user_id?.name) {
        communityMemberObj["_id"] = communityMember.user_id._id;
        communityMemberObj["name"] = communityMember.user_id.name;
        communityMemberObj["email"] = communityMember.user_id.email;
        communityMemberObj["status"] = communityMember.user_id.status;
      } else {
        communityMember.user_id.groupos_join_date =
          communityMember.user_id.groupos_join_date ?? null;
        communityMember.user_id.migration_date =
          communityMember.user_id.migration_date ?? null;
        communityMemberObj["user_id"] = communityMember.user_id;
      }
      if (communityMember.subscription_id) {
        communityMemberObj["subscription_id"] = communityMember.subscription_id;
      }

      communityMembersList.push(communityMemberObj);
    }

    return ReturnDataObj({
      data: communityMembers,
    });
  };
  /**
   * Service for create the user migration from MDS to GroupOs
   */
  createUserForMigration = async ({ data }) => {
    let userMigrationLog = null;
    try {
      const skipEmail = [];
      const addUserEmail = [];
      const conflictEmail = [];

      try {
        userMigrationLog =
          await this.userMigrationLogRepository.createUserMigrationLog({
            payload: {
              data,
            },
          });
      } catch (error) {
        console.log("🚀 ~  createUserForMigration= ~ error:", error);
      }

      for await (let migrate of data) {
        const fetchUser = await this.repository.findUser({
          filter: {
            "Preferred Email": migrate.email,
            isDelete : false,
          },
        });

        if (fetchUser) {
          if (fetchUser?.isDelete === true) conflictEmail.push(migrate.email);
          else skipEmail.push(migrate.email);
        } else {
          //* add the user in the system
          let createUser = await this.repository.generateUser({
            userData: {
              first_name: migrate.first_name ?? migrate.email,
              last_name: migrate.last_name ?? "",
              display_name: migrate.first_name
                ? `${migrate.first_name.trim()} ${
                    migrate.last_name.trim() || ""
                  }`
                : migrate.email,
              "Preferred Email": migrate.email,
              status: "ACTIVE",
              isDelete: false,
              ...migrate,
            },
          });

          createUser = await this.repository.saveGeneratedUser({
            generatedUser: createUser,
          });

          if (createUser) addUserEmail.push(migrate.email);
          else conflictEmail.push(migrate.email);
        }
      }

      try {
        if (userMigrationLog) {
          await this.userMigrationLogRepository.updateUserMigrationLog({
            filter: {
              _id: userMigrationLog?._id,
              isDelete: false,
            },
            data: {
              response: {
                skipEmail,
                addUserEmail,
                conflictEmail,
                skipEmailCount: skipEmail.length,
                addUserEmailCount: addUserEmail.length,
                conflictEmailCount: conflictEmail.length,
                totalEmailCount: data.length,
              },
            },
          });
        } else {
          userMigrationLog =
            await this.userMigrationLogRepository.createUserMigrationLog({
              payload: {
                data,
                response: {
                  skipEmail,
                  addUserEmail,
                  conflictEmail,
                  skipEmailCount: skipEmail.length,
                  addUserEmailCount: addUserEmail.length,
                  conflictEmailCount: conflictEmail.length,
                  totalEmailCount: data.length,
                },
              },
            });
        }
      } catch (error) {
        console.log("🚀 ~  createUserForMigration= ~ error:", error);
      }

      return ReturnDataObj({
        success: true,
        message: "Create user migration script is run successfully!",
        data: {
          skipEmail,
          addUserEmail,
          conflictEmail,
          skipEmailCount: skipEmail.length,
          addUserEmailCount: addUserEmail.length,
          conflictEmailCount: conflictEmail.length,
          totalEmailCount: data.length,
        },
      });
    } catch (error) {
      console.log(
        "🚀 ~ file: users-service.js:7568 ~ UsersService ~ createUserForMigration= ~ error:",
        error
      );

      try {
        if (userMigrationLog) {
          await this.userMigrationLogRepository.updateUserMigrationLog({
            filter: {
              _id: userMigrationLog?._id,
              isDelete: false,
            },
            data: {
              response: {
                skipEmail,
                addUserEmail,
                conflictEmail,
                skipEmailCount: skipEmail.length,
                addUserEmailCount: addUserEmail.length,
                conflictEmailCount: conflictEmail.length,
                totalEmailCount: data.length,
              },
            },
          });
        } else {
          userMigrationLog =
            await this.userMigrationLogRepository.createUserMigrationLog({
              payload: {
                data,
                response: {
                  skipEmail,
                  addUserEmail,
                  conflictEmail,
                  skipEmailCount: skipEmail.length,
                  addUserEmailCount: addUserEmail.length,
                  conflictEmailCount: conflictEmail.length,
                  totalEmailCount: data.length,
                },
              },
            });
        }
      } catch (error) {
        console.log("🚀 ~  createUserForMigration= ~ error:", error);
      }
      return ReturnDataObj(error.message, 500, false);
    }
  };

  /**
   * Service for generate the intercom JWT
   * @param {Object} user_id
   * @param {Object} email
   * @returns {Object}
   * @description This function generates a JWT token for Intercom using the provided user_id and email.
   */
  generateIntercomJWT = async ({ user_id, email }) => {
    try {
      const data = {
        user_id,
        email,
      };

      const token = await generateJWTForIntercom({ data });
      return token ?? null;
    } catch (error) {
      console.log("🚀 ~ UsersService ~ generateIntercomJWT= ~ error:", error);
    }
  };
}

module.exports = UsersService;

// services
const UserEdgesService = require("../../user-edges/services/user-edges-service");
const RolesService = require("../../roles/services/roles-service");
const PlatformService = require("../../platform/services/platform-service");
const MailService = require("../../../../../libraries/mail-service");
const S3FileUpload = require("../../../../../libraries/file-upload-service");
const UserMigrationsService = require("../../user-migrations/services/user-migrations-service");
const CommunitiesService = require("../../communities/services/communities-service");

const mailService = new MailService();
const userEdgesService = new UserEdgesService();
const rolesService = new RolesService();
const platformService = new PlatformService();
const s3fileUploadService = new S3FileUpload();
const userMigrationsService = new UserMigrationsService();
const communitiesService = new CommunitiesService();
