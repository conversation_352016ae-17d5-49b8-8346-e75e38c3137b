const express = require("express");
const applicationFormFieldsRouter = express.Router();

const { tryCatch, parseSize } = require("../../../../../utils");
const ApplicationFormFieldsService = require("../../../services/application-form-fields-service");
const { isAuth } = require("../../../../../../../middleware/authtoken");

const service = new ApplicationFormFieldsService();


// Validate the application form field data
function validateApplicationFormField(data) {
  const errors = [];

  // Validate if the type is 'file'
  if (data.type === 'file') {
    // Check that allowed_file_types is optional but if provided, it should be an array
    if (data.allowed_file_types && (!Array.isArray(data.allowed_file_types) || data.allowed_file_types.length === 0)) {
      errors.push("allowed_file_types should be a non-empty array if provided.");
    }

    // Either min_value or max_value can be provided, or both
    if (data.min_value || data.max_value) {
      // If both are provided, validate the relationship between them
      if (data.min_value && data.max_value) {
        const minSizeInKB = Number(data.min_value);
        const maxSizeInKB = Number(data.max_value);
        if (minSizeInKB > maxSizeInKB) {
          errors.push("max_value must be greater than or equal to min_value");
        }
      }
    }
  }

  // Validate if the type is 'number' or 'text'
  if (["number", "text"].includes(data.type)) {
    // Either min_value or max_value can be provided, or both
    if (data.min_value || data.max_value) {
      // If both are provided, validate the relationship between them
      if (data.min_value && data.max_value) {
        const minValue = Number(data.min_value);
        const maxValue = Number(data.max_value);
        if (maxValue < minValue) {
          errors.push("max_value must be greater than or equal to min_value");
        }
      }
    }
  }

// Validate if the type is 'tel'
  if (data.type == "tel") {
    if (!data.country_code) {
      errors.push("Country code is required if the type is 'tel'.");
    }
  }

  // Return errors if any, or success message
  if (errors.length > 0) {
    return { valid: false, errors };
  }

  return { valid: true, errors: [] };
}

applicationFormFieldsRouter.post(
  "/",
  isAuth,
  tryCatch(async (req, res) => {
    try {
      const {
        label,
        placeholder,
        type,
        info,
        choices,
        required,
        error_message,
        enable_range, 
        min_choices, 
        max_choices, 
        platform,
        min_value,
        max_value,
        allowed_file_types,
        is_payable,
        payable_value,
        country_code,
        numberOfAnswer,
        exactValue,
        supportedFileTypes
      } = req.body;

      if (!label || !type || platform === undefined) {
        throw new Error("data is required!");
      }
      const { valid, errors } = validateApplicationFormField(req.body);
      if (!valid) {
        return res.status(400).json(errors[0]);
      }
      if (is_payable) {
        if (!payable_value) {
          return res.status(400).json("payable_value is required when is_payable is true");
        }
        if (!Number(payable_value)) {
          return res.status(400).json("payable_value must be a number");
        }
      }
      const { data, code } = await service.createApplicationFormField({
        applicationFormFieldData: {
          label: label,
          placeholder: placeholder,
          type: type,
          info: info,
          choices: choices,
          required: required,
          error_message: error_message,
          platform: platform,
          relation_id: req.relation_id,
          min_value: min_value ? Number(min_value) : null,
          max_value: max_value ? Number(max_value) : null,
          allowed_file_types: allowed_file_types,
          is_payable: is_payable,
          payable_value: Number(payable_value) ? Number(payable_value) : null,
          country_code: country_code,
          enable_range: enable_range ,
          min_choices: min_choices,
          max_choices: max_choices,
          numberOfAnswer: numberOfAnswer,
          exactValue: exactValue,
          supportedFileTypes: supportedFileTypes,
        },
      });

      res.status(code).json(data);
    } catch (error) {
      res.status(500).json(error.message);
    }
  })
);

applicationFormFieldsRouter.get(
  "/",
  isAuth,
  tryCatch(async (req, res) => {
    const { data, code } = await service.findAllApplicationFormFields({
      filter: {
        relation_id: req.relation_id,
      },
    });

    res.status(code).json(data);
  })
);

applicationFormFieldsRouter.get(
  "/:application_form_field_id",
  isAuth,
  tryCatch(async (req, res) => {
    const { application_form_field_id } = req.params;

    const { data, code } = await service.findApplicationFormField({
      filter: {
        _id: application_form_field_id,
      },
    });

    res.status(code).json(data);
  })
);

applicationFormFieldsRouter.patch(
  "/:application_form_field_id",
  isAuth,
  tryCatch(async (req, res) => {
    try {
      const { application_form_field_id } = req.params;
      const { label, type, choices, required, error_message, enable_range, min_choices, max_choices,  min_value, max_value, allowed_file_types, is_payable, payable_value, country_code,
        placeholder, info, platform, numberOfAnswer, exactValue, supportedFileTypes
      } 
      = req.body;
      const { valid, errors } = validateApplicationFormField(req.body);
      if (!valid) {
        return res.status(400).json(errors.join(","));
      }
      if (is_payable) {
        if (!payable_value) {
          return res.status(400).json("payable_value is required when is_payable is true");
        }
        if (!Number(payable_value)) {
          return res.status(400).json("payable_value must be a number");
        }
      }
      const { data, code } = await service.updateApplicationFormField({
        application_form_field_id,
        applicationFormFieldData: {
          label: label,
          type: type,
          choices: choices,
          required: required,
          error_message: error_message,
          min_value: min_value ? Number(min_value) : null,
          max_value: max_value ? Number(max_value) : null,
          allowed_file_types: allowed_file_types ? allowed_file_types : [],
          is_payable: is_payable,
          payable_value: Number(payable_value) ? Number(payable_value) : null,
          country_code: country_code,
          placeholder: placeholder,
          info: info,
          platform: platform,
          enable_range: enable_range ,
          min_choices: min_choices,
          max_choices: max_choices,
          numberOfAnswer: numberOfAnswer,
          exactValue: exactValue,
          supportedFileTypes: supportedFileTypes,

        },
      });

      res.status(code).json(data);

    } catch (error) {
      res.status(500).json(error.message);
    }
  })
);

applicationFormFieldsRouter.delete(
  "/:application_form_field_id",
  isAuth,
  tryCatch(async (req, res) => {
    const { application_form_field_id } = req.params;

    const { data, code } = await service.deleteApplicationFormField({
      application_form_field_id,
    });

    res.status(code).json(data);
  })
);

module.exports = applicationFormFieldsRouter;
