const { application_form_fields } = require("../models");

class ApplicationFormFieldsRepository {
  /**
   *
   * @description object creation
   */
  createApplicationFormField = async ({ applicationFormFieldData }) => {
    const applicationFormFieldObj = new application_form_fields(
      applicationFormFieldData
    );
    return await applicationFormFieldObj.save();
  };

  /**
   *
   * @description When api have pagination
   */
  getCount = async ({ filter }) => {
    return await application_form_fields.countDocuments(filter);
  };
  findAllApplicationFormFields = async ({
    filter,
    skip = 0,
    limit = 100,
    expand = false,
  }) => {
    if (expand) {
      const populateObj = {};
      return await application_form_fields
        .find(filter)
        .populate(populateObj)
        .skip(skip)
        .limit(limit);
    } else {
      return await application_form_fields.find(filter).skip(skip)
    }
  };

  /**
   *
   * @description for accessing object in same service
   */
  getApplicationFormField = async ({
    applicationFormFieldId,
    expand = false,
  }) => {
    if (expand) {
      const populateObj = {};
      return await application_form_fields
        .findById(applicationFormFieldId)
        .populate(populateObj);
    } else {
      return await application_form_fields.findById(applicationFormFieldId);
    }
  };
  updateApplicationFormField = async ({
    applicationFormFieldId,
    applicationFormFieldData,
    getUpdatedData = true,
  }) => {
    if (getUpdatedData) {
      return await application_form_fields.findByIdAndUpdate(
        applicationFormFieldId,
        applicationFormFieldData,
        {
          new: true,
        }
      );
    } else {
      return await application_form_fields.findByIdAndUpdate(
        applicationFormFieldId,
        applicationFormFieldData
      );
    }
  };
  deleteApplicationFormField = async ({ applicationFormFieldId }) => {
    return await application_form_fields.findByIdAndDelete(
      applicationFormFieldId
    );
  };

  /**
   *
   * @description for accessing object in other service / same service
   */
  findApplicationFormField = async ({ filter, expand = false }) => {
    if (expand) {
      const populateObj = {};
      return await application_form_fields
        .findOne(filter)
        .populate(populateObj);
    } else {
      return await application_form_fields.findOne(filter);
    }
  };
  findAndUpdate_ = async ({ filter, _Data, getUpdatedData = true }) => {
    if (getUpdatedData) {
      return await application_form_fields.findOneAndUpdate(filter, _Data, {
        new: true,
      });
    } else {
      return await application_form_fields.findOneAndUpdate(filter, _Data);
    }
  };
  findAndDelete_ = async ({ filter }) => {
    return await application_form_fields.findOneAndDelete(filter);
  };
}

module.exports = ApplicationFormFieldsRepository;
