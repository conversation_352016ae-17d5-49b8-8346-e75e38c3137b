const mongoose = require("mongoose");
const {
  DB_COLLECTIONS,
} = require("../../../../../../utils/db-collection-names");

const applicationFormFieldsSchema = new mongoose.Schema(
  {
    label: { type: String, required: true },
    placeholder: { type: String },
    type: {
      type: String,
      default: "text",
      enum: [
        "text",
        "number",
        "date",
        "checkbox",
        "radio",
        "select",
        "file",
        "email",
        "tel",
        "textarea"
      ],
      required: true,
    },
    choices: [String],
    info: { type: String },
    platform: { type: Boolean, required: true },
    relation_id: {
      type: mongoose.Schema.Types.ObjectId,
      ref: function () {
        return this.platform
          ? DB_COLLECTIONS.PLATFORM
          : DB_COLLECTIONS.COMMUNITIES; // 'this' refers to the document being populated
      },
      required: true,
    },
    is_payable: { type: Boolean, required: false },
    enable_range: { type: Boolean, default: false },
    min_choices: { type: Number, default: null },
    max_choices: { type: Number, default: null },
    payable_value: { type: Number, default: null },
    min_value: { type: Number, default: null }, // for field type number,text and file
    max_value: { type: Number, default: null }, // for field type number,text and file
    allowed_file_types: {
      type: [String],
      enum: [
        "mp4",
        "mov",
        "webp",
        "xlsx",
        "avi",
        "mkv",
        "wmv",
        "flv",
        "mpeg",
        "mpg",
        "3gp",
        "m4v",
        "doc",
        "docx",
        "wps",
        "wpd",
        "tex",
        "txt",
        "rtf",
        "odt",
        "mp3",
        "wav",
        "aac",
        "ogg",
        "wma",
        "flac",
        "m4a",
        "aiff",
        "aif",
        "mid",
        "midi",
        "amr",
        "png",
        "jpg",
        "jpeg",
        "gif",
        "heic",
        "pdf",
        "csv",
      ], // Add other types if needed
    },
    required: { type: Boolean, default: false },
    error_message: { type: String },
    country_code: { type: String },
    exactValue: { type: Number, default: null },
    numberOfAnswer: {
      type: String,
      enum: ['Unlimited', 'Exact Number', 'Range'],
      default: 'Unlimited'
    },
    supportedFileTypes: {
      type: String,
      enum: ['', 'any', 'images', 'videos', 'documents', 'sheets'],
      default: ''
    }
  },
  { timestamps: true }
);

// pre hook function
const projectData = function (next) {
  this.select("-platform -relation_id -isDelete -createdAt -updatedAt -__v");
  next();
};

// apply pre hooks
applicationFormFieldsSchema
  .pre("find", projectData)
  .pre("findOne", projectData)
  .pre("findOneAndUpdate", projectData)
  .pre("findById", projectData)
  .pre("findByIdAndUpdate", projectData);

// create Index
applicationFormFieldsSchema.index({ isDelete: 1 });

module.exports = mongoose.model(
  DB_COLLECTIONS.APPLICATION_FORM_FIELDS,
  applicationFormFieldsSchema,
  DB_COLLECTIONS.APPLICATION_FORM_FIELDS
);
