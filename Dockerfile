FROM public.ecr.aws/docker/library/node:22.13

# ENV OTEL_SERVICE_NAME="mds-prod-groupos-api"

# Install system dependencies
RUN apt update -y && apt install ffmpeg -y && apt install -y apt-transport-https gnupg wget supervisor

# Add Grafana repository key
RUN mkdir -p /etc/apt/keyrings/
RUN wget -q -O - https://apt.grafana.com/gpg.key | gpg --dearmor | tee /etc/apt/keyrings/grafana.gpg > /dev/null

# Add Grafana repository
RUN echo "deb [signed-by=/etc/apt/keyrings/grafana.gpg] https://apt.grafana.com stable main" | tee /etc/apt/sources.list.d/grafana.list

# Update package lists and install Grafana Agent
RUN apt update -y && apt install -y grafana-agent

# Copy Grafana Agent configuration file
COPY grafana-agent.yaml /etc/grafana-agent.yaml

# Create Supervisor configuration file for Grafana Agent
RUN mkdir -p /etc/supervisor/conf.d
RUN echo "[program:grafana-agent]" > /etc/supervisor/conf.d/grafana-agent.conf
RUN echo "command=/usr/bin/grafana-agent --config.file=/etc/grafana-agent.yaml" >> /etc/supervisor/conf.d/grafana-agent.conf
RUN echo "user=node" >> /etc/supervisor/conf.d/grafana-agent.conf   # Specify the non-root user

# Set working directory for Node.js app
WORKDIR /usr/app

# Copy Node.js app files
COPY . .

RUN chmod +x start.sh

RUN npm install

# RUN apt update -y && npm install && npm install @opentelemetry/sdk-node @opentelemetry/api @opentelemetry/auto-instrumentations-node @opentelemetry/sdk-metrics && npm install --save @opentelemetry/exporter-trace-otlp-proto @opentelemetry/exporter-metrics-otlp-proto 

# Expose Node.js app port
EXPOSE 8080

# Run the startup script
CMD ["./start.sh"]