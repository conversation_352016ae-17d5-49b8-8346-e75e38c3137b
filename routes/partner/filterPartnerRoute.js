const express = require("express");
const router = express.Router();
const { isAuth  } = require("../../middleware/authtoken");
const { checkAccess } = require("../../middleware/accessValidator");
const { MODULES_NAME } = require("../../utils/module-names-for-check-access");
const controller = require("../../controller/partner/filterPartnerController")
const statisticController = require("../../controller/partner/partnerStatisticsController")


// partner filter and search routes
router.get("/getPartnerBySearchAndFilter", isAuth, checkAccess([MODULES_NAME.PARTNER_MODULE]), controller.getPartnerBySearchAndFilter);
router.get("/getPartnerByFilter", isAuth, checkAccess([MODULES_NAME.PARTNER_MODULE]), controller.getPartnerByFilter);
router.get("/getPartnerBySorting", isAuth, checkAccess([MODULES_NAME.PARTNER_MODULE]), controller.getPartnerBySorting);

router.get("/getPartnerByFilterAndSorting", isAuth, checkAccess([MODULES_NAME.PARTNER_MODULE]), controller.getPartnerByFilterAndSortingV2);
router.get("/getPartnerByFilterAndSortingCount", isAuth, checkAccess([MODULES_NAME.PARTNER_MODULE]), controller.getPartnerByFilterAndSortingCountV2);
router.get("/getPartnerDetails/:id", isAuth, checkAccess([MODULES_NAME.PARTNER_MODULE]), controller.getPartnerDetailsV2);

router.get("/getPartnerReviewDetails/:id", isAuth, checkAccess([MODULES_NAME.PARTNER_MODULE]), controller.getPartnerReviewDetails);

router.get("/getPartnerDetailVideos/:id", isAuth, checkAccess([MODULES_NAME.PARTNER_MODULE]), controller.getPartnerDetailVideos);      
router.get("/getPartnerDetailPosts/:id", isAuth, checkAccess([MODULES_NAME.PARTNER_MODULE]), controller.getPartnerDetailPosts);
router.get("/getPartnersInOtherCategories/:id", isAuth, checkAccess([MODULES_NAME.PARTNER_MODULE]), controller.getPartnersInOtherCategories);

/*admi side apis*/
router.get("/getPartnerDetailsForAdmin/:id", isAuth, checkAccess([MODULES_NAME.PARTNER_MODULE]), controller.getPartnerDetails);
router.get("/getPartnerReviewDetailsForAdmin/:id", isAuth, checkAccess([MODULES_NAME.PARTNER_MODULE]), controller.getPartnerReviewDetails);
router.get("/getPartnerDetailVideosForAdmin/:id", isAuth, checkAccess([MODULES_NAME.PARTNER_MODULE]), controller.getPartnerDetailVideos);
router.get("/getPartnerDetailPostsForAdmin/:id", isAuth, checkAccess([MODULES_NAME.PARTNER_MODULE]), controller.getPartnerDetailPosts);
router.get("/getPartnersInOtherCategoriesForAdmin/:id", isAuth, checkAccess([MODULES_NAME.PARTNER_MODULE]), controller.getPartnersInOtherCategories);
router.get("/getClaimOfferDetailsForAdmin/:id", isAuth, checkAccess([MODULES_NAME.PARTNER_MODULE]), statisticController.getClaimOfferDetails);

//category list for drop down - partner listing page
router.get("/AllCategoryListForUser", isAuth, checkAccess([MODULES_NAME.PARTNER_MODULE]), controller.getAllCategoryLists);

//sub category list for drop down - partner listing page
router.get("/getSubCategoryListForUser", isAuth, checkAccess([MODULES_NAME.PARTNER_MODULE]), controller.getSubCategoryListForUser);


module.exports = router;