const express = require("express");
const router = express.Router();
const { checkAccess } = require("../../middleware/accessValidator");
const { MODULES_NAME } = require("../../utils/module-names-for-check-access");
const controller = require("../../controller/partner/partnerReviewController");
const { uploadPartnerImages, uploadPartnerImagesS3Bucket } = require("../../utils/mediaUpload");

const { isAuth } = require("../../middleware/authtoken");

// partner review crud operations from the user side
router.post("/createPartnerReview", isAuth, checkAccess([MODULES_NAME.PARTNER_MODULE]), controller.createPartnerReview);
router.post("/editPartnerReview/:id", isAuth, checkAccess([MODULES_NAME.PARTNER_MODULE]), controller.editPartnerReview);
router.patch("/deletePartnerReview/:id", isAuth, checkAccess([MODULES_NAME.PARTNER_MODULE]), controller.deletePartnerReview);
router.get("/getAllPartnerReview/list", isAuth, checkAccess([MODULES_NAME.PARTNER_MODULE]), controller.getAllPartnerReviewList);
router.post("/reportReview", isAuth, checkAccess([MODULES_NAME.PARTNER_MODULE]), controller.reportReview);


// partner review admin APIs routes
router.get("/partnerReview/list",  isAuth, checkAccess([MODULES_NAME.PARTNER_MODULE]), controller.getPartnerReviewList);
router.get("/partnerReview/getPartnerReviewSuggestionList",  isAuth, checkAccess([MODULES_NAME.PARTNER_MODULE]), controller.getPartnerReviewSuggestionListV2);
router.get("/reviewDetail/:id",  isAuth, checkAccess([MODULES_NAME.PARTNER_MODULE]), controller.getPartnerReviewDetail);
router.get("/reviewsByPartner/:id",  isAuth, checkAccess([MODULES_NAME.PARTNER_MODULE]), controller.getPartnerReviewListById);
router.get("/reviewSuggestionList/:id",  isAuth, checkAccess([MODULES_NAME.PARTNER_MODULE]), controller.getPartnerReviewSuggestionList);
router.get("/getPartnerReviewByIdSuggestionList/:id",  isAuth, checkAccess([MODULES_NAME.PARTNER_MODULE]), controller.getPartnerReviewByIdSuggestionList);
router.patch("/deleteReview/:id",  isAuth, checkAccess([MODULES_NAME.PARTNER_MODULE]), controller.deleteReview);
router.post("/approveOrRejectReview/:id",  isAuth, checkAccess([MODULES_NAME.PARTNER_MODULE]), controller.approveOrRejectPartnerReview);
router.post("/sendApproveOrRejectReviewEmail/:id",  isAuth, checkAccess([MODULES_NAME.PARTNER_MODULE]), controller.sendApproveOrRejectPartnerReviewEmail);

//partner new review
router.get("/getNewPartnerReview",  isAuth, checkAccess([MODULES_NAME.PARTNER_MODULE]), controller.getNewPartnerReviewCount);
router.post("/UpdateNewPartnerReviewFlag",  isAuth, checkAccess([MODULES_NAME.PARTNER_MODULE]), controller.UpdateNewPartnerReviewFlag);
router.get("/getPartnerReviewReportUserList/:id",  isAuth, checkAccess([MODULES_NAME.PARTNER_MODULE]), controller.getPartnerReviewReportUserList);

module.exports = router;