const express = require("express");
const router = express.Router();
const { isAuth } = require("../../middleware/authtoken");
const { uploadBannerImage, uploadBannerImageS3Bucket } = require("../../utils/mediaUpload");
const adminBannerController = require("../../controller/newsManagement/adminBannerController");
const { checkAccess } = require("../../middleware/accessValidator");
const { MODULES_NAME } = require("../../utils/module-names-for-check-access");


router.post("/news/createBanner", isAuth, checkAccess([MODULES_NAME.NEWS_MODULE]), uploadBannerImage, uploadBannerImageS3Bucket, adminBannerController.createBannerV2);
router.patch("/news/editBanner/:id", isAuth, checkAccess([MODULES_NAME.NEWS_MODULE]), uploadBannerImage, uploadBannerImageS3Bucket, adminBannerController.editBannerV2);
router.patch("/news/deleteBanner/:id", isAuth, checkAccess([MODULES_NAME.NEWS_MODULE]), adminBannerController.deleteBanner);
router.get("/news/getAllBanner", isAuth, checkAccess([MODULES_NAME.NEWS_MODULE]), adminBannerController.getAllBanner);
router.get("/news/getAllBannerSuggestionList", isAuth, checkAccess([MODULES_NAME.NEWS_MODULE]), adminBannerController.getAllBannerSuggestionList);
router.get("/news/getBannerById/:id", isAuth, checkAccess([MODULES_NAME.NEWS_MODULE]), adminBannerController.getBannerDetail);
router.post("/news/reOrderBanner", isAuth, checkAccess([MODULES_NAME.NEWS_MODULE]), adminBannerController.reorderBanner);
router.get("/news/getAllBannerUsers", isAuth, checkAccess([MODULES_NAME.NEWS_MODULE]), adminBannerController.getAllBannerUsersV2);
module.exports = router;  